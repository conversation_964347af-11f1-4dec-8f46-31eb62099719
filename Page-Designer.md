# editor

## 本地开发

**⚠️ 重要提示**

如果本地根路径没有 `.env.development` 文件，请自行创建，内容如下：

```env
# 仅用于开发环境(admin/admin@123)
# 由于 Fortify 禁止在代码中存储密码，本地开发时请自行填写。
VITE_CDP_LOGIN_CODE=admin
VITE_CDP_LOGIN_PWD=
```


moduleId: 66 目前比较常用
http://localhost:8888/packages/amis-editor/?moduleId=[]&pageId=[]#/

## 部署

先构建 125325, 再构建 40764

## 增加自定义 icon

在 packages/amis-editor-core/src/icons/index.tsx 文件注册

## editor 入口

packages/amis-editor/examples/Editor.tsx

## editor 数据管理

packages/amis-editor-core/src/store/editor.ts
packages/amis-editor-core/src/component/Editor.tsx

# 让参数可选

addScope: 可以增加可以解析的参数

# action

button submit reset 都是使用这个 renderer

# 增加事件

packages/amis-editor/src/renderer/event-control/actionsPanelPlugins/index.tsx

# 全局事件 A/B

例子中的演示，不需要可以删除

# 增加自定义组件

## 注册组件

packages/amis-core/src/renderers/register.ts

## 注册组件对应插件

packages/amis-editor-core/src/manager.ts

### Form

条件判断中使用 this 能获取到 form 所有值，而 data 不行

# 国际化

## 编辑器工具本身的国际化

### 工作原理

1. 使用 plugin-react-i18n 插件将文件中的中文替换成\_i18n(xxx)形式
2. 引入 locale 目录下的国际化内容，根据当前语言转化为指定内容
3. 通过获取 localStorage 的 umi_locale 的值来设置 suda-i18n-locale，从而初始化工具的国际化语言。

### 使用方法

1. 当我们在工具内增加中文后，需要进入对应 package，运行如下命令：

```javascript
npm run i18n:update
```

2. 运行后会生成新增文本的国际化内容。进入 locale 目录下的 zh-CN.ts 下可以看到新增的国际化内容
3. 在 en-Us.ts 文件下增加对应的英文翻译即可

# 同步 business 代码

如果只更新了 packages/amis/src/renderers/Business 下的代码，[phoenix92-page-designer]packages/amis/src/renderers/Business 替代[phoenix92-journey-designer]src/views/LocalTest/renderers

如果是 mac 可以安装 rsync 工具直接执行
rsync -av --delete 本人代码路径/phoenix92-page-designer/packages/amis/src/renderers/Business/ 本人代码路径/pageflow-frontend/src/views/LocalTest/renderers/Business/

如果新增了文件需要将[phoenix92-page-designer]packages/amis/src/business.tsx 中的代码同步到[phoenix92-journey-designer]src/views/LocalTest/lowcode.ts

iconfont 更新
[phoenix92-page-designer]packages/amis-editor/pos-assets [phoenix92-journey-designer]public/pos-assets

# 研发云 token

b9fca5005c7e1acd790bd66d41f1e4796158a8fc

# 展示信息

给某个组件添加数据，让没有数据的时候可以展示一些假数据 filterProps 数据可以实现

# 原理

## 渲染器

1. 注册渲染器/解析渲染器 packages/amis-core/src/factory.tsx
2. 执行渲染器 packages/amis-core/src/SchemaRenderer.tsx
3. 注册分为同步注册和异步注册

buildMockData ??? 构建模拟数据

# 弹窗取不到值的问题

packages/amis-editor-core/src/component/SubEditor.tsx 添加缺少的属性

# 文本替换逻辑

packages/amis-core/src/utils/replaceText.ts

# 目前发现的问题

1. 表单校验无法取消校验出错时全局提示
2. 通过setValue给表单设置值时，校验方式选择改动即触发的方式的表单项会触发校验
3. 缺少常用的popEdit组件，目前通过设置比较费时间
4. 设置发送数据时，如果是对象不能通过页面设置需要直接改schema
5. 请求数据中字符串为空的情况
6. 无界removeEventListener函数没有清除掉捕获的函数 https://developer.mozilla.org/zh-CN/docs/Web/API/EventTarget/removeEventListener#usecapture