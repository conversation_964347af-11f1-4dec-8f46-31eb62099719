import { registerRenderer } from 'amis-core';

import './renderers/Business/locale/index';
import './renderers/Business/AccountList';
import './renderers/Business/AddonOfferInfo';
import './renderers/Business/ChooseNumberAndSim';
import './renderers/Business/Customer360/AccountBalance/AccountBalance';
import './renderers/Business/Customer360/AccountBalance/BalanceCardInfo';
import './renderers/Business/Customer360/AccountBalance/BalanceCardInfoList';
import './renderers/Business/Customer360/CustomerCase';
import './renderers/Business/Customer360/CustomerCommonPanel/CommonPanelListItem';
import './renderers/Business/Customer360/CustomerCommonPanel/CommonPanelTagItem';
import './renderers/Business/Customer360/CustomerCommonPanel/CustomerCommonPanel';
import './renderers/Business/Customer360/CustomerCommonTabs';
import './renderers/Business/Customer360/CustomerGroup/CustomerGroup';
import './renderers/Business/Customer360/CustomerProfile';
import './renderers/Business/Customer360/CustomerProfile/CustomerProfileCardInfo';
import './renderers/Business/Customer360/CustomerProfile/CustomerProfileCardInfoList';
import './renderers/Business/Customer360/CustomerProfile/CustomerProfileTextInfo';
import './renderers/Business/Customer360/CustomerProfile/CustomerProfileTextInfoList';
import './renderers/Business/Customer360/CustomerSpecialList/CustomerSpecialList';
import './renderers/Business/Customer360/CustomerSubscriberItem/CustomerSubscriberItem';
import './renderers/Business/Customer360/CustomerSubscribers/CustomerSubscribers';
import './renderers/Business/Customer360/CustomerSubscriberTag/CustomerSubscriberTag';
import './renderers/Business/Customer360/CustomerTimeline/CustomerTabs';
import './renderers/Business/Customer360/CustomerTimeline/CustomerTimeline';
import './renderers/Business/Customer360/CustomerTimeline/CustomerTimelineDun';
import './renderers/Business/Customer360/CustomerTimeline/CustomerTimelineItem';
import './renderers/Business/Customer360/CustomerTitle/CustomerTitle';
import './renderers/Business/Customer360/GoodsReservation/GoodsReservation';
import './renderers/Business/Customer360/Interaction';
import './renderers/Business/Customer360/OfferRecommend/OfferRecommend';
import './renderers/Business/Customer360/OrderInProcess/OrderInProcess';
import './renderers/Business/Customer360/OrderInProcess/OrderInProcessItem';
import './renderers/Business/Customer360/OrderInProcess/OrderInProcessItemButtons';
import './renderers/Business/Customer360/OrderInProcess/OrderInProcessItemCopy';
import './renderers/Business/CustomerProfile';
import './renderers/Business/CustomerSearch';
import './renderers/Business/CustSearchEntry';
import './renderers/Business/Header';
import './renderers/Business/HomeMenuList';
import './renderers/Business/HomeProductAd';
import './renderers/Business/HomeTopSearch';
import './renderers/Business/HotOfferList';
import './renderers/Business/LeftMenu';
import './renderers/Business/LeftMenuItem';
import './renderers/Business/LoginContent';
import './renderers/Business/LoginForm';
import './renderers/Business/MenuItem';
import './renderers/Business/OrderConfirmation';
import './renderers/Business/OrderEntryHomeTab';
import './renderers/Business/OrderSuccessfully';
import './renderers/Business/PaymentTab';
import './renderers/Business/SearchCustomer';
import './renderers/Business/ShopHeader';
import './renderers/Business/ShopOfferCardList';
import './renderers/Business/ShopOfferTypeList';
import './renderers/Business/ShopSearchFilter';
import './renderers/Business/SubscriberList';
import './renderers/Business/OrderList';
import './renderers/Business/SubsEventName';
import './renderers/Entity/CfsAttribute'
import './renderers/Entity/RfsAttribute'
import './renderers/Entity/ResAttribute'
import './renderers/Entity/SubCfsAttribute'
import './renderers/Entity/ServiceOrderAttribute'

registerRenderer({
  type: 'pad-pos-signature',
  getComponent: () => import('./renderers/Business/Signature'),
});

registerRenderer({
  type: 'pad-pos-icon-font-render',
  getComponent: () => import('./renderers/Business/IconFontRender'),
});

registerRenderer({
  type: 'pad-pos-input-cmp',
  getComponent: () => import('./renderers/Business/InputCmp'),
});

registerRenderer({
  type: 'pos-icon-picker',
  getComponent: () => import('./renderers/Business/IconPicker'),
});

registerRenderer({
  type: 'pad-pos-background-image-area',
  getComponent: () => import('./renderers/Business/BackgroundImageArea'),
});

registerRenderer({
  type: 'gsm-new-connection-addon-list',
  getComponent: () =>
    import('./renderers/Business/GsmNewConnection/AddonList/AddonList'),
});

registerRenderer({
  type: 'business-addon-list',
  getComponent: () => import('./renderers/Business/AddonList/index'),
});

registerRenderer({
  type: 'addon-cmp',
  getComponent: () => import('./renderers/Business/AddonCmp/index'),
});
registerRenderer({
  type: 'addon-cmp-preview',
  getComponent: () => import('./renderers/Business/AddonCmpPreview'),
});
registerRenderer({
  type: 'pad-pos-fixed-footer',
  getComponent: () => import('./renderers/Business/FixedFooter'),
});

registerRenderer({
  type: 'Data-Debug',
  getComponent: () => import('./renderers/Business/DataDebug'),
});

registerRenderer({
  type: 'coc-side-menu',
  getComponent: () => import('./renderers/Business/SideMenu/index'),
});

registerRenderer({
  type: 'pad-pos-subs-card',
  getComponent: () => import('./renderers/Business/SubsCard'),
});

registerRenderer({
  type: 'pad-pos-customer-order-info',
  getComponent: () => import('./renderers/Business/CustomerOrderInfo'),
});

registerRenderer({
  type: 'pad-pos-order-list',
  getComponent: () => import('./renderers/Business/OrderList'),
});

registerRenderer({
  type: 'pad-pos-customer-order-info-item',
  getComponent: () => import('./renderers/Business/CustomerOrderInfoItem'),
});

registerRenderer({
  type: 'crm-attr-form',
  getComponent: () =>
    import(
      './renderers/Business/CrmFishComponentsRegistered/AttrForm/AttrFormRender'
    ),
});

registerRenderer({
  type: 'crm-select-number',
  getComponent: () =>
    import(
      './renderers/Business/CrmFishComponentsRegistered/SelectNumberPopView/SelectNumberPopRender'
    ),
});

registerRenderer({
  type: 'crm-gsm-new-port-in-info-view',
  getComponent: () =>
    import(
      './renderers/Business/CrmFishComponentsRegistered/GsmNewPortInInfoView/GsmNewPortInInfoView'
    ),
});

registerRenderer({
  type: 'crm-cust-order-info-page-view',
  getComponent: () =>
    import(
      './renderers/Business/CrmFishComponentsRegistered/CustOrderInfoPageView/CustOrderInfoPageView'
    ),
});

registerRenderer({
  type: 'crm-gsm-new-sim-type-view',
  getComponent: () =>
    import(
      './renderers/Business/CrmFishComponentsRegistered/GsmNewSimTypeView/GsmNewSimTypeView'
    ),
});

registerRenderer({
  type: 'crm-gsm-new-check-port-in-view',
  getComponent: () =>
    import(
      './renderers/Business/CrmFishComponentsRegistered/GsmNewCheckPortInView/GsmNewCheckPortInView'
    ),
});

registerRenderer({
  type: 'crm-select-customer',
  getComponent: () =>
    import(
      './renderers/Business/CrmFishComponentsRegistered/SelectCustomerView/index'
    ),
});

registerRenderer({
  type: 'crm-order-query',
  getComponent: () =>
    import(
      './renderers/Business/CrmFishComponentsRegistered/OrderQueryView/index'
    ),
});

registerRenderer({
  type: 'crm-order-table',
  getComponent: () =>
    import(
      './renderers/Business/CrmFishComponentsRegistered/OrderTableView/index'
    ),
});
registerRenderer({
  type: 'crm-bo-service',
  getComponent: () =>
    import(
      './renderers/Business/CrmFishComponentsRegistered/BOServiceView/BOServiceView'
    ),
});

registerRenderer({
  type: 'crm-subscriber-list',
  getComponent: () =>
    import(
      './renderers/Business/CrmFishComponentsRegistered/SubscriberView/index'
    ),
});

registerRenderer({
  type: 'crm-cust-main-detail-info',
  getComponent: () =>
    import(
      './renderers/Business/CrmFishComponentsRegistered/CustMainDetailInfo/index'
    ),
});

registerRenderer({
  type: 'crm-order-view',
  getComponent: () =>
    import(
      './renderers/Business/CrmFishComponentsRegistered/CrmOrderView/index'
    ),
});

registerRenderer({
  type: 'crm-account-list-view',
  getComponent: () =>
    import(
      './renderers/Business/CvbsFishComponentsRegistered/AccountListView/index'
    ),
});

registerRenderer({
  type: 'crm-add-account-view',
  getComponent: () =>
    import(
      './renderers/Business/CvbsFishComponentsRegistered/AddAccountView/index'
    ),
});

registerRenderer({
  type: 'crm-offer-category',
  getComponent: () =>
    import(
      './renderers/Business/CrmFishComponentsRegistered/CrmOfferCategory/index'
    ),
});

registerRenderer({
  type: 'easy-form',
  getComponent: () => import('./renderers/EasyForm'),
});

registerRenderer({
  type: 'crm-installation-address',
  getComponent: () =>
    import(
      './renderers/Business/CrmFishComponentsRegistered/InstallationAddress/index'
    ),
});

registerRenderer({
  type: 'crm-maintenance-contact',
  getComponent: () =>
    import(
      './renderers/Business/CrmFishComponentsRegistered/MaintenanceContact/index'
    ),
});

registerRenderer({
  type: 'easy-table',
  getComponent: () => import('./renderers/EasyTable'),
});

registerRenderer({
  type: 'crm-coupon',
  getComponent: () =>
    import('./renderers/Business/CrmFishComponentsRegistered/Coupon/index'),
});

registerRenderer({
  type: 'crm-account-selector',
  getComponent: () =>
    import(
      './renderers/Business/CrmFishComponentsRegistered/AccountSelector/index'
    ),
});

registerRenderer({
  type: 'crm-installation-information',
  getComponent: () =>
    import(
      './renderers/Business/CrmFishComponentsRegistered/InstallationInformation/index'
    ),
});

registerRenderer({
  type: 'pad-pos-order-successfully',
  getComponent: () => import('./renderers/Business/OrderSuccessfully'),
});

registerRenderer({
  type: 'customer-360-customer-profile-card-info',
  getComponent: () =>
    import(
      './renderers/Business/Customer360/CustomerProfile/CustomerProfileCardInfo'
    ),
});

registerRenderer({
  type: 'customer-360-customer-profile-card-info-list',
  getComponent: () =>
    import(
      './renderers/Business/Customer360/CustomerProfile/CustomerProfileCardInfoList'
    ),
});

registerRenderer({
  type: 'customer-360-customer-common-panel',
  getComponent: () =>
    import(
      './renderers/Business/Customer360/CustomerCommonPanel/CustomerCommonPanel'
    ),
});

registerRenderer({
  type: 'customer-360-common-panel-tag-item',
  getComponent: () =>
    import(
      './renderers/Business/Customer360/CustomerCommonPanel/CommonPanelTagItem'
    ),
});

registerRenderer({
  type: 'customer-360-common-panel-list-item',
  getComponent: () =>
    import(
      './renderers/Business/Customer360/CustomerCommonPanel/CommonPanelListItem'
    ),
});

registerRenderer({
  type: 'customer-360-customer-timeline',
  getComponent: () =>
    import(
      './renderers/Business/Customer360/CustomerTimeline/CustomerTimeline'
    ),
});

registerRenderer({
  type: 'customer-360-customer-timeline-item',
  getComponent: () =>
    import(
      './renderers/Business/Customer360/CustomerTimeline/CustomerTimelineItem'
    ),
});

registerRenderer({
  type: 'customer-360-customer-tabs',
  getComponent: () =>
    import('./renderers/Business/Customer360/CustomerTimeline/CustomerTabs'),
});

registerRenderer({
  type: 'customer-360-customer-title',
  getComponent: () =>
    import('./renderers/Business/Customer360/CustomerTitle/CustomerTitle'),
});

registerRenderer({
  type: 'customer-360-customer-title',
  getComponent: () =>
    import('./renderers/Business/Customer360/CustomerTitle/CustomerTitle'),
});

registerRenderer({
  type: 'customer-360-customer-timeline-dun',
  getComponent: () =>
    import(
      './renderers/Business/Customer360/CustomerTimeline/CustomerTimelineDun'
    ),
});

registerRenderer({
  type: 'customer-360-account-balance',
  getComponent: () =>
    import('./renderers/Business/Customer360/AccountBalance/AccountBalance'),
});

registerRenderer({
  type: 'customer-360-order-in-process',
  getComponent: () =>
    import('./renderers/Business/Customer360/OrderInProcess/OrderInProcess'),
});

registerRenderer({
  type: 'customer-360-order-in-process-item',
  getComponent: () =>
    import(
      './renderers/Business/Customer360/OrderInProcess/OrderInProcessItem'
    ),
});

registerRenderer({
  type: 'customer-360-order-in-process-item-copy',
  getComponent: () =>
    import(
      './renderers/Business/Customer360/OrderInProcess/OrderInProcessItemCopy'
    ),
});

registerRenderer({
  type: 'customer-360-order-in-process-item-buttons',
  getComponent: () =>
    import(
      './renderers/Business/Customer360/OrderInProcess/OrderInProcessItemButtons'
    ),
});

registerRenderer({
  type: 'customer-360-goods-reservation',
  getComponent: () =>
    import(
      './renderers/Business/Customer360/GoodsReservation/GoodsReservation'
    ),
});

registerRenderer({
  type: 'customer-360-customer-subscriber-item',
  getComponent: () =>
    import(
      './renderers/Business/Customer360/CustomerSubscriberItem/CustomerSubscriberItem'
    ),
});

registerRenderer({
  type: 'pad-pos-left-menu-item',
  getComponent: () => import('./renderers/Business/LeftMenuItem'),
});

registerRenderer({
  type: 'customer-360-customer-subscribers',
  getComponent: () =>
    import(
      './renderers/Business/Customer360/CustomerSubscribers/CustomerSubscribers'
    ),
});

registerRenderer({
  type: 'crm-social-media',
  getComponent: () =>
    import(
      './renderers/Business/CrmFishComponentsRegistered/SocialMedia/index'
    ),
});

registerRenderer({
  type: 'crm-select-subscriber',
  getComponent: () =>
    import(
      './renderers/Business/CrmFishComponentsRegistered/ChooseExistSubsWin/index'
    ),
});
registerRenderer({
  type: 'bcare-account-tree-select',
  getComponent: () =>
    import('./renderers/Business/Bcare/AccountTreeSelect/index'),
});

registerRenderer({
  type: 'crm-addon',
  getComponent: () =>
    import('./renderers/Business/CrmFishComponentsRegistered/Addon/index'),
});

registerRenderer({
  type: 'crm-select-number-port-in-view',
  getComponent: () =>
    import(
      './renderers/Business/CrmFishComponentsRegistered/SelectNumberPortInView/index'
    ),
});

registerRenderer({
  type: 'crm-article-title',
  getComponent: () =>
    import(
      './renderers/Business/CrmFishComponentsRegistered/ArticleTitle/index'
    ),
});

registerRenderer({
  type: 'crm-order-entry-plan-info-view',
  getComponent: () =>
    import(
      './renderers/Business/CrmFishComponentsRegistered/OrderEntryPlanInfoView/index'
    ),
});

registerRenderer({
  type: 'crm-add-bundle-member',
  getComponent: () =>
    import(
      './renderers/Business/CrmFishComponentsRegistered/AddBundleMember/index'
    ),
});

registerRenderer({
  type: 'crm-agreement',
  getComponent: () =>
    import('./renderers/Business/CrmFishComponentsRegistered/Agreement/index'),
});

registerRenderer({
  type: 'bcare-customer-info',
  getComponent: () => import('./renderers/Business/Bcare/CustomerInfo/index'),
});

registerRenderer({
  type: 'bcare-access-button',
  getComponent: () => import('./renderers/Business/Bcare/AccessButton/index'),
});

registerRenderer({
  type: 'bcare-customer-count-info',
  getComponent: () =>
    import('./renderers/Business/Bcare/CustomerCountInfo/index'),
});

registerRenderer({
  type: 'bcare-bill-account',
  getComponent: () => import('./renderers/Business/Bcare/BillAccount/index'),
});

registerRenderer({
  type: 'cfs-attribute',
  getComponent: () => import('./renderers/Entity/CfsAttribute'),
});

registerRenderer({
  type: 'rfs-attribute',
  getComponent: () => import('./renderers/Entity/RfsAttribute'),
});

registerRenderer({
  type: 'res-attribute',
  getComponent: () => import('./renderers/Entity/ResAttribute'),
});

registerRenderer({
  type: 'sub-cfs-attribute',
  getComponent: () => import('./renderers/Entity/SubCfsAttribute'),
});

registerRenderer({
  type: 'service-order-attribute',
  getComponent: () => import('./renderers/Entity/ServiceOrderAttribute'),
});

registerRenderer({
  type: 'entity-attribute-guide',
  getComponent: () => import('./renderers/Entity/EntityAttributeGuide'),
});

registerRenderer({
  type: 'crm-cust-order-basic-info-view',
  getComponent: () => import('./renderers/Business/CrmFishComponentsRegistered/CustOrderBasicInfoView/index'),
});
