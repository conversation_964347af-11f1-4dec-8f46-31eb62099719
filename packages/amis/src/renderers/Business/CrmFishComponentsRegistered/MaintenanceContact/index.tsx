import { FormItem, RendererProps, uuidv4 } from 'amis-core';
import classNames from 'classnames';
import React from 'react';
import debounce from 'lodash/debounce';
import { parseJson, remove$$Ids } from '../../../../utils/common-utils.help';
import '../../assets/fish-custom-css/custom-form.css';
import customClassNameFishInPageDesigner from '../../constant/customClassNameFishInPageDesigner';
import styles from './index.module.less';

@FormItem({
  type: 'crm-maintenance-contact',
  autoVar: true,
  strictMode: false,
})
export class MaintenanceContact extends React.Component<RendererProps, any> {
  state = {};

  id = uuidv4();
  
  // 解决窗口抖动问题
  private debouncedRenderFish: ReturnType<typeof debounce>;

  constructor(props: RendererProps) {
    super(props);
    this.debouncedRenderFish = debounce(this.renderFishComponent, 1000);
  }

  // 选择联系人回调
  onCallbackValues = (data: any) => {
    const { dispatchEvent = () => {} } = this.props;
    dispatchEvent('onSelect', data);
  };

  // 数据清除时回调
  onClearValues = () => {
    const { dispatchEvent = () => {} } = this.props;
    dispatchEvent('onSelect', '');
  };

  externalCustomObj = {
    onCallbackValues: this.onCallbackValues,
    onClearValues: this.onClearValues,
    customClassName: customClassNameFishInPageDesigner,
  };

  // 渲染 fish 组件
  renderFishComponent = () => {
    this.removeFishComponent();

    const {
      contactManTitle,
      bfmKey,
      optionsData = {},
      maintenanceContactMockData,
    } = this.props;
    const options: any = {};
    const iData = remove$$Ids(
      maintenanceContactMockData || parseJson(optionsData) || {},
    );
    if (contactManTitle) {
      options.contactManTitle = contactManTitle;
    }
    if (bfmKey) {
      options.commonDataId = bfmKey;
    }

    const newOptions = { ...iData, ...options };
    const $targetEl = $(`#${this.id}`);
    const self = this;
    window.require(
      ['crm/standardizedcomponents/fix/InstallInfo/views/component/AddContactManComponent'],
      function (View: any) {
        new View($targetEl, {
          ...newOptions,
          externalCustomObj: self.externalCustomObj,
        });
      },
    );
  };

  // 卸载 fish 组件
  removeFishComponent = () => {
    const escapedId = CSS.escape(this.id);
    const myDiv: any = document.querySelector?.(`#${escapedId}`);

    if (myDiv) {
      while (myDiv.firstChild) {
        myDiv.removeChild(myDiv.firstChild);
      }
    }
  };

  componentDidMount() {
    this.renderFishComponent();
  }

  componentDidUpdate(prevProps: Readonly<RendererProps>): void {
    if (
      prevProps.contactManTitle !== this.props.contactManTitle ||
      prevProps.bfmKey !== this.props.bfmKey ||
      prevProps.optionsData !== this.props.optionsData
    ) {
      this.renderFishComponent();
    }
  }

  render() {
    const { className, style } = this.props;
    return (
      <div
        className={classNames(
          styles.maintenanceContactWrapper,
          'form-code-in-fish',
          className,
          customClassNameFishInPageDesigner,
        )}
        style={style}
        id={this.id}
      ></div>
    );
  }
}
