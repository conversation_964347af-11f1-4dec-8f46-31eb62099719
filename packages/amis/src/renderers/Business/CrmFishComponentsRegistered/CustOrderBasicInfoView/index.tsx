import { Renderer, RendererProps, uuidv4 } from 'amis-core';
import classNames from 'classnames';
import React from 'react';
import { parseJson, remove$$Ids } from '../../../../utils/common-utils.help';
import customClassNameFishInPageDesigner from '../../constant/customClassNameFishInPageDesigner';

interface CustOrderBasicInfoViewProps extends RendererProps {}

@Renderer({
  type: 'crm-cust-order-basic-info-view',
  autoVar: true,
})
export class CustOrderBasicInfoView extends React.Component<
  CustOrderBasicInfoViewProps,
  any
> {
  state = {};

  constructor(props: any) {
    super(props);
  }

  id = uuidv4();

  // 渲染 fish 组件
  renderFishComponent = () => {
    this.removeFishComponent();

    const {
      bfmKey,
      custOrderBasicInfoViewMockData,
      custOrderBasicInfoViewOptionsData,
    } = this.props;
    
    const curOptions = remove$$Ids(
      custOrderBasicInfoViewMockData ||
        parseJson(custOrderBasicInfoViewOptionsData) ||
        {},
    );

    if (bfmKey) {
      curOptions.commonDataId = bfmKey;
    }

    const curId = this.id;
    const that: any = this;

    window.require(
      ['crm/standardizedcomponents/pos/components/CustOrderBasicInfoView.js'],
      function (View: any) {
        that.myView = new View({
          ...curOptions,
          el: $(`#${curId}`),
        });
        that.myView.render();
      },
    );
  };

  // 卸载 fish 组件
  removeFishComponent = () => {
    const escapedId = CSS.escape(this.id);
    const myDiv: any = document.querySelector?.(`#${escapedId}`);

    if (myDiv) {
      while (myDiv.firstChild) {
        myDiv.removeChild(myDiv.firstChild);
      }
    }
  };

  componentDidMount() {
    this.renderFishComponent();
  }

  componentDidUpdate(prevProps: Readonly<CustOrderBasicInfoViewProps>): void {}

  render() {
    const { className, style } = this.props;
    return (
      <div
        className={classNames(className, customClassNameFishInPageDesigner)}
        style={style}
        id={this.id}
      ></div>
    );
  }
}
