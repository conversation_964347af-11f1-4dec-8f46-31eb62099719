import { Renderer } from 'amis-core';
import { ConfigProvider as ConfigProvider5 } from 'antd-v5';
import { OrderList } from 'pos-components';
import React from 'react';

@Renderer({
  type: 'pad-pos-order-list',
  autoVar: true,
})
export default class IOrderList extends React.Component<any, any> {
  
  qryOrderList = async (pageData: any) => {
    const { env, source, custId } = this.props;
    if (!source || !custId) return;
    const fetcher = env?.fetcher;
    const pageIndex = pageData?.pageIndex + 1 || 1;
    const res = await fetcher?.({
      url: source?.url,
      method: source?.method,
      data: {
        custId,
        pageIndex,
        pageSize: 8,
      },
    });
    return {
      list: res?.data?.orderList || [],
      pageIndex,
      pageSize: pageData?.pageSize || 8,
    };
  };

  render() {
    return (
      <ConfigProvider5 prefixCls="ant5">
        <OrderList
          qryOrderList={this.qryOrderList}
          // qrySubsDetail={this.qrySubsDetail}
          // qrySubsList={this.qrySubsList}
          // custId={this.props.custId}
          // onBusinessItemClick={this.handleBusinessItemClick}
        /> 
      </ConfigProvider5>
    );
  }
}
