import { Renderer } from 'amis-core';
import { ConfigProvider as ConfigProvider5, Flex, TabsProps } from 'antd-v5';
import { OrderEntryHomeTab } from 'pos-components';


import React, { useEffect, useState } from 'react';
export default function IOrderEntryHomeTab(props: any) {
  const {
    goToSubscriber,
    goToAccount,
    subscriberList,
    accountList,
    orderList,
    env,
    style = {},
    className,
    data,
    subscriberCount,
    accountCount,
    orderCount,
    custId,
    render,
    ...rest
  } = props;

  const { fetcher } = env;
  const { url: subscriberUrl, method: subscriberMethod } =
    subscriberCount || {};
  const { url: accountUrl, method: accountMethod } = accountCount || {};
  const { url: orderUrl, method: orderMethod } = orderCount || {};

  const [subscriberData, setSubscriberData] = useState<any>({});
  const [accountData, setAccountData] = useState<any>({});

  const queryTabsItemDataFn = async (
    id: number,
    url: string,
    method: any,
    type: string,
  ) => {
    if (url) {
      const iData = await fetcher?.({
        url,
        method,
        data: {
          custId: id,
        },
      });

      if (iData) {
        switch (type) {
          case 'subscriber':
            setSubscriberData(iData);
            break;
          case 'account':
            setAccountData(iData);
            break;
          case 'order':
            setOrderData(iData);
            break;
          default:
            break;
        }
      }
    }
  };

  useEffect(() => {
    subscriberUrl &&
      queryTabsItemDataFn(
        custId,
        subscriberUrl,
        subscriberMethod,
        'subscriber',
      );
  }, [custId, subscriberUrl, subscriberMethod]);

  useEffect(() => {
    accountUrl &&
      queryTabsItemDataFn(custId, accountUrl, accountMethod, 'account');
  }, [custId, accountUrl, accountMethod]);

  useEffect(() => {
    orderUrl && queryTabsItemDataFn(custId, orderUrl, orderMethod, 'order');
  }, [custId, orderUrl, orderMethod]);

  const items: TabsProps['items'] = [
    {
      key: 'Subscriber',
      label: `Subscriber${
        Number(subscriberData?.data?.subsListCount)
          ? `(${subscriberData?.data?.subsListCount})`
          : ''
      }`,
      children: render('tab1', subscriberList),
    },
    {
      key: 'Account',
      label: `Account${
        accountData?.data?.value?.length
          ? `(${accountData?.data?.value?.length})`
          : ''
      }`,
      children: render('tab2', accountList),
    },
    {
      key: 'Order',
      label: (
        <Flex align="center">
          <div>Order</div>
        </Flex>
      ),
      // children: <OrderList data={data} env={env} {...rest} />
      children: render('tab1', orderList),
    },
  ];

  return (
    <ConfigProvider5 prefixCls="ant5">
      <OrderEntryHomeTab items={items} classname={className} style={style} />
    </ConfigProvider5>
  );
}

Renderer({
  type: 'pad-pos-order-entry-home-tab',
  autoVar: true,
})(IOrderEntryHomeTab);
