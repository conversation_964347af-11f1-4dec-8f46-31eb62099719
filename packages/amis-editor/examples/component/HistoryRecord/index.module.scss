@import '../../themeVariable.module.scss';

.hisIcon {
  font-size: 16px;
  cursor: pointer;

  &:hover {
    color: $THEME_MAIN_COLOR;
  }
}

.hisWrapper {
  width: 240px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);
  .hisHeader {
    padding: 10px;
    font-size: 12px;
    font-weight: 500;
    background-color: #1f38580a;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .close {
      cursor: pointer;
    }
  }
  .hisContent {
    min-height: 200px;
    max-height: 500px;
    overflow-y: auto;

    .hisItem {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 10px;
      border-bottom: 1px solid #e0e0e0;

      &:hover {
        background-color: #1f38581a;
        cursor: pointer;

        .createdDate {
          font-weight: 600;
        }

        .oper {
          display: block;
        }
      }

      .content {
        display: flex;
        flex-direction: column;
        gap: 4px;

        .createdDate {
          font-size: 12px;
          color: #00000099;
        }

        .createdName {
          font-size: 12px;
          color: #00000099;
        }
      }
      .oper {
        display: none;
        font-size: 12px;
        color: #1a1a1a99;

        &:hover {
          color: $THEME_MAIN_COLOR;
          cursor: pointer;
        }
      }
    }
  }
}
