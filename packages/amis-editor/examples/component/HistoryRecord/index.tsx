/*
 * @Description: 修改历史记录
 * @Author: wang.minglei[0027012155]
 * @Date: 2025-07-31 13:53:02
 * @LastEditors: wang.minglei[0027012155]
 * @LastEditTime: 2025-08-01 17:00:00
 */
import { IconFontRender } from 'amis-editor';
import { Dropdown, Empty, message, Popconfirm, Space, Tooltip } from 'antd';
import moment from 'moment';
import React, { useState } from 'react';
import NoDataImg from '../../../assets/img-nodata.png';
import { getReversionHistory, patchPage } from '../../services';
import { HistoryDataType } from '../../typing';
import styles from './index.module.scss';

interface IHistoryRecordProps {
  page: any;
  pageVid: number;
  curSchema: any;
  initPageVersion: (pageVid?: number, hisContent?: string) => void;
}

const HistoryRecord = (props: IHistoryRecordProps) => {
  const { page, pageVid, curSchema, initPageVersion } = props;
  const [open, setOpen] = useState<boolean>(false);
  const [historyMenu, setHistoryMenu] = useState<HistoryDataType[]>([]);
  const queryReversionHistory = async () => {
    if (!pageVid) {
      return;
    }
    getReversionHistory({
      pageVid,
      limit: 20,
      offset: 1,
    }).then(res => {
      const dataTemp = res?.data?.list || [];
      const schemaStr = JSON.stringify(curSchema);
      const resourceKey = schemaStr.match(/i18n:[0-9a-z]{8}/g);

      dataTemp.unshift({
        seq: -1,
        createdDate: moment().format('YYYY-MM-DD HH:mm:ss'),
        createdByName: '当前版本',
        content: JSON.stringify(
          resourceKey ? { ...curSchema, resourceKey } : curSchema,
        ),
      });
      setHistoryMenu(dataTemp);
    });
  };

  const changeHistory = (content: string) => {
    if (!pageVid) {
      console.error('pageVid is null');
      return;
    }
    initPageVersion(pageVid, content);
    message.success('切换成功, 请注意保存!');
  };

  const rollback = async (hisData: HistoryDataType) => {
    await patchPage(page.pageId, hisData.content, undefined, pageVid);

    if (window.parent !== window.self) {
      window.parent.postMessage(
        {
          actionType: 'PHOENIX_PAGE_SAVE',
          actionData: {
            triggerType: 'PAGE_SAVE',
            versionState: page?.versionState || '',
            pageData: {
              pageId: page.pageId,
              content: hisData.content,
              pageImage: {},
              pageVid,
            },
          },
        },
        '*',
      );
    }

    message.success('数据回滚成功!');

    setTimeout(() => {
      window.location.reload();
    }, 500);
  };

  const renderHistoryMenu = () => {
    return (
      <div className={styles.hisWrapper}>
        <div className={styles.hisHeader}>
          <span className={styles.title}>修改历史</span>
          <div className={styles.close} onClick={() => setOpen(false)}>
            <IconFontRender type="icon-CloseOutlined" />
          </div>
        </div>
        <div className={styles.hisContent}>
          {historyMenu?.length ? (
            historyMenu?.map((item: HistoryDataType, index: number) => (
              <div
                key={item.seq}
                className={styles.hisItem}
                onClick={() => changeHistory(item.content)}
              >
                <div className={styles.content}>
                  <span className={styles.createdDate}>{item.createdDate}</span>
                  <span className={styles.createdName}>
                    {item.createdByName || '-'}
                  </span>
                </div>
                {index !== 0 && (
                  <Popconfirm
                    title="历史记录回滚"
                    description="确定要回滚到该历史版本吗?"
                    onConfirm={() => {
                      rollback(item);
                    }}
                    onPopupClick={e => {
                      e.stopPropagation();
                    }}
                    okText="确定"
                    cancelText="取消"
                  >
                    <div
                      className={styles.oper}
                      onClick={e => {
                        e.stopPropagation();
                      }}
                    >
                      <Space size={4}>
                        <IconFontRender type="icon-return" />
                        <span>回滚</span>
                      </Space>
                    </div>
                  </Popconfirm>
                )}
              </div>
            ))
          ) : (
            <Empty image={NoDataImg} description="暂无数据" />
          )}
        </div>
      </div>
    );
  };

  return (
    <>
      <Tooltip title="修改历史">
        <Dropdown
          open={open}
          trigger={['click']}
          popupRender={renderHistoryMenu}
          onOpenChange={(open: boolean) => {
            setOpen(open);
            if (open) {
              queryReversionHistory();
            }
          }}
          destroyOnHidden
        >
          <IconFontRender
            type="icon-HistoryOutlined"
            className={styles.hisIcon}
          />
        </Dropdown>
      </Tooltip>
    </>
  );
};

export default HistoryRecord;
