export type PageType = {
  catgId: number;
  createdBy: number;
  createdDate: string;
  moduleId: number;
  oldFlag: 'N' | 'Y';
  pageId: number;
  pageImages: string;
  pageName: string;
  pageSchema: string;
  pageType: string;
  pageVersion: string;
  projectId: number;
  remark: string;
  state: string;
  updateBy: number;
  updateDate: string;
  content: string;
};

export type PageImageItem = {
  imageType: string;
  imageContent: string;
};

export type TemplateType = {
  templateId: string;
  templateName: string;
  templateType: string;
  templateDesc: string;
  moduleId: string;
  content: string;
};

/**
 * 扩展主题类型
 */
export type ExtendThemeType = {
  name: string;
  cssPath: string;
}

/**
 * 修改历史数据类型
 */
export type HistoryDataType = {
  pageVid: number;
  seq: number;
  dataType: string;
  createdBy: number;
  createdDate: string;
  content: string;
  createdByName: string;
  comments: string;
}
