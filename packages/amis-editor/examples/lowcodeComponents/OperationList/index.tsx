import { IconFontRender } from 'amis-editor-core';
// import { Popconfirm } from 'antd';
import React, { Fragment } from 'react';
import styles from './index.module.scss';
// import { useI18n } from '@/hooks/useI18n';

type IMuRecordByKey<U> = {
  [SubType in keyof U]: {
    mode: SubType;
  } & U[SubType];
}[keyof U];

type IOperaionListProps = {
  activeKey?: string;
  items: Array<
    IMuRecordByKey<{
      common: {
        key?: string;
        icon?: string;
        text: string;
        isDelete?: boolean;
        onItemClick: () => void;
        isHidden?: boolean;
      };
      spliter: {
        isHidden?: boolean;
      };
    }>
  >;
};

const OperaionList: React.FC<IOperaionListProps> = props => {
  const { items, activeKey } = props;
  // const { i18n } = useI18n();

  // wrapper with delete
  // const WrapperWithDelete = (isDelete: boolean, innerChildren: any, onConfirm?: () => void) => {
  //   return isDelete ? (
  //     <Popconfirm
  //       rootClassName={styles.popConfirmStyles}
  //       placement="top"
  //       title={formatMessage({ id: 'PHX_COMMON_DELETE_COMFIRM' })}
  //       description={formatMessage({ id: 'PHX_COMMON_IS_SURE_TO_DELETE' })}
  //       onConfirm={(e) => {
  //         e?.stopPropagation();
  //         onConfirm?.();
  //       }}
  //       okText={formatMessage({ id: 'PHX_COMMON_YES' })}
  //       cancelText={formatMessage({ id: 'PHX_COMMON_NO' })}
  //     >
  //       {innerChildren}
  //     </Popconfirm>
  //   ) : (
  //     <div
  //       onClick={(e) => {
  //         e?.stopPropagation();
  //         onConfirm?.();
  //       }}
  //     >
  //       {innerChildren}
  //     </div>
  //   );
  // };

  return (
    <div className={styles.list}>
      {items.map((item, index) => {
        return item.mode === 'spliter' ? (
          // 分割符号
          <React.Fragment key={`item-pop-${index}`}>
            {item?.isHidden ? null : (
              <div key={`spliter-${index}`} className={styles.spliter} />
            )}
          </React.Fragment>
        ) : (
          // 普通item项 注意区分包裹isDelete的PopConfirm
          <React.Fragment key={`item-pop-${index}`}>
            {/* {WrapperWithDelete(
              !!item.isDelete, */}
            <>
              {item?.isHidden ? null : (
                <div
                  key={`item-${index}`}
                  className={
                    activeKey === String(item?.key || '')
                      ? styles.activeItem
                      : styles.item
                  }
                  onClick={item?.onItemClick}
                >
                  <div className={styles.itemPos}>
                    {/* icon */}
                    {item?.icon ? (
                      <IconFontRender
                        className={styles.icon}
                        type={item.icon}
                      />
                    ) : null}
                    {/* text */}
                    <div className={styles.text}>{item.text}</div>
                  </div>
                </div>
              )}
            </>

            {/* item?.onItemClick, */}
            {/* )} */}
          </React.Fragment>
        );
      })}
    </div>
  );
};

export default OperaionList;
