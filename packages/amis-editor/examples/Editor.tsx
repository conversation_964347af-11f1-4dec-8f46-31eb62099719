/* eslint-disable */
import {
  addSchemaFilter,
  extendLocale,
  registerFilter,
  registerLocale,
  toast,
  // Select,
  uuid,
} from 'amis';
import { EditorManager, IconFontRender } from 'amis-editor-core';
import { currentLocale } from 'i18n-runtime';
import * as React from 'react';
import { Portal } from 'react-overlays';
import {
  BasePlugin,
  GlobalVariableEventContext,
  GlobalVariablesEventContext,
  PluginEvent,
  setThemeConfig,
  ShortcutKey,
} from '../src/index';
import { Icon } from './icons/index';
import LayoutList from './layout/index';
import PadPosCmpList from './padpos/index';

import { cxdData } from 'amis-theme-editor-helper';
import EditorFunc from './EditorFunc';
import {
  getBlockDetail,
  getFlows,
  getModelDetail,
  getModelList,
  getPage,
  getPageByName,
  getPageByVersion,
  getPluginList,
  getTemplateDetail,
  modifyBlock,
  modifyTemplateContent,
  patchPage,
  queryCorpus,
  queryLanguages,
} from './services';
import { getParamByKey, getParams } from './utils/params';
import {
  html2PngProxy,
  mergeLayoutSchema,
  mergeSteps,
  transformNode2Step,
} from './utils/util';

// 测试组织属性配置面板的国际化，可以放开如下注释
import { findProperty } from 'amis-editor-core';
import { Space } from 'antd';
import HistoryRecord from './component/HistoryRecord';
import UpdatePage from './component/UpdatePage';
import './renderer/InputTextI18n';
import './renderer/TextareaI18n';
import './utils/overwriteSchemaTpl';
const i18nEnabled = true;
// const i18nEnabled = false;

setThemeConfig(cxdData);

const schema = {
  type: 'page',
  title: 'Simple Form Page',
  regions: ['body'],
  body: [
    {
      type: 'form',
      body: [
        {
          type: 'input-text',
          name: 'a',
          label: 'Text',
        },
      ],
    },
  ],
};

const formSchema = {
  type: 'doc-entity',
  fields: [],
};

const schemas = [
  {
    type: 'object',
    properties: {
      'amisUser': {
        type: 'object',
        title: '用户信息',
        properties: {
          id: {
            type: 'string',
            title: '用户ID',
          },
          name: {
            type: 'string',
            title: '用户名',
          },
          email: {
            type: 'string',
            title: '邮箱',
          },
          nickName: {
            type: 'string',
            title: '昵称',
          },
          phone: {
            type: 'string',
            title: '手机号',
          },
          avatar: {
            type: 'string',
            title: '用户头像',
          },
        },
      },
      'amisApp': {
        type: 'object',
        title: '应用信息',
        properties: {
          id: {
            type: 'string',
            title: '应用ID',
          },
          name: {
            type: 'string',
            title: '应用名称',
          },
          logo: {
            type: 'string',
            title: '应用Logo',
          },
          env: {
            type: 'string',
            title: '当前运行环境',
          },
        },
      },
      'amisCompany': {
        type: 'object',
        title: '组织信息',
        properties: {
          id: {
            type: 'string',
            title: '组织ID',
          },
          name: {
            type: 'string',
            title: '组织名称',
          },
          logo: {
            type: 'string',
            title: '组织Logo',
          },
          key: {
            type: 'string',
            title: '组织标识',
          },
        },
      },
      'window:location': {
        type: 'object',
        title: '浏览器变量',
        properties: {
          href: {
            type: 'string',
            title: 'href',
          },
          origin: {
            type: 'string',
            title: 'origin',
          },
          protocol: {
            type: 'string',
            title: 'protocol',
          },
          host: {
            type: 'string',
            title: 'host',
          },
          hostname: {
            type: 'string',
            title: 'hostname',
          },
          port: {
            type: 'string',
            title: 'port',
          },
          pathname: {
            type: 'string',
            title: 'pathname',
          },
          search: {
            type: 'string',
            title: 'search',
          },
          hash: {
            type: 'string',
            title: 'hash',
          },
        },
      },
      'activeMenu': {
        title: '激活菜单项',
        type: 'string',
      },
      'custInfo': {
        type: 'object',
        title: '客户信息',
        properties: {
          custId: {
            type: 'number',
            title: '客户标识',
          },
          custName: {
            type: 'string',
            title: '客户名称',
          },
          custType: {
            type: 'string',
            title: '客户类型',
          },
          defLangName: {
            type: 'string',
            title: '客户母语',
          },
          cert: {
            type: 'object',
            title: '证件信息',
            properties: {
              certNbr: {
                type: 'string',
                title: '证件号码',
              },
            },
          },
        },
      },
      'loginInfo': {
        type: 'object',
        title: '登录信息',
        properties: {
          jobData: {
            type: 'object',
            title: 'job信息',
          },
          localData: {
            type: 'object',
            title: 'local信息',
            properties: {
              currencyPrecision: {
                type: 'string',
                title: '精度',
              },
              language: {
                type: 'string',
                title: '语言',
              },
            },
          },
          loginData: {
            type: 'object',
            title: '登录信息',
            properties: {
              spId: {
                type: 'number',
                title: 'spid',
              },
              userCode: {
                type: 'string',
                title: '用户编码',
              },
              userId: {
                type: 'number',
                title: '用户标识',
              },
              userName: {
                type: 'string',
                title: '用户名',
              },
            },
          },
        },
      },
      'systemPrecisionData': {
        type: 'object',
        title: '金额精度信息',
      },
    },
  },
  {
    type: 'object',
    properties: {
      __query: {
        title: '页面入参',
        type: 'object',
        required: [],
        properties: {
          name: {
            type: 'string',
            title: '用户名',
          },
        },
      },
      __page: {
        title: '页面变量',
        type: 'object',
        required: [],
        properties: {
          num: {
            type: 'number',
            title: '数量',
          },
        },
      },
    },
  },
];

const variableSchemas = {
  type: 'object',
  $id: 'appVariables',
  properties: {
    ProductName: {
      type: 'string',
      title: '产品名称',
      default: '对象存储',
    },
    Banlance: {
      type: 'number',
      title: '账户余额',
      default: '0.00',
    },
    ProductNum: {
      type: 'integer',
      title: '产品数量',
      default: '0.00',
    },
    isOnline: {
      type: 'boolean',
      title: '是否线上环境',
      default: 'false',
    },
    ProductList: {
      type: 'array',
      items: {
        type: 'string',
        title: '产品名称',
      },
      title: '产品列表',
      default: '["BOS", "CFS", "PFS", "CloudFlow", "MongoDB"]',
    },
    PROFILE: {
      type: 'object',
      title: '个人信息',
      properties: {
        FirstName: {
          type: 'string',
          title: '名字',
        },
        Age: {
          type: 'integer',
          title: '年龄',
        },
        Address: {
          type: 'object',
          title: '地址',
          required: ['street', 'postcode'],
          properties: {
            street: {
              type: 'string',
              title: '街道名称',
            },
            postcode: {
              type: 'number',
              title: '邮编',
            },
          },
        },
      },
    },
  },
  default: {
    ProductName: 'BCC',
    Banlance: 1234.888,
    ProductNum: 10,
    isOnline: false,
    ProductList: ['BCC', 'BOS', 'VPC'],
    PROFILE: {
      FirstName: 'Amis',
      Age: 18,
      Address: {
        street: 'ShangDi',
        postcode: 100001,
      },
    },
  },
};

const variableDefaultData = {
  appVariables: {
    ProductName: 'BCC',
    Banlance: 1234.888,
    ProductNum: 10,
    isOnline: false,
    ProductList: ['BCC', 'BOS', 'VPC'],
    PROFILE: {
      FirstName: 'Amis',
      Age: 18,
      Address: {
        street: 'ShangDi',
        postcode: 100001,
      },
    },
  },
};

const variables: any = [
  {
    name: 'appVariables',
    title: '内存变量',
    parentId: 'root',
    order: 1,
    schema: variableSchemas,
  },
];

const EditorType = {
  EDITOR: 'editor',
  MOBILE: 'mobile',
  FORM: 'form',
};

const editorLanguages = [
  {
    label: '简体中文',
    value: 'zh-CN',
  },
  {
    label: 'English',
    value: 'en-US',
  },
];

const globalEvents1 = [
  {
    name: 'globalEventA',
    label: '全局事件A',
    description: '全局事件动作A',
    mapping: [
      {
        key: 'name',
        type: 'string',
      },
      {
        key: 'age',
        type: 'number',
      },
    ],
  },
  {
    name: 'globalEventB',
    label: '全局事件B',
    description: '全局事件动作A',
    mapping: [
      {
        key: 'name',
        type: 'string',
      },
    ],
  },
];

/**
 * 自定义渲染器示例
 */
// @Renderer({
//   type: 'my-renderer',
//   name: 'my-renderer'
// })
// export class MyRenderer extends React.Component {
//   static defaultProps = {
//     target: 'world'
//   };

//   render() {
//     const {target, width, height} = this.props;

//     return (
//       <p style={{width: width || 'auto', height: height || 'auto'}}>
//         Hello {target}!
//       </p>
//     );
//   }
// }

/**
 * 自定义渲染器编辑插件
 */
class MyRendererPlugin extends BasePlugin {
  // 这里要跟对应的渲染器名字对应上
  // 注册渲染器的时候会要求指定渲染器名字
  rendererName = 'my-renderer';

  // 暂时只支持这个，配置后会开启代码编辑器
  $schema = '/schemas/UnkownSchema.json';

  // 用来配置名称和描述
  name = '自定义渲染器';
  description = '这只是个示例';

  // tag，决定会在哪个 tab 下面显示的
  tags = ['自定义', '表单项'];

  // 图标
  icon = 'fa fa-user';

  // 用来生成预览图的
  previewSchema = {
    type: 'my-renderer',
    target: 'demo',
  };

  // 拖入组件里面时的初始数据
  scaffold = {
    type: 'my-renderer',
    target: '233',
  };

  // 右侧面板相关
  panelTitle = '自定义组件';
  panelBody = [
    {
      type: 'tabs',
      tabsMode: 'line',
      className: 'm-t-n-xs',
      contentClassName: 'no-border p-l-none p-r-none',
      tabs: [
        {
          title: '常规',
          body: [
            {
              name: 'target',
              label: 'Target',
              type: 'input-text',
            },
          ],
        },

        {
          title: '外观',
          body: [],
        },
      ],
    },
  ];

  // /**
  //  * 配置了 panelControls 自动生成配置面板
  //  * @param context
  //  * @param panels
  //  */
  // buildEditorPanel(context, panels) {
  //   panels.push({
  //     key: 'xxxx',
  //     title: '设置',
  //     render: () => {
  //       return <div>面板内容</div>;
  //     }
  //   });
  // }

  // scaffoldForm = {
  //   title: '标题',
  //   body: [
  //     {
  //       name: 'target',
  //       label: 'Target',
  //       type: 'input-text'
  //     }
  //   ]
  // };

  // onActive(event) {
  //   const context = event.context;

  //   if (context.info?.plugin !== this || !context.node) {
  //     return;
  //   }

  //   const node = context.node;
  //   node.setHeightMutable(true);
  //   node.setWidthMutable(true);
  // }

  // onWidthChangeStart(event) {
  //   return this.onSizeChangeStart(event, 'horizontal');
  // }

  // onHeightChangeStart(event) {
  //   return this.onSizeChangeStart(event, 'vertical');
  // }

  // onSizeChangeStart(event, direction = 'both') {
  //   const context = event.context;
  //   const node = context.node;
  //   if (node.info?.plugin !== this) {
  //     return;
  //   }

  //   const resizer = context.resizer;
  //   const dom = context.dom;
  //   const frameRect = dom.parentElement.getBoundingClientRect();
  //   const rect = dom.getBoundingClientRect();
  //   const startX = context.nativeEvent.pageX;
  //   const startY = context.nativeEvent.pageY;

  //   event.setData({
  //     onMove: e => {
  //       const dy = e.pageY - startY;
  //       const dx = e.pageX - startX;
  //       const height = Math.max(50, rect.height + dy);
  //       const width = Math.max(100, Math.min(rect.width + dx, frameRect.width));
  //       const state = {
  //         width,
  //         height
  //       };

  //       if (direction === 'both') {
  //         resizer.setAttribute('data-value', `${width}px x ${height}px`);
  //       } else if (direction === 'vertical') {
  //         resizer.setAttribute('data-value', `${height}px`);
  //         delete state.width;
  //       } else {
  //         resizer.setAttribute('data-value', `${width}px`);
  //         delete state.height;
  //       }

  //       node.updateState(state);

  //       requestAnimationFrame(() => {
  //         node.calculateHighlightBox();
  //       });
  //     },
  //     onEnd: e => {
  //       const dy = e.pageY - startY;
  //       const dx = e.pageX - startX;
  //       const height = Math.max(50, rect.height + dy);
  //       const width = Math.max(100, Math.min(rect.width + dx, frameRect.width));
  //       const state = {
  //         width,
  //         height
  //       };

  //       if (direction === 'vertical') {
  //         delete state.width;
  //       } else if (direction === 'horizontal') {
  //         delete state.height;
  //       }

  //       resizer.removeAttribute('data-value');
  //       node.updateSchema(state);
  //       requestAnimationFrame(() => {
  //         node.calculateHighlightBox();
  //       });
  //     }
  //   });
  // }

  popOverBody = [
    {
      name: 'target',
      label: 'Target',
      type: 'input-text',
    },
  ];
}

const placeholderSchema = {
  type: 'page',
  body: [],
  id: 'u:123456789',
  regions: ['body'],
};

LayoutList.push(MyRendererPlugin);

export default class AMisSchemaEditor extends React.Component<any, any> {
  state: any = {
    // preview: localStorage.getItem('editting_preview') ? true : false,
    // type: localStorage.getItem('editting_preview_type') || EditorType.EDITOR,
    preview: false,
    type: EditorType.EDITOR,
    // schema: localStorage.getItem('editting_schema')
    //   ? JSON.parse(localStorage.getItem('editting_schema')!)
    //   : schema,
    schema: null,
    curLanguage: currentLocale(), // 获取当前语料类型
    page: null,
    pageVersion: null,
    models: {},
    saving: false,
    editTemplateId: getParamByKey('editTemplateId'),
    editBlockId: getParamByKey('editBlockId'),
    loaded: false,
    displayPlugins: {
      simplified: [], // 简易模式下显示的组件
      professional: [], // 专业模式下显示的组件
      business: [], // 业务组件显示列表，只限制分类tag, 不区分最小单元组件。tag请使用英文
    },
    corpusLoaded: true,
    showUpdatePage: false,
    moduleId: getParamByKey('moduleId'),
  };

  constructor(props: any) {
    super(props);

    localStorage.removeItem('editting_preview');
    localStorage.setItem('editting_preview_type', EditorType.EDITOR);

    if (i18nEnabled) {
      this.state = {
        ...this.state,
        corpusLoaded: false,
      };
      this.loadCorpus();
    }

    const type =
      localStorage.getItem('editting_preview_type') || EditorType.EDITOR;

    // this.state.schema = this.getSchema(type);
    this.runRegister();
  }

  runRegister() {
    const filters = findProperty('filters') || [];
    const locale = findProperty('locale') || [];
    const schemafilters = findProperty('schemafilters') || [];

    if (filters.length) {
      filters.forEach((filter: any) => {
        const { name, func } = filter || {};
        if (name && typeof name === 'string' && typeof func === 'function') {
          registerFilter(name, func);
        }
      });
    }

    if (locale.length) {
      locale.forEach((item: any) => {
        const { language, corpus } = item || {};
        if (language && corpus) {
          registerLocale(language, corpus);
        }
      });
    }

    if (schemafilters.length) {
      schemafilters.forEach((item: any) => {
        if (typeof item === 'function') {
          addSchemaFilter(item);
        }
      });
    }
  }

  getSchema(type: string) {
    if (type === EditorType.FORM) {
      const schema = localStorage.getItem('editting_schema_form');

      if (schema) {
        return JSON.parse(schema);
      }
      return formSchema;
    }

    const lsSchema = localStorage.getItem('editting_schema');

    if (lsSchema) {
      return JSON.parse(lsSchema);
    }

    return schema;
  }

  loadCorpus = async () => {
    try {
      const moduleId = getParamByKey('moduleId');
      const { curLanguage } = this.state;
      const { list = [] } = await queryCorpus({
        appId: moduleId || '-1',
        resourceType: 'P',
        offset: 1,
        limit: 10001,
      });
      const replaceText = list.reduce((acc, item) => {
        acc[item.resourceKey] = item.translation?.find(
          child => child.langTag === curLanguage,
        )?.value;
        return acc;
      }, {});
      this.setState({
        replaceText,
        appCorpusList: list,
      });
      extendLocale(curLanguage, replaceText, false);
    } finally {
      this.setState({
        corpusLoaded: true,
      });
    }
  };

  addListener = async () => {
    const from = getParamByKey('from');
    if (from === 'journey') {
      window.addEventListener('message', this.handleMessage);
    }
  };

  handleMessage = (event: MessageEvent) => {
    if (event?.data?.actionType === 'refreshSchema') {
      const curFlowJson = event?.data?.curFlowJson;
      const newStepNodes = transformNode2Step(
        curFlowJson?.nodes,
        curFlowJson?.edges,
      );
      const { newSchema, isChanged } = mergeSteps(
        this.state.schema,
        newStepNodes,
      );
      if (isChanged) {
        this.setState({
          schema: newSchema,
        });
        this.props?.onJourneyChanged(true);
      }
    }
  };

  /**
   * 获取展示组件列表
   */
  getPlugins = async () => {
    const res: any = await getPluginList();
    const { data = { simplified: [], professional: [], business: [] } } = res;
    this.setState({
      displayPlugins: data,
    });
  };

  // 初始化模板内容
  initTemplate = async id => {
    try {
      const res = await getTemplateDetail(id);
      const pageSchema = JSON.parse(res.data?.content || '{}');

      this.setState({
        schema: pageSchema,
        page: res.data,
      });
      this.props.onPageLoaded && this.props.onPageLoaded(res?.data || {});
    } catch (error) {
    } finally {
      this.setState({
        loaded: true,
      });
    }
  };

  // 初始化区块内容
  initBlock = async id => {
    try {
      const res = await getBlockDetail(id);
      const pageSchema = res.data?.content
        ? JSON.parse(res.data?.content)
        : {
            type: 'container',
            body: [],
          };

      this.setState({
        schema: pageSchema,
        page: res.data,
      });
      this.props.onPageLoaded && this.props.onPageLoaded(res?.data || {});
    } catch (error) {
    } finally {
      this.setState({
        loaded: true,
      });
    }
  };

  getPageModel = specId => {
    getModelDetail(specId).then(res => {
      this.setState({
        models: res.data,
      });
    });
  };

  initPageVersion = async (pageVid?: number, hisContent?: string) => {
    const { preview } = this.state;

    try {
      const res = await getPageByVersion(pageVid);

      if (hisContent && res) {
        res.content = hisContent;
      }

      const pageSchema = JSON.parse(res?.content || '{}');

      if (pageSchema.layout?.pageId) {
        if (preview) {
          await this.loadLayout(pageSchema.layout?.pageId);
        } else {
          this.loadLayout(pageSchema.layout?.pageId);
        }
      }
      this.setState({
        schema: pageSchema,
        pageVersion: res,
      });
      this.props.onVersionLoaded && this.props.onVersionLoaded(res);
    } catch (error) {
    } finally {
      this.setState({
        loaded: true,
      });
    }
  };

  // 使用pageName查询页面内容
  initPageByName = async (pageName: string) => {
    const { moduleId, preview } = this.state;
    try {
      const res = await getPageByName(moduleId, pageName);
      const pageSchema = JSON.parse(res?.content || '{}');
      this.setState({
        schema: pageSchema,
        page: res,
      });
      if (pageSchema.layout?.pageId) {
        if (preview) {
          await this.loadLayout(pageSchema.layout?.pageId);
        } else {
          this.loadLayout(pageSchema.layout?.pageId);
        }
      }
      this.props.onPageLoaded && this.props.onPageLoaded(res);
      this.props.onVersionLoaded && this.props.onVersionLoaded(res);
    } catch {
    } finally {
      this.setState({
        loaded: true,
      });
    }
  };

  // 初始化页面内容
  initPage = async vid => {
    const { editTemplateId, editBlockId, preview } = this.state;
    if (editTemplateId) {
      this.initTemplate(editTemplateId);
      return;
    }

    if (editBlockId) {
      this.initBlock(editBlockId);
      return;
    }

    const pageName = getParamByKey('pageName');

    if (pageName) {
      return this.initPageByName(pageName);
    }

    try {
      const pageId = getParamByKey('pageId');

      const res = await getPage(pageId);
      this.setState({
        page: res,
      });

      const pageVid = vid || getParamByKey('pageVid') || res?.pageVid;
      this.initPageVersion(pageVid);

      if (res?.specId) {
        this.getPageModel(res.specId);
      }
      this.props.onPageLoaded && this.props.onPageLoaded(res);
    } catch (error) {
      this.props.onPageLoaded && this.props.onPageLoaded();
    }
  };

  // 加载布局页面
  loadLayout = async (pageId: string) => {
    const res = await getPage(pageId);
    const layoutSchema = JSON.parse(res?.content || '{}');

    this.setState({
      layoutSchema,
      layoutRes: res,
    });
  };

  updatePages = async () => {
    const res = this.props.pages;

    const pages = res.reduce((acc: any[], item: any) => {
      if (!item.leaf) {
        acc.push({
          label: item.name,
          value: item.id,
        });
      }
      return acc;
    }, []);

    pages.forEach((page: any) => {
      page.children = res.reduce((acc: any[], item: any) => {
        if (item.leaf && item.pid === page.value) {
          acc.push({
            label: item.name,
            value: item.id,
          });
        }

        return acc;
      }, []);
    });

    this.setState({
      pages,
    });
  };

  getFlows = async () => {
    const projectId = getParamByKey('projectId');
    const moduleId = getParamByKey('moduleId');
    const res = await getFlows(projectId, moduleId, 'R');
    this.setState({ flows: res || [] });
  };

  getLanguage = async () => {
    const res = await queryLanguages();
    this.setState({
      languages: res || [],
    });
  };

  getModelList = async () => {
    const res = await getModelList();
    const modelList = (res.data || []).map((cat: any) => {
      return {
        label: cat.systemName,
        options: (cat.entityList || []).map((item: any) => {
          return {
            label: item.specName,
            value: item.specId,
          };
        }),
      };
    });
    this.setState({
      modelList,
    });
  };

  // 遍历 schema，检测是否存在 fish 组件
  checkFishComponentsFn = (obj: any) => {
    if (!Array.isArray(obj)) {
      return false;
    }

    for (const item of obj) {
      if (typeof item === 'object' && item !== null) {
        if (
          typeof item.type === 'string' &&
          (item.type.startsWith('crm-') || item.type.startsWith('cvbs-'))
        ) {
          return true;
        }

        const propertiesToCheck = ['items', 'body'];
        for (const prop of propertiesToCheck) {
          if (Array.isArray(item[prop])) {
            if (this.checkFishComponentsFn(item[prop])) {
              return true;
            }
          }
        }
      }
    }

    return false;
  };

  // 处理来自iframe的消息
  handleIframeMessage = (event: MessageEvent) => {
    // 处理消息
    if (event.data.portalCsrf) {
      window._csrf = event.data.portalCsrf;
      console.log('event.data.portalCsrf:', event.data.portalCsrf);
    }
  };

  async componentDidMount() {
    try {
      window.addEventListener('message', this.handleIframeMessage);
      // await this.getPages();
      await this.initPage();
      this.addListener();
      this.getPlugins();
    } catch (error) {
      console.error(error);
    }
  }

  componentWillUnmount() {
    window.removeEventListener('message', this.handleIframeMessage);
    window.removeEventListener('message', this.handleMessage);
  }

  componentDidUpdate(prevProps: Readonly<any>): void {
    // 使用CommonLogoArea的数据，复用接口
    if (this.props.pages?.length && prevProps.pages !== this.props.pages) {
      this.updatePages();
    }
  }

  handleChange = (value: any) => {
    const type = this.state.type;
    // console.log('this.state.schema', this.state.schema);
    // console.log('value', value);
    if (type === EditorType.FORM) {
      localStorage.setItem('editting_schema_form', JSON.stringify(value));
    } else {
      localStorage.setItem('editting_schema', JSON.stringify(value));
    }

    this.setState({
      schema: value,
    });
  };

  // changeLocale(value: any) {
  //   localStorage.setItem('suda-i18n-locale', value);
  //   window.location.reload();
  // }

  onSave = () => {
    const curSchema = this.state.schema;
    localStorage.setItem('editting_schema', JSON.stringify(curSchema));
  };

  handlePreviewChange = (preview: any) => {
    localStorage.setItem('editting_preview', preview ? 'true' : '');
    const { schema, layoutRes } = this.state;
    if (
      schema?.layout?.pageId &&
      schema?.layout?.pageId !== layoutRes?.pageId
    ) {
      this.loadLayout(schema.layout?.pageId);
    }

    this.setState({
      preview: !!preview,
    });
  };

  togglePreview = () => {
    if (!this.state.preview) {
      document.body.classList.add(`AMISCSSWrapper`);
    } else {
      document.body.classList.remove(`AMISCSSWrapper`);
    }
    this.handlePreviewChange(!this.state.preview);
  };

  handleTypeChange = (editorType: any) => {
    const type = editorType || EditorType.EDITOR;
    localStorage.setItem('editting_preview_type', type);

    this.setState({
      type: type,
      schema: this.getSchema(type),
    });
  };

  clearCache = () => {
    localStorage.removeItem('editting_schema');
    this.setState({
      schema: schema,
    });
  };

  onEditorMount = (manager: EditorManager) => {
    manager.on('api-control', () => {
      setTimeout(() => {
        this.getFlows();
        this.getLanguage();
        this.getModelList();
      }, 500);
    });
  };

  renderEditor() {
    const { theme, developMode } = this.props;
    const {
      preview,
      type,
      schema,
      pages,
      flows,
      models,
      replaceText,
      layoutSchema,
      pageVersion,
      loaded,
      displayPlugins,
      languages,
      corpusLoaded,
      appCorpusList,
      modelList,
    } = this.state;

    // 计算出存在schema里面的global events
    const globalEvents = schema?.globalEvents?.eventList || [];

    const isMobile = type === EditorType.MOBILE;

    const pageParams = getParams();

    // 在页面没有加载完成之前，使用默认的空 schema，主要解决选中高亮不正确的问题
    let showSchema = loaded && corpusLoaded ? schema : placeholderSchema;

    if (preview && layoutSchema) {
      showSchema = mergeLayoutSchema(layoutSchema, schema);
    }

    const handleSchemaBeforeRender = findProperty('handleSchemaBeforeRender');

    const props = {
      preview,
      isMobile,
      value:
        typeof handleSchemaBeforeRender === 'function'
          ? handleSchemaBeforeRender(showSchema)
          : showSchema,
      schemas,
      variables,
      onChange: this.handleChange,
      onPreview: this.handlePreviewChange,
      onSave: this.onSave,
      className: 'is-fixed',
      i18nEnabled,
      theme: theme || 'cxd',
      showCustomRenderersPanel: true,
      plugins: [...PadPosCmpList, ...LayoutList], // 存放常见布局组件
      $schemaUrl: `${location.protocol}//${location.host}/schema.json`,
      pages,
      flows,
      languages,
      models,
      appCorpusList,
      modelList,
      specId: pageVersion?.specId,
      actionOptions: {
        showOldEntry: false,
        globalEventGetter: () => globalEvents,
      },
      onGlobalVariableInit: onGlobalVariableInit,
      onGlobalVariableSave: onGlobalVariableSave,
      onGlobalVariableDelete: onGlobalVariableDelete,
      amisEnv: {
        variable: {
          id: 'appVariables',
          namespace: 'appVariables',
          schema: variableSchemas,
          data: variableDefaultData,
        },
        replaceText,
      } as any,
      ctx: {
        __page: {
          num: 2,
        },
        ...variableDefaultData,
      },
      pageParams: {
        specId: pageVersion?.specId,
        ...pageParams,
      },
      developMode,
      displayPlugins,
      onEditorMount: this.onEditorMount,
    };

    const hasFishComponents = this.checkFishComponentsFn(showSchema?.body);

    return <EditorFunc hasFishComponents={hasFishComponents} {...props} />;
  }

  // 生成页面截图
  generatePageScreenshot = async (ele: HTMLElement | null) => {
    if (!ele) return '';

    return await html2PngProxy(ele, {
      quality: 1,
      backgroundColor: '#fff',
      canvasWidth: 320,
      canvasHeight: 180,
    });
  };

  handleTemplateSave = async () => {
    try {
      const { editTemplateId } = this.state;

      const ele =
        document.querySelector('.cxd-Page') ||
        document.querySelector('.ae-PreviewIFrame');

      const imageContent = '';

      modifyTemplateContent({
        templateId: editTemplateId,
        content: JSON.stringify(this.state.schema),
        imageContent,
      })
        .then(res => {
          if (res.code == 200) {
            toast.success('Save successfully!');
            // 重新查询一下Page
            this.initPage();
            return;
          } else {
            toast.error('Save failed!');
          }
        })
        .catch(error => {
          // 处理异常
          toast.error(error);
        });
    } finally {
      this.setState({ saving: false });
    }
  };

  handleBlockSave = async () => {
    try {
      const { editBlockId } = this.state;

      const ele =
        document.querySelector('.cxd-Page') ||
        document.querySelector('.ae-PreviewIFrame') ||
        document.querySelector('.ae-Preview-inner');

      const imageContent = await this.generatePageScreenshot(
        ele as HTMLElement,
      );

      modifyBlock({
        blockId: editBlockId,
        content: JSON.stringify(this.state.schema),
        imageContent,
      })
        .then(res => {
          if (res.code == 200) {
            toast.success('Save Block successfully!');
            // 重新查询一下Page
            this.initPage();
            return;
          } else {
            toast.error('Save failed!');
          }
        })
        .catch(error => {
          // 处理异常
          toast.error(error);
        });
    } finally {
      this.setState({ saving: false });
    }
  };

  handleSaveSchema = async () => {
    if (this.state.saving) return;
    this.setState({ saving: true });
    await this.props?.showLoader();

    // 如果是模板编辑，那么进入模板保存函数
    const { editTemplateId } = this.state;
    if (editTemplateId) {
      this.handleTemplateSave();
      this.props?.hiddenLoader();

      // 保存之后对外发送消息
      if (window.parent !== window.self) {
        window.parent.postMessage(
          {
            actionType: 'PHOENIX_PAGE_SAVE',
            actionData: {
              triggerType: 'TEMPLATE_SAVE',
            },
          },
          '*',
        );
      }

      return;
    }

    // 如果是区块编辑，那么进入区块保存函数
    const { editBlockId } = this.state;
    if (editBlockId) {
      this.handleBlockSave();
      this.props?.hiddenLoader();

      // 保存之后对外发送消息
      if (window.parent !== window.self) {
        window.parent.postMessage(
          {
            actionType: 'PHOENIX_PAGE_SAVE',
            actionData: {
              triggerType: 'COMPONENT_SAVE',
            },
          },
          '*',
        );
      }

      return;
    }

    try {
      const { pageId, pageVid } = this.state.page;
      const { pageVid: currentPageVid } = this.state.pageVersion;
      if (!pageId) {
        toast.warning('No Page Can Be Saved');
        this.setState({ saving: false });
        this.props?.hiddenLoader();
        return;
      }

      const ele =
        document.querySelector('.cxd-Page') ||
        document.querySelector('.ae-PreviewIFrame');

      const imageContent = await this.generatePageScreenshot(
        ele as HTMLElement,
      );

      const schemaStr = JSON.stringify(this.state.schema);
      const resourceKey = schemaStr.match(/i18n:[0-9a-z]{8}/g);

      patchPage(
        pageId,
        JSON.stringify(
          resourceKey
            ? { ...this.state.schema, resourceKey }
            : this.state.schema,
        ),
        {
          imageType: '1',
          imageContent,
        },
        currentPageVid || pageVid, // 优先取当前版本的
      )
        .then(res => {
          if (res.code == 200) {
            toast.success('Save successfully!');

            if (window.parent !== window.self) {
              window.parent.postMessage(
                {
                  actionType: 'PHOENIX_PAGE_SAVE',
                  actionData: {
                    triggerType: 'PAGE_SAVE',
                    versionState: this.state?.page?.versionState || '',
                    pageData: {
                      pageId,
                      content: JSON.stringify(this.state.schema),
                      pageImage: {
                        imageType: '1',
                        imageContent,
                      },
                      pageVid: currentPageVid || pageVid,
                    },
                  },
                },
                '*',
              );
            }

            // 重新查询一下Page
            this.initPage(currentPageVid || pageVid);
            this.props?.onJourneyChanged(false);
            return;
          } else {
            toast.error('Save failed!');
          }
        })
        .catch(error => {
          // 处理异常
          toast.error(error);
        });
    } finally {
      this.setState({ saving: false });
      this.props?.hiddenLoader();
    }
  };

  render() {
    const {
      preview,
      type,
      curLanguage,
      page,
      pageVersion,
      editTemplateId,
      editBlockId,
      showUpdatePage,
      schema,
      moduleId,
    } = this.state;
    const { versionState } = page || {};
    const { blockKey } = schema || {}; // 编辑区块时，schema 里面会带 blockKey

    return (
      <div className="Editor-inner">
        <Portal container={() => document.querySelector('#headerBar') as any}>
          <>
            <div className="Editor-view-mode-group-container">
              <div className="Editor-view-mode-group">
                <div
                  className={`Editor-view-mode-btn ${
                    type === EditorType.EDITOR ? 'is-active' : ''
                  }`}
                  onClick={() => {
                    this.handleTypeChange(EditorType.EDITOR);
                  }}
                >
                  <Icon icon="pc-preview" title="PC模式" />
                </div>
                <div
                  className={`Editor-view-mode-btn ${
                    type === EditorType.MOBILE ? 'is-active' : ''
                  }`}
                  onClick={() => {
                    this.handleTypeChange(EditorType.MOBILE);
                  }}
                >
                  <Icon icon="h5-preview" title="移动模式" />
                </div>
              </div>
            </div>

            <div className="Editor-header-actions">
              {/* {editTemplateId || editBlockId ? null : (
                <VersionConfig
                  page={page}
                  initPageVersion={this.initPageVersion}
                ></VersionConfig>
              )} */}
              <ShortcutKey />
              {/* {
                // @ts-ignore
                // vite编译时替换
                __editor_i18n ? (
                  <Select
                    className="margin-left-space "
                    options={editorLanguages}
                    value={curLanguage}
                    allowClear={false}
                    onChange={(value: any) => this.changeLocale(value)}
                  />
                ) : null
              } */}

              {/* {i18nEnabled && (
                <Button
                  className="ml-2"
                  level="info"
                  onClick={() => {
                    let _uuid = uuid();
                    // console.log('点击测试国际化按钮', _uuid);
                    this.setState({
                      appLocale: _uuid,
                      replaceText: {
                        'i18n:1189fb5d-ac5b-4558-b363-068ce5decc99': _uuid,
                      },
                    });
                  }}
                >
                  切换语料内容
                </Button>
              )} */}

              {editTemplateId || editBlockId || preview ? null : (
                <Space size={16}>
                  <HistoryRecord
                    page={page}
                    pageVid={
                      pageVersion?.pageVid ||
                      page?.pageVid
                    }
                    curSchema={schema}
                    initPageVersion={this.initPageVersion}
                  />
                  <>
                    <this.props.RetireButton />
                    <this.props.ReleaseButton />
                  </>
                </Space>
              )}

              <div
                className={`header-action-default-bordedr-btn ${
                  preview ? 'primary' : ''
                }`}
                onClick={() => {
                  this.togglePreview();
                }}
              >
                <IconFontRender
                  className="default-border-icon"
                  type={preview ? 'icon-EditOutlined' : 'icon-EyeOutlined'}
                ></IconFontRender>
                {preview ? (
                  <span className="default-border-text">编辑</span>
                ) : (
                  <span className="default-border-text">预览</span>
                )}
              </div>
              {versionState !== 'T' ? (
                <div
                  className={`header-action-btn ${preview ? 'primary' : ''}`}
                  onClick={this.handleSaveSchema}
                >
                  <IconFontRender type="icon-SaveOutlined"></IconFontRender>
                  保存
                </div>
              ) : null}
              {editBlockId ? (
                <div
                  className={`header-action-btn ${preview ? 'primary' : ''}`}
                  onClick={() => this.setState({ showUpdatePage: true })}
                >
                  <IconFontRender type="icon-SyncOutlined"></IconFontRender>
                  保存并更新
                </div>
              ) : null}
              <UpdatePage
                blockKey={blockKey}
                moduleId={moduleId}
                open={showUpdatePage}
                schema={schema}
                handleSavelock={this.handleBlockSave}
                onCancel={() => this.setState({ showUpdatePage: false })}
              />
              {/*
              <PickerContainer
                showTitle={true}
                bodyRender={({
                  value,
                  onChange
                }: {
                  onChange: (value: any) => void;
                  value: any;
                }) => {
                  return <div>Humm</div>;
                }}
                // value={formulaPickerValue}
                onConfirm={() => {}}
                size={'lg'}
              >
                {({onClick}: {onClick: (e: React.MouseEvent) => any}) => (
                  <Button
                    size="sm"
                    classPrefix={theme + '-'}
                    className="btn-set-expression"
                    onClick={e => onClick(e)}
                  >
                    点击编写表达式
                  </Button>
                )}
              </PickerContainer> */}
            </div>
          </>
        </Portal>
        {this.renderEditor()}
      </div>
    );
  }
}

// 通过 localstorage 存储全局变量
// 实际场景肯定是后端存储到数据库里面
// 可以参考这个利用这三个事件来实现全局变量的增删改查
function getGlobalVariablesFromStorage(): Array<any> {
  const key = 'amis-editor-example-global-variable';
  let globalVariables = localStorage.getItem(key);
  let variables: Array<any> = [];

  if (globalVariables) {
    variables = JSON.parse(globalVariables);
  }

  return variables;
}

function saveGlobalVariablesToStorage(variables: Array<any>) {
  const key = 'amis-editor-example-global-variable';
  localStorage.setItem(key, JSON.stringify(variables));
}

function onGlobalVariableInit(event: PluginEvent<GlobalVariablesEventContext>) {
  event.setData(getGlobalVariablesFromStorage() || []);
}

function onGlobalVariableSave(event: PluginEvent<GlobalVariableEventContext>) {
  const item = event.data;
  const variables = getGlobalVariablesFromStorage();
  const idx = item.id
    ? variables.findIndex((it: any) => it.id === item.id)
    : -1;

  if (idx === -1) {
    item.id = uuid();
    variables.push(item);
  } else {
    variables[idx] = item;
  }

  saveGlobalVariablesToStorage(variables);
}

function onGlobalVariableDelete(
  event: PluginEvent<GlobalVariableEventContext>,
) {
  const item = event.data;
  const variables = getGlobalVariablesFromStorage();
  const idx = item.id
    ? variables.findIndex((it: any) => it.id === item.id)
    : -1;

  if (idx === -1) {
    return;
  } else {
    variables.splice(idx, 1);
  }

  saveGlobalVariablesToStorage(variables);
}
