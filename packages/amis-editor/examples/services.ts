import { Modal } from 'antd';
import originAxios from 'axios';
import { PageImageItem, PageType, TemplateType } from './typing';

const axios = originAxios.create({
  timeout: 30000, // 设置请求超时（10秒）
});

const getUrl = (url: string) => {
  if (url?.startsWith('/cdp') || url?.startsWith('/portal-web')) {
    return url;
  }
  return `../easycode${url}`; // 接口采用请求上一级路径下的/easycode的方式
};

axios.interceptors.request.use(
  (config: any) => {
    // Do something before request is sent
    config.headers['X-CSRF-TOKEN'] = sessionStorage.getItem('X-CSRF-TOKEN');
    return {
      ...config,
      url: getUrl(config.url), // 变相实现axios的baseUrl
    };
  },
  error => {
    // Do something with request error
    return Promise.reject(error);
  },
);

axios.interceptors.response.use(
  res => {
    return res.data;
  },
  err => {
    if (err.response.status === 401) {
      const originFlag = new URLSearchParams(window.location.search).get(
        'originFlag',
      ); // OriginFlag来源标记

      // 入参未说明跳过 modal 则显示 modal（接口重新调用一次，而避免直接提示异常）
      if (!err.response.config.skipModal) {
        Modal.confirm({
          title: 'You have been disconnected, please log in again.',
          onOk() {
            if (!originFlag) {
              // 未标记来源 直接刷新
              window.location.reload();
            } else if (originFlag === 'cdp') {
              // 来源二次开发平台 回到登录页面 因为可能被flow嵌套 端口号不一样 拿不到window.top
              try {
                window.top!.open(`${window.top!.location.origin}/cdp`, '_self');
              } catch (error) {
                window.location.reload();
              }
            }
          },
          onCancel() {
            console.log('Cancel');
          },
        });
      }
    }

    return Promise.reject(err);
  },
);

/**
 * 查询页面信息
 * @param pageId
 * @returns
 */
export const getPage = (pageId: string) =>
  axios.get<any, PageType>('/page/detail/query', {
    params: {
      pageId,
    },
  });

/**
 * 保存页面信息
 * @param pageId
 * @param content
 * @param pageImage
 * @param pageVid
 * @returns
 */
export const patchPage = (
  pageId: string,
  content: string,
  pageImage: PageImageItem | undefined,
  pageVid: number,
) =>
  axios.patch<any, any>('/page/modifyDetail', {
    pageId,
    content,
    pageImage,
    pageVid,
  });

/**
 * 查询模板缩略图
 * @param formVid
 * @returns
 */
export const getTemplateThumb = (templateId: string) =>
  axios.get('/page/thumb', {
    params: {
      pageId: templateId,
      imageType: '2',
    },
  });

/**
 * 查询page列表
 * @param systemId
 * @param funType
 * @returns
 */
export const getPageList = (systemId: string, funType: string) =>
  axios.get<any, PageType>('/formDesigner/build/bsdk/form/list', {
    params: {
      systemId,
      funType,
    },
  });

/**
 * 新增page版本
 * @param data
 * @returns
 */
export const addPageVersion = (data: any) =>
  axios.post<any, PageType>('/formDesigner/build/bsdk/form/version', data);

/**
 * 新增page
 * @param data
 * @returns
 */
export const addPage = (data: any) =>
  axios.post<any, PageType>('/formDesigner/build/bsdk/form', data);

/**
 * 删除page/template版本
 * @param versionId
 * @returns
 */
export const deletePageVersion = (versionId: string | number) =>
  axios.delete<any, PageType>(
    `/formDesigner/build/bsdk/form/version/delete/${versionId}`,
  );

export type PageItem = {
  id: number;
  name: string;
  pageType: string;
  type: string;
  children: PageItem[];
};

/**
 *
 * @param projectId
 * @param moduleId
 * @returns
 */
export const getPages = (
  projectId: string | number,
  moduleId?: string | number,
) =>
  axios.get<any, PageItem[]>('/page/list', {
    params: {
      projectId,
      moduleId,
    },
  });

/**
 *
 * @param projectId
 * @param moduleId
 * @returns
 */
export const getFlows = (
  projectId: string | number,
  moduleId?: string | number,
  versionState?: string,
) =>
  axios.get('/page/chain/list', {
    params: {
      projectId: moduleId ? '' : projectId,
      moduleId,
      versionState,
    },
  });

// FIXME: 安全检查会有问题，记得删除
/**
 *
 * 解决开发环境下认证问题
 */

export const login193 = async (loginTime: number = 0) => {
  // 登录193
  const params = new URLSearchParams();
  let userName = '';
  let skipModal = false;
  if (window.sessionStorage.getItem('portalUserName')) {
    userName = window.sessionStorage.getItem('portalUserName') as string;
  } else {
    userName = loginTime === 0 ? 'admin' : 'weijianfa_001';
    skipModal = !loginTime;
  }

  params.append('username', userName);
  params.append('password', 'Tmdwb3J0YWxfMTU=');

  try {
    await axios.post('/portal-web/login', params, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      skipModal,
    });

    // 获取193的token
    await axios.get('/portal-web/prod/logged', {}).then(res => {
      window._csrf = res?._csrf?.token ?? '';
    });
  } catch (error) {
    if (loginTime === 0) {
      login193(1);
      console.log('193环境登录异常，正在重试');
    } else {
      console.log('193环境登录异常', error);
    }
  }
};

export const login = async () => {
  // 登录193
  await login193();
  // 登录 cdp
  return axios.post('/cdp/portal/login', {
    loginCode: import.meta.env.VITE_CDP_LOGIN_CODE,
    passwd: import.meta.env.VITE_CDP_LOGIN_PWD,
  });
};

type TemplateRes = {
  code: string;
  data: TemplateType[];
  message: string;
};

/**
 * 新增模板
 * @param data
 * @returns
 */
export const addTemplate = (data: any) =>
  axios.post<any, TemplateRes>(`/page/template`, data);

/**
 * 修改模板基础信息
 * @param data
 * @returns
 */
export const modifyTemplate = (data: any) =>
  axios.put<any, TemplateRes>(`/page/template`, data);

/**
 * 修改模板详细信息(内容和缩略图)
 * @param data
 * @returns
 */
export const modifyTemplateContent = (data: any) =>
  axios.put<any, TemplateRes>(`/page/template/modifyDetail`, data);

/**
 * 删除模板
 * @param templateId
 * @returns
 */
export const deletTemplate = (templateId: string) =>
  axios.get<any, TemplateType>(`/page/template/${templateId}`);

/**
 * 查询模板列表
 * @param moduleId
 * @returns
 */
export const getTemplateList = (moduleId: string) =>
  axios.get<any, TemplateRes>('/page/template', {
    params: {
      moduleId,
    },
  });

/**
 * 查询模板详情
 * @param templateId
 * @returns
 */
export const getTemplateDetail = (templateId: string) =>
  axios.get<any, TemplateType>(`/page/template/${templateId}`);

/**
 * 通过模板新增页面
 * @param data
 * @returns
 */
export const addPageByTemplate = (data: any) =>
  axios.post(`/page/addPageByTemplate`, data);

/**
 * 根据版本查询页面信息
 * @param pageVid
 * @returns
 */
export const getPageByVersion = (pageVid: string) =>
  axios.get<any, PageType>('/page/version/queryVersionDetail', {
    params: {
      pageVid,
    },
  });

/**
 * 查询版本列表
 * @param pageId
 * @returns
 */
export const queryVersion = (pageId: number) =>
  axios.get<any>(`/page/version/${pageId}`);

/**
 * 修改版本
 * @param data
 * @returns
 */
export const modifyVersion = (data: any) =>
  axios.put<any>(`/page/version`, data);

/**
 * 新建版本
 * @param data
 * @returns
 */
export const createVersion = (data: any) =>
  axios.post<any>(`/page/version`, data);

/**
 * 复制版本
 * @param params
 * @returns
 */
export const copyVersion = (params: any) =>
  axios.get<any>(`/page/version/copyPageVersion`, {
    params,
  });

/**
 * 查询区块列表
 * @returns
 */
export const getBlockList = () => axios.get<any>(`/page/block`);

/**
 * 查询区块详情
 * @param blockId
 * @returns
 */
export const getBlockDetail = (blockId: string) =>
  axios.get<any>(`/page/block/${blockId}`);

/**
 * 修改区块(包括内容和缩略图)
 * @param data
 * @returns
 */
export const modifyBlock = (data: any) => axios.put<any>(`/page/block`, data);

/**
 * 查询模型详情
 * @param specId
 * @returns
 */
export const getModelDetail = (specId: string) =>
  axios.get(`/entity/detail?specId=${specId}`);

// 查询page目录+items
export const queryCatalogAndItemsPageSearch = (moduleId: string) =>
  axios.get(`/page/list?moduleId=${moduleId}`);

// 查询template目录+items
export const queryCatalogAndItemsTemplateSearch = () =>
  axios.get(`/page/template/list?moduleId`);

// 查询BLOCK目录+items
export const queryCatalogAndItemsBlockeSearch = () =>
  axios.get(`/page/block/list?moduleId`);

/**
 * 获取组件列表
 * @returns
 */
export const getPluginList = () => axios.get('/page/plugins');

/**
 * 查询配置的语言
 * @returns
 */
export async function queryLanguages() {
  return axios.get('/common/i18n/supported');
}

/**
 * 查询语料
 * @returns
 */
export async function queryCorpus(params: {
  appId: string;
  resourceType: 'P' | 'F';
  offset?: number;
  limit?: number;
  fuzzyValue?: string;
}) {
  return axios.get('/common/i18n', {
    params,
  });
}

/**
 * 增加语料
 * @param data
 * @returns
 */
export async function addCorpus(data: {
  appId: string;
  resourceType: 'P' | 'F';
  resourceKey: string;
  translation: Array<{
    langTag: string;
    value: string;
  }>;
}) {
  return axios.post('/common/i18n', data);
}

/**
 * 查询模型列表
 * @returns
 */
export const getModelList = () =>
  axios.get('/entity/list', {
    params: {
      specCategory: 'ENTITY',
    },
  });

/**
 * 获取扩展主题配置
 * @returns
 */
export const getExtendTheme = () => axios.get('/page/runtime/extendTheme');

/**
 * 根据区块Key获取页面列表
 * @param blockKey
 * @returns
 */
export const getPagesByBlockKey = (blockKey: string, moduleId?: string) => axios.get(`/page/pageList/${blockKey}`, {
  params: {
    moduleId,
  },
});

/**
 * 编辑态根据页面名称获取页面数据
 * @param pageName
 * @returns
 */
export const getPageByName = (moduleId: string | number, pageName: string) => axios.get(`/page/pageDetail`, {
  params: {
    moduleId,
    pageName
  },
});

/**
 * 通过页面名称获取版本列表
 * @param pageName
 * @returns
 */
export const getVersionListByPageName = (pageName: string) => axios.get(`/page/version/pageName`, {
  params: {
    pageName
  },
});

/**
 * 获取页面修改历史
 * @param pageVid
 * @returns
 */
export const getReversionHistory = (params: { pageVid: number, limit: number, offset: number  }) => {
  return axios.get('/page/data/his/', {
    params,
  })
};
