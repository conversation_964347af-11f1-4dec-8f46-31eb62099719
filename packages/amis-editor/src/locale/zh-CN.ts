import {extendLocale} from 'i18n-runtime';

extendLocale('zh-CN', {
  'f7e68bde2caa2cb5696d6a37fe4a23a4': '常用',
  'ea15ae2b7fba76c83eec6d0986d15197': '选项',
  '3fea7ca76cdece641436d7ab0d02ab1b': '状态',
  '24d67862f87f439db7ca957aecb77cce': '属性',
  'afcde2611bdd13c1e65b4fb6a2f13425': '外观',
  '6d0034a2419e1f394dedab07994b9665': 'Label CSS 类名',
  '2cadb6621afe19333b142faa541b0f91': '控件 CSS 类名',
  '0e627e6a0ff773ee76bc4cc0871cb48d': '描述 CSS 类名',
  '10b2761db5a8e089049df39675abc550': '事件',
  'dc19704991f1476fa4dcbb80c50bedd6': '提示标题',
  '55713166f8bddcc0aefc3a32464746f1': '请输入提示标题',
  '2d711b09bd0db0ad240cc83b30dd8014': '内容',
  '59b9e2022323a63079c6ddab63fec112': '弹出位置',
  'af767b7e4ae069d54f9ea839858d4c6d': '上',
  '3850a186c3235bc646d4c2f79cebac36': '下',
  'd2aff1417831aa621c16cd5b95306b4b': '左',
  '4d9c32c23df5d234e629c922c58d8e12': '右',
  '5ef69f62dc668c1a3e68b51c50a2530a': '图标',
  '4434b33a8731a73613ba5fa1eb984efb': 'CSS 类名',
  '159dbc2fafd57b9d3652f16659b1b519': '触发方式',
  '45a51525391d4a3771b22f2cf1aa96b3': '浮层触发方式默认值为鼠标悬停',
  '728c7cdfa431821d291b5108394ec65a': '鼠标悬停',
  '4363c17ebb346b646af55bd8c8075915': '点击',
  '5632c68dac780bd766f740830481f4b0': '点击空白关闭',
  '9efb0ce5a4510ef29345b6edb3e58bc2': '必须有 td',
  '3ce57bd19e37d2b27145dc6fcfff3520': '找不到第一个 cell',
  'f7d205072a2ceb63b4f48a8b6f32fd25': '行线数量不对',
  '852228c640b1daefe6b0853390e66791': '列线数量不对',
  '26526c3354307798dfa84f17decf5140': '找不到对应的 id',
  '38d2ccdde0ae0c2329defd3c75c59d8b':
    '由于内边距限制，太小的高度设置会不生效，可以调小默认内边距',
  'd59379f4227af3b2c60214e2f4f903ba': '合并单元格',
  '02d9819ddaaaeb1b7b22b12608c7e5ca': '提示',
  '4be3194e93cdd2899d06b499c184195b':
    '用来做文字特殊提示，分为四类：提示类、成功类、警告类和危险类。可结合 <code>visibleOn</code> 用来做错误信息提示。',
  '6f2b01db04cbf7e460b5c6f4e37a5e76': '提示内容',
  '132a78bdf68d6d17bde00aa807bbf032': '内容区',
  '4092ed98e9035652d4c9ca9441701ed7': '基本',
  '226b0912184333c81babf2f1894ec0c1': '类型',
  '330363dfc524cff2488f2ebde0500896': '成功',
  '900c70fa5f7dbc014e6f762d5e0e885c': '警告',
  'e2e27a87257599f83c817c43e724b6aa': '严重',
  'cbda486dbec5bdacb593294e240c5e63': '可关闭',
  '7372dc9f39a173dd0c75a185373245b1': '自定义图标',
  'bede211909a1022b13e9728ed162b77e': '锚点导航',
  'f6da8aa86fa3e399da95dab399a67985':
    '锚点导航，在多行内容展示时，可以将内容用锚点导航分组的形式展示，点击导航菜单可以定位到对应内容区域。',
  '22c799040acdb2601b437ed5449de076': '容器',
  '5879dec0aea52871e0ae6969893a315b': '锚点1',
  'ce08bc4247c040bac13155befc7c1638': '这里是锚点内容1',
  'd89a42cb938819f67172ea64037c19fe': '锚点2',
  '96f05e63c6f606925636d6810167e7ea': '这里是锚点内容2',
  '31327b9041b825a0b455b3ff9ddd3909': '锚点3',
  '68284dd430993f495a6f2254ae5480ae': '这里是锚点内容3',
  '9ad515106f02f3909974b75d22625b0d': '锚点设置',
  '40c6e9ed3d9949a326f5a216d936324d': '添加锚点',
  '8cfd149e7d73ebae6a797d21728292ff': '请输入锚点标题',
  '6be15e9949e4be7fc485e1eaae472942': '锚点',
  'c624c875ea37f790665d0cae8959d4e2': '这里是锚点内容',
  'f612a2f944af178fa07a719c79e8438b': '锚点{{@1}}',
  '42ff02f6763799ebfa5ce8dd5f99913d': '这里是锚点内容{{@1}}',
  '39f4fbc5b5ba681f7e8c4d4a4ddb3e2f': '默认定位区域',
  '9959b2ad2d4f75f7a6be46872642df6d': '导航布局',
  '4cde06e6162ed66720e3133cb83bc059': '水平',
  '75ac842f8e77305846f1d776f97dfaf8': '垂直',
  '056f2d7df6e6b64625c3a2d27ce07b05': '导航',
  '696754a8b2b23e30b11187303d1238f5': '区域内容',
  '33be689a0f0de129ce37f7a96052002e': '内容区域',
  '32c65d8d7431e76029678ec7bb73a5ab': '标题',
  '6bd854c27cd4c2e97dee65cf3f3f8551': '锚点内容{{@1}}',
  '726dd5df4319e9e158e2ca3c22177b6c': '音频',
  'e7a707f9fc7da36828db940ca2960f4b': '音频控件，可以用来播放各种音频文件。',
  '997c7a5d34f6fc628893f509d0df32e0': '功能',
  '22b777e6fcb613b8ba83ced9594cd07e': '常规',
  '37b12f2666b9e4e37f33eb5e83533d5e':
    '<p>当前为字段内容节点配置，选择上层还有更多的配置。</p>',
  'f8f176147db276063e7ec15f076e39e0': '音频地址',
  '91d3cd46d6b6919749e56056d5acc9bc':
    '支持获取变量如：<code>\\${audioSrc}</code>',
  'a945269af10da66c82cdb7336bc490d1': '音频倍速',
  '7ccd84ca5c16cd03d26f5ecd5e6f6bd2': '加速范围在0.1到16之间',
  'fc03b83d19e2fd12f1e7c56a11d7dc18': '内部控件',
  'd37d357dee041774b993daaf5c8bb752': '倍速',
  'b85270cd3c06d8eb635eadcffbb10119': '播放',
  '19fcb9eb2594059036dfede5f4ec53e8': '时间',
  'c7bff79d059a0b7ff9b02441959d8be2': '进度',
  '09b095d8fc867cb968673be9dcc00a93': '音量',
  'ad751bba0aed43a673c40b652a239fc3':
    '选择倍速后，还需要在常规选择栏中配置倍速',
  '54c6bb48170611ec995f634319312156': '自动播放',
  '56e6ecf97176d30c06b30cfa428ef832': '循环播放',
  '2fb0853874c2cc8cc42f0c7520af662a': '内联模式',
  '33bf801796fd255b5f6147e33146669b': '显隐',
  '4c50eef3bdaf0b4164ce179e576f2b2d': '头像',
  '18dc831ec12d358d05902bef1f00e1f1': '用户头像',
  '027446c2f9070b0f5b16a18208bf5fc7': '展示',
  '20def7942674282277c3714ed7ea6ce0': '图片',
  'ca746b1ff10193a3ce20878dec04a733': '文字',
  'bfe68d5844f8e54602760e18f45954f7': '链接',
  'b54f4a65cd257c87db32ee1023e1daa1': '填充方式',
  'def423db04dd24b226911b9e4cf5dc9c': '图片大小与控件大小不一致的图片处理方式',
  '74735df86a8e1c15bce50e2c1dd42e98': '等比例裁剪长边',
  '9d7f3e170404f211a4f95c214f044b05': '等比例留空短边',
  '9854f491213784118614be4a1970bcf9': '拉伸图片填满',
  '3d2b60dda894eba9a042beddf7daf3cc': '按原尺寸裁剪',
  'c7fff1a6f585d7fb22c94bb1ef82707d': '边框距离',
  'f1b4c635cdb39c91461f181d27d06f8c':
    '文字居中，文字过多时保持与边框最小的距离',
  'dfd0cd7f2cba96917484569a646bdc8d': '长度',
  'c1df04eec5fa0857bc0df2d68d8e953c': '高度',
  '0103eb2d3dca70270d1a74e9ec987ac9': '圆角',
  '9a233b241eef54521cfe9365bfaa7b2f': '内外边距',
  '961534b4ea37e4e88aada736b299d063': '边框',
  '8e1b944f4389bdaab6f11d5bc83190c8': '背景',
  '803205e38834280d9e6a75993ac00764': '阴影',
  '0d98c74797e49d00bcc4c17c9d557a2b': '其他',
  '34dac4adbc96afd65f060cc4cfff1feb': '透明度',
  '169b227aff15b834b64205d0fdcb0f33': '面包屑',
  '3576258acd7269da9f44859a2edec1aa': '面包屑导航',
  'db1c89e0f6e62f9642018cbb531bbd4f': '首页',
  '2e8bf3c87641fba59c2a9cb6636d7e88': '上级页面',
  '12d358955755488ff3790bbd6d75673a': '<b>当前页面</b>',
  '894b94fbb15650da7f9290e7106446f3': '分隔符',
  'a38100f22f59f7cd89e36139faa6fd4d': '动态数据',
  '66ab5e9f24c8f46012a25c89919fb191': '新增',
  '97d07614380da93d257f9fbf81aa56fb': '文本',
  'ba7f916a39c0beb545388ea3b7f6b3b7': '面包屑的 CSS 类名',
  'e3acc9857c852dae27e064ace5e5688e': '分隔符的 CSS 类名',
  'fa966345577ba81af19408f203db968f': '按钮',
  '89de611b2d759a1802542b5d3a06829f':
    '用来展示一个按钮，你可以配置不同的展示样式，配置不同的点击行为。',
  '7af5e3ef39ff71d39fe3f645c8079124': '点击时触发',
  'f6d767f39ba3bf955077a3c0ce81e581': '鼠标移入',
  'bcdd89d453da0dc0622a2f3189728357': '鼠标移入时触发',
  'e272b0b8c7fedc670a87075514d9b49f': '鼠标移出',
  '727309bc724ff237c5e2cdf7a90cf28e': '鼠标移出时触发',
  'd7ec2d3fea4756bc1642e0f10c180cf5': '名称',
  '939d5345ad4345dbaabe14798f6ac0f1': '提交',
  '4b9c3271dc2f299dc3aeffb369187513': '重置',
  '5e64227f46b221001e3b151d72fa6412': '是否关闭',
  'd5bb99590ef447a1af8e9782786c751d': '指定此次操作完后关闭当前 {{@1}}',
  '1e7196ce05260faa55e5b7ea5a1667c2': '二次确认',
  'ed2f135144b4e138cb29419c1f245f4b':
    '点击后先询问用户，由手动确认后再执行动作，避免误触。可用<code>\\${xxx}</code>取值。',
  '0d0c6ff7b65e62eba3ddd523e2976389': '确认内容',
  '7e9646e2db5ce66dc2b4b922ece483ba': '气泡提示',
  '5daaffe964aee9d884e0a681b2818a17': '正常提示',
  '2fd82aa9dd7fedea2c16b7dfe93b6d0e':
    '正常状态下的提示内容，不填则不弹出提示。可用<code>\\${xxx}</code>取值。',
  'fb37b983a93aabdcdbbd59ae48c519fb': '禁用提示',
  'f4f168a3fec79443d2ca8fd1955edee8':
    '禁用状态下的提示内容，不填则弹出正常提示。可用<code>\\${xxx}</code>取值。',
  'd420160a9e04c481e55a9686ab158caa': '鼠标悬浮',
  'd6763cb7640bed39caa058f156007a86': '聚焦',
  'd586324c6d6b45cb78a4172d836dab3e': '提示位置',
  'c182ad6b97f0909596a523b1f04c28d2': '左侧图标',
  'ad7e6f016bc1d9a9bbc6e18224d73247': '右侧图标',
  '39003734d1d700d5bd97bf1e7a2fcf73': '样式',
  'c12ba7b658a9fccf0815f3951bc759b6': '高亮样式',
  '6aa6b9e2dca63d27dc74eb155020271d': '块状显示',
  'c8339fd2a85af4ba66084d28df808de4': '尺寸',
  'c8caf94205105bac5833ab31cc2129d7': '按钮组',
  '66ae9ce23b0b2e243aff087d906a2489':
    '用来展示多个按钮，视觉上会作为一个整体呈现。',
  'e9d2f66bbd44c96a3e03494bf6b1ebf0': '按钮1',
  'ce35a17d2ba7caac95092a7a66ac9a0d': '按钮2',
  'de26e1294acedb55155a418630d57924': '布局方向',
  '8eb18b36f5a27fa8e6d32bc66546ce05': '平铺模式',
  '2e28645c67c5742e473888a27aab7bd6':
    '使按钮组宽度占满父容器，各按钮宽度自适应',
  '66774850742a81e8b2393195290b7330': '按钮管理',
  'f9f2b9cc91cd332db6b2b732c3869233': '新增按钮',
  '40f3bc0a4f4d0f4230fc7fa6f4fcec92': '子按钮',
  'f05dd80af77a441216ef940e7887a8db': '按钮工具栏',
  '433e2e80ec74563daf4368e59b525e34':
    '可以用来放置多个按钮或者按钮组，按钮之间会存在一定的间隔',
  '6651fec0511e3593d3438a113dff23d6':
    '可排序、可移除、如要编辑请在预览区选中编辑',
  'd7213304d1a8a02a73a2f4010839e061': '增删改查',
  '7a9f1ec32752de8b69ef21138970f64d':
    '用来实现对数据的增删改查，支持三种模式展示：table、cards和list. 负责数据的拉取，分页，单条操作，批量操作，排序，快速编辑等等功能。集成查询条件。',
  '48c68eb5a42e2b3e679c53b00f6e203f': '渲染引擎',
  '64ca9bab920a2983bcf270320d850d00': '重新加载',
  '9ef5597ac0b4da662bcd967da37eceb4': '触发组件数据刷新并重新渲染',
  '95b351c86267f3aedf89520959bce689': '编辑',
  '607e7a4f377fa66b0b28ce318aab841f': '查看',
  '5b48dbb8dc710cffe6313bb56a7f6d47': '查看详情',
  '2f4aaddde33c9b93c36fd2503f3d122b': '删除',
  '6c546666aab964c39cd8bfdf4fbd46b8': '确定要删除？',
  '7fb62b30119c3797a843a48368463314': '批量删除',
  'e73cefac9d030927da1618c7b15c98c9': '批量编辑',
  '19c6b7463e1bf73bb4b12ba07abd5444': '字段1',
  'cf12e55021998a8328201800ec356773': '查询条件',
  'cfb5f18c43753ad5329348d626bd3739': '关键字',
  '4a1e3c50547e61503a2d1c356005eb08': '增删改查快速开始-CRUD',
  '85624c8e8b0fc98954eecbe508e8b59d': '接口地址',
  '3dd674542204724eb5417efc7354ec73': '格式校验并自动生成列配置',
  '3266535dc49863d976b9960adad29fef':
    'API返回格式不正确，请点击接口地址右侧示例的问号查看示例',
  '07b59fd09f6007bac246d9a73b793a49': '启用功能',
  'bee912d79eefb7335988c4997aa9138d': '查询',
  '0f61da949d2b45534967e197cc2eee29': '批量修改',
  'aa85b3cd6aa4cdfd45bfe5a96678ad2f': '操作栏-编辑',
  '653eb2792d3126a60caa6982f89a906b': '操作栏-查看详情',
  '8a4d6dfbcd8072555b5951091f171000': '操作栏-删除',
  'f4b368051b455e386a314688c7c75c1f': '启用的查询字段',
  '23c7ea8ee9519459598b2de65fe2a2eb': '每列显示几个字段',
  '41a344642681efaaa418c228ba7fb45c': '绑定字段名',
  'ffb01e5bcf4c00447f5150d3cba81371': '纯文本',
  '59cf15fe6b8d659c9bd2f86143534a06': '模板',
  '4ff1e74e43a3586339251494117185ad': '日期',
  '9da188491dd34c4382a5b9f006194e41': '映射',
  '8abc564260a1564521e0c3a1d5419b4a': '操作栏',
  '2b6bc0f293f5ca01b006206c2535ccbc': '操作',
  '07a88fae7dd11f87891af91fb54a74bb': '启用查询条件',
  '7f7c624a843b0d539a4ea59a696702f9': '批量操作',
  '46e6edaeb9968e7f7ab549d4f2f82d6d':
    '通过此可以管理批量操作按钮，只有设置了批量操作按钮才会出现选择框，可在外观中配置批量操作按钮位置。',
  '8347a927c09a4ec2fe473b0a93f667d0': '修改',
  'f13a0697d58d975d14eb3f3c72c2cbf2': '单条操作',
  '3674ea51498e7118732e5e1c53d4bc80':
    '设置后，当鼠标悬停行数据上，会出现该操作按钮，同时顶部操作栏也会显示该按钮，勾选成员时与批量按钮智能切换。',
  '4916771d080ddf6d0551de4d6d2f42a4': '悬停隐藏',
  '569343b4fe5e48131b78611c11eadbeb': '同步地址栏',
  '6dbee29a8c54eef9d042ef3280999ad9':
    '开启后会把查询条件数据和分页信息同步到地址栏中，页面中出现多个时，建议只保留一个同步地址栏，否则会相互影响。',
  '2171d1b07d045e796cba4a05bcf7d13f': '默认参数',
  '01e7eb2256821085a14708d6ddf36814':
    '可以用来设置默认参数，比如 <code>perPage:20</code>',
  '58ed0a7a5a91996dbb4c7d6dc7679364': '保留条目选择',
  '36ac0011faae6f88ee0ec3e642328327':
    '默认分页、搜索后，用户选择条目会被清空，开启此选项后会保留用户选择，可以实现跨页面批量操作。',
  '2a0b47ba76e886070c3bd6abeae3d1c0': '单条描述模板',
  '6ab3e5b6c5d5d2cf621e911226495433':
    '开启【保留条目选择】后会把所有已选择条目列出来，此选项可以用来定制条目展示文案。',
  '987b04af242bb2dafaf32d890ab952ff': '指定主键',
  'a270e70be12fb13a24ca3e4ac70fa838':
    '默认<code>id</code>，用于批量操作获取行级数据',
  '54ea89b497ec3bb319c68844dfa3687f': '接口',
  'db7ee36de8063c2d5a6c123eac65641a': '数据拉取接口',
  '0951dad1723aa1191ce1a2e96db76051': '是否初始拉取',
  '0a60ac8f02ccd2cf723f927284877851': '是',
  'c9744f45e76d885ae1c74d4f4a934b2e': '否',
  'a9400c408441f1f7f6d6954deb05ae9a': '表达式',
  '55b22f5d136e73b26cef65aedd8ba86e': '用 JS 表达式来决定',
  'bedc70d448b656d828bd3ed7926b3e4d': '一次性拉取',
  '559fa334442f0f75b0343bbf38b7ff05':
    '开启后，数据只会在初始的时候拉取，后续分页、排序不再请求接口，都由前端直接完成。',
  '1af68c43e1780249f11b9de1eeaeb281': '开启定时刷新',
  '9800f1ce2f78a23b81c8d944ebf9cce9': '设置后将自动定时刷新，单位 ms',
  '19c5410b23fba4bbfd1a58bbd5268c9b': '静默刷新',
  '04f840b0772f4b5d59954a29a76f4e7b': '设置自动定时刷新时是否显示loading',
  '6037dae99e9446deaed45f7e408f47ab': '停止定时刷新检测表达式',
  '32e3a79e80dcf7f7304e8092dd7acc6f':
    '定时刷新一旦设置会一直刷新，除非给出表达式，条件满足后则不刷新了。',
  '154ef40e477c031f6c1ec15caefb570a': '当有弹框时关闭自动刷新',
  '50f7c85bf60a9f43522789644566c62b': '弹框打开关闭自动刷新，关闭弹框又恢复',
  'd8905a70e93a33c7a86240f467c653d4': '是否可拖拽排序',
  '040a78b24eaff47d4fa7e266473635b4': '顺序保存接口',
  'ee850a0e326b217bdeb61d936c521177': '快速保存接口',
  '8343f619879fa79bc8ef1a35b2fc3e78':
    '当 column 中设置了快速编辑后将使用此接口批量保存数据。',
  'd891e79d4a8718a7dbd47ac68aaaa5cb': '快速保存单条接口',
  '38db6e045e214ffcd03ede695002271c':
    '当 column 中设置了快速编辑且设置了立即保存，将使用此接口保存数据。',
  '522110866c19dace2ce38336617405c2': '默认消息提示',
  '3b69c2e540c05e9ca530ed30cf224472':
    '覆盖默认消息提示，但如果 api 返回 msg 则会优先使用这个 msg',
  'fb24383a41f23196349548b5d0cb98ce': '获取成功提示',
  '62e3e15c8fb9038f2780329bc26e8bab': '获取失败提示',
  'c62a1b7f314be10aead10475e7543f6a': '保存顺序成功提示',
  'c8035507b7a576d43e9f227c91c7a7b5': '保存顺序失败提示',
  '7cb0932b806559be232d2a69453224e7': '快速保存成功提示',
  'fd79a193a487b8c9d5a302d0d88c1c2c': '快速保存失败提示',
  'ff57a3bf69a1065707e77c18c51f7bbb': '内容展示模式',
  '20aadc3f9b7edb564dc58898898e0dc8': '列信息',
  '72cf373be86a38b29f6d2f15900b0da1': '副标题',
  'f26225bde6a250894a04db4c53ea03d0': '详情',
  '3bb4d608c6bee2b7b6d788417cde04e3': '简单的展示数据：$a $b',
  'b339aa87104709397ba68e7ebbc6e5ba': '表格',
  'd87f215d9ac688b1d3399bf575a0ef6f': '卡片',
  '3712972d84adf48acbd6ad24b4d75ad0': '列表',
  'e4fd8d64804a4f3d743eff384a6eb20a': '非内建内容请在预览区选中后编辑',
  '14555503d6e09ecd66661d04c882e79b': '顶部工具栏配置',
  '3862626c138ce5945e0e273a1bdfbad0': '分页',
  '439a19857be1fb8d3e6017258e32c486': '统计数据',
  'bc908986a4e4eec3dca3530afd1d4306': '切换页码',
  '77281549955309c49f5eef77838a85e5': '加载更多',
  '1add12ff3338a990b60154b75019c348': '导出 CSV',
  '7d57852b946d9d4d64fb2a48ca2b4a44': '导出 Excel',
  '16c6099bc0efaa669d8de698c105e333': '列选择器',
  '073694928db5b0b5423ebe095ec62d39': '查询条件切换',
  '5b9c3e6ce478196f802722cb09d61f0b': '拖拽切换',
  '66eeacd93a7c1bda93906fe908ad11a0': '全选',
  'd5bc35360607472de4525358af126de4': '对齐方式',
  '413f48cc71f71083ce532a86e3efdc21': '左对齐',
  'fd8e9572cc6bf87653c4d8f8b8dd0db9': '右对齐',
  '1325969c143a639294c1c6ab370b35a3': '底部工具栏配置',
  '440a3a2d7f1b123718be75e2baee8391': '详情请在预览区域选中后进行编辑。',
  '34da7e4a0ecdb63967427f43adf2de3e': '是否可显隐查询条件',
  '25c0db3ddce9bfffd497d0c824cf3c72': '查询条件默认是否可见',
  '259d11c300a365b6767228c12d29ce53': '隐藏顶部快速保存提示',
  'fa9417bacb7714e82663655345ca213d': '是否总是显示分页',
  'e3d2a85f20608a5bde7d539969d03167': '隐藏选择按钮',
  'af1af0a7fad9e8bdcd21694d0e036e12': '内容 CSS 类名',
  'c11322c9cc43ce3c004cf03f5ac0acd0': '数据源',
  '62569fcb0fc8314daea97989bba3877c':
    '不填写，默认读取接口返回的 items 或者 rows 属性，如果是别的，请在此设置，如： <code>\\${xxxx}</code>',
  '606bc931d2b3ebba2569cb1494719e2c': '每页数量',
  '004d01f56242e07cbdc97256bb11c75b': '翻页时保留选择',
  '3d330edb46341a26ccc9aaa7f7938a8e': '最大选择数量',
  '95e68bea2d8c8e41ffa619d4364a0d6f': '页码字段名',
  '537f8b09437bdb7fac429dc4e8666763': '分页步长字段名',
  '30c7bd5acd4564057bd89e0846f01728': '排序权重字段',
  'dd8d217677e4c5df28b7f46aa99b22a8':
    '设置用来确定位置的字段名，设置后新的顺序将被赋值到该字段中。',
  'e04e8f570e2fb43a533b224f3c48523d': '切换每页数',
  '0fc1de8f71a7470213fc68f981abdbc2': '配置单条可选中的表达式',
  '7d5fefd589000879088063dceb4b2552':
    '请使用 js 表达式，不设置的话每条都可选中。',
  '614ec7801e03f7ee009e4448e6ed4001': '开启单条点击整个区域选中',
  '530ab79908eabe5b329ffe17695079e2': '自动跳顶部',
  '9092afb1ccb692308ef06d8001da2208': '当切分页的时候，是否自动跳顶部',
  'c8cf39b24bb52d0562472c33b86824fe': '同步查询条件',
  'd3c172700af4f3f3afb028d6bb4a90a4': '查询后将返回的数据同步到查询条件上',
  '5db6b2160b9169b9d89de247d14ab740': '「增删改查」编辑辅助区',
  'a4313469fd7361486fe47076629c76ac': '新增记录',
  '50abd0bf31e07dbee84e9e5b9a407840': '新增数据',
  '73f28ac83b623fb81d73f16fb3856fa0': '数据导入',
  '8d9a071ee2ef45e045968e117a205c07': '导入',
  '09d44261d7883bf5141a462a790d2816': '数据导出',
  '55405ea6ff6fd823ffab7e6b10ddfa95': '导出',
  '6ff4bf3d567e977aa4c90c27dff1e6db': '模糊查询',
  'e5f71fc31e7246dd6ccc5539570471b0': '搜索',
  'c26996a6506adf397f0668d376d0b40b': '简单查询',
  '9c4666fd08c2738eb9611a3721cb5f0f': '高级查询',
  'e22b59b6bda1cf9a58f8979fd0a0b43c': '编辑记录',
  'a790208cafd5c95a18dd9a168319ecf8': '删除记录',
  '39ccc34fa3ee9be12d8bae7e6fecbac2': '操作列',
  '240145572215920ae06db1eeb85b29c0': '可选择',
  '099cf136d6a4b6ed4646af4a2ed066b2': '保留选择项',
  '60011314ed92794f3d4f3e874c359279':
    '默认切换页面、搜索后，用户选择项会被清空，开启此功能后会保留用户选择，可以实现跨页面批量操作。',
  '949a8b7bd2c10070a2fae16f9c66afbb': '列设置',
  '2816cea6c4887a53c417831deb5fbe00': '自定义显示列',
  'd3c5079f7e26b1a7374ff76341376de4': '自动即列数量大于10自动开启。',
  '4a3ebd0ef27212de3b0c39e6a9701b1d': '搜索设置',
  'a9a3a4608d48214efbdfac99888f6e0f': '操作设置',
  '84a2f40385427bbf2edc79e3368e4e0f': '添加操作',
  'cb43abed5ba14bf32fbb1058e12d2303': '更多与分页',
  '48b42e5c3ea447092eaf0a1a13195540': '分页方式',
  'd81bb206a889656035b929cd8bb1ef10': '无',
  '16b8ff2b147382be4cf8654f829df904': '前端分页',
  'd2e20bb1e977f9571a9e2d1b39a7ff10':
    '数据一次性加载到浏览器，而非每次请求后端用户所请求的当页数据，不建议开启，对性能影响较大',
  '73721e611daaafe5c34aa9f3f901d016': '数据容器',
  '46a0f3086dce242abe54e48bd86e0394': '列表展示',
  '278249b178c958cee0f5ee9ee9d1e0f5': '{{@1}}创建向导',
  'd75a7984d3fa5b32f5d8312e899aeea8': '数据配置',
  'c2f1f9254c245976e346377515c2e578': '功能配置',
  '012f602372cd2dbd639cd966c63e1f90': '工具栏',
  '0943d61befec4c6cf2d21d170c9b066e': '条件查询',
  '5246d2c81fa12b1f4f73635c257e232d': '数据操作',
  '1b79a4f49b7a21e62b8868f12189b0b0': '数据加载',
  '5a28d015b7b3518f75bc4cc27d9f6c20': '列表数据翻页',
  'cfd84204d9476936c949d071cc2338cf': '数据查询',
  'c6bd3393c21379d3f75d179abe36da3d': '使用指定条件完成列表数据查询',
  '3a6ecf25c38317b21b8c6287100f053a': '触发数据查询',
  'b3a4d6a345372c5def1d5a1bf6077bce': '加载更多条数据到列表容器',
  '34e83e1be408c4f198464da1bf56bf9c': '加载更多数据',
  'fe9e25f4e4b3aeefeb9b7a9c368ede7e': '全部数据',
  '2c77cfaef73ce2e81131861e9c6d670e': '选中数据',
  '9c9153c49491c381dc2adb2c36fccb04': '未选中数据',
  '9a4fe969f1066e197fd2369a44d879ac': '当前页码',
  'a7f33a2d99056edcdaced5c8841a9bcb': '总数据条数',
  '1a63ac23010e0573f7c0a8cd3314b8c6': '示例',
  '093c340f7e1fbde1928ca56b5c7f9cc4': '卡片列表',
  'a0c35361a003527d123cb581f5c68f4b':
    '围绕卡片列表的数据增删改查. 负责数据的拉取，分页，单条操作，批量操作，排序，快速编辑等等功能，集成查询条件。',
  '9caecd931b956381e0763d05aa42835c': '字段',
  '860827796ce2fa94e9ee39e53f011ec0':
    '围绕列表的数据增删改查. 负责数据的拉取，分页，单条操作，批量操作，排序，快速编辑等等功能，集成查询条件。',
  '629645f147f378869fe9d7ee2bbc2857': '副标题内容',
  'f49d40842a3c66c4de2e57a48157c707': '展示单个卡片。',
  '0212e8c9b113143a031d1f3e25167794': '请从左侧组件面板中点击添加按钮元素',
  '6312e80e416fa79ff3383e1577c243b8': '请从左侧组件面板中点击添加内容元素',
  'a3cf7c9ee581ae71eb51d18b610b05b6': '新增内容',
  '00a1f644f34b9ee43adf82cb3449158c': '支持模板语法如： <code>\\${xxx}</code>',
  'f7a82c9758acc4ff4c5350182f2ca378': '图片地址',
  '3bdd08adab6ea90b9164b20a0e4151ac': '描述',
  'd85562778760b00b1372af642c4630e6': '是否高亮表达式',
  '4011e5fb23b422f755d81269a5242d22': '如： <code>this.isOwner</code>',
  'd4f223e0619836d03d488c89558f38e7': '卡片一行最多能放按钮个数',
  '620868e5e60e5c22249c3277c971bb13': '标题 CSS 类名',
  '1fee6fa022c97f5dd7f48e362cea5acf': '高亮 CSS 类名',
  '2a3c7d647a29fb7dc5aedabac216575e': '副标题 CSS 类名',
  'a5680444d449b2099b067e9963fe43aa': '图片外层 CSS 类名',
  'b8e1e46cbdea20de4e5fc130d31b7bcc': '图片 CSS 类名',
  'a31119cb2fddee3366163a311f317cf7': '内容区 CSS 类名',
  'e82eb2350b4283822aeea21aff9d97b5': '字段{{@1}}',
  'bcee820bc20342a4d388a35ed32a35fe': '列名称',
  '97d03d4621f0024cf045afbd901197a5': '选择区域',
  '44705bb94d83e7bd6b3b6c1480ebfb38': '点击触发选中或取消选中的区域',
  'e30a958a6397e53fae9d5316e851d3aa': '整个',
  '31ad7a215f3df84c33b8c28223147b8e': '选框',
  '563ae5ccda5004fa1fce77c1ed5bf057': '隐藏选框',
  'aeca6900b9e45f7f235eb9d443f0dd61':
    '不再显示选择框，可以通过自定义选中态外观实现选中样式',
  '1d4103a96a70de2cb69dd597d679fefe': '选中态',
  '85c17b016309a3f867a1803049b3bcd8':
    '功能类似于表格，但是用一个个小卡片来展示数据。当前组件需要配置数据源，不自带数据拉取，请优先使用 「CRUD」 组件。',
  '6223c41373004e3111e768225450b4e8': '卡片集',
  '3983b9f5575ae146d2d06f8ec5447a4d': '配置单项信息',
  'a3f38735bf211edb2066ac4e51b55cb2': '打开外部链接',
  '7ea26d0cb93e59339daf6a1ac68624f3': '绑定当前环境变量',
  '21efd88b67a39834582ad99aabb9dc60': '暂无数据',
  '35ba83e053cef95e55dfffde279822b5': '无数据提示',
  'e18d033cc4baab3ebb329f6b29eb3cef': '是否显示头部',
  '412593f58b9d062a43cbe2ce38f4dc65': '是否显示底部',
  'e494f1aa112068688ca863db7f39a0b5': '头部 CSS 类名',
  'd267231d2d8b60e267acc7d7d9955ae2': '底部 CSS 类名',
  'b2d2593bfb7a627817c0bd1ef6a254a8': '卡片 CSS 类名',
  '58e78d512d9ff40c73a263ab616cc652': '每行显示个数',
  '3cf0da9fe51f92842e0a6d375fa5c605': '不设置时，由卡片 CSS 类名决定',
  '953e91f3df59837ac2965cc04dec4b0d': '启用瀑布流',
  '39a6853b109ae98f1aabca90283d7edc': '配置成员渲染器',
  '6cb01692eea2fa7066d20fe2b6ccaea3': '假数据',
  '0c0180cb06a322199a67f10d4ec41cd5': '轮播图',
  '1007c97dbf952e032ce13be3cb811f23':
    '用来渲染轮播图，可以配置每一页的内容（不只是图片），可以配置过渡动画。',
  '0a2907a421b8f980986117e4f3044f92': '关联字段',
  'e760ec18028fc075c5705bf184589e70': '静态设置',
  'd314558953b3c76adb7e131aaec8bd86': '字段名',
  '793a763e73f1f742e3a16ddc2ed95ccb':
    '设置字段名，关联当前数据作用域中的数据。',
  'b9994cc749b4cfbbac0a9b140addd242': '轮播选项内容',
  'c6c7456d446d62a906c2809b6ba19ce1': '图片标题',
  'ab8a46ccf46acbf82d020d11468291b1': '图片标题类名',
  '098c3d959911b48b4d912cb85ccc4942': '图片描述',
  'c6fc4066471664a8602c636cfe1cc766': '图片描述类名',
  'cfb6f6e4c92a61ed0e0717abc8d0eec7': '<p>html 片段</p>',
  '97cc997910b99083bd23c6ac39294ff3': '自动轮播',
  '8c2a12c5dee794b8b9608bc1f8087947': '动画间隔',
  '6265104f900789dd51d75b449c3b9f89': '动画时长',
  '5bc37c57ee54d407f441b222f02391db': '动画效果',
  '8a0d716ded7baa7ee356ff39c3cf0bec': '控制按钮主题',
  'f7471313dce32bc3669d338764a0d036': '控制显示',
  '1c0fe943329339003e9e3c1b12a27fe3': '底部圆点',
  'e030190fd1c10b0c967f48e789fa86b1': '左右箭头',
  'c28479019e24e0e4745f4948e9e97ee7': '宽度',
  'ad5a36ee5f4054594c30b6dc481e2d81': '上个卡片',
  '49e0f68798e3c69b7990076bd8f1f47f': '下个卡片',
  'd09504750ebc1d75c38d86e16060f83d': '图表',
  '10e14f791d73c7c0f4113787f36f1626':
    '用来渲染图表，基于 echarts 图表库，理论上 echarts 所有图表类型都支持。',
  '67b6bec14c39be3f2602271e2fe3bcde': '更新数据',
  '0310aa5a05fe07147d8b7ef20616f659': '触发组件数据更新',
  '3d6abfdea70af544603da70f93ed3d24': '接口拉取',
  '03677b68a73eb59e5faf281f49f3c130':
    '接口可以返回配置，或者数据，建议返回数据可映射到 Echarts 配置中',
  '1396ebc166bd49c1e3b67549a1b4caa0': '初始是否拉取',
  '2af32ab13a9dece315cec2764d6aa7d4': '定时刷新间隔',
  'bc827aaffaa35a95607346cc104c0785': '设置后将自动定时刷新，最小3000, 单位 ms',
  '57c7d1125d2803b946a5b25c3f80f249': 'Echarts 配置',
  '3d3fa75d8b345b22a5fbd14a65a0af71':
    '支持数据映射，可将接口返回的数据填充进来',
  '43ddd80698198791d4a738bb20a2d7f4': '配置 DrillDown',
  'c8da43fe6ad1c537f86cecb353250145': '删除 DrillDown',
  '4fd9621d4facc101aba3afec055e14d1': '数据加工',
  'e8b90f43fc3cfb0a68e4392054de97e9':
    '\n              如果后端没有直接返回 Echart 配置，可以自己写一段函数来包装。\n              <p>签名：(config, echarts, data) => config</p>\n              <p>参数说明</p>\n              <ul>\n              <li><code>config</code> 原始数据</li>\n              <li><code>echarts</code> echarts 对象</li>\n              <li><code>data</code> 如果配置了数据接口，接口返回的数据通过此变量传入</li>\n              </ul>\n              <p>示例</p>\n              <pre>debugger; // 可以浏览器中断点调试\\n\\n// 查看原始数据\\nconsole.log(config)\\n\\n// 返回新的结果 \\nreturn {}</pre>\n              ',
  'd98ef182637b4d10e16e8073c1101e51': 'Chart 配置完全替换',
  'f1f13cb0ca4720a727cbfba4c82e5890':
    '默认为追加模式，新的配置会跟旧的配置合并，如果勾选将直接完全覆盖。',
  '40128a51e9667fe6a20a0454069368ba': '<p>内容 <code>${value|json}</code></p>',
  '7e3f6245e2a6adb903cf85c77cb1bbd7': '配置 DrillDown 详情',
  '38bbd995a790f5a67211e034b007c145': '代码高亮',
  '86e38e6425f722ba725718af2366ac08': '固定值',
  'e90e6ff080f179215c3566a61ca62367': '折叠器',
  '452b62e9b7e650fa163300da2893654a':
    '折叠器，可以将内容区展开或隐藏，保持页面的整洁',
  '81d2b9f20fb2083c75a5b052b84e897a': '展开标题',
  '7349194c139069b32889101768aa7428': '折叠器处于展开状态时的标题',
  '3a8647306ee6456517b7bf9c8bc7de23': '标题位置',
  'c949729cd1a1e425595c1a297649c7c6': '顶部',
  '12c4c5e8abda77e1fdc93653d6414187': '底部',
  '731f9b470e0948cbf56341a53c423213': '显示图标',
  '96c0cc844a06e0850c04f7c44b6475fb': '可折叠',
  '63b67eead04256e42ea9f6f7218731ee': '标题类名',
  '66d88b3e01aff17c7973181e53fc8c0c': '内容类名',
  'd09980a88568f75e9267ca7b531c74eb': '折叠面板',
  '0d571a7ab19e098820e8cea4d5a80f7d':
    '折叠面板，当信息量较大且分类较多时，可使用折叠面板进行分类收纳。',
  '17dcbf1f144607d4af0bb718e008682f': '标题1',
  'f7fb20f6cacd5e40c7f5732cb377d0bf': '这里是内容1',
  '72d41bd9eb3882f7da6f55d0ff0a39f6': '标题2',
  '0431ee7033364800e261d1e560808231': '图标位置',
  'a7eaff29603a9c40927f726013d2c016': '左边',
  '128d58f066a18ddb2ddb701921d5c77c': '右边',
  '47b4e22880eb59ce9989b8419222e88a': '手风琴模式',
  'bb3548f0bb97ab11ee92284ecf37ec16': '手风琴模式，只允许单个面板展开',
  'f24544b06700857ec11b434cb2916692': '面板管理',
  'c5ceab33d3e275262b4992a8cb43317f': '新增折叠器',
  'b839e579e920068bd817d34cd7927069': '请添加折叠器',
  '030a54b0afb54fc7f90e1a0f64eb8205': '默认展开此面板',
  '39b066b81835fd66bd4529d1220c9dd3': '标题{{@1}}',
  'a955021cdf0249de1f3818f83cf248b7':
    '用来展示表格的自定义显示列按钮，你可以配置不同的展示样式。',
  '24bdc7e8957abfc5d82f4206e92bb518': '按钮文字',
  '787366b0d678071a5ed98e55d31eba84': '按钮提示',
  '6a0508144ae12bfa79001693d713c0d6': '是否默认展开',
  'ae3816c5b00fcff4111842ac19f0a706': '按钮图标',
  'fe805d91ae93be775670c61214dd2f28': '按钮尺寸',
  'fbbbe30d78c335bad27f3dfc5efd2a5d': '显示列样式',
  'ac3880323853de9adc4f66bc06d438ff': '按钮样式',
  'ded228f9173b241dd8df2a4811ea0e98':
    '一个简单的容器，可以将多个渲染器放置在一起。',
  'a823cfa70cfa46c788e1eedae043f6e5': '容器标签',
  'f7d64e5e79994c3c8853f2608d7b2d25': 'HTML标签不合法，请重新输入',
  '473d2078518479669823205110842376': '自定义代码',
  '8b9c77fa4e646b9cb270f833252e511b': '通过内嵌代码来实现功能',
  'f8c5e0ac29e905e91146e967cfd39dc9':
    "\n      const button = document.createElement('button');\n      button.innerText = '点击修改姓名';\n      button.onclick = event => {\n        event.preventDefault();\n      };\n      dom.appendChild(button);",
  '0b13dc6251002bf556263fb3e4675b11': 'HTML 内容',
  'd6b917c76b92aa9b92b6bebdcab993f3': 'onMount 代码',
  'e64739dd24bb0bfcb6f6e1ee2cce5413': 'onUpdate 代码',
  '0601b7aa5b53cbc3616e24719bcd2aaa': 'onUnmount 代码',
  'a8065b6d2062bf061762b9200a88251a':
    "\n        const button = document.createElement('button');\n        button.innerText = '点击修改姓名ddd';\n        button.onclick = event => {\n          onChange('new name');\n          event.preventDefault();\n        };\n        dom.appendChild(button);",
  'b82231f254baf9a28bf752683d31b169': '自定义容器',
  'e5b5798a8bab7dc8a578431991731040': '通过自定义代码来实现容器组件',
  '96ec95de2d7da5b16465eb980f74deae': '<p>自定义容器区域</p>',
  '749f710d280419b1da031c9bc79b3b07': '自定义容器区',
  '356b1959a9da95997b4de31415d9d74e': '日期展示',
  '2bc6d101e5701a70f2fb9e0b67581594':
    '主要用来关联字段名做日期展示，支持各种格式如：X（时间戳），YYYY-MM-DD HH:mm:ss。',
  'a2344febfc246ddc7281f62217ba42c0': '日期数值',
  '84ff80a2dc4717cc01acd486040a6763': '显示日期格式',
  '6eea1b15be458a6999c9259aa2280a70': '请参考 moment 中的格式用法。',
  'a7032449ae8761aea61cc30e32d3be10': '数据日期格式',
  '4c1cff4d8c05daa6ed9352a241ee628c': '占位符',
  'b54e0f0a60f8e2c4c31f3b1ad7d5a613': '日期时间展示',
  '2a898869829eae8adcfca290fd34a67d': '日期时间数值',
  'b0d6f2d882adc2163e6a08a121d18677': '显示日期时间格式',
  'ab3aec075a09d055b2a28c8b61925ee0': '弹框',
  'e83a256e4f5bb4ff8b3d804b5473217a': '确认',
  '773ddc154f1e9b80f04e8bc9d83d2caf': '点击弹窗确认按钮时触发',
  '625fb26b4b3340f7872b411f401e754c': '取消',
  '08ab4ffcd1bddd249a193e6353bb52bb': '点击弹窗取消按钮时触发',
  '4708bcefff645287c8781a1de2a0f20b': '触发弹窗确认操作',
  'af17a4e37e5c6d68fff33c084192801b': '触发弹窗取消操作',
  '391b8fa9c747a1799353ab856e666ad5': '小',
  'aed1dfbc31703955e64806b799b67645': '中',
  'ab18e30c0dc4093ceeda8961fac3d1f4': '大',
  '949934d97c42801151673a51d3adc421': '超大',
  '8c8fbec263e20f087555c9abcb6dd07a': '展示关闭按钮',
  'f29ab26877ed22ffa59636d747d824b9': '可按 Esc 关闭',
  'dcba76890a534e1fe94421be2a17b484': '左下角展示报错消息',
  'af5876b89583552eef4c781718886dec': '左下角展示loading动画',
  'dd10fdec63a2224aa3d28b48d428cb98': '数据映射',
  '261bba7ad82914e477f4b37f6a83874e': 'CSS类名',
  '5e5d3f13111593b2710673006d4c8297': '外层',
  '7e1eb2c588aa1301f4aa19395ef0a177': '分隔线',
  'bc43ae8e61f1ad4be2b0a9e70501e490':
    '用来展示一个分割线，可用来做视觉上的隔离。',
  '33f1fc330b325469b43614c9d96f590e': '抽屉式弹框',
  '0c5a0448b07419ad2900c36867e8e4e0': '点击抽屉确认按钮时触发',
  '57f215184a2fb83541f7cfa22d039feb': '点击抽屉取消按钮时触发',
  '97b6bad87c4320faac2f6a5cf556c26c': '触发抽屉确认操作',
  '909ba2872b2d670ec0ecbcacc4c8c833': '触发抽屉取消操作',
  'd4d2a66820d30e07b44c850eb3f116c0': '位置',
  '6d7f8b31caaf80deb65eb2c8bdd41cd7': '超小',
  'bde8a41fc64bfe78d0c4951ac6f93718': '显示蒙层',
  '4a757588f5aee8cd039b1d166b096d1a': '点击外部关闭',
  '874cf31274d782914c7833cc39836c4e': '可拖拽抽屉大小',
  'd3e329f73ae4b58d95cc7769eeca8370': '位置为 为 左 或 右 时生效',
  '45ce37c24c6e7252d98c6d450e3ca4ad': '标题区域',
  '660553eee939d2bd8ea68172fa7216df': '页脚区域',
  'cc70a816b7d61e7212d57335c0a15af5': '下拉按钮',
  '74bafe23b0be1a14aa218b396cb33bd0':
    '下拉按钮，更多的按钮通过点击后展示开来。',
  '213a4e49568569943403ff31fff31ee5': '配置下拉按钮集合',
  'b5b57c711fd2d2309cc390a8b44d2b69': '按钮文案',
  '314454bbee226e4b32b612afdd8e9442': '鼠标经过',
  '9951d740257c40978c238a683b1d4a80': '点击内容关闭',
  '205cb6cc6c8d37f3bed62d9c8bfae976': '默认展开',
  '697eb55e1c6cecf43e63a26232dda5b2': '选择后下拉菜单会默认展开',
  'b2aa282e908597d1d700c1f4de17b8aa': '菜单对齐方式',
  'e57996d3d771141f1b3080bbd8ad605b': '选择后按钮占满父容器宽度',
  '1ce673c48f29162208e75bc210307bfc': '展示样式',
  'c9e265ec462b61887af6f58928923925': '隐藏下拉图标',
  '938ac86e738246ccd0ca0224031f96af': '下拉菜单',
  'ff9f6c2d74c413daa3cd6fb12f8dfd3e': '循环 Each',
  'f34111ff3694a6c6de6e31bef8ebadcb':
    '功能渲染器，可以基于现有变量循环输出渲染器。',
  '874268022baac239b06c40600d3ce080':
    '<%= data.index + 1 %>. 内容：<%= data.item %>',
  '69bdc66bb88ac5b63053e2bb7db41801': '循环',
  'b7c16dedc4291d333fba7628ec9eb073':
    '如果所在容器有下发 value 则不需要配置，如果没有请配置变量名，支持多层级如：a.b，表示关联a对象下的b属性。目标变量可以是数组，也可以是对象。',
  '4726ff4e62d3fcfa4b090aaefc393229': '暂无内容',
  '5d5f9d49fcb2109f94a43590ef796ca7':
    '当没有关联变量，或者目标变量不是数组或者对象时显示此占位信息',
  'f549581bf93c72ed69c37e906e04191d': 'Flex 布局',
  '6ab8332f3da284b137d54f6ba901e93c': 'flex 布局',
  'ef5abdfc944546ddcbe10e9884cf5832': '第一列',
  '874fdb7b3a5730910a4de1c58220c337': '第二列',
  '3a76596e73fa265257ce90b7bed684c7': '第三列',
  '5aefca559c5a41d10078e21e6d616825': '布局',
  '2fdc3722b88a2ba5077e0d11156ede6a': '子节点水平分布方式',
  '4ba6c2256050d805ae5cd1e0e84737cf': '起始端对齐',
  '56c17ba6a56c01706ae00a31611deb03': '居中对齐',
  'abeb360ab1e66534a041fb8b44e1a00e': '末尾端对齐',
  'd5a3cb1cc31a0469b011abdbd3e947f7': '均匀分布（首尾留空）',
  '85530444a72a840ee657e2df99970136': '均匀分布（首尾对齐）',
  '9aad08fbd356fb4279f0efa81b3d016e': '均匀分布（元素等间距）',
  'ae558cbf4c35d381b6542f517f2e8dff': '均匀分布（自动拉伸）',
  '70d39e84bc1ecefaf6e19cf78c9574fe': '子节点垂直方向位置',
  'ed97c73866617b40a7b1215867e0f489': '基线对齐',
  '7ac1519928de413cfe36f5d2e0610430': '自动拉伸',
  '8e15f51c9512fdbf4287794d6642a90b': '子节点管理',
  'bc78248b34b7bf18691e6d385e0f544b': '子节点内容',
  '023c4bfc60545a2668c2d5111171b5d8': '外层CSS类名',
  'a4611da51ffee9140976d01668e45d45': '子节点集合',
  '729a4cca5ed3504793c1f3a87d2b48b9': '按钮点选',
  '29513434492e5d19a9660e0a918befd1':
    '用来展示多个按钮，视觉上会作为一个整体呈现，同时可以作为表单项选项选择器来用。',
  '6edda84461bf13d38328cb401c8c23db': '选项1',
  '39692081e75ef73c6479fc25f8f10dfc': '选项2',
  'a457872a51628ccadfb9bcfa23428a98': '按钮点选可以当选项用。',
  '755955372bcc0c7ebf161a656bc389b3': '值变化',
  '2fc76872efce1eabd3b74a3e4fd5b976': '选中值变化时触发',
  '528609917710d813a55e5a6cecf1e458': '选中值',
  '288f0c404c4e546aa3683ff5054e85e2': '清空',
  'c374a44713fa5ff5da2565beb398c7d3': '清除选中值',
  '8db57ba01ffb9bf29c1656de5f0208f5': '将值重置为初始值',
  '8f40db3b25528063f1b6a59602a05cd5': '赋值',
  '944908c981a86bfa0cfab9360ab38184': '使按钮宽度占满父容器，各按钮宽度自适应',
  '0b98b0bea3db6ae5b67a09c7bb2d032b': '按钮选中样式',
  '55b45c73ae417c4dead67905b1550e85': '表单项',
  '3b49c8cece3f495f0e76b73823c68bfa': '按钮集合',
  'fd179c2844536ce198290441c38c814e':
    '<span class=label label-default><% if (data.type === button-group) { %> 按钮组 <% } else { %><%= data.label %><% if (data.icon) { %><i class=<%= data.icon %>/><% }%><% } %></span>',
  'b6872877e1eb5ddedd904c170db26024': '链式下拉框',
  'fdf1664c0790d25f236bd596aef1acef':
    '通过<code>source</code>拉取选项，只要有返回结果，就可以无限级别增加',
  '556988a9dc1816dd979e96eb5cd19a85': '链式下拉',
  '225f3ed00750ae78ad1e6ea42c8f5087': '默认值',
  'da3ca8191fb919fb34e8e78fc6f2fc78': '请填入选项 Options 中 value 值',
  'bc8d09093edd98769d5cb39e759aa498': '拼接值',
  '2646ee1ebb6922a5c9359de6cd3b3639':
    '开启后将选中的选项 value 的值用连接符拼接起来，作为当前表单项的值',
  '1395eba8d9efe27aa1ecd1a45e3e5dcd': '获取选项接口',
  '1495c26f33625552e4845630b6b7b44a':
    '<div>可用变量说明</div><ul>\n                      <li><code>value</code>当前值</li>\n                      <li><code>level</code>拉取级别，从 <code>1</code>开始。</li>\n                      <li><code>parentId</code>上一层选中的 <code>value</code> 值</li>\n                      <li><code>parent</code>上一层选中选项，包含 <code>label</code> 和 <code>value</code> 的值。</li>\n                  </ul>',
  '4ea50507bf8b9ceb908677f30fb20e68': '选项标签字段',
  'fe4c9c2eed1ad213040d84036c675454':
    '默认渲染选项组，会获取每一项中的label变量作为展示文本',
  'be43687d4ed1d9e663c729e12618166d': '选项值字段',
  'f1e6b60c4b6df555a6b03f91033091f4':
    '默认渲染选项组，会获取每一项中的value变量作为表单项值',
  'f411d0f1f925d9b48d8c1d451bd809b1': '说明',
  '2ef0fb6299da5954f5ea84a088684ee5': '选项说明',
  '454e60f5759903d7d3dba58e3f9bd590': '勾选框',
  '81c8c07d13f425215010783bbf3bf06e': '勾选表单',
  'c75fde0e2d329ce62f55cb1a207181ae': '选中状态变化时触发',
  'ddd6650e02f5a266c3df2703daf37270': '选中状态',
  'db0258df1ddbd88749b335aecdc8425e': '值格式',
  '53235c46364db079356d57da5870f967': '勾选值',
  '56f3150f1713a5e5c6e7c55fb0b79b75': '未勾选值',
  '7c7a88eb1bb4b40206c6c680bd8995a8': '默认勾选',
  'db98f889ce6bc235e66bd4b2a788d137': '复选框',
  '6d1383a2806f0516aac2c115f96799eb':
    '通过<code>options</code>配置多个勾选框，也可以通过<code>source</code>拉取选项',
  '05f87b331e1c97691776d93a6598373f': '选项A',
  'f38c0a46797523b11051e35ec0f82a42': '选项B',
  '9c541222ced2435288c24b34f8ad1fb8': '可全选',
  '05bef457e8350e1a5d8007cad41b70e5': '默认全选',
  '84f31b24ffc8ea2b81d61a6f054b5bb6': '代码编辑器',
  '08346c5bb99d8e3bfa406995b8c4f067':
    '代码编辑器，采用 monaco-editor 支持：{{@1}}等等',
  'ab0710b367acefa1d6a78e2338291e86': '获取焦点',
  '4638e799b95e1b71edd55f278a6f707c': '输入框获取焦点时触发',
  'efa3ddc1e26ed5d8cf7d9855cc7bd42e': '当前代码',
  'fc96a5f1b79cb734afe08e401b6ba5e7': '失去焦点',
  'c776ab86eb24f6b3db35114e43026f75': '输入框失去焦点时触发',
  '829c57d7064cad467ec466ae26244ebb': '输入框获取焦点',
  '295bb704f5205801d57d672780b94117': '语言',
  '44fe0e1bcabcea83d6a30e6af0fd42af': '可全屏',
  'b3b97a293baac13db6367aba5539a09c': '控件尺寸',
  '18c63459a2c069022c7790430f761214': '默认',
  '3386da5f56fac758ed0f18e024ecb943': '特大',
  '254bb8aa6b92d588d957a383df24db1e': '组合输入',
  '4db731c7d73988e40a01b4bf1a7f00d7':
    '多个表单项的组合，可配置是否增加和删除初始设定的模板',
  'b58c7549c0246c55b9cac96383200338': '添加',
  '8575b828c7320de82b9f99058aa1f55f': '添加组合项时触发',
  'aacd80d2c978abae7b955510a938788c': '当前组合项的值',
  '4933bd64bb23de03ca8ed246fa5509c5': '删除组合项',
  '0410242a74850f010a9a8061bc0cd891': '删除项的索引',
  '74333901ae9ad27a93487dc850e45609': '现有组合项的值',
  '95e09290c4e0f01323bb5abf301c950b': '切换tab',
  'ba2bd765f6c2e2b717139c5453884e14':
    '当设置 tabsMode 为 true 时，切换选项卡时触发',
  'b04c6cf9cb1212b9c173ddfeec129028': '选项卡索引',
  '055f2f284d2bdb15bd9e542ea9f93285': '固定成员类型',
  'e3b9236d585eb9c93a074f264737cb65': '多分支',
  '66e867eb73a118649800c0a064d0b5aa': '类型名称',
  '5aa528690fd771f89683a7f00868f39e': '分支管理',
  '35b65a5a3f8d721e12cae310463d69d6': '命中条件',
  '85485d70be6b380294428018e54fc9b9': '根据成员数据判断是否使用此分支',
  '50bfed6ada3e7d0ef4d20eb727b3d7df': '配置子表单项',
  'd68162ea1904f627b033fe3953da295d': '配置子表单集合',
  'aab09f676645f2651655a711d5e3327c': '新增初始值',
  'dda5ffc9ecbad13d424346890bacca6a': '是否可切换类型',
  '8a0c77e91392d70df522b55eb4d403e2': '多行模式',
  '26343b8bcb694fa5e333b47d8b663d1c': '即是否要换行',
  '86032735a191d117b02111e447494380': '是否将值打平',
  '28a59fee1a4714493a8d6cec05c1417b':
    '默认数组内的数据结构为对象，如果只有一个表单项，可以配置将值打平，那么数组内放置的就是那个表单项的值',
  '7f2579cabd4d654458a720eed517a37d': '是否可新增',
  'ea1e5695bf682ea3b31aba0c35198ae3': '新增按钮文字',
  '1ab31449faaaeeeb7200d724eab9a7ab': '是否可删除',
  'fa3e9f9e49f5a81c998f949155f86b50': '删除前的请求',
  'f962922d46ef18e68127642e5c00165a': '删除确认提示',
  'fc763fd5ddf637fe4ba1ac59e10b8d3a': '确认要删除',
  'f41a714bc8b26dc27a93a07c44e329a8': '拖拽排序的提示文字',
  '91396e9bc25c9e8b63907fe22408e2bb':
    '可通过拖动每行中的【交换】按钮进行顺序调整',
  '8ee004bdd55b578acdb10b1bcd3fa4f7': '去掉边框',
  'e9cbda74a1ffc06228fca68e4d16c4dd': '限制最小数量',
  '3baaa61e619e32fc36eff14839b1a63f': '限制最大数量',
  'b8ccbc166c72b2eb54aac1332c99fb49': '有子表单项限制失败时提示',
  'b34cbe877b2c8464c625858fcf19f4eb': '最小长度验证失败时提示',
  'ea2474ff679195d9b54bd5ff3384fdfd': '最大长度验证失败时提示',
  '1c5b9cb245f04413a2d888bd59442097': '是否自动填充父级同名变量',
  'e673084b4261d10104d27ae9e4d014a4': '采用 Tabs 展示方式',
  '6e7bd650f763085de3bddd51a8d6aa88': 'Tabs 的展示模式',
  'fd6e80f1e0199d6ecc3ee81ae04aa9ef': '正常',
  '2dd25b8c21efbfee4a198787810d65d8': '内联',
  'fc6c9fa3af230165c39fb314c086be22': '选项卡标题的生成模板',
  '9ff4713f6b17e96e9cd76650fd5892be': '懒加载',
  '93dc24d4ad01981d5994ba7f8ffaf345':
    '如果数据比较多，比较卡顿时，可开启此配置项',
  '98d52b737d6bc171b6d5bad9a42f6e23': '严格模式',
  'cb5ca128b6c78a90f26673e21b0b3f28':
    '如果你希望环境变量的值实时透传到 Combo 中，请关闭此选项。',
  'bc91f4844843d6c8ec1acb78a1f1aba4': '配置同步字段',
  '07bf6c08bb5f0bdb32dbfecc7d3fe290':
    '如果 Combo 层级比较深，底层的获取外层的数据可能不同步。但是给 combo 配置这个属性就能同步下来。',
  '3f2176cdae8a4ed6a4c4eaff002a3b24': '允许为空',
  'ead7156521ca11acb7ca7bdf4c9c87f6':
    '如果子表单项里面配置验证器，且又是单条模式。可以允许用户选择清空（不填）。',
  '7ab968b8219f6c348478da255ebcbcb9': '各列 CSS 配置',
  '6e87bfe16db746db13966f0d7552b052': '条件组件',
  'ab7f2096d3ea8aa85f453b204bfbc493':
    '用于设置复杂组合条件，支持添加条件，添加分组，设置组合方式，拖拽排序等功能。',
  'ae5e21c7b57aaaff2071b757f35dbb3e':
    '适合让用户自己拼查询条件，然后后端根据数据生成 query where',
  '55d4790c5d819cd0462cbe89561b0dd4': '数字',
  '97b0b6499334ed889b372513290a2a52': '布尔',
  '0c3bf4fce50589b1073baf15f8a00d36': '日期时间',
  '6d6b4f2bbd2fd0b5faee33673d7f06ae': '快速开始-条件组合',
  '22ed9ec0e00b5deab2c335ad1fd2e343': '条件类型',
  'e996419dedc2f1ffd96e7a0e808127d0': '字段名称',
  'c322edb884724d04842fc35f4d29a24e': '最小值',
  '5da893141114a59da868052b3a17a79a': '最大值',
  'd26404c10871481ab6bbb4837a34ae95': '步长',
  'dbd5b99c34260412f10835d82b59919c': '日期显示格式',
  '94575fbef23460cb02524d20d152d728': '时间显示格式',
  'f20a86701d87369e5166c28a71b8b8cd': '字段选项远程拉取，支持接口或数据映射',
  'ed4ad0f9e6374d6915ce3d2c0dec7c2c': '操作符',
  '61260d9386fd95a268dfc93d977c2706': '表单项容器',
  'c5739a29e7c403fc212608cefe70cf29': '元素集合',
  'a00f44e570f896de5490cba9d2462951': '插入一个新的元素',
  'a9a8efb2541ee6f89ea7b83e610ebf7f': '新增元素',
  'bac53d3c739f7fb3327704efd5b40eee': 'Diff编辑器',
  '7b4fe9415d80d3694d2f630411ac7e9c':
    '左右两边的代码做对比，支持的语言包括：{{@1}}等等',
  '0517b74cbe247a903faf40991a5a335f': 'diff编辑器',
  '42f04184315801c372989820106cc4ee': '右侧输入框获取焦点时触发',
  '58ec44a580ba7cdcdf65985f5c44940c': '右侧输入框失去焦点时触发',
  'b42cb49fc7c4ec140697e6d78c39e585': '获取焦点，焦点落在右侧编辑面板',
  '2a69150aa382f6a309c03a96145d4266': '左侧默认值',
  '04b8c503707c34f9c275d349275787f2': '支持使用 ${xxx} 来获取变量',
  'a71e655ab56c9962742f72623f67ca76': '右侧默认值',
  '460bc46ffeb31b737669e2312c5bae72': '字段集',
  'a89cd8150a1dbc60ac7063580e0852e2': '多个表单项的组合，可配置是否折叠',
  'a13a2fa224ca5b6f44d5aee33ec29d58': '文本1',
  '37dd6f28ffb87d8907a286e0ef4dc7fe': '文本2',
  'd6c40a2ee219c010edbcdaa2eeb94ddd': '子表单项',
  'ec2a8ec81d1d2588db8c7827ba99e7d3': '是否可折叠',
  'f315bd4984fd09c30581674d28287f12': '默认是否折叠',
  'ab2d2b13794ae1e2d7bf3bcd5af55dce': '控件样式',
  '23ecf42cada8bf2715792d718544d107': '极小',
  'e5a226534fb99ab260865b936d3c85ba': '内容区域 CSS 类名',
  '12b54e3fdccdbb5588785fce5534edbd': '请从左侧组件面板中点击添加子表单项',
  '1297c46c0ea697a0041c3899b15d69c6': '添加子表单项',
  'cd948961f71f87ecc72b251147d96144': '控件类型',
  '0766a6467bed7f2840871a5f0638669d': '单行文本框',
  '15d169d28cd48c97fe751e4cc92ca926': '多行文本',
  '829abe5a8dcd0579a340d20636b59276': '分组',
  '9597dcaf432ceba92a160d61cb1ef65f': '数字输入',
  '9913107b19cb6012250134ff91377430': '单选框',
  '006ded9fa277cf030592021f595a07d5': '下拉框',
  'a6beb974cc0b50eebd18120b8110a88b': '开关',
  '481e034e6026969aae4ce7ce7c8a7b6f': '文件上传',
  '6bfb9bb2218ff32b6139e98bc93707c0': '图片上传',
  '24b6d4c0892a8f3ee2a982e3ab0afe38': '富文本编辑器',
  'fdf6f7f6497556de440fe434b721ee99': '显示名称',
  '712538d3e674792ec94feb9a5eb2cc0a': '提交字段名',
  'eee1e2258d7ea163fec625ee44be9637': '表单',
  '10b3d542748da2043f79395bfa2ab35f':
    '可用于新建、编辑或者展示数据，配置初始化接口可从远端加载数据，配置提交接口可将数据发送远端。另外也可以将数据提交给其他组件，与其他组件通信。',
  '5ac57ce6df8c2a19668b7429aebd9f33': '文本框',
  '1b6f9adf1e6a41a18b9a76407d945618': '快速创建表单',
  '380c80efc8d38510d31f50578690b781': '提交地址',
  'ffc2c1671eb7e3f6751006cd7a9961f4': '文字与输入框展示模式',
  '81f8a50d084992815ab844109b3ea27f': '上下',
  '3720b9ef8053b7b8a54c7d8ace051912': '左右摆放',
  '9d68944682609cb230534195ca7c62ae': '表单控件',
  'cf993a1d9c0352215055d180aca60b97': '分组内的控件',
  '3673ed1983c1be059126e3715fc34922': '表单集合',
  '741e41f5247b1541bde22c00e65f4401': '初始化完成',
  'f75e31cd2e3bfb79be420b0e61a533cc': '远程初始化接口请求成功时触发',
  '211f02318e3cceff5ee50d4949a5c8ed': 'initApi 远程请求返回的初始化数据',
  'b2a5322c8dbc0d8343315cafbd39b7ce': '数值变化',
  'd6fc04abf4889a864bea240d6b67963a': '表单值变化时触发',
  '66f1115691b0a3e434dd3e8a6e733730': '当前表单数据',
  '7233c9cadee5096097673c9590eae9b8': '表单项校验成功',
  '6611594c527756e23d4044f908fedfa9': '表单项校验成功后触发',
  'e00a32d415785d5a5d43a593d26cbaa0': '表单项校验失败',
  'd4c12cea41e1595329358edb365c5f5b': '表单项校验失败后触发',
  '368f9bab722b255f1fdb669a89f0c594': '表单校验成功',
  '35e69ab84129d721229bc5b48afdedd2': '表单校验成功后触发',
  '641fc404690a43cb13e4666ce272974f': '表单校验失败',
  '5d7dfa5bf9bcd278f06fa37e482a2c35': '表单校验失败后触发',
  '23b62e9cbc868e024b80d2e3fad80ac7': '提交成功',
  '8c9d9d2594b9cc39294c6939bd08a5d5': '表单提交请求成功后触发',
  '18344d8a27aa678e401d5e575d4efe99': '提交成功后返回的数据',
  'f5d008dea7d2e953195a5588dea3c8e4': '提交失败',
  '86555672b37841b639311e7d49f0f593': '表单提交请求失败后触发',
  '17aa713bc661f98e585ec3725d6d4f0d': '提交失败后返回的错误信息',
  '4e34003861eee3de1e0c9c1222249bbb': '提交表单',
  'a360c5d4e723ad78a5e52eb1f5f3f2a2': '触发表单提交',
  '1b6f5cc49e71c90a5b85a796285e3135': '重置表单',
  '35de8c264c2a87836ccbf302b4ee673f': '触发表单重置',
  '4a3deab45c0a7218b8ae58a33fd24c28': '清空表单',
  'c18255cd6a048da86045c59a65fdc92d': '触发表单清空',
  '17f2bf425eeb7d20d79c595344e9dc94': '校验表单',
  '27f35bc7086bf54e27e254f5d77c3168': '触发表单校验',
  '60ad7d0d170b973ab9cdb0b23e636704': '提交按钮名称',
  'a834505b13627a2e9eb4e0a0c2746e5c': '当没有自定义按钮时有效。',
  'cbef9ad64297efd7657d5b67b2112751': '自动聚焦',
  'ded8caee55c05aa0f1a4e6a197c480bd':
    '设置后将让表单的第一个可输入的表单项获得焦点',
  'c8801f299681b3080968effcb13a57fe': '禁用回车提交表单',
  '4fe8162504ae5fb955c30863d88834fa':
    '设置后无法通过键盘 “回车” 按键进行表单提交',
  '6232c762a93aeb3c89cc759c06802610': '提交完后重置表单',
  '7d61784cd115d333f01a3c0c46408a1c':
    '即表单提交完后，让所有表单项的值还原成初始值',
  '49086a13c74f262de398e60f448ab056': '初始化后提交一次',
  '1babd035cabfeb997ac3eee3f938d794': '开启后，表单初始完成便会触发一次提交。',
  '0d1fb15904862f5fd2d81d2fc8f371d6': '提交后是否关闭对话框',
  'e91209a4a78c0b34c26b681b49e0681a': '提交给其他组件',
  '4133d2c3613ece9792c90d185ec32306':
    '可以通过设置此属性，把当前表单的值提交给目标组件，而不是自己来通过接口保存，请填写目标组件的 <code>name</code> 属性，多个组件请用逗号隔开。当 <code>target</code> 为 <code>window</code> 时，则把表单数据附属到地址栏。',
  '7653297de32f34fdec0dd0653aebf358': '跳转',
  '6d00e21637c382cbd4d949b7735a2e41':
    '当设置此值后，表单提交完后跳转到目标地址。',
  '672b63d7523095b949f5fad233fa0cde': '是否开启本地缓存',
  '8a0544ca2f7ddaf8768ab599858315a6':
    '开启后，表单的数据会缓存在浏览器中，切换页面或关闭弹框不会清空当前表单内的数据',
  'ce2457fe99197e0fe81b6fb08e3eaf0e': '提交成功后清空本地缓存',
  'bc01ee1a28f980c298679610fe4d2d66':
    '开启本地缓存并开启本配置项后，表单提交成功后，会自动清除浏览器中当前表单的缓存数据',
  'f5e55fb88f5adc71beb0b1fff60face6': '表单组合校验',
  '8101a0aec7eba32e633e3cc29f4b7ede': '校验规则',
  '80ce5ea9ac2c3001e6e8ea3175ecc12d': '报错提示',
  'ff7cc75cc43c25c823d05d87cb8190b0': '保存接口',
  'd325cfafec323a62463af3d3da9b6ede': '用来保存表单数据',
  '057a9c2b2027a6b443741d8a0c04e4be':
    '{\n    "status": 0,\n    "msg": "",\n\n    // 可以不返回，如果返回了数据将被 merge 进来。\n    data: {}\n  }',
  'dd1b9892e274b16540aeda961437870d': '采用异步方式?',
  'e638bd32b4342dfc7a15990a79f588ae': '什么是异步方式？',
  '6df230c8f18709575777172f0a9a588c':
    '异步方式主要用来解决请求超时问题，启用异步方式后，程序会在请求完后，定时轮询请求额外的接口用来咨询操作是否完成。所以接口可以快速的返回，而不需要等待流程真正完成。',
  'daf3aec137ac74941adb1b1d3e3dd1d3': '异步检测接口',
  '0705e4aba9f22ae70d906e2201a4a68d':
    '设置此属性后，表单提交发送保存接口后，还会继续轮训请求该接口，直到返回 finished 属性为 true 才 结束',
  'b4bc91701b86fe8543d649e97daea602': '初始化接口',
  '258c437ef67e5ef12f3a766ff1802f85': '用来初始化表单数据',
  'e8c46074d8432532cac25eba56bca354': '采用异步方式？',
  'd2af24c0f76cf325f1c8fa939576c379':
    '设置此属性后，表单请求 initApi 后，还会继续轮训请求该接口，直到返回 finished 属性为 true 才 结束',
  'ab94e2c30b3cf0fd11eea477f70dcbaf': '初始化失败',
  'b64292a1903bd969d0e3a558c334f5bf': '默认消息信息',
  '8a5e590f69e1ae52d86396410ceeee5a':
    '可以不设置，接口返回的 msg 字段，优先级更高',
  '3b108349b93f7c8c4e2346f8d48c092a': '保存成功',
  '6de920b4e4e08b261cda928d9beefab4': '保存失败',
  'b66ef8966dad62d377bc5310d8b88e7f': '保存成功提示',
  'cf538bbe1fb431f9e2668da4d84cfadf': '保存失败提示',
  '6509e435d66db2a105b2444b1d3d0db1': '验证失败提示',
  '02e977ba56a7ccc59c277d2d5be43ba0': '用 Panel 包裹',
  '1cb01363e2463443bc8105f544ce2736':
    '关闭后，表单只会展示表单项，标题和操作栏将不会显示。',
  '1fdadb49bcabfdc36a4a9e020f597f4d': '展示模式',
  '57d348e83d929dd4cb3bab04debc93a5': 'Panel 的 CSS 类名',
  '9b26fa2b5943c827eea294e79b1427fa': '可以设置 Panel--info 之类的',
  '85ca671c1bb6de5a15456a9692d2edf4': '开启调试',
  'f24b7483069b44490a6379be5b90f4e3': '显示当前表单的数据在表单顶部',
  'eb332076d766c2e817285f0a6d4860b9': '公式',
  '000cf7353ce03857c671e89c91b35464':
    '通过公式计算指定的变量值，并将其结果作用到指定的变量中',
  '9da7578f9329ccaee1bbdf6a766744fd': '计算公式',
  '77562ec3db28683ec71612a11e6b39ef':
    '公式计算结果会作用到此字段名对应的变量中。',
  'c80c42a0be1b39ed899b1f5560875cf8':
    '支持 JS 表达式，如： <code>data.var_a + 2</code>，即当表单项 <code>var_a</code> 变化的时候，会自动给当前表单项设置为 <code>var_a + 2</code> 的值。若设置为字符串，则需要加引号',
  '89a8549c2ed7fc23a683599731d92b22': '作用条件',
  '3df193f5d4f652a4bac331a69761041b': '是否初始应用',
  '86cebf86c66c6a4e6731b840c3967ab0':
    '是否初始化的时候运行公式结果，并设置到目标变量上。',
  'aaff9b44fa3c84058d6bec9888b7b39f': '是否自动应用',
  '677568b4f6505bdd63387137bfcbf35a':
    '是否自动计算公式结果，有变化时自动设置到目标变量上。<br />关闭后，通过按钮也能触发运算。',
  '222c7bc60f06552b019d71bce57013ed': '功能组件（公式）',
  'bb09a378529edac439abc2edf05c10d7': '表单组',
  '39d36f049a794eb8df305b5ca7710c36': '水平展示多个表单项',
  '32f6f7f8164c7f78e4b46eb50c158ab9': '子表单',
  '400fbff5e744d3b7317dd130eaad723e': '请从左侧组件面板中点击添加表单项',
  'bd1aded1c983ab7fcf3990f0dc31047c': '列宽度配置',
  'a170a375b264f7fe0c02a7ca8c268784': '宽度设置',
  'daa0f354e189c0da577ea25be13f874d': '适配宽度',
  '4db804afe5c99f7ca4fe988ada35c77f': '适配内容',
  'f1d4ff50f3828f9b73412e7d94e6dd6e': '自定义',
  'd5d885add2551454955bd70411769c88': '宽度占比',
  'ff7e66f1feaaed3260b6e8ef432efc79': '间隔大小',
  '1b3408880b73544a3fad501dafbb71e6': '列 CSS 类名配置',
  '551481accddd97e18d7152f511fb8987': '变成多行',
  'd27d7b1542d91641d1d232c9f58b96d1': '隐藏域',
  'd50b9a7291d45d1da8633439427afaef': '隐藏表单项',
  '4f6595b5030e171fcead42d321ba767f': '功能组件（隐藏字段）',
  'b6946d13d670fc49b721b626bca426b7': '数组输入框',
  '6e43f86d0b2973c34078bc7c7bc02eb0':
    'Array 数组输入框，可自定义成员输入形式。其实是 Combo 的 flat 值打平的一种用法，可直接用 combo 代替。',
  '02cc4f8f5a9aefbc03c778f7a5c989c7': '请输入',
  'a4b72cd5d7c953c57b00e3597b9ef666': '数组框',
  '89e34c77922f9362fc8f4033e7f6bdf4': '启用拖拽排序',
  '1362211a6bfc8cc4130d54643e8e9732': '可拖拽排序提示文字',
  'ba6a6f2cdb46e8f80fbfd4fed20eafac': '城市选择',
  '20700607ccdb7b6c83f3b5bc525e6975': '可配置是否选择区域或者城市',
  'ee167d4c74e600248aefe9d0ba474705': '选中值变化',
  '770fe9e7899cd310b035ef50a39ab2ae': '重置为默认值',
  '9633e6be5ecb44fbd69b3e8328cc8430': '允许选择区域',
  'eec86dd33ae6186937da563fcde3555e': '允许选择城市',
  '95892a76bc26c2db31087c9914e442b4': '是否出搜索框',
  'd1b5ad85ada6350ea1a1432b813700be': '颜色框',
  '5a9e72d006165ae3dacdbd96f931f70e':
    '支持<code>hex、hls、rgb、rgba</code>格式，默认为<code>hex</code>格式',
  '6b36c6f7ec834692ec6c8e3816349fdd': '颜色',
  '277daf93adca7889605057f566b584bf': '输入框内容变化',
  '40fa260f07ed5a14f9516c0c37fbfd4e': '输入框失去焦点',
  '3086da6514671fb8950171bf3af4ab2d': '清空输入框内容',
  'a47b08f4c83158a058c06e176cff501a': '拾色器',
  '1b25b3b1b5076f0c2e6bd12d73c56f79': '隐藏调色盘',
  '9b161db0e2e749c1106c702c8097d380':
    '开启时，禁止手动输入颜色，只能从备选颜色中选择',
  '05a64e0df1490a5db391e7a43eced6e0': '备选色',
  '6cbabc9a4cc07c1e26bb131c02833f8d': '拾色器底部的备选颜色',
  '79d7c8cd739a1f94c7bb30c74323eaa7': 'X(时间戳)',
  '84c7af622906c4e9d62bbf905d6ee517': 'x(毫秒时间戳)',
  'aa2fb1b6cffd7c9783c11047adcdbae4': '请选择日期',
  '8935dbb864f032bacc618b0938887ad7': 'YYYY年MM月DD日',
  'ff91b28a49de6fd998dea31598198a73': '请选择日期以及时间',
  '3e253325914594e1dc483c0b64ea7643': 'YYYY年MM月DD日 HH时mm分ss秒',
  'fbd05a89ca037ca9e5a6af4c2206d488': '请选择时间',
  '90696835bfa70d38b02ef5533d33c88d': 'HH时mm分',
  '6154f88c0ac259ace41b0a5be45d753c': 'HH时mm分ss秒',
  '904393383755b88e61f5c242eafabdda': '请选择月份',
  '55546b74d8819d49cccda6277f3a3282': '请选择季度',
  '13ef7828361c8aebbf6ebe09370f5e41': '请选择年',
  '4be42a8a2cbb9088b4d051cfd824d68c':
    '支持 <code>now、+1day、-2weeks、+1hours、+2years</code>这种相对值用法，同时支持变量如<code>\\${start_date}</code>',
  'be28cd64f978dd70c1cd345ebe751bca':
    '日期框、input-datetime、日期时间框、input-time、时间框、input-month、月份框、input-quarter、季度框、input-year、年框',
  '52ea2070560eb55083b64f5ba748697d':
    '年月日选择，支持相对值设定，如<code>+2days</code>两天后',
  'e54ca3223a36c6489de0fb8a39d4049f': '日期配置',
  '96f9d9fc9cef8b18e3cd1cf9077147d1': '时间值变化时触发',
  '0a72b3858efffaa1bab685fa840b701b': '时间值',
  'f6db3514c72bdc34922f137a8a92b997': '输入框获取焦点(非内嵌模式)时触发',
  '0f5fc3b84cf9c24ff3acae45ae22fb57': '输入框失去焦点(非内嵌模式)时触发',
  'ecd1a3cadcf1d55250afafbbde767250': '显示格式',
  '182503224cfaf1f63c566f13da56a2a4':
    '请参考 <a href=https://momentjs.com/ target=_blank>moment</a> 中的格式用法。',
  'bf8f46b5c3b309f666965a74d57ac093':
    '支持 <code>now、+1day、-2weeks、+1hours、+2years</code>等这种相对值用法',
  'f0789e79d48f135e5d870753f7a85d05': '模式',
  'a553741d5ebb9c80d7d2a63b202cf4b8': '浮层',
  'c6e1b91d329a61b691d0d5d2eb343ddd': '内嵌',
  'a2847d82fc998cbe25447b14d113234b': '请选择日期范围',
  '3f9c3a9eb55b7116bcaa6b614ecb38be': '请选择日期时间范围',
  'ddc4a982defd88cd164c61da914819e1': '请选择时间范围',
  'c899221db27c8b3606ce7c807f0765f2': '请选择月份范围',
  'c09ddfc72d3c34ae6aa76d5a457cb403': '请选择季度范围',
  'cb6deedf9cd4a0b65abd70798cfed85e': '请选择年范围',
  '7866226eb814f681dcc4037e7489aab8': '日期范围',
  '28de3d73d839b616bd617b3636669780':
    '日期范围框、input-datetime-range、日期时间范围、input-time-range、时间范围、input-month-range、月份范围、input-quarter-range、季度范围、input-year-range、年范围',
  '9024ff398faf8340b92bf0208431973b':
    '日期范围选择，可通过<code>minDate</code>、<code>maxDate</code>设定最小、最大日期',
  'e7271563debf3c7bcb85e23816c35acb': '最小跨度',
  '6f44927b5ffddc499e4dc26889169143': '例如 2days',
  '6da95498bea887b2ea7c6c2bb4b2fdc3': '最大跨度',
  '61fd7e3e86b168be41ac2e37237e1788': '例如 1year',
  '2f8d6f1584b73bfc6dada44526abb502': '昨天',
  '0dc86a275145ad5a7774e594f0d94a06': '这个周',
  '79abd4ee3661ff70c7d79716c8aaed83': '上周',
  '73bef6f0c997ffe317c868c4716baab0': '最近7天',
  '8f2a5a5f6e3777c7a0e1ce9484a2f7d7': '这个月',
  'd5578d93388a5b2552316418cd1124da': '上个月',
  'ffb2b7fbf227d9d21e03d0f160fb2a34': '这个季度',
  'dd657784cc0d5511d2f25459e49ead1a': '上个季度',
  'd3dbc7a7fd9fc5ccd168084c8579c1ec': '今年',
  '2f92fc7bf6ef3dd57c514d0797fe2f1e': '前占位提示',
  '592c59589144ddc68d05d528da17dcdc': '开始时间',
  'a04c4894d83323d187f65cc357fa646e': '后占位提示',
  'f782779e8b5d709462c8e71e0d9019f2': '结束时间',
  '59a81f21a829e24e9a765c4d6e509e68': '年月日时分选择',
  '5eb8cb70e4dc97622c967d7c7efd6566': '日期时间范围',
  '7f4466c0a16a1808b5ee398ce55997ab':
    '日期时间范围选择，可通过<code>minDate</code>、<code>maxDate</code>设定最小、最大日期',
  'ab37cc3baa3ec204bd7ebfa450568c40': '邮箱框',
  'e3c0d799e8880a36edb07e34fd1bed67': '验证输入是否符合邮箱的格式',
  '3bc5e602b2d4c7fffe79258e2ac6952e': '邮箱',
  '899339c1133a6100a1b223680d26692c': '上传 Excel',
  'b0e4a21f29af38986eebbbf867eee31b': '自动解析 Excel',
  '7caadb59b5892d107a909816b6571c66': 'excel 上传解析完成后触发',
  'eb3daf37c93971703187ecbacb00c74e': 'excel 解析后的数据',
  'd9435aa8028acfc660276c4e0af5536a': '解析模式',
  'b14494137c805dc66bdc9ed88d7fd2de': '对象',
  '0e67d4b0e351b00f4bea9840aa6b99d7': '数组',
  'fe5c59cbac3d45314ec6397d9f75789a': '是否解析所有 Sheet',
  '50b10b178196378f4359ce11bbc31372': '是否解析为纯文本',
  '1647e4bfb548f2e8468d10b4b3bfbe21': '是否包含空内容',
  'ec1f230a0181d79b37967a455b1f3515':
    '可上传多个文件，可配置是否自动上传以及大文件分片上传',
  '8dccb3d6633a85edb21fa52599211654':
    '上传文件值变化时触发（上传失败同样会触发）',
  '5560230c43dd8d8c2b4a62d829f152b3': '上传的文件',
  '7dcb3fb3ccc93369798d4b6950e96058': '移除文件',
  '03d585240162dad662a0a6b5d90a4692': '移除文件时触发',
  '6775136a73e41c1b2cb4ab025773e326': '被移除的文件',
  'a7699ba73144aad544eb9ac49f82749d': '上传成功',
  'e309c24c2b07e5970208a82388f1d88e': '上传文件成功时触发',
  'a27518f50ea00aaacb2c4e52f113eeb1': '远程上传请求成功后返回的结果数据',
  '54e5de428ca9d59119d4624706215a4d': '上传失败',
  '4855bc3f3d06b9cf58d14b61839c5a51': '上传文件失败时触发',
  '236dc056d6800bf423df47520637c52d': '远程上传请求失败后返回的错误信息',
  '36d40a48f3da92af9fe55ee77cfae46f': '清空数据',
  '18843505278637e44a29e7e1f39e0b06': '清除选择的文件',
  'cf6e87cb9edfa398ccfc3db377e34ca4': '按钮名称',
  '0ec0e6e04b9e918939ac8e0daf407b75': '最大数量',
  '0a9dced8856958fbd05508d1218f8090': '最大体积',
  '3e999689c93e7cd95ce6acdded1835fb': '提交方式',
  'd28879b6a8a4ddb62bf6f2ab59303df7': '随表单提交',
  '3a76423b7ae40b0fa8b0bedb470cce7e': '独立上传',
  '7c6722203327e8173be987f36fadf610': '数据格式',
  '6168fb08fe64663a502a132c5589b73d': '二进制',
  '7245fe895fa1cfc42b5217a3de314565': '自动上传',
  'cf9e4c80962e712eaa55551cccff317e': '开启分块',
  '454dbf9b835af8627d4dfff2903298b7': '分块大小',
  '2af0c8b5999a1d243ec5fe503da20026': '分块准备接口',
  '5548252bd28fc1d217d13a9e5771ecb0':
    '用来做分块前的准备工作，一个文件只会调用一次。如果出错了，后续的分块上传就会中断。',
  'd7832abe5b9ce8e41537b4964fd7cf70': '分块上传接口',
  '14008d63c109cc0d0c4ba305671800d2':
    '用来接收每个分块上传，大文件会根据 chunkSize 分割成多块，然后每块上传都会调用这个接口。',
  '70c935fa7ae03aeb1ff87b878e16841f': '上传完成接口',
  'c88aaeddb5aa95c7627d84df33929e3f':
    '等所有分块上传完后，将上传文件收集到的 `eTag` 信息合并一起，再次请求后端完成文件上传。',
  '1e4dc4d5f4a3a95ddc349147d4d8cd39': '文件接收器',
  '258d9e27231b06769dd584a3365545ba':
    '默认不填写将上传到 bos，可以在系统配置中设置为自己的 bos 地址。',
  '28b988ce6ae6dd62d24bcb8a52119179': '文件类型',
  'f0a37d6f810c73a2f936f33035d99056':
    '请填入文件的后缀，多个类型用<code>,</code>隔开',
  '58892b7a6a785706712761d5aebb4639': '模板链接',
  'af4b910df15b00ba7fb5163558389cfe':
    '适用于excel上传等有上传格式要求的场景，为用户提供一个模板下载入口',
  '26d384ebe61b6ffe0e64310331f9e998': '拖拽上传',
  '644d03767c8148de6651cc6b00b0173f': '选择按钮',
  'd1b06a828d05b0ff72328d50b16a8443': '上传按钮',
  '0d4ebf2f4a10336011cfe0466b29ba5c': '输入组合',
  '5b7363cba6bad37b6614809e9bd90746': '输入组合，支持多种类型的控件组合',
  '13370c4a5c95eff126068be5cfd2a9df': 'input 组合',
  'ff10d1d90be63fbcdc7377435bf18f96': 'Input 组合',
  '0a7f8e17f8487c3715a154bea698778e':
    '可以对图片实现裁剪，限制图片的宽高以及大小，支持自动上传及上传多张图片',
  '7c5e7ad8954effd97cb78dcb0e8f99d9': '图片类型',
  'f816b3decb401dcd0c22db7f104beccc':
    '请填入图片的后缀或 <code>MimeType</code>，多个类型用<code>,</code>隔开',
  '99e6b6011bacaabc18aeac70390252a3': '占位图片地址',
  'd24bada35447c81d7d7ddec13c44b576': '文件接收接口，默认不填则上传到 hiphoto',
  'e3be7b8a459a08fec8f378a0660b642b': '可多选',
  'd4633ba7a30303a59891d051a7715cc9': '开启后，不能同时开启裁剪功能',
  'e23c9e6279487960ebf85b9985dcba07': '固定尺寸',
  '037805d2f16cfe69e3145c9f436c06ef': '隐藏上传按钮',
  '80f7e6960804df47856301e7ad8bef6d': '开启压缩',
  '2c4a34adf66f12d0a08716383304a66e': '由 hiphoto 实现，自定义接口将无效',
  'd4efa8f599c1effe67bf6a7f38a9c40d': '是否显示压缩选项',
  '4838f2f4ecafd0a3a4fcfe82521acdd9': '开启裁剪',
  '3d948d24bc4c29689c5a1d4bcb9f2b98': '开启后，不能同时开启多选模式',
  'e09f3466f78e62c1494fb36816950501': '裁剪比率',
  'c7fa7f54ece94ae684aee1ee2088a5ae': '裁剪时可旋转',
  'bd698e3f47ede4e59aafb28a291b77e7': '裁剪时可缩放',
  'f785a357a820555445acd6f7051b1048': '裁剪区域',
  'bc436447f54b10a9ac3f0ee3e1b863dc': '无限制',
  '7ccc0313ac4c40b364f482341ecc3bb8': '绘图区域',
  '4db4c540f05524c38e9431173736517b': '图片限制',
  '13446481eaf0a047d8fddf159d981a7c': '超出大小不允许上传，单位字节',
  'b7623c7e17098d5950694437aa7584ad': '宽高比率',
  '2e81b2a5e6d523a70db93481f01af875': 'limit.限制最小高度',
  '1765a2daba3ba8c5af95e66fa83545d4': '宽高比描述',
  'a7b7da38fcb8e246e910e178c5fe4ebc':
    '当宽高比没有满足条件时，此描述将作为提示信息显示',
  'ed55564c631322fa3042c77286d6562c': 'KV 键值对',
  '746dd83e6891ccc9a19804c23c2c4443': '用于编辑键值对类型的数据',
  'b3747d09769d3a2ea8aa664edc901212': '删除组合项时触发',
  '654ad5670efdd25f1417958a7026a355': '值类型',
  '9648d874d516a87965066342394e86e6': 'key 的提示信息',
  '4dae7425b21494a318cd4a69ce24608f': 'value 的提示信息',
  '233662283039ded8c29f070d1a807029': '是否可排序',
  '4f9c849b6b7f048cfd242cef3e707a58': '月份选择',
  '7c3885e3c3eca4d95521b3e220586628': '月份范围',
  '5b4ad7ffa6eee985e72e002bf18ac09a':
    '月份范围选择，可通过<code>minDate</code>、<code>maxDate</code>设定最小、最大日期',
  '1e65b8181e9a40e76b86e2c261cafbe0': '数字框',
  'c45782c425bfc31824af8129bd6e0282': '支持设定最大值和最小值，以及步长与精度',
  'acc945d0c8449dce7dc1441f466ec413': '输入值',
  '5f914e36c49db618d06981df7b3c4d81': '数字框获取焦点',
  'e52c2701b682679d6c670a8578cfa382': '当前值',
  'efdd0d8923130f281b54506eef2121cf': '数字框失去焦点',
  '0849471829a565a8af6b70a46346e574': '清空数字框内容',
  '33bf23fcb86101a9994b7e4bfdc2b062':
    '请输入数字或使用 <code>\\${xxx}</code> 来获取变量，否则该配置不生效',
  'e284e64008fd8b066da04bca335d032a': '小数位数',
  'b317cbf67c94f986fc44cf4cbc2280c6': '根据四舍五入精确保留设置的小数位数',
  '4c9bb42608b11278a5d9a2471b74eb40': '前缀',
  '0c8fe8b3675b0c7f5a896a153461ea46': '仅在输入内容前展示，不包含在数据值中',
  '242d641eab57223af01fb29940a096ed': '后缀',
  '7915dcc78c28ed3cda8fc949a86e4806': '单位选项',
  '8c80ed1f85135cc9153d0b7406ac5b38': '快捷编辑',
  '0a8c852e27763a18ce5b72a87ba8b5ba': '单侧按钮',
  'de93563a076f72b3e919870c9dad9935': '两侧按钮',
  '5dd92ede50cc183f0f067dd29be5c325': '密码框',
  'a8105204604a0b11e916f3879aae3b0b': '密码',
  'a483bccf85587055ab31314ad1d2f82a': '季度',
  '549a2a421951fc8c304c6223f346e024': '季度选择',
  '39891e1760c79287985adece9a107fd6': '季度范围',
  '6036c91d6b0b6d1a2468684e597d9f70': '滑块',
  '4c65f10eacbfaf580236b5cbe2de68de': '选择某个值或者某个范围',
  '0a6a4aee139530801791c556e7177a7f': '滑块值变化时触发',
  'f34b1a3e26aadb6f60c546dbe9c62232':
    '当设置 showInput 为 true 时，输入框获取焦点时触发',
  '404625f170839b2a24d7ab65304c2fe7': '滑块当前值',
  '7c9ec5db3bf23f367c1a905d356aab5c':
    '当设置 showInput 为 true 时，输入框失去焦点时触发',
  '1659f1112dd649ec55390fe7c8e3b1d0': '清除输入框',
  '7220e4d5f9f65e4fadee5183f2e0c54d': '方式',
  '9ec172e2cdff6146d623087d567c8574': '单滑块',
  '753a8c54d3944e252be1af928b8e7afd': '双滑块',
  'f2996845b6bf0a07fe26f74f35e42ebe': '单位',
  'e48d95a94e49b81b12a0113a5c253d8f': '值标签',
  'a465db53b8c98f42caa15ca5662f9c90': '方向',
  '3aed2c11e95a9c0ea1d853d4aee72e8c': '自动',
  '1d1a0bd85740653320d80f0d6e0918e7': '可输入',
  'd22aa5f57ff8679ee7b7d473ef31417d': '可重置',
  '15628d1d85aee23c229c528a70419414': '轨道',
  '108db12ed4454cf1ab4242ca98442b7a': '评分',
  'f11a0f49123c2099ca6c6162ca37f0ec': '支持只读、半星选择',
  'ce9201dd7d5816945fbedae79927518f': '评分值变化时触发',
  'cef3580fce1b3156908cc852dadef892': '评分值',
  'e2c6470c3f2e76cb98ba951a64e41c3d': '清空评分值',
  '09bbfb387dce6201df1ccef2aab161a6': '可清除',
  '5ea244a79d480632f635a254c95c38df': '是否允许再次点击后清除',
  'fa6f66437097fe23c948f4f9b78ecec0': '允许半星',
  '1a10cd6599219eafb615b34234cfc0b5': '描述内容',
  '9c07532d0c9acfecfc4ec2eb265c3e03': '字符',
  '2a3d1f6d5b0afdbf04033e2f2c1fa11e': '未选中色值',
  '497562b3159756834c7adfe9525bd7b5': '默认未选中色值为 #e7e7e8',
  '4dd547945b10f672a7e48d7ffc42496e': '选中色值',
  'f186d55a48868a88b026d48efc1cf36f': '评分描述',
  'd38056d1c6aa9456f4b6b2c20aab06a2': '重复周期选择',
  'b3389af540d99fe9e51fb2995dbd6dff': '选择重复的频率，如每时、每天、每周等',
  '2d842318fbd931286be387aaf5b2a7c3': '周期',
  'a2d92b5adb07a4bf8a45e55643bc39f8': '启用单位',
  '42c036311993897680ef37f11e4e20a8': '可自定义富文本的配置栏',
  'e2591e971f4c28db7c80a5f546084a1d': '富文本',
  'b582587147013afc52c58de5fe34d7d7': '文件接收接口',
  '70209fc05c1a5b21f05b186a0f7ba2ee': '编辑器类型',
  'eca97e97331bd76aab9f6160991e02bb': 'froala 设置项',
  '39624e820b79bbd62561357f6179c8d7': '鼠标覆盖配置栏中配置时显示的提示语言',
  'a7bac2239fcdcb3a067903d8077c4a07': '中文',
  'f9fb6a063d1856da86a06def2dc6b921': '英文',
  '81fafee54baebeb9591b5e7840d7e650': '大屏时展示的配置项',
  '0b6eee152cb2553ed4298ca2fe82d3f8': '屏幕宽度 ≥ 1200px',
  'f126c4368fbf51cfd470684e7c3d31c9': '中屏时展示的配置项',
  '3b8c320d14dba586c581ceb951917397': '屏幕宽度 ≥ 992px',
  '89bc688b5b497e515ce3c026a1d92669': '小屏时展示的配置项',
  'a849d8a0f59f3ae146b149b7b4fc7480': '屏幕宽度 ≥ 768px',
  'b8c8f1c6a56e902bd837420da0f554b3': 'tinymce 设置项',
  'e4d0e65de0018b63698ff22d683d6dd5': '是否显示菜单栏',
  '63068211669d69b4028eebe0052425e7': '启用的插件',
  '1a920d9999206a9fa5f588c7405fe081':
    'SubForm, 配置一个子<code>form</code>作为当前的表单项',
  '18c113b99afa964ee988f30e81baf12b': '名称字段名',
  '4d930a9c13fca9d74e1a77496abfaa58':
    '当值中存在这个字段，则按钮名称将使用此字段的值来展示。',
  '307ae20f587910da3e9bb4e885334b6a': '按钮标签名',
  'e366ccf1556c0672dcecba135ed5472e': '设置',
  '70c04a0f4522e39fde5f18ebc85b6232': '允许最少个数',
  'f58ea00f6a84bcb1cac174d1f68c88bd': '允许最多个数',
  'e41fd1934b82f93f5737827be7323119': '表格编辑框',
  '0fbb26a646da61d2123319b4d0144ba4':
    '可以用来展现数据的,可以用来展示数组类型的数据，比如 <code>multiple</code> 的子 <code>form</code>',
  '4ba0387a2daf11ad9c67a75b52819eb3': '表格表单',
  'a80f36ea893b8135df8d9266bf13afac': '说明文字',
  '1f9794dd5634220ed0a498c666cf46fe': '列集合',
  'b2c712c788d3a143206eee22fe24d9f1': '绿色',
  '6b93fcfc1a5795189c6072fa6e86d4f6': '快速构建表格',
  '0c14e431f1b5ecd163f8fa010a0654c7': '新增一列',
  '13c250c68608118463871ce7cd8b292c': '表格编辑',
  '6a2f67fe2d8bb1d389eb934dd8a72b1f': '增加按钮名称',
  '44177f78c596be113d4fe7c6bd7bb46a': '增加按钮图标',
  '1d34fdbdcac357588c255c1ed2336a3f': '新增时提交的 API',
  '96848841ea9b2a7d692aa6de57aa5e3e': '删除按钮名称',
  '34f020715121478c6a8e4654954c84fd': '删除按钮图标',
  'a497289d862c6aeff8e61b6479b1081a': '删除时提交的 API',
  '748f181258041bac1fe7346e970dfebc': '是否可编辑',
  '412e319fdd893e6f3b26a1975d21b0e0': '编辑按钮名称',
  '396ffcbab4e21acb77f6987f18ec94a3': '编辑按钮图标',
  'c128de224b606b094bd8139890fbe215': '是否可复制',
  'd59abc7020b4a6d943a0031da4518422': '复制按钮名称',
  '5d04af0e9d59ab3f1265cd8345c2517c': '复制按钮图标',
  '681bdd6184b526ade9665e6d74976863': '修改时提交的 API',
  'c29e632a658ecb98edb874833b070e98': '确认编辑按钮名称',
  '24c39bb36955b3113baefcfc20c5f60b': '确认编辑按钮图标',
  '9c68c27e8939af40897d1b0bca0360a5': '取消编辑按钮名称',
  'e54289c1f0e9311185adee89af24e618': '取消编辑按钮图标',
  '1205e138ba64dddf61c1f8e6eb3a1aa7': '确认模式',
  '1c1618f67cfea6fefb5f287932be2c27': '获取父级数据',
  '677fb705e57897710d28867b7628307e': 'Input CSS 类名',
  '14d342362f66aa86e2aa1c1e11aa1204': '标签',
  '1b4996b29c7b4d5fb45ca701e6438d14':
    '配置<code>options</code>可以实现选择选项',
  '52636511861a0e08cbe6a0eb1c27d816': '红色',
  '9c9aabab3f7627ff4bb224b2738b26ea': '蓝色',
  '9888468550779b1777260b8fafe6abc2': '选项提示',
  'a2cbb42e488dffe644bdb87c26afbc07': '最近您使用的标签',
  '6f6fa31a91b516b28ebee7a86a9b13e2':
    '设置选项后，输入时会下拉这些选项供用户参考。',
  '379b5486e7860a9f38f37dddabbb094c':
    '文本框、邮箱框、input-email、URL框、input-url、密码框、input-password',
  'f222f5413d3854897741e0ec4b0d2c7b':
    '文本输入框，支持普通文本、密码、URL、邮箱等多种内容输入',
  '9306b956ca5950203ee49a2680cac63d': '输入框左侧或右侧的附加挂件',
  'd548d3e938088d5d469a28c20e939750': '自动补全',
  '424cd425082360322678a5c8d6910b80':
    '根据输入内容，调用接口提供选项。当前输入值可用${term}变量',
  '7e9c83e86beb612377a94e6e8d1fc644': '显示字段',
  '26ff46d82166741297ce666b2792af85':
    '选项文本对应的数据字段，多字段合并请通过模板配置',
  '6d4ce0631f37676a887c9599691fabec': '选项文本对应的字段',
  '2e01f5f5889e33d003bec7857cd38445': '值字段',
  '959c27193eb0a41d01f4b53dcc4b9245': '值对应的字段',
  '82a61b32c76fba3dc83e2611624e93ec': '时间框',
  'c54993eb4c2a46263cca2e6bdebb051d': '时分秒输入',
  'c166d8f9804ecef74b3b74190599a7b8':
    '时间范围选择，可通过<code>minDate</code>、<code>maxDate</code>设定最小、最大日期',
  '479728c411bac59bc44d5ab8dc3cb4f0': '树选择框',
  '15ac5d800c0163b4b806622739478e9b':
    '树型结构来选择，可通过<code>options</code>来配置选项，也可通过<code>source</code>拉取选项',
  '57a6105deead3fec79028cce7bfa2004': '选项C',
  '9bc04a190ce0a5ba1ed473cb628b5ed4': '选项D',
  'f77f634e6892c6447c5d9df623e42aff': '树选择',
  'b35014ec330e91860bb188200fba12db': '选中节点的值',
  '65d76d0590aa6e05d61fe8db4c6a11ca': '新增选项',
  '0761b99481b1bd10c1d6aad6028a8281': '新增节点提交时触发',
  'de2392357fc78e9e0f0946bcc0af87b6': '新增的节点信息',
  'cf965b232227a0e9d14f4f7e31b01c62': '选项集合',
  'cd994c38456676f5a55c5593b6a652bf': '编辑选项',
  'e385c7c6f726dc2641866d9050777efb': '编辑的节点信息',
  'd015e18748f42f53bb6ab213e9b06187': '删除选项',
  '58cbd355c5bdb80653a8ae3d3b316c37': '删除的节点信息',
  '58995b490ba67e5d29dd87f422b14861': '懒加载完成',
  'ec2fb7a5db150690b14b63f83d9d4b30': '懒加载接口远程请求成功时触发',
  'e6b94af26512b3f7ea8eb1433d4a2aaa': 'deferApi 懒加载远程请求成功后返回的数据',
  'e2edde5adbdf33f6dce59a299cbf5fad': '展开',
  '38747bcbc3c47924098076ee87e59933': '展开指定层级',
  'a148ea1749eaf611df5cc95b533751ec': '展开到第',
  '37252d3a5eb0ebab17bfce14968b47c9': '层',
  '01356df4561f9da87d2876ec9c0dacfe': '展开层级',
  'def9e98b60e3bfc493bcd7693e702096': '收起',
  '3ebb9059b6abc8f80a212b5795270ec2': '收起树节点',
  'd81f0b9079d5a38bbedffeacaad8b905': '清除数据',
  'ce5e449208bb568eaf722bab9a20380e': '重置数据',
  '25ae4ca8d4b8a67b273066a97a516327': '选项 Options',
  'fe7509e0ed085b86f07e3e9975cc5b3d': '值',
  '72453d792655604f1fab821146133d7d': '子选项',
  '210da23d108e85b2f0bbfa85846cb792': '新增子选项',
  '204770091fd4b7cd0611ddc65bf21d37': '子节点自动选',
  'be7d848c40dfdd3e20f233c373af00a5': '当选中父节点时级联选择子节点',
  '4c3ed2fc7331db687fc0e8ffb8f672a7': '子节点可反选',
  '0ae8c01434dc2a38a90561fcbf5d79b5': '子节点可反选，值包含父子节点',
  '22c023bf51970a461cc164e711f3d3ff': '值包含父节点',
  '6e19e48a52986659ae5ba1bfe612ba8a':
    '选中父节点时，值里面将包含父子节点的值，否则只会保留父节点的值',
  'cccb3595f8ff536e27d94ec91a49bfdf': '值只含子节点',
  '49ccf100ac35e1cc3d40b7222cdcd1a7':
    'ui 行为级联选中子节点，子节点可反选，值只包含子节点的值',
  '4e373f3ff64ad29a01da87317eb92088': '节点最小数',
  '160cb9a7d57c5b5ca65b5cdf79e8fd28': '节点最大数',
  '0d83078816aa273f2941c9b55ec82bf3': '数据',
  '1cd9e1bb335cb0643d9e310dd4edd830': '图标字段',
  'a0e965072593eb1b19b4568fa26d977c': '只可选择叶子节点',
  '8865c6822a31e0da6bc7eece8677d8f0': '可新增',
  'bc560e477282dafc3c37b7a665af4f9c': '顶层可新增',
  '89664aa96ba7eb788ea273c0d36467cc': '顶层文案',
  'b4eab506cf436d3cdd1cd68fc857ec04': '添加一级节点',
  'dfac151de712ab0b3618072c8a7f0a0f': '高级',
  '2b3073b1ba5f9deab7818d8be02de3a1': '高亮节点字符',
  '14dc30e68a3d8d53e8ddd98876c1d480': '选项值包含父节点',
  '68c7da9593da32100cb82ef5106047e1': '开启后对应节点值会包含父节点',
  '9cb417cfac37e6d65dedbc35ee9e916e': '路径分隔符',
  'd6840c02c4d9cd5f4f5fd4d9f3e6b916': '显示顶级节点',
  'e2c310a329c2cf62a0764fc912f2f583': '节点文案',
  'c48d9f48ce8a798772f17d1f47560529': '顶级',
  '8c9a3a8cd5de83121c03c83f909a7534': '显示节点图标',
  'ae7d563d3190ddbafdda8093fc28fc5f': '显示节点勾选框',
  '4444d03faa51f8550a8a4547fb471ab7': '单选情况下，也可显示树节点勾选框',
  'b39c6fb1cdf629d3f3032d6c7d4694a8': '数值是否携带子节点',
  '1fa482fed3042cac9f96c6f43c13f84a': '数值是否只包含子节点',
  'd7eae84550f9b9bc0c78a94e4072ad0d': '自定义展开层级',
  '346f7e0c7ef27b6a83f3c9f8f406d737':
    '默认展开所有节点层级，开启后可自定义展开层级数',
  'b40163733b8e3a420e38f295bfab369d': '设置层级',
  '55b50a43f58fa746600d6bafdf405336': '顶级文字',
  'feec2a81557811b7fc38b0e6b758c3f7': '是否显示图标',
  '945da00807804cb992a3aeb0ed6ea698': '是否显示单选按钮',
  '13604cddfe74bc9b9078dd61f9fb94ef': 'URL输入框',
  'ed2e16700e32afdcdd745aa31454edb9': '验证输入是否为合法的 URL',
  'b233766d3fae72574d3f9ee98c5be120': '年选择',
  '32a750b4fd61f1cfe37cac7147f05b3c':
    '年份范围选择，可通过<code>minDate</code>、<code>maxDate</code>设定最小、最大日期',
  '973b69af999dbdf4fa124df8c928ca6e': '只读模式',
  '710ad08b11419332713360d2750cd707': '禁用',
  '7abf78a41095c6d21a1cc89b4a876233': '隐藏时删除表单项值',
  'cd8992b644e6c18367861a4d913fd116': '验证',
  '1040279cf7b8dbdb842f597c30095f62': '校验接口',
  '133886b10fd9721e6cf91b76f2df5b6e': '单独校验这个表单项的接口',
  'b95c6e14a4d8f6a6c6d99d583916f327': '合成一行',
  'd22dfe420e4b00e000b93f94db3c856e': '列表选择',
  '5e391af52da238f13c78b471a7cc54f3':
    '单选或者多选，支持<code>source</code>拉取选项，选项可配置图片，也可以自定义<code>HTML</code>配置',
  'd1f923b8e23b66b3e48953ba1ce17839': '地理位置选择',
  '989ea9acbab9b17d2c15e2946b5365bb': '位置选择',
  'dffd9d86d2003615897b12ce7597d77b': '百度地图的 AK',
  'b472ba224a7d132c487ee6ec4798f835': '坐标格式',
  '36443b53c845b197db8d39eeda433ab9': '百度坐标',
  'f58cb611aec0998a44ef104b5c950b40': '国测局坐标',
  '076a041dcf45a36b29c5cdb8b8eca922': '矩阵开关',
  '183aae5eec1ff547833dc338aaeffc9a':
    '可配置行单选，列单选，以及全部选项只能单选或者全部选项多选',
  '7583b85ff3579d60a9f9d323c2f6674a': '行标题说明',
  '3fc35bb610852289cf718f07a5b79369': '列1',
  '50987924540e6e35c78246031499e75b': '列2',
  '08a6996be7a86af5692cbca41922a5d1': '行1',
  'ce31d858c63395098c5e4cd892bd473b': '行2',
  'e9b376d39966c8182683019f95ff8cf3': '行级、列级或者单个单元单选',
  '0f244785fd9f555aae9797db4d14fb09': '列级',
  '5a57bd526cc3170b6c86f920fc24cdee': '行级',
  '45dde5879e77d7f7a15d04b8fed26ec8': '单个单元',
  'dc7558211f2990370954c2e7ca498ee9': '列配置',
  'f4c807fd8453f4b3fdaab02fb9c21ba7': '添加一列',
  '39886861ea5d8b526e0ac5ecc78d110c': '列说明',
  '34ad26bd1fb448c7f2384252d856c02b': '行标题文字',
  '48baa62373a2f90d6aa743d7821be956': '行配置',
  '854af3c2cd9c275ac70fc5121ea4fb2e': '行说明',
  'bf306308e467aeb7b00df0e2dd127d06': '添加一行',
  '75bf5fd49520bce97de632700fc8a129': '获取矩阵数据接口',
  '24d9de25721d1fb0ecf89ef81c43d877': '级联选择器',
  '10d1cfb7219a5445ef1e70aa16e4451d':
    '适用于选项中含有子项，可通过<code>source</code>拉取选项，支持多选',
  '03cd388fe29a4fc116c0021e496a113a': '选项b1',
  'fb00d1c1a65d3739c03a0b2715168327': '选项b2',
  '353ae08afdb3d0a3587e9b27ca239c33': '选项c1',
  'bea7ad76f0f321124ff614a099cb9a85': '选项c2',
  'dbdae74eb12668e2b9568b013bf27d45': '可检索',
  'ae45cdb51c795df3b046f71afe3611bf': '空提示',
  '601bb29fe72e56930dea03ae3e1aa555': '检索无结果时的文本',
  '6a7f7d89c8f1f3e6aab0962ff920b145': '父级作为返回值',
  'c23eb92d1f249452c3ff3ee13738b47a':
    '开启后选中父级，不会全选子级选项，并且父级作为值返回',
  '8fcbfdc1a9403f6339d81911473806da': '列表选取',
  '819776e8d96b1438eca3594f47bdb1c6':
    '通过<code>pickerSchema</code>配置可供选取的数据源进行选择需要的数据，支持多选',
  'dc0c50a5c9832b393df34835111c34a3': '开启内嵌模式',
  '1ac065ed64b81380384a0b371d5b404f': '配置选框详情',
  'd64b585847f015eaa1443a3a03562350': '已选定数据的展示样式',
  'c2c23b4fc7f695c58e947ad413f2c5d8': '选框类型',
  '33e6c41fed95c25e3b426d596d504579': '抽出式弹框',
  '5a7af8d3e471d98339c84b07a844547b':
    '通过<code>options</code>配置选项，可通过<code>source</code>拉取选项',
  '1e890599eec94efccec337cd474f7f1b': '默认选择第一个',
  '46110787e4774b81418b274e9b06127e': '一行选项显示',
  '83a00680e0872e2a35496be7e12c1309': '每行选项个数',
  'c30264927c3c170efd2e7763becf12fc': '单个选项',
  '8a0c1f35cff334fc105b6d52b195369d':
    '支持多选，输入提示，可使用<code>source</code>获取选项',
  '75cc2a992ea150d3a6c68ac4bc486637': '新增的选项',
  'a13b85bddbcdab63ef2b2d98dd46afe9': '编辑的选项',
  '8aa4d6aedd7957ebc6b87fec655695ef': '删除的选项',
  'e19c0792886a147d74fc662d7af138bb': '单行显示选中值',
  'b28aa9c36d0b506a71aa78b628e796c6': '标签展示数',
  '2b23767de575e27fc9e4e0949e885f81':
    '标签的最大展示数量，超出数量后以收纳浮层的方式展示，默认全展示',
  '406af2b98e6210cd42d7f824cb6dfd16': '选项值检查',
  'fa8d03e8b5458c1a1d742736bc26e25b':
    '开启后，当选项值未匹配到当前options中的选项时，选项文本飘红',
  '33e7b7433fdbeafa1ef71105ee28c061': '静态展示框',
  'c99e0a675933de39693b7e80d5b70405':
    '纯用来展示数据，可用来展示<code>json、date、image、progress</code>等数据',
  '134492cd10a0646da6f23a743feee6d4': '静态值',
  '04f5f12c49c2a6fdc43da049591328ad': '静态展示',
  '663a93dacbba9f7860dca783cf772419': '可快速编辑',
  'abd733d00ec656e7b0cfd88deedf102f': '快速编辑模式',
  '78f395c15aaf8c92d9223f6ca69b41a4': '下拉',
  '5e2d1e872682befd5350687f94a6b417': '立即保存',
  '2d2eb2ac28f074d1caef0d332d61cfb0':
    '开启后修改即提交，而不是标记修改批量提交。',
  'eccba4475f3144e417e55fd96e831e09': '立即保存接口',
  'c8e5c062d5ad38e54413abd9c7cfb2f4':
    '是否单独给立即保存配置接口，如果不配置，则默认使用quickSaveItemApi。',
  'c3ed36e4abb96c18a6c83350994cdea7': '配置快速编辑类型',
  '45a6c55d8d2d92af84e219defef084d5': '配置快速编辑',
  '3f337f436989e9847790e4435423f77e': '查看更多展示',
  'f7247cd14bd964b883bbb481892c440b': '弹出模式',
  '2a2924380dfcaea998bd8a49703545a9': '抽屉',
  'a06e8df4e9232cc606e0202e06a198d8': '浮层位置',
  '68b54e7b408c4fb83561c931aa668eae': '目标中部',
  'e18459c93769a5afec01e1ce60f9b9fd': '目标左上角',
  '1fc3cdd8437f5057774cde2f2c51f97c': '目标右上角',
  '63dd9faca92bccfd436ff21a6b4b3151': '目标左下角',
  'd01c239688b9c8fc145191ee642dc080': '目标右下角',
  '8fcf9802436282672a8e28ebd6262390': '页面左上角',
  'aed25160b4e8cfc613a743c4549e9222': '页面右上角',
  'd4a4ab1916187e09b9c037705fd49ffa': '页面左下角',
  '921fccbb84c829bf8c6f0b9957029f44': '页面右下角',
  '1d53f83df1d889a70131b7a93c819575': '内容详情',
  '8dda45360dcf9ca501fd7d0eb53045b5': '配置查看更多展示内容',
  'fd389de167730ba8542217ad31b95562': '查看更多内容配置',
  'f9a9fcc3bf6a3c8ff1e99fa48ed6d03d': '可复制',
  'deb65aca8dba2ff9d0cfaed0a3441068': '复制内容模板',
  '622e14515c4fd5ca6fe6946e3a1bfb4a': '默认为当前字段值，可定制。',
  '16a20243f9b741c08216dc9548de2968': '整体',
  'e1b2f870112bd38f8d7e14a0ad589930': '控件',
  'd38b7fc2d31e0ae21cb4bc7d7df14e92': '开关控件',
  '6e17d8bb70df1c1e379fa86cb235ac82': '开关表单',
  'ddccb436305b0a984c77d4ffa0725375': '开关值变化时触发',
  'a75c768fda740b2c8f6e2dcc76400f23': '开关值',
  '7e1eec8349c4998d142960009305f27a': '填充文本',
  'c580bacf343343f04a1b551e46d02c4f': '开启时',
  '9ff352ae39cdaeaa4fc54f83575eedc9': '关闭时',
  '48433deca382e67b26af31c6ff967d04': '默认勾选后的值 true，未勾选的值 false',
  'cadd676e2710e50ac149211078f8a306': '勾选后的值',
  '13f19e1d0dd528aafd6263fafdc35e82': '未勾选的值',
  '617a63f1b19b5aad029f973479bac917': '组合穿梭器',
  '4f26f1edebcdeea90f6e4247a501dbaf': '组合穿梭器组件',
  'ab5dea29793d933fa7b5487a7309df6a': '成员',
  '71f8043aefd52572b172e7d1fbd5af57': '法师',
  'fda93c79275b812a6c1c189fbebf8b08': '诸葛亮',
  '573cb0d34bd1cdc7b368c59db9b7bb7d': '战士',
  'e37a86d1a1dbed0cd53c95582977f075': '曹操',
  'ccddd2de691ff2d56b651877f72d90ed': '钟无艳',
  'c0db8e7b42528eeae96310c6629e53b3': '打野',
  '293040fc607f40caf6d6e16042012182': '李白',
  'b5256ec780343c4e20e397b43cab96a9': '韩信',
  '49dcf9f88e7b7b8ca7448631021d7d12': '云中君',
  '1fd02a90c38333badc226309fea6fecb': '用户',
  '8b3a9a5c912610c384bc8dc2c8514386': '选中所有选项',
  'e1112a529dc969a03bbbb409905ff2ec': '选项卡切换',
  'd202bc660c4d2eeb58e194b6320bd235': '选项卡切换时触发',
  '9e7a97a3d043f566f2435329e01e09f9': '当前激活的选项卡索引',
  '91208131116f2823993daf99f15e1325': '清空选中内容',
  'dda222620b789d07c2c5c279475caaf1': '重置选择的内容',
  '2ac24a383a1faae33f81772b757b2817': '修改选中tab',
  '91f894b900f593c848e5b21f2b414b05': '修改当前选中tab，来选择其他选项',
  '791959f9b90734dce08da79f4ac27a41': '检索接口',
  '6eaeb8ccaa8473e2b985095be2bf3cd1': '查询时勾选展示模式',
  '6541f1702af367c41a3127ed8511eb50': '列表形式',
  'd58ba4b5e94680fcb08300e176502fb8': '表格形式',
  '406573cea6af9b0c6462295108e1f5c0': '树形选择形式',
  '6c5358b981a475da2a83e95e4170647a': '级联选择形式',
  '0911a348aaf24601e633e318ccb8aace': '左侧选项标题',
  '84a76ba52297727f6bb47d8a1cc74094': '右侧结果标题',
  'c6a16ef980efc2ac48c58727e5bade81': '多行文本框',
  '2af7e3952d7430d1e4c86ea5aca4e4fa': '支持换行输入',
  '5bc28acd4afb712dcbc234927357cd87': '输入框值变化时触发',
  'cd7f479dd052edc1261261c15fb0b50f': '去除首尾空白',
  'd3a8fe0d542476cf7989ef9e69cdd6f7': '开启后，将不允许用户输入前后空格',
  '829d96cf23f19759e4ef988fb5320032': '最小展示行数',
  'c0afd6005e68838e37e26dc7c34cf368': '最大展示行数',
  '685e5f4815e87c4690dda7d7aa163904': '穿梭器',
  '26b4a55f458275a108c1ef213a354ced': '穿梭器组件',
  '1b09b6621ebf0d10ce98f0178fa1bda1': '触发组件数据更新，多值用“,”分隔',
  'a2888bca5f435690ff9f5a5d615a968a': '左侧选项面板',
  '1e409f26f9c1d58ce0c47a68104d45f0': '展示形式',
  'eebda442c4bff2aaaf8274383d0ed12f': '树形形式',
  'b3a17ee1af382c467d857d33089fc0c7':
    '左侧选项渲染模板，支持JSX，变量使用\\${xx}',
  'd5dbf5285b2dbe07b481fbd5d4536c60': '右侧结果面板',
  'b720b2abd62161c3c99625c8160df987': '跟随左侧',
  'acef03eee638dc4239ee60f627f33d85':
    '查询功能目前只支持根据名称或值来模糊匹配查询',
  'e3ed623b79100791f156d3586751c652': '支持排序',
  'd4865602e26e9c985814380beae99b6d':
    '结果选项渲染模板，支持JSX，变量使用\\${xx}',
  'e40d15221a911e060b8a05ec7aa9533c': '树下拉框',
  '26aa42c81825364d2d691261017b6797': '点击输入框，弹出树型选择框进行选择',
  '382df0b291e5306c423bcf471eb1734f': '树下拉',
  'ac34e635d05de0ac2de78737ea313995': '新增选项提交时触发',
  '5f5e2d89e987fa64d56471b2e3d50cda': '新增的选项信息',
  '8784005bbfb2b66592cc0c5cc0a62f2d': '编辑选项提交时触发',
  '25a4d7b5ce2585aada1205e1121e9967': '编辑的选项信息',
  '91a6d3a760184e581eccd52bfa3f7a18': '删除节点',
  '3c72d934764bb7205f593903c63ac7ec': '删除选项提交时触发',
  '7768e7d47fa6e9b811d1a1f684703cfb': '删除的选项信息',
  '0aa73cfbe5a84cd34a212de5bab2058d': '选项文本仅显示选中节点',
  '4dabfefd787102b8159b5c4a221aa048':
    '隐藏选择框中已选中节点的祖先节点的文本信息',
  '0f993a54d2c67716220632577d3b5b51': '显示层级展开线',
  '846da282ebfa6dfe61deb1d58145158c': '显示树层级展开线',
  '93199f3864cf5a68b286b927fa68ae6a': 'tree容器',
  '43474db97aff2ed0a876a47b6f4cdc7d': '自动生成的 UUID',
  '43e1548e15272e1007051d7c8b70adf6': '自动按 UUID v4 格式生成，无需配置',
  '844d72db7e57be4d77881ee9b4294e75': 'UUID（展现将隐藏）',
  '2820712cac089483cf2b4a0c939fc780': '分栏',
  '9e17d57a0ba39254a75c0265aa3063ca': '分栏布局',
  'e63907bf0db529e84866d1ae737bfc0d': '栏',
  '9bdb07e72d3a9a6084201a7398523f5a': '插入',
  '3c43c5860b4dfaced296d7a63eae1513': '下方插入新行',
  'decaeded2b6f2c66f191ff34b868b1eb': '上方插入新行',
  'eb22d47f16f92e6f621c2b9d87119303': '列数',
  'c288b519484207bea1d51884a5e9acaf': '列间距',
  '98d695d6a6a48cfb4bc2f6396ee64787': '水平对齐',
  '1cc9a6949b47913462ff832cb684bdde': '中间对齐',
  'da1b972efb29f850b50e219ad4d98ba5': '两端对齐',
  '11b7fc50778c89572391ec4c6c4b55e1': '垂直对齐',
  '2a6ad292447e6354ca39ee7f40d2fcc8': '顶部对齐',
  'd68c21b6b65e7a2e361762b65b8a5032': '底部对齐',
  '0d9eb53682343f1cbd562c9a944ef5bf': '左侧插入新列',
  '814276d30e338eccbaee96c3e6bacb61': '右侧插入新列',
  '2a3e7f5c382c56faf261a87573658325': '手动',
  '19432e481cefd1bf88fc707f20ea62c5': '栏 CSS 类名',
  '5ea5dbb698afe7ca566b94e92f6a3835': '第{{@1}}栏',
  '9cc03fc4b3e87e8410e10357a219b44e': '左侧插入一栏',
  '6b12fc99e2a46aed982d46b93ac191a0': '右侧插入一栏',
  '57ff158884582ed498a87d03aed46146': '上方插入一行',
  'bd63eab86ac0e0b35a06d0f24a098096': '下方插入一行',
  '69a519f6b7fd6d0bebba72e7572ea1d6':
    '用来实现左右排版布局，默认平均分配，可以通过 columnClassName 配置某列的宽度。',
  'f9c91bffab8b1202cf32ab508879e805': '固定宽度<br />w-xs',
  '8c187c1862900db487c8d47e19490fac': '自动填满',
  'cb2f68c9c24e85d21e6b090b6e5657d8': '列',
  '92e2c6523449dfad4350f58908260266': '请按住高亮框右侧方块拖动调整宽度',
  '129b16a89a82c6d5e03ab075633b3892': '列 CSS 类名',
  'd34cc10492e80595a901a292d0c16bb8':
    '可以添加宽度类样式调整宽度，默认宽度为平均分配。',
  '84b620405949e278f214a811a3a19e2b': '第{{@1}}列',
  '4bfd6e7e1ec0fe485aa0e7741d1670db': '左侧插入一列',
  '9d75cc0b193601391a139285d55a3493': '右侧插入一列',
  '713315591970d7c8b49d1c732fe54fde': '可以用来嵌入现有页面。',
  '8f1c078c6d42759e6ccb1a9bf35f1629': '页面地址',
  '51ad722113289f70b6d77c78ddf0c64a':
    '默认宽度为父容器宽度，值单位默认为 px，也支持百分比等单位 ，如：100%',
  'c78d9b9ab9db5951eb5526e8a46677d9':
    '默认高度为父容器高度，值单位默认为 px，也支持百分比等单位 ，如：100%',
  '9ef4425332e5f8bcad86de483b4faedf': 'IFrame 页面（{{@1}}）',
  '67997ccf7ea846c3c2d278b01ed9600b': '图片展示',
  '6dcf991e992a0b765df0933411fe9bb2':
    '可以用来展示一张图片，支持静态设置图片地址，也可以配置 <code>name</code> 与变量关联。',
  'e18aa5e376437da71083a29c4cddaf46': '缩率图',
  'fb43d5ffa21f3c4055c29fad350f27e4': '原图',
  '582570bef8c57c5af7658c4a4eea45ff': '缩略图地址',
  '1193e1aab7bea094279ae7b4288ba848':
    '如果已绑定字段名，可以不用设置，支持用变量。',
  'f5865bf2d791d293374af4aa76d27c4d': '无数据时显示的图片',
  'e1110b854bceeed0e887cb951bb3d2f3': '开启图片放大功能',
  '7ffade593e390a2a2cc43e6663461b71': '原图地址',
  '214953c5f7557b1a5f1310c87238ee03': '如果不配置将默认使用缩略图地址。',
  '6d45b56ee94ea873a554ec41b9f2074a': '是否显示图片尺寸',
  '00cedb73310cc531a43d23cfa5ba0e5f': '缩略图展示模式',
  'b0267f4aa776e75443b7ef6e8dad257e': '宽度占满',
  'c30b1b6f29debb05449aa3cb40268e7e': '高度占满',
  'e13556bb3580ac3746e1f8663eb15896': '包含',
  '47303119ba97a66d168ff042575b9de4': '铺满',
  '319501b9acacdd6f94a9bdd0637a3cd2': '缩略图比率',
  '4d080f1e18d86051b8d651c68685f319': '缩略图 CSS 类名',
  'e040fc4bf539dd1cf6ebca6b4bff857a': '图片集',
  '93f46deec83b8f9005233a1467498d52': '展示多张图片',
  '2382c315a6ba396be1932dd8dafaff52': '图片1',
  'ce6e2814f207c9333f10785606c57df3': '图片2',
  '45a4922d3f42d0b137c6845c4c77031f': '关联数据',
  '2d4247be13246794180897b40fcdbcb1':
    '比如：\\${listVar}，用来关联作用域中的已有数据。',
  '81a2b634d7ddcffc21b787613673943c': '图片集数据',
  '0f088d8f579c362068d7a3858e207280': '缩略图',
  'ebb2073c604e72ffd9ae1b796665b702': '图片列表 CSS 类名',
  '5acc93183b7fe3816a845aca52baeff2': 'JSON展示',
  '5a4b81442789f3b2e7b665bd430eeabf': '用来展示 JSON 数据。',
  'f891291cbfaec7ba3754520e2a5227d8': '默认展开级别',
  '6dfe63d8c039df37787c87afe4c68604': '用来展示文字链接',
  '6ab20dc4b64021b85886ad9c12b6e0cc': '目标地址',
  '78ce29fdc55e6d0980b591a264c537a8':
    '支持取变量，如果已绑定字段名，可以不用设置',
  '5d809212900f3bc3ba122fe93638394d': '不填写时，自动使用目标地址值',
  '88a5126f6a1463fc9986b590ee4ab99f': '在新窗口打开',
  '39a2cb79c6d9762783e20522ea86dcff': '左侧',
  'de2a774bf98944b8f0ec8755d5f59d64': '右侧',
  'e370757f933a8ecd87bf0255c3ce45d0': '高级设置',
  'e35dbea2b0c097d7fb76173da0e0bba1':
    'HTML &lt;a&gt; 元素的target属性，该属性指定在何处显示链接的资源',
  '9f556fb46ecef854282d17e631578b1c':
    '展示一个列表，可以自定标题、副标题，内容及按钮组部分。当前组件需要配置数据源，不自带数据拉取，请优先使用 「CRUD」 组件。',
  '84c04f1e9ce6a6a228228dd3fb96b99e': '配置成员详情',
  '1ac0e1626be43287983fe3e5559320eb': '没有数据',
  '4e53bfe03e30bb3eae63d90d1f7f2a1c': 'List div CSS 类名',
  'd9eac94850a74ec881198f4ffe4edcfa': '列表项',
  '3ed7b428165499a1b91ed1eb287ba343': '支持模板语法如： ${xxx}',
  '456d29ef8bafd5202547e50d3e64d4ea': '日志',
  'd06f8476d115020496058363a32bc40b': '用来实时显示日志',
  'a2bc1edd7be9348e5a998feb0886c55d': '日志数据源',
  'ddeeaa33ae62ead1a77a859fb79b78d7':
    '对现有值做映射展示，比如原始值是：1、2、3...，需要展示成：下线、上线、过期等等。',
  '8b139ce9fa196b602bb1ee3bd25b25df': '映射表',
  'a9de5901385d644363cd056482be7517':
    '<p>当值命中左侧 Key 时，展示右侧内容，当没有命中时，默认实现 Key 为 <code>*</code>的内容</div>(请确保key值唯一)',
  '2b10dfa6dbdf8775ab2b38fc67e7ea49': '通配值',
  '87cac3a9c9576d9b5bad6639c891112c': '展示 markdown 内容',
  'eb99816b4a216b2eb884cc6194f00ec6': '## 这是标题',
  'f6a5891819c67a80c1179c7cdaa00935': '用来渲染导航菜单，支持横排和竖排。',
  '4cb18f42ea06a97b382397c40ed64fb2': '页面1',
  'a004407ac524b6d331671fb7a013c3fc': '页面2',
  '310c10c1fcd37bf760bc15c30f54e722': '菜单管理',
  'ba750e6e302292402a4c61ed72331cba': '新增菜单',
  'f7a63e1e77fac1bfa05a060d55e27692': '菜单中存在配置错误，请仔细检查',
  '7fa141f341e173e2339dcf0ce6869b5c': '跳转地址',
  '720fc47e20be9b7a2e67a4ed808b3bf2': '是否高亮',
  'a6ed2640c41d0e3df4acb4f15d37f6e3': '可以配置该菜单是否要高亮',
  'dc55fd2e990771fd670743ca5dd59275': '留空将自动分析菜单地址',
  '78b7cf23a2b93db1ec36e6c6cfe170db': '包含子菜单',
  '710b20ff1d85f7965bfaac44ae26344e': '子菜单中存在配置错误，请仔细检查',
  '02f925f6a5136c2b65b5da8d1216b5b8': '子菜单管理',
  '59ad4734917af165482774de9c2d50cc': '新增子菜单',
  '4d25cfe4cd14270af9420acd25526691': '获取菜单接口',
  '9fc8c79aac0302a60ebc0777183cd181':
    '如果菜单地址希望可以动态设置，请在此填入接口地址',
  '737669a8ef038ebd37fd7b2f3b5f389d': '是否竖着摆放',
  '8d79a8cbe0ed37accbe0739024d5d896': '操作栏，用于表格。',
  '6c0fe599b36c2a55efd8705681783ee5': '按钮集',
  '975903dc39691813530e108986e49ac1': '请从左侧组件面板中点击添加新的按钮',
  'c641fe0ae06e6c0547d89fefa91e7f75': '添加按钮',
  '88d1257b0cf667319085f3e0033b9607': '按钮行为',
  'f10f0be4aa9684eef9d78234072fe08b': '抽出式弹框（Drawer）',
  '4f02d2efe05a20232ab9da63c090595c': '发送请求',
  '5dfd5a78e2ba1bc8afb482a8745454ea': '下载文件',
  '4e58f9c94d345e14e2d69cc8496b7b5a': '页面跳转(单页模式)',
  '982db3084a2c470d1a9b34efa024511c': '页面跳转',
  'f20d9579ebdc9dfc30a212ae6cae931f': '刷新目标',
  '6a086902a84969a835423002718e86b4': '复制内容',
  'a56c9f13b1566009fdc7640bc551709e': '重置表单并提交',
  '30313d6fa06603604db18086bbcad9d3': '清空表单并提交',
  '0fc26855080a4219bbfad638029a795c': '跳转下一条',
  '8ba8a1bead7ca55554cff1c85246ae09': '跳转上一条',
  'c28f4d63beabc4833b17aaa10ca550db': '复制格式',
  '21fa07f18f80bee50695686831ae1286': '指定刷新目标',
  '8a089992349df754f182a5d02de8e8e0': '弹框标题',
  'b680997c50ca749acd4e6075cdca244e': '<p>对，你刚刚点击了</p>',
  'c3c8422fcecd8c1cc922cba15ab5cbc0': '配置弹框内容',
  '262c7c7b9874ae5607fb51da468d0e8c': '配置抽出式弹框内容',
  '5eb694a4252528628929ced97ca95823': '目标API',
  '84d38979bed546b93c4b3a399844419e': '<p>内容</p>',
  '0cc0fff6eb667b140d3fd06e34a8c69f': '配置反馈弹框详情',
  'deb9089ed7ebcacd712117fc4204c65f': '配置反馈弹框内容',
  '215f64480a93893fc56c51aeb5d40f11': '清空设置',
  '7984d95c01b725a2709fb8f5ee330fb4': '是否弹出表达式',
  'dfa07586a471e24b23fe68e11f5dc41a': '请使用 JS 表达式如：`this.xxx == 1`',
  '47186f00df86d3edad3b5595ba8c2a0a': '弹框取消是否中断后续操作',
  '4f21e04fe35d39c79e7779cdf2f4e232': '弹框确认是否中断后续操作',
  '0bbc3ec26c36a87c9df3183def6ca9e0': '是否用新窗口打开',
  'd0c3025a64b26e5fbf22005f400c06d7': '是否关闭当前弹框',
  '0c15a924dc3bedefb79c958972bef2b9': '确认文案',
  '06b13b11740f7663af325bf5426930ba':
    '点击后会弹出此内容，等用户确认后才进行相应的操作。',
  'fa9a0a79f29fef72e3060ea1af93c305': '刷新目标组件',
  '437d629f00e62cf99b3ad288f84ade46':
    '当前动作完成后，指定目标组件刷新。支持传递数据如：<code>xxx?a=\\${a}&b=\\${b}</code>，多个目标请用英文逗号隔开。',
  'b01f08bf5b9f8e3ef9d49e31d89bf770': '指定响应组件',
  'f667748a8e9717498da714d4e5087af2':
    '指定动作执行者，默认为当前组件所在的功能性性组件，如果指定则转交给目标组件来处理。',
  '80ddab8a52f74d707765501b0caae21f': '自定义点击事件',
  'babbd439bc04241ed3536f892668c250': '将会传递 event 和 props 两个参数',
  '867ade50f0bbb10bac65a5c3bc7895e9': '键盘快捷键',
  '45882ddedb42c1a38462949750bc8a84':
    '显示一个提示图标，鼠标放上去会提示该内容。',
  'ff88d5db9d61f14bce6e3397fd4652a5': '当没有值时用这个来替代展示',
  '348097cc50579e489f0bcb5433637d3a': '开启后可以根据当前列排序(后端排序)。',
  '9db64f772c11c614ee00bb3cc066f46f': '列分组名称',
  '19c4f5e98ad302574202de30dddbaf66': '启用快速编辑',
  '15c3796e07e33afc7252df751f610c5d': '是否立即保存',
  'ba5a0a1ff2c438ae7719ca48b0ce3af7': '启用查看更多展示',
  'd689e3c38fdb32c98fb27f8f35a26552': '查看更多弹出模式',
  '6e78b595d6a296938201a3c80660bf35': '启用内容复制功能',
  '65f7e01d58cb5065f49e0e8f48cc16be': '固定位置',
  '9ed8a4c5d1b3726121175dc986268b0c': '不固定',
  '0a5ac2f5c327e28c58d51db967a0e603': '默认展示',
  'a3bd2104e3df81df713de636b907462c': '触发底部显示条件',
  '986a5f50e946674bb91c9408fc974497': '总是',
  'a165f0fe5fb904049f6b6961105e433f': '手机端',
  '03ee8b948c9b34daca9584811bcca17d': '平板',
  '82d9f1f96084674e2b0101ecc04d5d58': 'PC小屏',
  'f4166de371b5dfb87efce228b17a3fbb': 'PC大屏',
  'f8cffd4d3fcdca390a3a3de24d209bb6': '内容强制换行',
  '4619988f1c5d9093dc00d1430633b2bd': '内部 CSS 类名',
  'bdd9d38d7e2929024089363dc8f48b7a': '列宽',
  'b198805e7a6b35830ba813f51db2fdc4': '固定列的宽度，不推荐设置。',
  'abb58b3bac0533ab4359ed977fa3c073': '<{{@1}}>列',
  '044892c0c637f2d9e78e78956b1ded01': '匿名列',
  '59ceff465ad16932d8972191ad815dfb': '页面',
  '54002bbf7eb3da8346dd4be61d642bca': '边栏',
  '49400a573b9f7a7bd693f84ec59379d7': '远程请求返回的初始化数据',
  '5d758dc5e33ba0122c256d80c1572e88': '下拉刷新',
  'd6fdfa4f989be6586a7a29ea85522f24':
    '开启下拉刷新后，下拉释放后触发（仅用于移动端）',
  '56e6db657d4775698984f883b71cb379': '工具栏内容',
  '4f9fa9ee5b0604d97da73e77fdbc281e': '边栏内容',
  '40fd4b2a194b2b1284a7f7f738b69640': '页面内容',
  '0e82bfaaec104a9f0eeb14820b42e7c8': '区域展示',
  '391555a3772260743f19278f01adf75e': '标题栏',
  '8d6b5924f187048cfa28d6c21fa6d2d6': '页面标题',
  '3ba265c6b63bde0319822afd6b9a649d': '标题提示',
  'e7f2f04f7c2b2e9e07b69767ea28d6ab': '边栏宽度可调节',
  '28d602809bd1dc6b47ceb38cb54f32de': '边栏固定',
  '36bded76593f98fab62453c7430b2918': '边栏内容是否固定，即不跟随内容区滚动',
  '4143d7418de740e8bc26ef4b27c63534':
    '用来获取初始数据的 api, 返回的数据可以整个 page 级别使用',
  'c95e748d5811faae5c52bdc07bee51a0': '移动端',
  '04f767eaa571383ea271432bee6deedf': '下拉文案',
  'e24bc5fd094c7c272725c6340d8aeb8e': '下拉过程提示文案',
  '0b3eef4f8a8061baa22416dc1e5dad03': '释放文案',
  '717b23399e04873441478fef1cc16d43': '释放过程提示文案',
  'f59b11ff84daeb60b027dc02490b627a': '分页组件',
  '7cfb5e222a5954e891fba6e3802c7ea6':
    '分页组件，可以对列表进行分页展示，提高页面性能',
  '9ed7d3adc032f6b78808e7f3786ec9cc': '总数',
  '8e60090c332693095d5852d92ee149ee': '每页条数',
  '0059d50e3f7b2fe7f1822ccc218fed66': '跳转页',
  '47c62ec10a240c35b8446cc923c5e8ef': '分页器',
  '97b8cf6ae269b6f6d75063073cd565cc': '分页改变',
  'b30f254eccefa14c9980235bcbec74f9': '简约',
  '41ed1e238b846005dfb0f087cbec644b':
    '选中表示启用该项，可以拖拽排序调整功能的顺序',
  '71fa2ba926cd032c0ebe0bcdd5d3eb10': '每页条数选项',
  '04519bf3bf428bb1a75938ac65dba040': '默认每页条数',
  '040f05137eb37e880873a763ff653fe9': '最多按钮数',
  '2ee8eb6b1a80a4b025f97fca6afb800d':
    '最多显示多少个分页按钮，最小为5，最大值为20',
  'cd6f79e7bff1337c12c86a15ceedd6da': '面板',
  'e04a2f1662121e5a3c397d496114185b': '展示一个面板，可以配置标题，内容区。',
  '4e9bb0326ab4d3a2af94d901c7f1b6a7': '这是一个面板',
  '5bf5f1fd54476671bd6fd9d97e3e6b6b': '这是内容区',
  'cac3ba71180c97b1b6432833b3417d2c': '内容区新增内容',
  '1fae678397df046c1754092f15a71d98': '固定底部',
  '68744acedf015b8cfc445af30583815e': '内容区域展示',
  '5a0fbcaaeb439684bb4ae5be579e4cd4': '表单展示模式',
  'f99d7e5f15906ca78c45753ee3b04a8b': '表单水平占比',
  '9970ad07468267e2f309f1467c75bb80': '主题',
  'fbae87bcc352f6933541fb77a07418ed': '主色',
  '540f84ddc0883866b229f71c2844199a': '危险',
  '65810a32a98f09be550b0c421df6c540': '头部区域',
  'f3b9889baa6d17ec63f05ea7d326bcfa': '底部区域',
  'da71dcbb13405815476cef28a8b9c4f6': '按钮外层',
  '6c5b1f0e8e361a801fa75da070d1cba5': '用来展示纯文字，html 标签会被转义。',
  '67e77a196826a8880e47ad949ce08ac0': '这是纯文本',
  '83077805e05ac6bedad69b47fca4462b': '进度展示',
  'c77048def6e8a2d6c556a3fcc9c66730': '进度条、progress',
  'b1aefb18d4bf96dc283a26d67abc41a8':
    '用来展示进度。可配置各个进度段用不同的颜色展示。',
  '2dde3029c4170a1c8e961a90766e0194': '继承',
  'ce179eca04fab0d584506b0d19736836': '线形',
  '2db0fcd5342b479688fd999a0108ef5a': '圆形',
  '3fa8b34a2744f62fe93dd599a275af39': '仪表盘',
  '1ca3fa86d8faa46cc75792bcf90d7fff': '进度值',
  'b0932e5bb7f7d95e3636d82d1a039854': '无数据空位提示',
  '940b12c19fcf7aced0cdd164edc9acbc': '占位提示',
  '9cf66e7783e4c9b1d74bcd411edb6950': '数据字段未定义时的值，不包括0',
  '2f0faae87508471abce4384b60900e15': '线条宽度',
  'bd9b3f7e564c9eeaedd72f1bcbb7fc9f': '缺口角度',
  '31a088147dc0006e4775d066980fa688': '缺口位置',
  '77a7419dd4fad70c3f3e4b075b2c3fcb': '显示动画',
  'a04564aaca658461333b00cbcd071518': '纯色',
  '0a9fc7083e2768107fc178ff36f29ba0': '条纹',
  'c035fb9e67c0b566fd5d35889035424e': '分配不同的值段，用不同的颜色提示用户',
  '23bf030ca760141f317dde1b7f49b11a': '属性表',
  '44f2bc36dacb88424dabf9df71da0e77': '机器配置',
  '1cb82ab4f259d5b75da0ae86583b31ff': '其它说明',
  '9a4ca43777061ebc91bc64cb994957bc': '每行显示几列',
  '17fa61e1da428936a31b51c955a99d65': '显示模式',
  '7fa5c95b26550753b0931fa21cea5d10': '属性取自变量',
  '6940ea5aa4c18e105cbcd32cbe410839': '属性列表',
  'ae41a992ccceb36f83024f72531186ec': '属性名',
  '52dff5b153bb5eaca33a008458ce0209': '属性值',
  '42c3762943823c37b537a10c09765822': '跨几列',
  '22b03c024d815ad327e8b95d684ced38': '二维码',
  '1857d9050ac0527374f4324c0a5ad910': '可以用来生成二维码',
  'e71377bb59c70af683be127ec49d01c7': '二维码值',
  '57eda9139c0b3bc0605ed4cf303ffbd2':
    '支持使用 <code>\\${xxx}</code> 来获取变量',
  'f13c3e0717ea842cddc1402e8dabfb6b': '复杂度',
  'c4bde7dba8c6eed0ca0165b071b259bf': '宽高值',
  '2f97db95d75280bfedc5afa72d2c717d': '背景色',
  'ebf2453eddf55441b711d187f3872ffe': '前景色',
  'd5e6d5c44426a82e18e31eadf7f1e09b': '一般用来重置表单数据到初始值。',
  '6a06f12bdf3fc96df6cb45467b9a7c2c': '服务 Service',
  '9cc50da6bb17ea7ecf44c254c9b37619':
    '功能性容器，可以用来加载数据或者加载渲染器配置。加载到的数据在容器可以使用。',
  'c5a33208cf3deab68cd9fe34679edff6': '功能性组件，用于数据拉取。',
  '54d99a6a50a2373055c0f66ab727a869': 'api 初始化数据',
  '7a0c222fcaa42473d1c75c113c3641b5': 'api 初始化完成',
  'acd4bd22755a537431d74b28c3c2ad67': 'schemaApi 初始化数据',
  '5a7d03912f38d0b24d04b28da40864a6': 'schemaApi 初始化完成',
  'f885d4055567877facf0a3ff376a114e': '重新构建',
  'c8f0e77a9eb5de26e6ab62695d8494b6': '触发schemaApi刷新，重新构建Schema',
  'e22855f53b7a1ab33e920375c0cd0e3d': '变量赋值',
  '8b10146a8a896b890b3796eefcc3c6d3': '更新数据域数据',
  '47d68cd0f4c3e91a86d23afe8afccfb8': '服务',
  'f754888421621d122c110d83e710e9d3': '添加内容',
  '31f8a7a967286a16eb404e3ba237619e': '数据接口',
  '54f876a529283de5668426b2dc8adb15':
    '设置 service 默认提示信息，当 service 没有返回 msg 信息时有用，如果 service 返回携带了 msg 值，则还是以 service 返回为主',
  '7fa237c1b62d04aaec0144d1fc89d620': 'WebSocket 实时更新接口',
  '9dfeacc54ab6cd6d2ac08df387777f9e': '数据接口初始加载',
  '98223d478e88ccbc2406412a46dda8c2': '静默加载',
  '71ae1c76cc4160f8fb76e404e35ca08f': '设置自动定时刷新是否显示加载动画',
  'b897babfafd35cc5d6e66470115c93cf': '停止定时刷新检测',
  '11bcbff684dfe6edf36e1fd1adc5ba30': 'Schema接口',
  '8cc3239eba9fe65b99242adb33634b33': '内容 Schema 接口',
  '3f423669b0ffeb3993b95085cd8a111e': 'Schema接口初始加载',
  '8e8aaafe8db0d8eb05e3b11550cbabe7': '全局配置',
  '7481babe858320dd6a4adcf307fd151d': '自定义函数获取数据',
  'bfdee34e14602e3113c88a9145843e86': '将会传递 data 和 setData 两个参数',
  'fb559ab354303d1927dcd9f0f2dffa23': '获取成功',
  '56f0a1c0bc0408556c5810ea4f219dd4': '获取失败',
  '29326bcd28fb39bd41e54242fa532c85': '走势图',
  '270301455c3de762a7e2b145dac7a8b4': '用于内嵌展示简单图表',
  '44e13bdad8c7eb6391e84d940513b927': '状态显示',
  '82a3047196be368be13dcdd2373520ff':
    '用图标更具关联字段来展示状态，比如 1 展示 √、0 展示 x。这块可以自定义配置',
  '774b2bcaca8a64f46c84b510cec89109': '图标配置',
  'b698a95f1e217e5465835ee0d23b1b1c': '配置不通的值段，用不通的样式提示用户',
  'aacb2b36c47395e4b4b409e351eb4279': 'Steps 步骤条',
  '863a8583132d087e57aebb7d89e18a50': '第一步',
  '9757f2c59c17e9aea46e0c8adb69597e': '第二步',
  '207e30c0e7318027d521dd7c6fab6a99': '第三步',
  'b8a2d347bdb22fde367a851df8335771': '步骤列表',
  '59cecbff0cc77511590d2161cc3058e9': '当前步骤',
  'f3f08da7f65e1d5596a66cedd7caeb9a': '以零为起点',
  '6bf1f392c0a404d1f7558e6dcdd6c2e6': '当前状态',
  'fb852fc6cce168301447d1baff276dc5': '进行中',
  '8797922788916874c39ee1524bbc3638': '等待',
  '769d88e425e03120b83ee4ed6b9d588e': '完成',
  'ad8e01fe719bf1a5af82ee0d100d246b': '出错',
  'd1f03ea8d9d3c3a241e8a340b8a384d3': '获取步骤接口',
  '74f0f6730053049f4c9beca2ab00c193':
    '用来提交表单，要求表单验证，如果在弹窗中会自动关闭弹窗。',
  '7b755ba413eac50423859395c68e6280': '表格V2',
  'ea2b32f5d78d2305b9b7bc21e056a009':
    '用来展示表格数据，可以配置列信息，然后关联数据便能完成展示。支持嵌套、超级表头、列固定、表头固顶、合并单元格等等。当前组件需要配置数据源，不自带数据拉取，请优先使用 「CRUD」 组件。',
  '257f5a3886d87d2255206f86b880d07e': '选择表格项',
  '6130b1f75d624b2f73f5d923492e92f7': '手动选择表格项事件',
  'aeddca0456d8fe520dc95545a83458e9': '已选择行',
  '4f907cb94921bb62a8399adec922bb60': '未选择行',
  'f3d21138c8ecf5683503c4f814cc7199': '列排序',
  'd84464cfb2a5828a200fe9c28a323122': '点击列排序事件',
  '652f155e644e82ebb0a1aed97ab6ab23': '列排序列名',
  '460e3a697d1680445a47139c0816fbe6': '列排序值',
  '3d0b957a99d0c366612c01913e17a0c7': '列筛选',
  'b35963687361af98e6acdc004e87fc3c': '点击列筛选事件',
  'ad11fba3ac676233f3105e76e7de0501': '列筛选列名',
  '8e4b9c88c51aaad1a28a28e8b536697f': '列筛选值',
  '93a5a0253f11e3a2e58f4e87a52fb094': '列搜索',
  '6d4c4990ab2c32efe8a17c5f22e10cb5': '点击列搜索事件',
  'a80a4486100baf3f45fab3a59e4a816d': '列搜索列名',
  'b9a565fe1dc488efae1d63464f277f09': '列搜索数据',
  '85ddd38957256b6e9026f42ed570bc35': '行排序',
  'd7a66def82af88cd5d408e38feb8a65a': '手动拖拽行排序事件',
  '1987561c006c7192ab619f81103d2a2f': '已排序数据',
  'ecfebbc91e2c18a512aeb11b7da15193': '列显示变化',
  'c94f45773a42dc386b9c9dcdc6fa542b': '点击自定义列事件',
  '70567329ee851a5ba7e7301bd8e9d9a1': '当前显示的列配置数据',
  '76e47871d654c3b0b0e301c0a076e55a': '行单击',
  'cc13521eab2c7423b3fb857772405cc3': '点击整行事件',
  '2fbbf5c38b66ac5496ac42246bbe9e0b': '行点击数据',
  '8eb3c8b16106e5487cd1fa3b8a1342ce': '设置选中项',
  '908cc16fe4f7972450167e26276ac726': '设置表格的选中项',
  'a2b39e5a8b5015234dcd8e07a2e00e3d': '选中项',
  '366a3c07289bd6efb7c2a182f7a12772': '设置全部选中',
  'e97c09cd119b64ae0a8dfd42a1d449cb': '设置表格全部项选中',
  'c3e8652924c258e121eed16414d3a9e5': '清空选中项',
  '7619ec29c0a854dd49e0a7a47bf1a127': '清空表格所有选中项',
  '642e5368b742c82472e2ef35114459c9': '表格数据',
  '8369004103635f8e75026217ebf237da': '绑定当前环境变量。',
  'bd3e7a1b636e4477a4ea59922ed2cc1e': '显示标题',
  '11d00f37d934b2464f3258952a398626': '表格标题',
  '4e3cd1a7b193e2fd3458278d10c530e2': '显示表头',
  '023ff3530e48493e653eb48e958a4eb8': '冻结表头',
  'dd9b85b2cd13ca724afd1f43567abdbf': '显示表尾',
  'c89b5fd3b706a17feb016d93c80e34b1': '表格尾部',
  'a5baa4818b14f4680955aa34dd559d02': '内容高度',
  '1ef8fd21130d17cb7c1613eaed6ca1e4': '固定',
  'ee18dc475df8654cb13ad67dd84eec28': '高度值',
  '8bb5781dc5f2745e6356cdc5e6d76b16': '内容宽度',
  '88a364068f684dc77aca5b6c006ef576':
    '当列内容过多，超出宽度时，可使用横向滚动方式查看数据。',
  '6e3d35b57c29b1b419569cc55b3a5d33': '宽度值',
  '8ce1cd75b6e9c0c0e3468589fcea822c': '占位内容',
  'ba4f461832cbdb7fbdb170fc9c1db647': '可调整列宽',
  'aa2bd9f54608c0c85d3ceecb707938c9': '用户可通过拖拽调整列宽度',
  'ed85be57262e5a0c3116293e88278fef': '行设置',
  '5a431ad16d8f7f23fac3be5650e51caa': '行高度',
  '3d2ac2fd2c60931fff1db814662334c3': '请选择高度',
  '85a49c5ed4628647f2ead9206224dba3': '跟随内容',
  '4296d7d293c9ea4a0e52c6415f0b5c96': '高',
  '1f1ca9df5fa3648c718ad04649888943': '可选区域',
  '596171970b639a35dadde2aa930d666a': '整行',
  '388855093d17f3df43ff80242d7a1bed': '行禁用条件',
  '46705a530ba9721527a4202bae7091bd': '选择菜单项',
  'aab57a3547a451f756bb8231a1eee8d7': '反选',
  '76159d0d1261c0b6c310901244457e36': '取消选择',
  'be4751b0c9adf1d8deee45226c6124ee': '选择奇数项',
  '49f4010dade8652e5aff6a2c67aa23a4': '选择偶数项',
  '49b4aa407b91ac997e27314e30c03110': '可展开',
  '8c1f5c49de09adab9a0e0c39e0106f78': '行展开条件',
  'caca6cb58342bb604483d94f49515234': '可嵌套',
  '5bdff9fd07d2a2430ac50e1559dbee27': '可拖拽',
  'dce5379cb978a8259ecfca8f08f00817': '隐藏',
  '33eaf97ecb3465754855e847f14d129c': '快速保存',
  'ce7d31d64f2315e1d4cede288b9dfc60': '快速保存单条',
  '927b639f244953f237cd12943c8b815c': '横向滚动',
  'fdd59ca00eba17d4bfebf744056ce4ab': '嵌套缩进',
  '6bfd4423d8aebbf0cac7ba4d74f245bd': '选择列宽度',
  '21922c6479665dcba83106f8e9ffdf68': '固定选择列的宽度',
  '31b7c58c2d9a170829b90314ff98b66a': '展开列宽度',
  '81ccf26d9622d139a13ba2a61bd9fea4': '固定展开列的宽度',
  '68e9249db7bd12ab17994b1761b049f5': '自定义行样式',
  'ef0c5b6fa16497343eedb76171d61d68': '展开行样式',
  '1a7bd457c08093cf2cf887403dc249d8': '展开行内容',
  'b94bd878cae4ddc567b00a2dc4f21d74': '开启排序',
  '5dc3017c21ae2e31ab127dbde8ec80e9': '开启表格拖拽排序功能',
  '25915fb58615ba9a5e145efa252fec30': '自动合并单元格',
  'd7d0936858fcdf4ffdb2899451ec74fc': '设置列数',
  '80174cabf025dfe269aee5390b813708':
    '设置从左到右多少列内启用自动合并单元格，根据字段值是否相同来决定是否合并。',
  '14921c3831512876f6d58b2a497b86df': '展示列显示开关',
  'cc42dd3170fdf36bdc2b0f58ab23eb84': '开启',
  'b15d91274e9fc68608c609999e0413fa': '关闭',
  '6fe0b71c07a5ce5f7a09f7fdb1d63827': '自动即列数量大于5个时自动开启',
  '2206c0e11aa5f4f154aa9e5dfffcb474': '是否固顶表头',
  '5006fdc5659989e42c3855c17c57f878': '是否开启单条底部展示',
  '9db359f376a1a588ef7dcbef53cc114a':
    '如果列太多显示会很臃肿，可以考虑把部分列放在当前行的底部展示',
  'df74194830e695efbfce16c0c64223cf': '底部默认展开',
  '94be543c4fd399f0839211464c8583ce': '第一条',
  '9a7b52fc8659f1786907fe93efa85bf7': '所有',
  'b07deca9076bd3354b1b2709d58d725a': '不展开',
  '76b3250fb1e8593fac075b64029300fa': '行高亮规则',
  '1fe38acb67e766f7767d9f8e88bfe990':
    "支持模板语法，如 <%= data.id % 2 ? 'bg-success' : '' %>",
  '734ebf8b33422c456e937fc27c9a16ce': '外层 CSS 类名',
  '320f489db3dade075d69f155b346f98b': '表格 CSS 类名',
  '08ca0d502abc4336855d837f281caef4': '顶部外层 CSS 类名',
  'b6469055adf2b7bfb187f74ae17dfe54': '底部外层 CSS 类名',
  '39ff38577b97cf98fc130f9bd596d4c4': '工具栏 CSS 类名',
  '4ca07911d10b74cc7c357b510e7cc948': '列字段',
  'eb2719a7e6cebda7ca234560f21fb448': '列标题',
  '7ad83bbe4646a0011ece7fd338d1e189': '在标题旁展示提示',
  'f35c90b504521a2da346960b9db23828': '当没有值时用这个来替代展示。',
  'b4521626a48dcb61001fc563d2433ed3': '可排序',
  'ac83dbca40c9d2151b5f7d81795535cc':
    '开启后可以根据当前列排序，接口类型将增加排序参数。',
  'af9cbd3988196fc104af4fed9461e152': '可搜索',
  '34dceb7c51000849ea3596fbaab6f67c': '配置列搜索类型',
  '9a899d9ab83d8ffa6308fb31e93f23a1': '快速编辑',
  '4562be5a3f9823a5f61b25f8d14b2b43': '修改立即保存',
  'c115f372bcdced1e70824bcbf42b5923': '开启后修改即提交，而不是批量提交。',
  'fba91204d335ae6eda35809023a94f7f': '配置编辑表单',
  '90ef7c485bd31fab681c6e9d9afd5be8': '查看更多',
  '71c0319fce9f416330b18e554e0acc55': '浮窗',
  '64035b04a21bc337a351b5a2a5d12acb': '浮窗位置',
  'ea71a81cf874780294d517b0314feada': '配置内容',
  'ca60c525372028b9f75ee4c708cccae1': '默认为当前字段的值',
  'ac04259507be8ba6b891dc9dc208f491': '内容模板',
  'c9f7324519225bc72fce24a09518a8a8': '合并行',
  '9af45e91a08b54764610ada28272d590': '合并列',
  'ad96280bb24a38f9a83051b16ebc9688': '自定义列时默认展示',
  'eb58b078f2f8560160ebf87bc7109de9': '内容超出换行',
  'f1f4c88f30744f2365b65f1790c71da8':
    '错误的组件合并对象，面板过老无法处理，除非增加新面板',
  '175e01917d9d4891a40eab43f4487030': '表格视图',
  '9fe8304dade75a37bc04f45515688325': '表格类型的展现',
  '2560b304e691da78ee2e02f5af9b494d': '地区',
  'f7d29dfae05b5d049b64b040b14d9a00': '城市',
  '44e7ebb4007104495dcb7afbbb6778fb': '销量',
  '3f0cb8b8c238c3b4e08898ce6d449c8d': '华北',
  '692e92669c0ca340eff4fdcef32896ee': '北京',
  'b8b75a5f9109919ff3f67b336b62afe9': '天津',
  'd1c2b2d68063b4a57af61e3027861cd8': '视图宽度',
  'b32ec25f2bdf7b2eed5e947cf82a4fde': '单元格默认内间距',
  '8a42ded5c9d58f3dd9e3a8968ec04b34': '显示边框',
  '9b4bae5d8251de0b6f00b704936b00d3': '边框颜色',
  'd273f56b3e598e794c3480f1e62f3ed9': '单元格 {{@1}},{{@2}}',
  '466c65230ac92494c3af79757b4b78aa': '行 {{@1}}',
  '1ebd0cd417700f3f4a7ee5f64518fcd1': '单元格',
  '4d775d4cd79e2ed6a2fc66fd1e7139c8': '显示',
  '7ec907e7059b758ace2f3adb9bb803ff': '文字颜色',
  '104711e38d3cd9335dbd1f4301178edb': '文字加粗',
  '65194da33aa3aa1d0fd08b5690af6f26': '单元格宽度',
  'fbd9998b10e690230b0f2fa9b24087ac': '单元格内边距',
  '0bbc2ea4e1d1f23feb576de5dca1ce3b': '居中',
  '4745afe0f89f665e41adf819da5df1b6': '水平合并列数',
  '7c2e1f863e86715e892f61a54e558b20': '垂直合并列数',
  'd17020cd3e6564f5154cf2251cd30f52': ' 行',
  'db439b129f3143e14a7024f08ea3732d': '行背景色',
  '30d6ed36667cb98165f07c59702754ea': '格子',
  '49d45317662097180e27fa53235d9b13': '找不到对应的 td id',
  '013326241579b9b2735756f2204bf8bc': '第一列没内容',
  'a896691b72032fe21a00b6487381a529': '左侧新增列',
  '24e4bbbf29a8d31e711c8d9366bf1a6f': '下方新增行',
  'aea2dd682bc9dc2974dd971581148459': '上方新增行',
  'bac058b86f8fd4e8f5e2ef3807799aea': '右侧新增列',
  '488d0742c010851e9c6ce3264df9542b': '拆分单元格',
  '9377e388f7189d6103a3985a321115c8': '选项卡',
  'a8b1273cb2d53ad858906ff9744a9891':
    '选项卡，可以将内容分组用选项卡的形式展示，降低用户使用成本。',
  'f78416dbd6c6a40a3ecd1c1f2b0672c8': '选项卡1',
  '9769ee568100b0c530a06ec3f0c0044d': '内容1',
  '4be268145385303e8ebeb480458a380e': '选项卡2',
  'c50159e2acff0f4ffdce4c67ec3513a3': '内容2',
  'c8794c58d0eb020ca40905d1904d88b2': '激活指定选项卡',
  'fcd3abb110aab48ebd0ac2a1d7040d6d': '修改当前激活tab项的key',
  '9d000284174ff09642502803887f28ed': '激活第',
  '29645b509093191cad34c673c1b3efb7': '项',
  '9578012b7d75a3a47c76acc176caf403': '激活项',
  'b744b72fd649d904e561358fc26c455f':
    '鼠标移动到选项卡标题时弹出提示，适用于标题超长时进行完整提示',
  '6e7fe62a865cb9ae90e52f85f4c6b8a0': '默认选项卡',
  'a8986713e2b83c9ecafe5b107c09b68e':
    '默认显示某个选项卡，选项卡配置hash时使用hash，否则使用索引值，支持获取变量，如：<code>tab\\${id}</code>、<code>\\${id}</code>',
  'acb839aac679bb34be9b4b49806b74a8':
    '可用<code>\\${xxx}</code>取值，根据该数据来动态重复渲染所配置的选项卡',
  '5b9af1bc3012bb3c8e07b983b423ec17': '激活时渲染内容',
  '4bcecc15d16e8c851dc3d155b8f30929':
    '只有激活选项卡时才进行内容渲染，提升渲染性能',
  '12c6a62683d63e404d71a31c9cb5209c': '隐藏后销毁内容',
  '5b52bcb62a2e1dab99ef841b05395b6d':
    '激活其他选项卡时销毁当前内容，使其再次激活时内容可以重新渲染，适用于数据容器需要每次渲染实时获取数据的场景',
  'ecfcea4b381d761fecd512761bc07954': '线型',
  '3d7443aeba7c8eaf1cbb42ad5232fa10': '加强',
  'ee5e5a588705699a51eb3c5778c3020a': '仿 Chrome',
  'c5b8044dacf2e63931d85e5e307a9168': '水平铺满',
  '22d18bf0c476ebe7aa9303108677ff2e': '选择器',
  '5bff38cb05e3710a0c0cb16ed3ced78f': '侧边栏',
  '593c1c61592f80831f58b2e44cfe63fa': '标题区位置',
  '030c8cb75e9707285b28c4931bfeddc5': '标题区',
  '32b4bc87bf7f95cd6094992f0135ff7f': '标题图标',
  '996a919888ae86e842c76245daae2360': '设置后，会同步更新地址栏的 Hash。',
  'f3051dd9b3538e170322fd5224b28de0': '激活时才渲染',
  'ee51f2d49fa12c730d2a0efef0d67e44':
    '当选项卡选中后才渲染其内容区，可提高渲染性能。',
  '024f24defb08c5c9d463a2668cbb9802': '隐藏即销毁',
  '370bb4d6806c88a7df2ac17ca2a7b6a6':
    '关闭选项卡则销毁其内容去，配置「激活时才渲染」选项可实现每次选中均重新加载的效果。',
  'f6724322c613ca164ea9a9d03e9055c9': '卡片{{@1}}',
  '8a471486c6c7bbe43e14392c6b127aea': '异步任务',
  '4f58f808d62c4e31c347e483898396d5': '用来做异步任务呈现或者操作。',
  '9ee043b0a77a26d22eec0f4ea99afbd3': 'hive 任务',
  '3709f71c9552ed5db76cbe8f3cb5d4be': '小流量',
  'c60ad696dee4e1eeff6f0f2c2e9b9fc0': '全量',
  '5c0dc424442c913c6d16a2cf43137da4': '初始任务信息',
  '78caf7115c5140f8913c581920239f22': '任务名称',
  '3a3778f20c0e1a55adafad4861a71216': '任务ID',
  'bc7e74f7ccf8ed6fa5b7b7649b221daa': '任务状态',
  '1d35dcbf191e36dcc6c15f71277d72ed': '任务说明',
  '76ba17faedd82297d09b2edd70c5914e': '新增任务信息',
  '093bcd735847b8464d683464165adbb8':
    '可以不设置，如果检测接口返回这些信息的话。',
  '7dca021cccc260dbe1d81dfc6b29f513': '状态检测接口',
  '358e55678114f19424efbb42c0a927d9': '定时检测间隔',
  '77bd60ba17a73ede5d81c4eeba0f830d': '提交接口',
  '1e057692fcf81e07e20b5f7c9073ea35': '重试接口',
  '24e3562a3262e80c3119f22b8f447f64': '任务名称栏标题',
  'cb8e07cea4df337bb6dcb8362b5f7e02': '操作栏标题',
  'f2acd3adcc0a0d73b318c83a29a4d2a9': '状态栏标题',
  '8a4cf07caf84c91a87e8ff3c48a944b9': '备注说明',
  '0cbbb89050458c2bcf0ca98c19dc8864': '备注栏标题',
  '879eb99c7b1aa3223925b9b2dbad4c63': '上线',
  '804b6382fa6d8b53c5a2a409f30f7fe2': '重试按钮名称',
  '132c5cdcceb0f1f17c8c088a42959aa4': '重试',
  'dd4e55c39cee201b82dbc9cb2aca173f': '未开始',
  'c0d2181d579cd1e965ed10d5183b1fc0': '就绪',
  'fad5222ca0acfaee54f06458188d916a': '已完成',
  '7a4b9e6f14bda48d2c3bf0fa431bd2b3': '状态标签文字配置',
  'f198581dbbc357ccc0283cfe02d56edd': '初始状态码',
  'd6bab2368de31490741ed95f732aaa25': '就绪状态码',
  '6eafca9359acbb0bedcf86d6b8609e41': '进行中状态码',
  '7e8b2e41a303cb8532b9ad2006da3c25': '错误状态码',
  '231b6f799949f9a743d5193006a15af7': '完成状态码',
  '003797f6b66c67cd87ec684cacb4ab70': '出错但可重试状态码',
  '89d19c1fda4906bd7a336895835ce20e': '按钮 CSS 类名',
  'c52b46333f6d5d2796ee64cb359cd58a': '重试按钮 CSS 类名',
  'b091a100499d48dd4ccf0b982aa37d68': '状态标签 CSS 类名配置',
  '7ac24322bc8eeac88db6823942423ac3': '时间展示',
  '82315a0cd63e7f81233ad804e0d02deb': '时间数值',
  'a389ce9c52a94bbdd5c7fa84af85348c': '显示时间格式',
  '7cef725b75da8afecda59900b780be75': '时间轴',
  '8f32bcb8d5baf6cbb410ef3f6dbed8d5': '用来展示时间轴',
  '1aa46d7cdc632756dfbf16c55436bcdf': '节点数据',
  'c360e994dbcffdf31e86d2d8875370e1': '排序',
  '825f53899a11e598fc9f9b43e0814a58': '正序',
  '8eb4b7abb66f0922778a39044b42d345': '反序',
  '0da2ab28a6e03922d4a0c78451146b87': '时间轴方向',
  'd1b490c01d24a1a70e9c83a29ac38fde': '文字位置',
  '00ff9356c34d05ecbfd6559a46f56e25': '文字相对时间轴位置',
  '71039986e2386573ab6e5681986c2230': '两侧交替',
  'c5d48d5732c64d5dea4bb0b4aaf13813': '请输入className',
  'dd438f6fb09d6223fd95df16383f0f44': '文字提示容器',
  '33813749a95477897085e2435acc16b6':
    '类似容器，可以将多个渲染器放置在一起，当用户鼠标悬停或者点击容器时，显示文字提示浮层',
  '9b14c9051067bef2dd9a15683201dd18': '提示文字',
  'ab97cef55407efa11f79211e17cb2b4b': '默认方式为”鼠标悬停“',
  'b47707f0e916e3e3f4ba885bc2cf2c11': '主题色',
  'a32b3b848eee6929634dfc9a9d8bcdb1': '亮色',
  'adb7e5312abdb9a44297e48d63815fa3': '暗色',
  '0de826c66ae3fe8043e9a39b35616ee6': '容器内联',
  '4583d3453c31cd3ff068c1358d8e7f1c': '点击容器外部关闭提示',
  '8e290c31bc0b4f76edbd58c3575b8420': '浮层偏移量',
  '03c87fc8d49f865f0c2895d9ef3fe352': '提示浮层位置相对”水平“、”垂直“的偏移量',
  '4e3e1e12e701890f4461808cc8f9d407': '可进入浮层',
  '9d6246f57f6924410b0c68f2172420f9': '关闭后鼠标进入提示浮层后也关闭浮层',
  '90919000a708f8d66b7591e21b8e33f1': '展示浮层箭头',
  'ed81f127f3b2aaff73a4f4dd5968fdcb': '关闭后提示浮层不展示指向箭头',
  'b60e5222037939812dabb7da9979c27d': '延迟打开',
  '82bb338503938f2da52e91f7244a34a2': '延迟关闭',
  'd55c726c99995e106ba5f3bb2b791a86': '内容区CSS类名',
  '0ce2605d7eed6782adb6bc62ffae2335': '浮层CSS类名',
  'edb5acdc9ee5e75fbc238ab4a0300eaf': '文字内容',
  'b1f824deef0d11e1fe3b73167a902e31': '文字格式',
  '74cfa7e77be335e8e5489a00ef099cb9': '普通文字',
  'd482086f653d92fa8f1011d39738dba3': '段落',
  'ae27115431c46fa374ac28200304f341': '一级标题',
  'de86106d6632da3fafb946f85ba91324': '二级标题',
  '27f2c5c60f373a4380ec107ad8895f0e': '三级标题',
  'c961174771e843ac4046b2b21c49424b': '四级标题',
  'a791de104833c917f801c1976b9af960': '五级标题',
  'de2687f9a16fea3654be84c74137b805': '六级标题',
  '65862c97143c6c5479e0c623093a25d6':
    '用来展示文字或者段落，支持模板语法可用来关联动态数据。',
  '38accbc34901ee6fd7bd7cd9f92f0a2a': '这是模板内容当前时间<%- new Date() %>',
  '590e147e49735ebbfc51ae2175c36eb0': '请编辑内容',
  '47ae015d04dda362d066e4f9ac09d647':
    '内联模式默认采用 <code>span</code> 标签包裹内容、非内联将默认采用 <code>div</code> 标签作为容器。',
  '7fcf42edf5817042904f4a122ff77582': '视频',
  '1d5bbe0ab0d90a223c162fb375997a98':
    '视频控件，可以用来播放各种视频文件，包括 flv 和 hls 格式。',
  'f50bc38cf567e68250a8d15edfd8eb27': '视频地址',
  '733dda7842619a437b2486f6f71b0c10':
    '可以写静态值，也可以用变量取比如：<code>\\${videoSrc}</code>',
  'fb103fc64a0caeec24c707b9e7b50870': '视频封面图片地址',
  '828f83110677bab8ef940f79f77b9049':
    '可以写静态值，也可以用变量取比如：<code>\\${videoPoster}</code>',
  '351cb1f8ffbcc9d2d4c1f35505e15864': '静音',
  'c7638cec349cb86eaeaeb983909fae0e': '直播流',
  '82f5f7bf3fb529360947cbb3b988037f':
    '如果是直播流，请勾选，否则有可能不能正常播放。',
  'df0134afa26415a560ae1320dee10c19': '视频比例',
  '0d682d277649d8c7952d36f836619a44': '分开显示封面',
  'ee1600dfbd6f9e86ca8761cf3fcf6a17': '视频速率',
  '8e7124c3069460d4a8a04c3e3d9ce752': '视频帧信息',
  '1fc7d723b3a82ce32bfbbfa0a1761969':
    '比如填写：<code>\\${videoFrames}</code>会在当前作用域中查找 videoFrames 变量，如果是对象，将生成视频截图列表，点击后可跳转到对应的帧。',
  '1fa035e78c5408c9079c20637acdb2bb': '用于渲染 Web Component 组件',
  '8df7c8a1f9f579f0ddc35bb4ee50f166': '包裹',
  '5acec91385a3b9093e3c803f6b0d869a': '向导',
  'c8c0339a6f9e105cfef45b76b788b635':
    '表单向导，可以将复杂的多个表单项拆分成多个步骤，一步一步指引用户完成填写。',
  '155149d24d20197bc0836bededf63abf': '点击完成',
  'd384a3c931bdf315e4760c9fc5980e6d': '最终提交时触发',
  '335d6c56c43204f9efcefe36f097d35a': '提交的表单数据',
  '5be9e21eb0797c9faa053eb0237c36f9': '步骤切换',
  'd837ddaacb39a13806590da335e89397': '切换步骤时触发',
  '02f706d7510e68c96aa073852d90ec20': '步骤索引',
  '33c627bce8015c50152941a5b6fada32': '最终提交成功时触发',
  '574f27f7223c86545a7724d18da96651': '最终提交失败时触发',
  '8a427977d8135a019e4f1056120bfad2': '步骤提交成功',
  'd65dcca33a0118a0a5ce343264192ea6': '单个步骤提交成功',
  '5d7d91d9da162ee8fddd4d331d1434b5': '步骤提交失败',
  '1c11d38e7ecf2facbf82f772d9222d45': '单个步骤提交失败',
  '357954d848a9e2f12208673b3906a972': '单个步骤提交失败后返回的错误信息',
  'd6c21651c32c63c8d61c85944c2c91af': '全部提交',
  '75e3dc4be4ae7aca7e1cebc13f7e486a': '提交全部数据',
  '0c3005b490ef428660ca2265a714bdbb': '分步提交',
  '832efcc5c30746b84b910cde8630d491': '提交当前步骤数据',
  'eeb6908870e058bc23d52c1e405a054e': '上一步',
  'fc5dbc5789e158384f634eb8ff466b46': '返回上一步',
  '38ce27d84639f3a6e07c00b3b4995c0e': '下一步',
  '5fa57cab26a9d4e659c2e497018729ef': '定位步骤',
  'fce22163929e8191b7de43699316f2a0': '切换到指定步骤',
  'd529f7d5ed8956cd890173b4b5045a67': '切换到第',
  '4a0ff5106d129883b446a29b1dac6f47': '步',
  '78ada959bf5bdd6c70ee411c4cf23601': '目标步骤',
  '098521c027a49fba5eb7f35430a6a9da': '步骤设置',
  'c5538d5c74235d2988e51e632c4eed0b': '新增一步',
  'dda36edbd4626e7fc868c14f9aa1556a': '其他设置',
  '1fd41e410930ac58e748f7704c3a05f3':
    '如果接口返回了 <code>step</code> 变量，且数值是数字类型，比如 <code>3</code>，提交完后回跳到第 3 步',
  '938b484df1447d8f01f96e45125eb031': '是否可被点开',
  '20022725ac2c53869f7af6646ca4ba29':
    '用表达式来决定，当前步骤是否可被点开。额外可用变量：currentStep 表示当前步骤。',
  'd4b8306441c00f01d4f044b3802c4266': '起始默认值',
  '37c0c041a0ad487d23c9f42c29f6d5e1':
    '从第几步开始。可支持模版，但是只有在组件创建时渲染模版并设置当前步数，在之后组件被刷新时，当前step不会根据startStep改变',
  '076bd7c0adfc4f5d2abde6b309d9f53b':
    '用来初始化向导数据，当接口中返回 <code>step</code> 字段时，可以控制默认跳转到第几步，注意数值一定得是数字类型。当返回 <code>submiting</code> 并且当前步骤中存在异步保存接口时，可以让 wizard 初始进入异步提交状态。',
  'dbb19fea1965f7ef88cf1d1e0450c0f4':
    '用来保存表单数据, 最后一步点击完成触发，<code>如果最后一步中已经设置保存接口，则此处设置无效。</code>',
  '0f04a65952b58cbbc5ca6cba868c3bec': '上一步按钮名称',
  'e54827ae56fcb690d879b9cdd29f0ac7': '下一步按钮名称',
  'abb7ba84b95c6c90341ac9c883fbc85b': '保存并下一步按钮名称',
  'bed196af058f458def957031f88abd09': '保存并下一步',
  '81b522590d543401ad15ae8a9155361d': '完成按钮名称',
  '22c2aa6b59ab30c88fd84e8e5b3c4ad7': '上个步骤',
  '7b91646d808737e7138ad8f32a3b6cde': '下个步骤',
  '52b36576f88c31ed3971ca4d1fccd46f': '步骤',
  '8164ad50987e0508caf7638c663f8b7b': '步骤{{@1}}',
  'de5e232d10e2fa6218259289d4de4835':
    '类似于容器，唯一的区别在于会默认会有一层内边距。',
  '3954d7a9c047b7522ef92ddd5fc35852': '内间距',
  '315dcc9412a4499cf846736fdbaaa72a': '子节点',
  '5db7ca044a5179bf05428b283ac0452c': '设置样式后，大小设置将无效。',
  '7c57a563ab87bc6eb5edd8f5b953f499': '接口设置',
  '6aa351f5dacd13d3d862d9c93e4a0241': '发送方式',
  '8dc91bca9bc83efea73150e3478657fc': '发送条件',
  '91ee84292a5bf5e59d3b6309f948f2f1': '用表达式来设置该请求的发送条件',
  '55409342e28d37db86fb23efbd84a025': '发送体格式为',
  'e06a14abe7ef66a8ead143db4ae9786e':
    '当发送内容中存在文件时会自动使用 form-data 格式。',
  '773a0e8384fd6f784088b829d7cc2f68': '是否设置缓存',
  'c1b110f13431df9662299f26def71df1': '设置该请求缓存有效时间，单位 ms',
  'a18ea11244325dd3d20c5988bc7f6e39': '文件下载',
  '68caa6082eda1745aa3f6b6d12efe423':
    '当接口为二进制文件下载时请勾选，否则会文件乱码。',
  '91831507074270c0da8a31ad9ff87495': '数据替换',
  '42be3061671b38468cc6ac84f6a0dd77':
    '默认数据为追加方式，开启后完全替换当前数据',
  '81fe75a5216d4f612f1809c122f5145a': '定时刷新间隔，单位 ms',
  '83f16354dd1532422dc8b3581d096e7b': '定时刷新停止',
  '620f826a77f079c5683a9d3c59461ea1':
    '定时刷新一旦设置会一直刷新，除非给出表达式，条件满足后则停止刷新',
  '90260d55567cfd97ec2f085963a60bcf': 'HTTP配置',
  '9ae7a582479116d4cb41e828fbd59798': '发送数据映射',
  '0fcbf036057c6dd88b7b809daa0c5eb7':
    '当没开启数据映射时，发送 API 的时候会发送尽可能多的数据，如果你想自己控制发送的数据，或者需要额外的数据处理，请开启此选项',
  '7dd590a9d9e783e980d318bd52891905': '返回结果映射',
  '7e295b6ff39ec7356e06c4534bfc4fb3':
    '如果需要对返回结果做额外的数据处理，请开启此选项',
  '417125a06b1d2bfff025e83a4e067bf0': '发送适配器',
  '62efcb25e5b21da47c09780119da3458':
    '函数签名：(api) => api， 数据在 api.data 中，修改后返回 api 对象。',
  '6eb8944029108ad3b6bb3572a648fafa': '接收适配器',
  'e83cbec70e17988749c4a02a3b73f55c':
    '函数签名: (payload, response, api) => payload',
  'be47bd270e7756d4233e59bbe4cd5b96': '请求头',
  'f80cc88446cc10da4838556666f6b9f1':
    '可以配置<code>headers</code>对象，添加自定义请求头',
  '9d191f6126f21da8222755c14f49707b':
    '设置 ajax 默认提示信息，当 ajax 没有返回 msg 信息时有用，如果 ajax 返回携带了 msg 值，则还是以 ajax 返回为主',
  'c5e1f01e3d98b4db536765ae0d5b91a9': '点击选择',
  '4e7c006f535b13b9737ac310bc34193a': '初始加载',
  '3c743b8b2d662421117a2fb1c445425d':
    '当配置初始化接口后，组件初始就会拉取接口数据，可以通过以下配置修改',
  '713ec76479b992652ed39364d3d03870': '如：this.id 表示有 id 值时初始加载',
  'e5e3131aaf96b6dd10574bc9beeaf934': '定时刷新',
  '9e8c8bc795ad25fc992cee9d81a8c46d': '可以配置headers对象，添加自定义请求头',
  'be604f8b7ec5e80288b091ee12bbab7f': '点',
  '9adcfe38533f68035b2bf7831d310381': '缎带',
  'cbc608353218e1d63e6f47c9db1eae64': '文本内容',
  '2613e43b46ca52dabc232054c1611c80': '角标主题',
  'd8c7e04c8e2be23dd3b81a31db6e04f1': '信息',
  'e4f10a8916d4c5375529e21d9b66e5f9': '角标位置',
  'ebc556841a9264ebaab728efad7af777': '偏移量',
  '81cdf47e8d8adfc70faac2cbc55e4067': '角标位置相对”水平“、”垂直“的偏移量',
  '7ba3812c61e01b3049404a46fac8deda': '自定义角标尺寸',
  '29e13ada94b145a1359291d5e1564655': '封顶数字',
  '42becf09dfd209746b66726e7d21d014': '尽在文本内容为数字下生效',
  'b599979e9a40823363451aeaadc0723f': '动画',
  'b8c467fce096a649583c0bc9d9281a5c': '角标',
  '005c50d1af6e833d6991ab882653b7ae': '加载可用字段失败，请联系管理员！',
  '76f47297fe988267a26073a9aaf7911f': '暂无可绑定字段',
  '020586d0c69f8211840ddf9ee9bbf6ab': '绑定字段',
  '91be693dd1ccea38f8f514318fd8a944': '输入名称搜索',
  '8517171ce4ad0e9a5b511bd6bb26f839': '暂无可用字段',
  'f7d2996639d97b4a03fc0e40e2eb853a': '快捷键',
  '71dc8feb597052ecd0e73c9062eecdeb': '未配置',
  'e8755fb1e985a5d26df0fce1f0b7b0f8': '添加选项',
  '51d8a85a3c59453eed398eb8f5d35585': '请选择默认值',
  'cb73fbd12620c6ff4d7e5d1047c3be4d': '请输入静态默认值',
  '303efd5ba79e639001b4328cd266dddc': '点击配置表达式',
  '7d92f998d24da41b58db140b1864f773': '当前表达式异常（存在循环引用）',
  'b5cc1cd60cd694f45142dc52a5bf53fc': '数值类型不匹配',
  'b51796f5778fdc31bac73769a85f89c7': '未找到对应组件',
  'eee03351367bb1907dcc3140ffa3e3b8': '去编辑',
  '01820262aa9ad5b130f8f5b86bfd2968': '自定义选项',
  'f99603414a616bdee85de0e6e3938b65': '外部接口',
  'e6ff6a97bf600c02942db3126a7077b8': 'API中心',
  'b4fdf79b8f54856b072ec3874b830d1f': '请输入显示文本',
  'f4ab507e2fa2d2bd66bcdeafd9fef797': '默认与文本一致',
  'aafda9e8f6b7b613680677c513edb7a6': '取消选中',
  '0560b060c438e9326f92718ccbc3f95b': '默认选中此项',
  '5d26b8a41e805204c9dcd5ea7e23b150': '请输入文本/值',
  '22de6ef85ed60ec54dbdc1d8583e5104': '批量添加',
  '421252e16c6cb544fe9ce0be94a190e0': '批量添加选项',
  '1e2f96a69fbef8caa8823a3067ebbdc7': '请输入选项内容',
  'a4f1ddbbfc96930d24e4b54cb815b62b': '无选项',
  '5b4ffa2eadaf629b833b37a3e8742b2c': '不分块',
  '3569877e498155b59ef5299870c43f80': '平均分',
  '15e1fdd9d5cc4dc595fba0eee0719cba': '按步长分',
  '56d37871117270ce5d157a8de90dacd6': '与分块保持一致',
  '6903085e7f31286560e4a2e160beac42': '分块',
  '7145575ab9e3b4529eea61fe5fe76d0e': '块数',
  '1c8737ec7da60e12207c9eb04ccabcd4': '新增分块',
  'dc4c91dfaa3b760147bd92e648560af4': '下标',
  '662d8b49913650f543c024d4d02009a2': '新增下标',
  '69fbb2e5fc9eb3ba06096cbedbf5a622': '条件',
  '8baf21fa26d6d24b4faa872953275d8d': '静态',
  '8494b2036d3ccfe6102e930d9d8a3397': '请输入{{@1}}条件',
  '311f6d1fa5f13b0e280d7b3c9d40c5be': '展开更多',
  '38aa9dc2a0f14555322061d2ff997349': '更多配置',
  '37087e5bb2d0367872a461f535580d91': '请输入时间',
  '96641a78cfd9f9f8ba68f0524347b186': '请输入标题',
  'a11cc7a65b27f3993d58438d275f3447': '请输入内容',
  '34df758502e02c7c1a58f804a6c96c28': '折叠前文案',
  '1d20d90b7c7301b7739900242d38544e': '无配置情况，默认显示标题',
  '8aea4138b4fac2627c9b72da37e0671f': '折叠后文案',
  'd584018521820dac9e92120737b733ba': '节点配置',
  'c8158b3cad598b0b5939788ca4efb298': '接口获取',
  '72a3c1690dead6e24f7ac1abc90d5063': '请输入显示时间',
  '79d3abe929f67f0644a78bf32adb3a89': '复制',
  'f86418b525af4b573aed36b8e3f9aeb8': '添加表格列',
  'ec159d98c6c25fadd38bcd9362f6a28e': '设置表格列选项',
  'f302b37cf6530d3fb39234a220d95437': '需设置表格列后，才能设置表格行',
  '6090f7af1ae15892abe97409b9e557b1': '添加表格行',
  'e6a10b831ae920bba1bb89a725e0fbe8': '设置表格行选项',
  '355c54009e364bf4396be424fba10e0f': '至少保留一个节点',
  '9046ad86a1a47f16e954f2ec38fb680a': '层级过深，建议使用【接口获取】管理选项',
  'f69608e93e9728f4fbef583bfa1326c1': '选项名称',
  '684a0d1aeca4e9acff89221b57826d4d': '选项值',
  'bbcbe681f9225f8adf3663f563a9f294': '添加子选项',
  'c08dbaf90614532aed9f526e58b7fef2': '选项管理',
  '442781667396d6eff51113f482d89e54': '添加校验规则',
  'd7a169e81b60ee08c82b5d9de473e362': '错误提示',
  '9d9cf35ff82a6d960538ecd650e09945': '系统默认提示：{{@1}}',
  '5ef6ce89f52b4331b080a0f3019414f3': '默认使用系统定义提示',
  'db1cac8e2f6206e8f179b1ff47a676df': '未匹配到数据',
  'e9908cdf79e965f6907ce9f291cdfcf8': '动作配置',
  'be5fbbe34ce9979bfb6576d9eddc5612': '保存',
  'd1d9049139d870edd490215530d90458': '执行动作',
  '9eac7e07ca0a3181766e5ecc70d20727': '请搜索执行动作',
  '0174bdde9517fa331bf7d716a553e023': '动作说明',
  '0aeca07a02601a8e701a46d1a8b5ce43': '基础设置',
  'da1ed600ce65be863766444e60c2da05': '执行条件',
  '13b2de1073f76444c49d2c6a21e46e26': '默认执行该动作',
  'd80bc0fcbfb250480320b683e48b1467': '选择组件',
  '07682f1424e400c467accdb556d59e1c': '无配置内容',
  '2e3ca80a58643bc28e87cc3b17bc9d80': '请选择执行动作',
  '7030ff64701a938becbc5aa67ddb86e8': '错误',
  'dec2eb7145e149f281cb7e75fbe8972a': '跳转链接',
  'ae10a948eca808b3dd77506b24f3fd0e': '跳转至指定链接的页面',
  'c7a34a3465d1beea2f85d53edcff8235': '跳转至',
  '0b72392143e4038e98128cb0f6f679b3': '页面参数',
  'c068b579db3bf0a553bd0af4f81cc14f': '参数名',
  'bfed4943c5f487de1b63a82f7230cce2': '参数值',
  '56aa76ab3c987377e855ae2c6c612050': '新窗口打开',
  'fd5fb471ecce1eea63a6a95b6707f815': '打开页面',
  '67e21dd387607ae3fb59846504fa2c4c': '打开指定页面',
  'd7098f5050f017673319c5db1473ada7': '打开',
  '39e107b7c4aa580f913ccbebc00f7534': '刷新页面',
  '261242fe62b18b620419802c7dd7da7f': '触发浏览器刷新页面',
  'ca180138a862543561d3a2c4f08b2e1b': '回退页面',
  '5f6b9e7a050ae3f34b38191435e14b24': '浏览器回退',
  '27e0ca877865238aad6940481b2984d4': '返回上一页',
  'f80d12dcd65429fd28841e768062d3c2': '弹框消息',
  '0561589c26e732981f29709a9b574234': '打开弹窗',
  '256dbc5161ae393ec8a0e83ae6cf9469': '打开弹框，弹窗内支持复杂的交互设计',
  '6cff4b6d794cc17f5d24dbe0d21e5732': '弹窗',
  '507c1d40c5d6b990cf8c832b0a91cadb': '弹框内容',
  'a532be3ad5f3fda70d228b8542e81835': '去配置',
  'b67cbb1ca7439053f06d59aac5e410dc': '抽屉内容',
  '3b02248ca3790e356e47b6900c0e3931': '关闭弹窗',
  'f33c2c6ff58bcec40d3e74e591bb3df2': '关闭当前弹窗',
  'e495f416b83e4c7ff3c66ec3be96a76f': '消息提醒',
  '61d7aaa88181c527cfb936d4c686d267': '弹出消息提醒',
  '4e5242a645864528e10f04dc2326a5c4': '消息：',
  '6d00710a2528332bfcac14b58e412042': '消息类型',
  'b87b77561e776367e6756e11ea652217': '消息内容',
  '43ab9af06e1e0f0b2a8767b46cf8b1cf': '标题内容',
  'f41a94bb85c5223181c4cdf83ea9021b': '持续时间(ms)',
  'a0a837f2873de80bc9ec353c30e73171': '显示位置',
  'f3296f64a8a1330d7a07f1d269a1db92': '左上',
  'b97a5adf068bee6c852db9dcea3a9799': '中上',
  'eafeba264b6338939f11f1b1adf40d2b': '右上',
  'd429ffb093e9aa3bf80da125f1be318c': '左下',
  'c241aa8f427118a719b94cbd8f2bb22d': '中下',
  '9cd707caffdfb314d939298f2f2c267c': '右下',
  '3f3a016027e540ef10a16dbd49fffde9': '展示图标',
  '0cd902f953656adb29985b68e6fc9754': '配置并发送API请求',
  '1535fcfa4cb8e4d467127154977e9788': '发送',
  'c14a21300b61bb83b4420a1586497951': '请求：',
  '88bdaf32c27ab169d3d686b86b3fae99': '配置请求',
  'c5dec2a8d2308c1c15ec2e5441fd721c': '静默模式',
  '4abbdba4b6b06ce00702a255bd89c92c':
    '勾选后，服务请求将以静默模式发送，即不会弹出成功或报错提示。',
  'e3b49b5bbbdea05598525e91dbdfa638': '存储结果',
  '4dca05af026848011eedee1b53efa61c': '请输入存储请求结果的变量名称',
  '4da82260041107e5780bcbb3a14ef791':
    '如需执行多次发送请求，可以修改此变量名用于区分不同请求返回的结果',
  '3f9e257178738d5d180ddc2996809c10': '状态标识',
  '99c74120cc62f4bf31d661e3212b7121': '提示信息',
  '89049706952412d790b801def284629e': '触发下载文件',
  'bb79667f37035e9562ec6bcffd6cf8ef': '组件',
  'c852fb60f1b8ce921c3def1eba000bc5': '组件可见性',
  '1bd4cfded5e11a7a8ea4dcfd2fa17e15': '控制所选的组件的显示/隐藏',
  'edf25860e3d457eb8ca9cb5dca06dfd7': '显示/隐藏',
  '12c8d50c55eeec7059ddd5c303e34f77': '组件可用性',
  '5e75800641ec5c1198092bcf9d34f180': '控制所选的组件的启用/禁用',
  '7854b52a889b3ef0590d9f542efeb4c8': '启用',
  'd86d5919f595226b7a1e8264635ca23d': '启用/禁用',
  'c5a9b6e8c522de8a14ad7fab51c1a1e3': '重新请求数据',
  'be4b778e7f5aa6aa5a811d7db4e1a8b3':
    '如果开启发送数据，会先发送配置数据到目标组件，然后重新请求数据。',
  '694fc5efa9e1d1c2c5eb6525e1c7fb29': '刷新',
  'ea4d3723b350b2cb8f4c1a615e1b7df1': '设置组件数据',
  'cb7add16ba6f0cd65d5ddcad71359813': '设置数据容器或表单项的数据',
  'b91ebe714155c83b6d3bc02b675a31e9': '的数据',
  '08ce6e74bb4a64753f1af2e5c836debb': '赋值方式',
  '139294edcce271bf483dda437c421c29': '指定序号',
  'a7b0b80a7bea1e5e973967c179866ef0': '输入序号',
  'e887792fbbd65d21e43e832a5cd63aac': '请输入待更新序号',
  'e3a6f648390842e0c7aa82d0f2c3f6e3': '字段赋值',
  'a25657422b40023f2731619587940bc7': '变量名',
  'a33903526e8fb3d1ac3066da70e7941e': '字段值',
  '82986a4cab1d0efdbc23b3ac5f0fd509': '清空表单数据',
  'f457845da8c119a8688e333a3554284f': '重置表单数据',
  'a84a1311bea7370f1749341ffa6f75e1': '校验表单数据',
  'b7579706a363e5f23b1040fecfbcb677': '校验',
  '51325230409d4b7c64aaeb3db9904801': '组件特性动作',
  '98d130cb9a360df782f6510abacbc022': '触发所选组件的特性动作',
  '557d01c07aa7c4450a414932e6c1ed2a': '复制文本内容至粘贴板',
  '5aa4369ec61715ddef3641992fad0d4d': '复制内容：',
  'c7f16d729f3bca8f6936416884a74fb8': '自定义JS',
  '1b5a6299ef404c1f7b4292c290b80f55': '通过JavaScript自定义动作逻辑',
  '9bef5e571702130c5710af4ee2c27455':
    "/* 自定义JS使用说明：\n  * 1.动作执行函数doAction，可以执行所有类型的动作\n  * 2.通过上下文对象context可以获取当前组件实例，例如context.props可以获取该组件相关属性\n  * 3.事件对象event，在doAction之后执行event.stopPropagation();可以阻止后续动作执行\n*/\nconst myMsg = '我是自定义JS';\ndoAction({\n  actionType: 'toast',\n  args: {\n    msg: myMsg\n  }\n});\n",
  '9a2ee7044ff04234a8892a13583d14b6': '变量值',
  '186c8d63db1c09c38bcfd048fb15846e': '滚动至上一张',
  'd9b6b8e29d63ac6bb7a0381e994ebcb5': '返回前一步',
  '47b9cbf9f3a3f08264b19f4a1228e865': '滚动至下一张',
  '211ae8c8666f8b803282a74f90fb0dc6': '打开提示对话框',
  '56eafb82d11c72b65efe07b9bedb5c19': '打开确认对话框',
  'e777eb796f8a02e97a891fde43d10ce4': '{{@1}}变量',
  'cc6aeb073ebc3cb29734a49164f8964c': '页面变量',
  '979a50681e278dcc0be18f68459e8217': '系统变量',
  '8deee3cdecdf06a05d22fcacc7031492': '组件动作',
  '9654916723a8d1d82f5ab9d2911edf93':
    '温馨提示：添加下方事件动作后，下方事件动作将先于旧版动作执行，建议统一迁移至事件动作机制，帮助您实现更灵活的交互设计',
  '88f7a3aef4888dd507482aedc02bb808': '配置动作(旧版)',
  'c500cfabdec9b2761fe9f1aa543933eb': '动作',
  'fa476b76ccbd4ac9316f0fd80257b77a': '重置并提交',
  'c0a8088f2bbc993500c5a01b0f0a1887': '数据来源变量',
  '71448f8c10c79b32312b7e3abe9104ef': '（{{@1}}结果）',
  'f4381cd48d8cb4307bc140613ea57d48': '{{@1}}结果',
  '0b446df580ad309e7c26e5242eddafac': '事件变量',
  '1fab2b4934161e87a1f0133b9d5bc1b5': '添加事件',
  '4db5110d41293fef57f5a1f364187896': '快去添加事件，让你的产品动起来吧',
  '5baa9ac58c00a7fb818d4a113d3f8cff': '未配置图片上传地址',
  '7241f67ee4fa8e0adb5d602b5f9516df': '开始颜色',
  '9f1b854df133912bb46203e84f0594ee': '起始色',
  '8ea65e3d4e52c871d1c58b1926380ab0': '结束颜色',
  '0c46a7f77a7247a9cc6d6e995c0ea8cb': '结束色',
  'c7706039e8ad85969df13ce3458d199a': '渐变角度',
  'f4882cbf65b232af449d7289354b9a16': '* 角度范围0-360度，0度表示从下至上渐变',
  '5b9ea77bb6ce2d6c82e97b120d757201': '点击或拖拽图片上传',
  '61a87a021904dc65995e99d4c476cb21': '图片位置',
  '737391648d1216a1f84ac9ff52da5aa2': '图片尺寸',
  '0f1fd39145bad43e18f81337e0144c8c': '充满',
  'e0d76824dfe5e09c7068b44f605266d2': '合适',
  'e39d3b7a3600d9327221a637f910fc0b': '拉伸',
  'e1ff2c83c09f2dc6cc74ae02ab6b8222': '平铺',
  '7ddd9dbf373f760acfd63778469b5c4b': '横向平铺',
  '4ab931e0f709f9b493e660156925a113': '纵向平铺',
  '21f3929a4484e6e992af64ec1dd3a576': '原始尺寸',
  'a80a25b59908402cf7ee31a07d0e7739': '左边框',
  'e0dfa25ebceabddb2180720d36d4c3b6': '上边框',
  '57463587977a534f7859eb9d7c536629': '右边框',
  '154d5216e42c916884431f0eea951999': '下边框',
  'a8b0c20416853bda54120bf19477ad11': '全部',
  'df68a5dc8f8847179b7afdf943f80796': '左上角',
  'e717b4ae480e7c073fd5a44647a7f0da': '右上角',
  '2a97dfb2d236c87c41fd588f006111dc': '左下角',
  '1c9bc9dab33944e953412f8b22cb024a': '右下角',
  '4dbe7c40ee82a56bb7a8152d4bbc07f9': '圆角尺寸',
  'bb1531cc9643230ba0cbd7465818b52f': '边框线型',
  'cc4c575642609fbf059a5df81eb86bfc': '实线',
  'b1e0ebac23ed95807ecc566da3ffab48': '点线',
  '61f6f4fc0b806ac9d41ad0792e6155f6': '虚线',
  'bade10099f8447210ce7b97fa106b527': '线宽',
  '5babfafd769570de3bba47605753361a': '边框宽度',
  'af208c560d926f0daf4b2ce2d396505d': 'X轴偏移量',
  'b6b46126bfb0851ca10e74541c5d7be1': 'Y轴偏移量',
  '4a32b29da68a6ee204b3743e0fab8bb3': '模糊半径',
  '22d460dcddb7fda718bc62034c459158': '扩散半径',
  'b24a723b73f96ab3340fe9502370ee13': '内阴影',
  '1cbac849ccc41edb12271d9fe9b65b5f': '阴影颜色',
  'e94d6fc08a97892ff9d8c8d9a8d9e0ab': '设置阴影颜色',
  'a5d833839a610994dc4752e2d91f4192': '显示类型',
  '2c86d897c71cc9c2e648222a200d5bbb': '区块',
  '0b0052c532b457b2d032c9f56af9e4e5': '行内区块',
  '70d457cd36de1acefe7d9587f3f862c9': '行内元素',
  'b5f55e5c7101d9be5218d63850e1ad8b': '弹性布局',
  'a648bd0b9eb3c86e39cabeac484917a5': '弹性布局配置',
  '452dba7c65211630f8066b070fdf157f': '自动换行',
  '098d946b6f0cc85110c54cfac8691cc3': '主轴方向',
  'aee1aeb8d65547f8a368bd7364cacf12': '默认水平',
  '4c38eb0fea71a9506fd434a7ac5cb1c2': '默认垂直',
  'ae07817b441091793c8af39256908373': '水平反向',
  '618515fd68ecfc4a5b58462f0cb9666b': '垂直反向',
  '58554717be87c1f8a6d6c769f74f0679': '主轴对齐方式',
  'dfc71567fb75f5c73da377013a223168': '首尾留空',
  'eb5ec10ef70689996dd5cd66e17a64aa': '首尾对齐',
  'ba3a7db7cc1ac5a908358f62fdbb21de': '元素等间距',
  'f6cbf29fdb5e94052a22986533c7267f': '交叉轴对齐方式',
  '6b461e0a4ffef9438fb8f01369091cac': '字体类型',
  '2539fe0e6d40a023dd95d0bd71d10bc9': '请选择字体',
  '690660d9dbd7312ad2825e554736e2f8': '字体颜色',
  '5f15efdc32badce0902c46a7a0105c51': '字体大小',
  '4c5fead0489fbc7651c91400dec5d379': '文字样式',
  '4ae7f423d9ed9ffa48c3e404f96bb51f': '文字排版',
  '65786fea9d354015d3a2724086f7f3d6': '默认字体',
  '226c06861b0605a3276311b1369204f2': '接口返回示例',
  '1f318234cab713b51b5172d91770bc11': '高级配置',
  'a0472043a9598b1b19f1adcec1e1d75d': '接口配置中存在错误，请仔细检查',
  '2816c3584802b2fc75db5bc3c6aa81e8': '设置该请求缓存的有效时间',
  '6e889f0cc8c1d8f705edfd5d0bbdaaa0':
    '当接口为二进制文件下载时请勾选，并设置 Content-Disposition',
  '508a38f518821a0f6bb2d15269b31ece':
    '默认数据都是追加方式，开启这个后是完全替换',
  '5c131eb3bc61f6b0a26e20449ad7ce56': '自定义适配器',
  'f514cabe63553a850063834c9d44a5ee':
    '可以通过接口获取动态选项，一次拉取全部。',
  '47d0a7caaa2baee8d38612a1c57421ef': '是否初始加载',
  '866a60d7fbdfeba6ae42c7e9c7b03059':
    '当配置初始化接口后，组件初始就会拉取接口数据，可以通过以下配置修改。',
  '3f4f3acd6968f38361dddc6612a0c54f': '后端代理',
  '8df0f3891f8a80a392816f6ca662a33d': '刷新间隔',
  '21157cbff85802e353409f647f1f1f91': '毫秒',
  '66670400b1f3e4b6c94cff171d441585': '表单项内联',
  '21a1d138166d5d92276d126cf1d6ecac': '控件宽度',
  'ffaa96ef7232cb6d8ee4d7e673dbf4ac': '占满',
  '4e9ce9dfe13d97031d228fc5ae229f0d': '隐藏标题',
  'e2773277c7765d4590f7128423c58cee': '隐藏后，水平布局时标题宽度为0',
  'cc572c07586f4ea0c5b9b1060eb777f2': '空内容提示占位',
  '8528426e54902956723f322bdbbcfafc': '输入框提示',
  'dc5dbe7a1b1ff5cae22ffbb636bc6380':
    '当输入框获得焦点的时候显示，用来提示用户输入内容。',
  'ff255db9b108589abd5c649e2a02e3f1': '控件提示',
  '9e783b34207fe24bc488136faba65442':
    '在输入控件旁展示提示，注意控件宽度需设置，否则提示触发图标将自动换行',
  'b3e55578af5dd473bab62641bb2f5f8e': '输入类型',
  '16084784a0f126b501e96994c792d411': '日期类型',
  '8190915888889ed18be44ea0351d0448': '月份',
  '8f30e9f8678c24496921bebae6ca2ac6': '年份',
  'dc46b7f19799047a26fee668d49be0f3': '选项渲染模板，支持JSX，变量使用\\${xx}',
  'b18ec08df2e47313bcc93430e7a25fd3': '支持 JS 表达式，如：`this.xxx == 1`',
  'b7dee01f2d085d90c47bcb8b490d9055': '点击选择图标',
  '689fad203a167d542c12bdc46f27e921': '名字',
  '6c1e18fd4cb57288a9f43603d5167292':
    '需要联动时才需要，其他组件可以通过这个名字跟当前组件联动',
  'ae344073ea6ca9ce742899cdf6d3e106': '请输入字母或者数字',
  '1a1ff1e1149a0cd1b39c0b231a334d04':
    '可以指定操作完成后刷新目标组件，请填写目标组件的 <code>name</code> 属性，多个组件请用<code>,</code>隔开，如果目标组件为表单项，请先填写表单的名字，再用<code>.</code>连接表单项的名字如：<code>xxForm.xxControl</code>。另外如果刷新目标对象设置为 <code>window</code>，则会刷新整个页面。',
  '4fe2f10c6d5bedac03f40a4362e4f69b': '请输入组件name',
  '3bce1a6217990c8dc087d254f1fe754a': '自动填充',
  'f01553e415ca33cc89d0bca84023f4b5':
    '将当前已选中的选项的某个字段的值，自动填充到表单中某个表单项中，支持数据映射',
  '89bc2a21c778b36d09c8d795aac8260e': '数据录入',
  '666352a09304cba42de24312f509c3b4': '自动填充或参照录入',
  '04d815a5d4b803d6bb956d2da5a82688': '是否必填',
  'a1b6281dc554ac84a3e6062f812fe50d': '表单项控件下方浅色文字描述',
  '85541bd9f7f39a6e6d9d26cbe09cbdd4': '只读',
  '4d681c4aa93c8d005ec2ca2370618d6e': '可见',
  '5d0595edc3d14aec24efef85534e4314': '默认值设置',
  '495333d64fc1efafd6c40bc9a3929fee': '不设置时根据 name 获取',
  '32f13c9db34f638c2c5cf2bf19326ebf': '键盘事件',
  '2882b15b04cb47d1497160b5061b9186': '默认是不启用',
  '530c4483c7e52dc409509b755eabee11': '千分符',
  '090dbd614a66a56a5eadec87f59ea15c': '图片路径',
  '2a0c4740f156a9536049f4610da25400': '文件',
  '4fa2ae7d726dc395cfea70ff3d7256d2': 'Markdown 内容',
  '52f43ce846b2bf73f86195cf196fe578': '计数器',
  'b9245d69d2d82b0081ced47a18c27f41': '全边框',
  '8f9b1b7e38cd2ed16f22807417ab3573': '半边框',
  '7fc7940b4f7f58b49c71bf9e237b633e': '无边框',
  '6fdccea6068e0698c565acd92052a86e': '必须选到末级',
  'a80c61384a8459ef7bfb5082a2b54b5f': '必须选择到末级，不能选择中间层级',
  '71758057056e7f31d73e3f3ac8860b4f': '隐藏时删除字段',
  'ce641d8297471a5d65c46cdfb023097c':
    '当前表单项隐藏时，表单提交数据中会删除该表单项的值',
  '3e719b87b9ee71d4613caefbf2fd1074': 'UTC转换',
  'd52e57147787797ae0153d43bf8be298':
    '开启后，提交数据和展示数据将进行UTC转换；存在跨地域用户的应用建议开启',
  '339b9ebd91070de050b4bfe483aa4474': '内嵌模式',
  '49a79f4047b81186c069ed1c9c151c66': '淡色',
  '41e8e8b9935c9ee4e88b06643a2d5b81': '深色',
  'bde770827b9137ddb3eb676878af9709': '次要',
  'b1ea078db7298ea7872d894283378507': '上传方式',
  '74cef1162781310e1503d2dc463a76fc': '对象存储',
  '38fbc7fb70b4399d7e4050d3cbcdf229': '存储仓库',
  'e5d59ccec2caa64ca83b7cc740645928': '平台默认',
  'b6789c926cf3d23d13a6a3220bcb8e23': '初始静态数据',
  'a4895ee2e87d1c47b734dbcf1a535aeb': '选择页面',
  '150075376834f1879d6ceb7c2d10ec67': '比例',
  '34598b2c71a8affb13da84ba39e905d5': '12 等份，标题宽度占比 n/12',
  '73ec114993142f627ab6c3e6706fa3c3': '标题宽度',
  'ae6fa9e75d38b9db24f78496b72bcc75': '固宽',
  '424f04d454332ddaff4daa0a365e9335': '子表单展示模式',
  'b17754a193ac835bcae0bf960f3a3772': '子表单水平占比设置',
  '2e282fb6405a60830112991f88e95753': '左侧宽度',
  'c41b3271487a3cc63e79feeb11b11de0': '比率',
  '0fcf69b5d0b44955fcbec9702f90b556': '小宽度',
  'b591aed69defa2abf0486da6a58dfb5e': '固定宽度',
  '0fa590019ace86acee8d7655e5fb11b7': '大宽度',
  '51c1e80408011ba118cdcc696f28283d': '左右分布调整(n/12)',
  'aa0ff71cb10c3e54d68874dc2b17acaf':
    '一共 12 等份，这里可以设置左侧宽度占比 n/12。',
  '1f08c91da33fc5f0616e8a85c0456a18':
    '静态数据暂不支持多级，请切换到代码模式，或者采用 source 接口获取。',
  'cf763c357566be6fdaee886a40ddcca7': '选项文案',
  '1ca0b9b486be3b766a92474189f11fc8': '拼接符',
  'ab8e2e8cd076bd115cdd600d17ca5020': '将多个值拼接成一个字符串的连接符号',
  '6df0630b4f00b6bd05de8af09c2f78ad': '仅提取值',
  'd7d810ec89408c206a220f62edde737f':
    '开启后将选中项的 value 封装为数组，关闭后则将整个选项数据封装为数组。',
  '4cff56e2b9703018efc48218b83844b1': '可创建',
  '457c4cf8e1e2e0daef8949085555563f': '配置事件动作可插入或拦截默认交互',
  'd03c96a2da4905c5f13a87c0d8ddbdb4': '新增接口',
  '26bb8418786593149c0bf9f8970ab6de': '新建',
  'a32b3bf74850faad3a9ae6a0a5dac781': '可编辑',
  'ea56ca3dac0d39e463a8233fd40a9eb6': '编辑接口',
  '3c87af7c432e6b1f59e4f415fd5060cf': '可删除',
  '793e260d5b7c67d43b5c6d5e885d2363': '删除接口',
  '63bbd68594c9a987d0ff41d645fafa16': '是否默认选择第一个',
  '0dd2e4469872c176ab1e85b66d99da98': '隐藏路径',
  'b7e26fcff328b28b393ef2e57e96e258': '隐藏选中节点的祖先节点文本信息',
  'c49fc970bcd168e1c652a8ced5d95d0d': '其他项',
  'a45b5d3fcdb8210d249b2c6d545b6691': '光标类型',
  '2c3888961c01fc9c466d88c88029158f': '无指针',
  '062d0b688adb10f3af5ebc2fd2667f1c': '悬浮',
  '92e3a830ae9a1252278da742c0af74c3': '帮助',
  '87e277f953fd3c867d7fa02897c2c124': '交叉指针',
  'c19639326396d2527268484379a671d8': '可移动',
  '183f48b3c1f0f84e538cd4315e5645e9': '可抓取',
  '4f9b192ce84b4df0900510257082ef43': '放大',
  'b21ac25366449b1720bdd75d39f163d2': '缩小',
  '93ab99d761c9ca97dc926e6db10469a3': '文字大小',
  '86cdd659decaa1b959795eff92a0c2d6': '文字粗细',
  'e8ed49e9fde0bb778e2185876c2d2697': '边距',
  '4e7f76261f8c4c6d78998f85fc1f4c6e': '外边距',
  '841d77223f0ec8cd0b530ed8e0775b20': '内边距',
  'b5bd9a6703f5433ff34aa0af9049740c': '圆角宽度',
  '14eb3a698316caf3fbe9b2ab9c3d9527': '特效',
  'cff1ec632eaf35f64791615e15ce6d76': '邮箱格式',
  'fab6f9029822dea7838bf9908d7f5c09': 'Url格式',
  '1111c44adfa40fe9cb22797d2c1e37e8': '字母',
  '725bf3485a0456cf7f65a507ce67254b': '字母和数字',
  '96c4ea83892a3227a2aa5b8f3759bca4': '整型数字',
  '35962d17a3fba5f4802d7845695a3e72': '浮点型数字',
  '28e8e048490110c8dd8e2ad6af324980': '固定长度',
  '8c4ee6022f1525097a1141acad094d4e': '最大长度',
  '17971609e210034c0d6a25b0186e2b7b': '最小长度',
  '92448a35f41de3a1fa69135acfed5ce9': '手机号码',
  '193a8c42c1c373f385a4c7b33ffc381e': '电话号码',
  '6102d474314f27577d89e85b4c6cc4a5': '邮编号码',
  '84e0cb5d57ed995b0cc04b4ab9a7997b': '身份证号码',
  '8dbec4f0c05be45a8acf6a5ae9d1f880': 'JSON格式',
  'eb242bc7524c797fb1aee2344dec92da': '与指定值相同',
  'c17d9577233793976d3902c117eed82b': '与指定字段值相同',
  'b457177c184722b655954a08cf3f71ca': '自定义正则',
  'd3927ffde0fdefc47f910e70226d6a4e': '自定义正则2',
  '0ebee58f4f2a0f807f08a6427dc58497': '自定义正则3',
  '15f52cddb226421e68c70488fff3db5b': '自定义正则4',
  '271b01959e09c0771760f59964baed56': '自定义正则5',
  '4eddee6a20aceddd6bcdf7e0736887ee': '验证规则',
  'ba3c802f3ce1641eb6f8986e8d19e672': '新增规则',
  '7be30fe376e9bfd8895ee50e6f4216f3': '设置长度',
  'd00f99fd76e86ba4dab6f70858010ca0': '设置最大值',
  'e993ecfbb3f934481257f1bb57056bfe': '设置最小值',
  '99f5d503544334c670cbe1f400aea9e1': '设置最大长度值',
  '91bdac623455c91b7400328a5600cec0': '设置最小长度值',
  '7002c4a1b7cb5bc32ffd52e1f2d74c70': '设置值',
  'aa9cfa5321e589494841ddd90a10c467': '设置字段名',
  'd22b6fb1c857777ba21467835efc65d6': '设置正则规则',
  '1b7e06ef04d7167e174eb6929421592f': 'Email 格式不正确',
  '2d163645de4d4b4760e9fbdb535a1a88': '这是必填项',
  '87c91ce706ab845b55ce95372265b92e': 'Url 格式不正确',
  '2b702fb5b95d47944246f79ae4032281': '请输入整形数字',
  '48e2aca8e6347b008b6fbdb48fc4b597': '请输入字母',
  'de66a286057d4e3f1ee2d9bccbd48ce5': '请输入数字',
  '84f46c9b82c3c8fe276dfa65173c59bb': '请输入浮点型数值',
  'fb73d98245f558dbb2d6a0c8d2699780': '只能输入字母、数字、`-` 和 `_`.',
  '923a91fdc2c777f8443c85278060195a':
    '格式不正确, 请输入符合规则为 `$1` 的内容。',
  'd87b1a3d180a1cc56bbd174d2860ca4c': '请输入更多的内容，至少输入 $1 个字符。',
  '9b8d6abb2f03fbcbdb0e4d2b1970a751':
    '请控制内容长度, 请不要输入 $1 个字符以上',
  '2a5d0be4d5cd088f0371ba6f8656fe7a': '当前输入值超出最大值 $1，请检查',
  '8256618e16217325e6a1d880f8eb7adb': '当前输入值低于最小值 $1，请检查',
  'f58829312013d929923b0c2a1fbacf19': '请检查 Json 格式。',
  '161278fb2c71e5a8aa8aac50f230233d': '请输入合法的手机号码',
  'ba42949accfe87c20e6c2486cd065dd2': '请输入合法的电话号码',
  '1d2c5048143328e21cb9c2dd84b696fb': '请输入合法的邮编地址',
  '038b3ed111e87b56572f3945a1b0e02c': '请输入合法的身份证号',
  '335e2618dda48c05f4f833ebb1e299df': '请输入长度为 $1 的内容',
  'bce45e909d6d14a126554c8cf6f65a13': '请不要全输入空白字符',
  '17bb8e76f78bd16a1841e36ab8462e53': '输入的数据与 $1 值不一致',
  'ab534c35774dccc322331a079ae6e7df': '输入的数据与 $1 不一致',
  'ebd0dc3ebde6182caa3b66e0faf658b6': '自定义验证提示',
  'd7772d568894afbb1c924bed7f7ddb32': '自带提示不满足时，可以自定义。',
  '58c9592f818d706420236c6f9f595517': '新增提示',
  '171f7b825707ddd79175fed3f8def6cd': '修改即提交',
  '6ccb7091c39a5229f7e77eff4dd44a0e': '设置后，表单中每次有修改都会触发提交',
  '1a006028adf7167ae28cdf532bb75ef4': '校验触发',
  '8d877748c3bc71b517e2d46344916b3f': '提交后每次修改即触发',
  '2c60032f2a57717e4f7c16ee185795d6': '修改即触发',
  '5ed62f810226722d7c910c2d8dc4a0e8': '提交触发',
  'ed3dd0bfa89500c5feb306cd4d9db56c': '正则',
  '537b39a8b56fdc27a5fdd70aa032d3bc': '必填',
  'ab90c616dd114af087b31b90d3cb4063': '字符数',
  '41e82a5a0e53ba94d1160ee855c72a7a': '请输入字符个数',
  'a85ce2404b26140080c929ad9c3ec305': '请输入更多的内容，至少输入 $1 个字符',
  'e93275245d529c486018e47136bfae2e': '请检查 Json 格式',
  '2c8c25bb51dfd9ddfc74fd75a8a380a1': '值内容',
  '859102d8ced9928cc71bb225961171bf': '请输入Js正则',
  '544fac400db790f57ea8ee4207cbeb6b': '标准',
  '7a377ac3904628fd73e7d33ce6130ae1': '表单校验最少选中的节点数',
  '674ddb63a7cd9e14a49813d52cf7e25e': '表单校验最多选中的节点数',
  '54d621d5bd588bea4e896de52147a229': '选中的行数据',
  'ae76ff4dcb615d3b0232dcd10115e149': '初始化静态数据',
  '152b66069787294e53cfc176a189c81f': '图标对应的字段',
  '10cfe8897db8c032986138ba7b2f48da': '数据域赋值',
  'a6e2cf5b7fa625f571a3e6d0df3a7327': '数据域成员赋值',
  '3371427f1b82095309092ef82418ec1a': '您已添加该事件',
  'a718dcd3a16bee2a4086244ef1eb0ab4': '请输入长度为 \\$1 的内容',
  'c323e3527a805cfdd264700fdf013daf':
    '请控制内容长度, 请不要输入 \\$1 个字符以上',
  'c762cefa0ff423010af0a943c04d603b': '请输入更多的内容，至少输入 \\$1 个字符',
  'b95aed5c6f2c8e49e23b382ac3d593ba': '当前输入值超出最大值 \\$1，请检查',
  'fd11733fbabaf2ae3cf1fcd3fe385cc5': '当前输入值低于最小值 \\$1，请检查',
  'acf719549561f28f38bf750a64cda508': '输入的数据与 \\$1 不一致',
  'e027500d91d46a962036f63c09492c6c': '输入的数据与 \\$1 值不一致',
  'd01886eeef1de19f2e99617017f4def8':
    '格式不正确, 请输入符合规则为 \\$1 的内容。',
  'f10b676db977ae808af8d96b327be7f3':
    '有哪些辅助类 CSS 类名？请前往 <a href="https://baidu.gitee.io/amis/zh-CN/style/index" target="_blank">样式说明</a>，除此之外你可以添加自定义类名，然后在系统配置中添加自定义样式。',
  '73ac822ddf4685dbfec661dec41a96b7':
    '<div>\n<h2>hello, world!</h2>\n<div id="customBox">自定义容器区域</div>\n</div>',
  '********************************': '弹窗数据',
  '94397b87ac63fe238c779120fadab024':
    '位置为 "左" 或 "右" 时生效。 默认宽度为"尺寸"字段配置的宽度，值单位默认为 px，也支持百分比等单位 ，如：100%',
  'a6d91e801974dfa735a4ae0e098c522a':
    '位置为 "上" 或 "下" 时生效。 默认宽度为"尺寸"字段配置的高度，值单位默认为 px，也支持百分比等单位 ，如：100%',
  '197af5d5971778e3b80deb25182d63e3':
    '<span class="label label-default">子节点${index | plus}</span>',
  '7341e991c8e8cfec68d31ffe0e06e429': '比如: this.type === "text"',
  '8add6799ceff24eb041c5a6a92bb1694':
    '支持如：<code>\\${xxx}</code>或者<code>data.xxx == "a"</code> 表达式来配置作用条件，当满足该作用条件时，会将计算结果设置到目标变量上。',
  'e02d111d524de97e8622121f7ce845cf':
    '提交数据前将根据设定格式化数据，请参考 <a href="https://momentjs.com/" target="_blank">moment</a> 中的格式用法。',
  'fcb9b16d5d056bfbf6b6cba9dcf61efa':
    '请参考 <a href="https://momentjs.com/" target="_blank">moment</a> 中的格式用法。',
  'fde1ab2f504097f3f717acbb653b4f09':
    '${formType ? asBase64 ? "小文件时可以使用，默认给 Form 提交的是文件下载地址，设置后给 Form 提交文件内容的 base64 格式字符串。" : "File 控件不接管文件上传，直接由表单的保存接口完成。和 Base64 选项二选一。" : ""}',
  '17a689143f0c7003123bb3c947d35273': '校验优先级比最大宽度和最大宽度高',
  '2aa41edf8cfa79e7e5fcf38c9742b495': '校验优先级比最大高度和最大高度高',
  '37ad5d98b12853d786b08cb52b91a43a': '开启后需通过CSS类设置其高度、宽度',
  '324d0ccd6fa9d976d2f17de3bf0b70bd': '开启固定尺寸时，根据此值控制展示尺寸',
  'd85a80d177db696b29b7338af1501055':
    '使用空格分开配置，使用<code>|</code>可将配置栏分组，<a target="_blank" href="https://www.froala.com/wysiwyg-editor/docs/options">参考文档</a>',
  '8cfcd52cebdc2555b202f4b03de93662':
    '使用空格分开配置，<a target="_blank" href="https://www.tiny.cloud/docs/general-configuration-guide/basic-setup/">参考文档</a>',
  '5831c1f77c5acefa6dfe9c64d06ca78a':
    '配置"canAccessSuperData": true 同时配置 "strictMode": false 开启此特性，初始会自动映射父级数据域的同名变量。需要注意的是，这里只会初始会映射，一旦修改过就是当前行数据为主了。也就是说，表单项类型的，只会起到初始值的作用',
  '24c5febd312d27b5e80354cf03e241f0': '外层容器',
  'a269e7de6c7735290733eb3e574c2129':
    '请从<a href="http://lbsyun.baidu.com/" target="_blank">百度地图开放平台</a>获取',
  '0861915dbac25ccb573b3bb72ffeebd7':
    '支持使用 <code>\\${xxx}</code> 来获取变量，或者用 lodash.template 语法来写模板逻辑。<a target="_blank" href="/amis/zh-CN/docs/concepts/template">详情</a>',
  '844318bd1d58ba0bf7dabbb771174b12':
    '<span class="label label-info">一</span>',
  '2fccd71275cdf5d6c0c677ef79dd84a0':
    '<span class="label label-success">二</span>',
  '198a96c4cfc64d5feba81b3f931f8289':
    '<span class="label label-danger">三</span>',
  '626a0f2fdf13bcadf11581006ab9eba3':
    '<span class="label label-warning">四</span>',
  'ce3fd44456123f571e9d083b98da9fcb':
    '<span class="label label-primary">五</span>',
  'f8fc21a9fd40881e8fd3d7f15919465c':
    '如果当前字段有值，请不要设置，否则覆盖。支持使用 <code>\\${xxx}</code> 来获取变量，或者用 lodash.template 语法来写模板逻辑。<a target="_blank" href="/amis/zh-CN/docs/concepts/template">详情</a>',
  '2c8a99d35cb5704994cabcc61a4c3a4a':
    '查看详情<a target="_blank" href="http://www.baidu.com">日志</a>。',
  'bf9e242338d2c26b182aa6b9c015d84c': '如：this.type == "123"',
  '5414824fb8efdb7d59beae4bf95fdefd':
    '<p>当没开启数据映射时，发送数据自动切成白名单模式，配置啥发送啥，请绑定数据。如：<code>{"a": "\\${a}", "b": 2}</code></p><p>如果希望在默认的基础上定制，请先添加一个 Key 为 `&` Value 为 `\\$$` 作为第一行。</p><div>当值为 <code>__undefined</code>时，表示删除对应的字段，可以结合<code>{"&": "\\$$"}</code>来达到黑名单效果。</div>',
  'c130bd5b55edefdaf8923269e9a52439':
    '每个选项单列一行，将所有值不重复的项加为新的选项;<br/>每行可通过空格来分别设置label和value,例："张三 zhangsan"',
  '1bf14fd24efe68f62bbff0538dee238a':
    '每个选项单列一行，将所有值不重复的项加为新的选项;<br/>每行可通过空格来分别设置time和title,例："2022-06-23 期末补考"',
  '0e9525b2bb1493c567c114dd61b69095': '温馨提示：',
  '7080fa6e0ca1d7e24f6f8cac05077a3a':
    '有哪些辅助类 CSS 类名？请前往 <a href="https://baidu.github.io/amis/docs/concepts/style" target="_blank">样式说明</a>，除此之外你可以添加自定义类名，然后在系统配置中添加自定义样式。',
  'f19464cd1d7c55610b84b4972eaf506f':
    '<div> 当开启数据映射时，弹框中的数据只会包含设置的部分，请绑定数据。如：{"a": "${a}", "b": 2}。</div>',
  '784cf5219012b5f16f2985b7f1fd52f5':
    '<div>当值为 __undefined时，表示删除对应的字段，可以结合{"&": "$$"}来达到黑名单效果。</div>',
  'af9f27383daeec508815a33753247f42':
    '${data["&"] ? "已开启定制参数功能，可点击关闭该功能。" : "如果需要在默认数据的基础上定制参数，请配置开启参数定制再定义key和value。"}',
  'ff0d36eac9b9c029d91c365cd2c7e6cf': '${data["&"] ? "立即关闭" : "立即开启"}',
  '7237d0dfa065bf451120d06330291b7d':
    "({\n          isEmail: 'Email 格式不正确',\n          isRequired: '这是必填项',\n          isUrl: 'Url 格式不正确',\n          isInt: '请输入整形数字',\n          isAlpha: '请输入字母',\n          isNumeric: '请输入数字',\n          isAlphanumeric: '请输入字母或者数字',\n          isFloat: '请输入浮点型数值',\n          isWords: '请输入字母',\n          isUrlPath: '只能输入字母、数字、\\`-\\` 和 \\`_\\`.',\n          matchRegexp: '格式不正确, 请输入符合规则为 \\`$1\\` 的内容。',\n          minLength: '请输入更多的内容，至少输入 $1 个字符。',\n          maxLength: '请控制内容长度, 请不要输入 $1 个字符以上',\n          maximum: '当前输入值超出最大值 $1，请检查',\n          minimum: '当前输入值低于最小值 $1，请检查',\n          isJson: '请检查 Json 格式。',\n          isLength: '请输入长度为 $1 的内容',\n          notEmptyString: '请不要全输入空白字符',\n          equalsField: '输入的数据与 $1 值不一致',\n          equals: '输入的数据与 $1 不一致',\n          isPhoneNumber: '请输入合法的手机号码',\n          isTelNumber: '请输入合法的电话号码',\n          isZipcode: '请输入合法的邮编地址',\n          isId: '请输入合法的身份证号',\n      })[data.type] || ''",
  '5e568c42a5ecb74db2dc3d8531079dd6':
    '<a target="_blank" href="https://www.tiny.cloud/docs/general-configuration-guide/basic-setup/">参考文档</a>',
  'cadf0e302ddbc6fdbf005aed670b5e3e': '代码变化',
  '6006074bdabc639b86f42aa18876e33a': '代码变化时触发',
  '28e5fd494ea37a09fd2ad36d0f98bacc': '被删除的项',
  '046de1a6cb21ff83af74958342c0db64': '被激活的项',
  'f6e5fc909971d2e5f6ffe23edae93584':
    '如果数据比较多，比较卡顿时，可开启此配置项。',
  '31dae237cbd03ae0d2dcf7ad2fd4fb76': '初始化接口请求成功后返回的数据',
  '850c62f7b1ebfb4b89182ecd51202a7d':
    '表单提交成功后触发，如果事件源是按钮，且按钮的类型为“提交”，那么即便当前表单没有配置“保存接口”也将触发提交成功事件',
  '88b2930823f9fd6706473805e9b11797': '保存接口请求成功后返回的数据',
  '3e07258baf3c4389c1ffd4a98c20b8fe': '保存接口请求失败后返回的错误信息',
  '0d6581b6dd51f6c03a4edf26475d75c6': '被移除的项',
  '4ea280a2e54969de1d1b9bbd5b708e63': '点击选项',
  'f05520432bb87ced419a1da818c6cc9d': '选项被点击时触发',
  '91f7b7fdf9b91073ca3519260f7a62d7': '所点击的选项',
  '6afde638796d237377b0755506d08ded': '目标组件',
  'fee99f2bcced486e4753a8f58f511d3f': '追加数据',
  '3f3f4c71c1b736e7f01cf910a553ff43':
    '选择“是”时，会将源组件所在数据域变量同步到目标组件的数据域。<br/>如果目标组件是增删改查组件，且增删改查组件的数据拉取接口是get请求，则源组件所在数据域变量将追加到目标组件的初始化请求query中。',
  '2aecb19ca1655d66fc80fc27a783cc9e': '追加方式',
  'b67b01a3b9170f1daf78082cfd0df793':
    '选择“合并”时，会将数据合并到目标组件的数据域。<br/>选择“覆盖”时，数据会直接覆盖目标组件的数据域。',
  'bd81577a6fd4956e676cec499bb70d00': '合并',
  'e09fea40f7e4abd4b2a495b315940688': '覆盖',
  '9a5500b6013ec1ebf61bdf0e18452348':
    '数据默认为源组件所在数据域，可以选择“自定义”来定制所需数据',
  'f30bcdccf71b19e858c37d8881d2b206': '源组件所在数据域',
  'fa644cb20c66f7530d8a376d4fa6a36e': '组件数据',
  '7d9260bd45b2e85a09398f218c25f220':
    '更新目标组件的数据域或目标表单项的数据值',
  'e7af71fb102cc86ab3be6a2fb32b5e3f': '数据设置',
  '77b1081c177fa3334cc93c99f0ecee75': '直接赋值',
  'f5c5e3d69daee06ea1606378ef466765': '成员赋值',
  '697af73997072e0ce9ee65b15a7b3715': '重置页码',
  'd7bf42dd6e66f2818f9a232603c4a53b': '选择“是”时，将重新请求第一页数据。',
  '3e573fd37473d789211ee44335d82fad': '静态 CSS 类名',
  '905407c57ccd033cb6bd64bfad20a8c7': '静态展示时 的 CSS 类名',
  '0d1f68afa19f3f3dd88e28d17c98ddf9': '表格2',
  'e052287273ad39a1d3fa9fa3decb5fd9': '组件展示态',
  '506f28f48dbebd5d19e19dfc721e13be': '控制所选的组件的输入态/静态',
  '0e35b091e18032508758899735664df7': '组件切换为静态',
  '34d361256526b04909e064c29d9a9b76': '组件切换为输入态',
  'd2e930293da37452638759e17d771adf': '组件状态',
  'b535bea11c97ec5588b1494799de4d60': '表单输入',
  'f7784642f42d33f506ba05f3daefc3c4': '表单静态',
  'a8797a840f3c0bbb5297aada95f9b13a': '表单项静态',
  '7bb3e24cc54f4b8ee0a65d14fa4c067c': '激活指定选项卡',
  'fd951a59a7c635d5330bc1aeec22c813': '自定义数据',
  'b62ce3df3c0d5772006c525b60d5eeab':
    '数据默认为源组件所在数据域，开启“自定义”可以定制所需数据',
  '0f9803bd27434940d4017007c105a861':
    '当选择“是”，且目标组件是增删改查组件时，数据接口请求时将带上这些数据，其他类型的目标组件只有在数据接口是post请求时才会带上这些数据。',
  '81e1ff0bb8917a9df99d737982ee24b7':
    'SubForm, 配置一个子 form 作为当前的表单项',
  'a5c2dba5ccf62851b24cfa12d4958ce2':
    '可以用来展现数据的,可以用来展示数组类型的数据，比如 multiple  的子 form',
  '74104c62ed33836f0bc74297539dd7c9': '配置 options 可以实现选择选项',
  'b5ade3d97d5b11bc784786111e011571':
    '树型结构来选择，可通过 options 来配置选项，也可通过 source 拉取选项',
  '2c05e451a6f2b2fe1cf55f7afb8c8423':
    '单选或者多选，支持 source 拉取选项，选项可配置图片，也可以自定义 HTML 配置',
  'c65c9862813c7a66c0df52e301e0e1d1':
    '适用于选项中含有子项，可通过 source 拉取选项，支持多选',
  '8f650b58c8421edecfb380d6f60ef40e':
    '通过 pickerSchema 配置可供选取的数据源进行选择需要的数据，支持多选',
  'b7a4abc1e4e975c9df5bb9d9cf4823ba':
    '通过 options 配置选项，可通过 source 拉取选项',
  'f2fc416c7d95a93a8da621f760be8417':
    '支持多选，输入提示，可使用 source 获取选项',
  'b4482a3d9523f48e83a816fa85911185':
    '纯用来展示数据，可用来展示 json、date、image、progress 等数据',
  'ecfd82eb65102274188011a502913d3a': '抽屉数据',
  '951f802ebd0c0d795fbae6767a5ee9b3': '初始化接口请求成功',
  'da0126992b4937a5fd847ef5366b02e6': '初始化接口请求成功返回的数据',
  '70b8342d743374233bfee0f56c7f0fc7': '节点示例数据',
  '38f85482d657cd4db1280c5efa1950fd': '{{@1}}对齐方式',
  '0a0574baedb8eb2abf7daf25159d8bb1': '设置子元素在主轴上的对齐方式',
  '5ccc4c05cd41195f202f550a4c307a64': '设置子元素在交叉轴上的对齐方式',
  'b1b98c19058af70d8bd499e1899e93bc': '布局容器',
  '03097563d201ad3a29c79165226764e5':
    '布局容器 是基于 CSS Flex 实现的布局效果，它比 Grid 和 HBox 对子节点位置的可控性更强，比用 CSS 类的方式更易用',
  '********************************': '新的一列',
  'e5f9b3a3655b8daddcee8b97b735887f': '向前插入布局容器',
  '577b33bf128fba16ed8e9bf7c395f455': '向后插入布局容器',
  '31f84d1bc6175fd0828a81b5bfd98736': '新增列级元素',
  'cbc1d00cc640b67ee34a29a694ef162a': '左侧（上侧）插入列级容器',
  'bb3cc092e17ff83e943554bde3d5771b': '右侧（下侧）插入列级容器',
  'b19b454fe603e03e98ad9772615c7c32': '定位模式',
  '8444f01399c0003fbb68eeff1310566c': '指定当前容器元素的定位类型',
  '5ddea41072a27a74a1715549dfb79bc2': '相对',
  'e9513a013011450c57cfe3ef51b7d4b0': '固定(相对窗口)',
  '3059599d8ebfec00a8ab53346d9b4fa3': '绝对(相对父容器)',
  '86a6b5a0a45bba5b6187cc2277e3375e': '布局位置',
  '6e72759ed1cbf9f9e8523197dd93888f':
    '指定当前容器元素的定位位置，用于配置 top、right、bottom、left。',
  '6896da744f8ae9633263d55af0fceae1': '层级',
  '6f649980c839dffca1506f20d534fe3d':
    '指定元素的堆叠顺序，层级高的元素总是会处于较低层级元素的上面。',
  'a8489cf57d7f44e889aff79434776f47':
    '默认为块级，可设置为弹性布局模式（flex布局容器）',
  '4180e30c34190007ffaa654e0959b8a3': '行内弹性布局',
  'ebe7bde5c9094813e2924473488d281a': '行内块级',
  'dde193342b8c350ae29795117c0c5b9a': '水平对齐方式',
  '5b15af1f73b4f2d5bb152410863602f4': '垂直对齐方式',
  '78d32d2bd35c0262fe77b517c5a4fb62': '排列方向',
  '3fa460b81736c0360f6f7571801935b1':
    '设置成水平排列方向，则从左到右放置子项；设置成垂直排列方向，则从上到下放置子项',
  'fa228d6bec96d052de0ad369407f5241': '水平（起点在右端）',
  '2df3bc66ab3fcb0de1caf11831eff595': '垂直（起点在下沿）',
  '98b2fea2d8f3ceb81e9ce32d66383f05': '如何换行',
  '9af509c2a9636343199b9072e001826c': '默认（不换行）',
  'd4054144c4341872496e3550fdb1b826': '自动换行（颠倒）',
  'ee2df1c1a0d99094f641166535948d4b': '弹性模式',
  '947c03e411c20563c7ac67d0a5ad741b': '设置为弹性模式后，自动适配当前所在区域',
  'f92626f9e56b3e2d0c47495a446acf71': '弹性宽度',
  'cf8852316501c22ea19c4e432c59e7d7': '默认宽度',
  '9cc69c8469b23b77519065d3df381113':
    '定义在分配多余空间之前，项目占据的主轴空间（main size）',
  '0ad8b3b736ae5b9e23cf16ac13e1e283': '占比设置',
  'fa6bb048a2f73975a40789b30c5b8a06':
    '定义项目的放大比例，默认为0，即如果存在剩余空间，也不放大。',
  'c19b79073b676b9bade80613aba2dbfa': '固定高度',
  'd1b91a1a24f0d4935c2dd13e6a22b6d4': '最大宽度即当前元素最大的水平展示区域',
  'c18457fe4f249f06b48297ccfe6224e8': ' x轴滚动模式',
  'c2ed47a1f0f45cf7e2d22bddffc8a732': '用于设置水平方向的滚动模式',
  'cbc7af1d6422e88f4b87ade748e0f07d': '超出显示',
  'b48a90c77b5e792260d830c2d68c527e': '超出隐藏',
  'ddea62517e2bd1007712689746ebfe00': '滚动显示',
  '55becc96b40692cc9cf898b331d16976': '自动适配',
  'ede82efb4a69c35743185c6c73ab771e': '最小宽度即当前元素最小的水平展示区域',
  '6f420734edfaff00a8210a4c762a9207': '最大高度即当前元素最多的展示高度',
  '411f9d120093314cd38e6dd5cce398c6': '最小高度即当前元素最小的垂直展示区域',
  'ff9e9329fe186be342ef59ee711b9371': ' y轴滚动模式',
  'b31c6aaa78f8e24df665ce80ab5301e2': '用于设置垂直方向的滚动模式',
  '4fc0e68b093db41b45a4ea706fbe56f3': '居中显示',
  '55efb233147f9539de019d9abc7653f9':
    '通过将设置 margin: 0 auto 来达到居中显示',
  '2bf5bcbe21f39b254a601664fb8b264d': '默认（自动换行）',
  'b2d418355cb59a5613ecff7b150c588f': '不换行',
  '7d1313925f158b747c094a7f2480e535': '参考位置',
  '41a7494315a528f0f9618646f7e0dddf':
    '可设置为左上角、右上角、右下角、左下角，默认为右下角',
  '845c61ac8f51c6702dd22e5657c07e8d': '右下角(默认)',
  '2794fe303cf8ad4395fe93271fae7925':
    '布局容器主要用于设计复杂布局的容器组件，基于 CSS Flex 实现的布局效果，比 Grid 和 HBox 对子节点位置的可控性更强，比用 CSS 类的方式更简单易用',
  '********************************':
    '在分配多余空间之前，其默认占据的主轴空间（main size）',
  'dbb93e8f413074ead24b6ed822247d98': '上方插入布局容器',
  '5b5765b3fd7e72e04a5cd3e2ef6218a4': '下方插入布局容器',
  'ee466872b9a43e720e296813dbc5adee': '{{@1}}插入列级容器',
  '14c495b1248756310c75396cd41f4fe9': '上方',
  'e33ac3a4c1a95a02a18f1555038804da': '下方',
  'e731c52010266b8ade1e7e78e25cdccc': '常见布局',
  'f80bd0a5546465336f4f9cafdfa8b67f': '默认高度',
  'ba9ccf1040d7abd0848046330ba3558c': '经典布局',
  '230d65546ea0d299907943403608233c':
    '常见布局：经典布局（基于 CSS Flex 实现的布局容器）。',
  '9bbb7cfaeb34a2b5c095ac253355f028': '悬浮容器',
  'a3e91631c1a3a43e09526ea7f6b8595c':
    '常见布局：悬浮容器（基于 CSS Flex 实现的布局容器）。',
  'd423930b823fc45f08c18922b19e4e9e': '吸底容器',
  'b8b4eb373d8ba6f98271b681fba2511d':
    '常见布局：吸底容器（基于 CSS Flex 实现的布局容器）。',
  'faaa6444a709917ff33e0d58948504dc': '吸顶容器',
  '1facf0bd0f56c66759857345e7434443':
    '常见布局：吸顶容器（基于 CSS Flex 实现的布局容器）。',
  'f416a3a2566dda04bc0ef67027e6f460': 'x轴滚动容器',
  'e3d9ad8453925764f2918dbfd6ff824e':
    '常见布局：x轴滚动容器（基于 CSS Flex 实现的布局容器）。',
  '053e0cbf18c8fe59b928d52fcd556b88': 'y轴滚动容器',
  'c9f089cefc06c217c6dddfe2fc772ea3':
    '常见布局：y轴滚动容器（基于 CSS Flex 实现的布局容器）。',
  '3587540660a01f8a8aff6a2c0409a404': '开启弹性模式后，自动适配当前所在区域',
  '2ea6f1f33dec7cb3c23a1bf1f9eab11a': '鼠标点击',
  'fe9228649853d08eebee72ad5521a3dd': '鼠标点击时触发',
  'a643d2fe12d205eb8fb5cffe92f62c35': '鼠标悬停时触发',
  '764c134791952dd1acb0f23587e75421': '切换图例选中状态',
  '262cd2f688751332c1907a659e686210': '切换图例选中状态时触发',
  'fd312ae2f1c24b8a14d9412bb3c6bb76': '最近n天',
  '68cba5f27ab003cfada5eb4c1f29eb21': 'n天以内',
  '34b06708894a178c440e6f6539e95e9e': '最近n周',
  'f0e1b8d8a6e731360d7348bc8301d44a': 'n周以内',
  '5cad2778bb8f01d1a1b1226082eb2117': '最近n月',
  'ff76f60b3da86a0d2c6663b170a7955a': 'n月以内',
  '9ff9b56c9ed633ee09396830e93113ad': '最近n季度',
  '2b80b20008b3ae81136217ae10a1fbaf': 'n季度以内',
  '84752114d27119dc50d8a7b9ac0b788b': '最近n年',
  '5df2352afebd73d1f568ebaa81e2db28': 'n年以内',
  '8572d14f815d840bd9e940cd8ee4e380': '通过键盘上下方向键来加减数据值',
  '925d31bb30d63576600299475a910c33': '输入内容前展示，不包含在数据值中',
  '42677544e2cbee28f7e7df216e685543': '输入内容后展示，不包含在数据值中',
  '046c6233e03af774df7b90b4504fa96c': '最近',
  '249aba763258bbe488af3e79a381d265': '天',
  '2f8ab07bea442bc6ce8a9e99ff88e5c1': '天以内',
  'a657f46f5bb00961adfae80d12e41b3d': '周',
  'b1599eaa05dc9b3d90be5aebc5c84338': '周以内',
  'e42b99d59954ce6437e66f416850425a': '月',
  '3c690347976de82df1909750cbc82b80': '月以内',
  'b62ba98a627851e911bef8fbb005bd4a': '季度以内',
  '465260fe80b0c3338d06194bb7a94446': '年',
  '137bcb84f52bbd48623c37d8dfdebdff': '年以内',
  '279d93f2d745ed08e9034022941510dc': '常用跨度',
  '7a1e5f93e362d371519bcb2bfdb0fc9a': '自定义跨度',
  '4a12e9b9fc5443e5e9999b5a7c56c19a': '默认（占满）',
  '2cb472ff9cad0c89a033c53996b52053': '初始化',
  '76ddcc0ad85aa4be6875b73244a64faf': '组件实例被创建并插入 DOM 中时触发',
  '584e4b6108e132be92c9de09d7bbed72': '当前数据域',
  '9328b90ded33d16a873db5c0dbd815b8': '初始化数据接口请求成功',
  'f3b97bd71a77cca1e9288089a537cf3b': '远程初始化数据接口请求成功时触发',
  '9787088794f42c7e476cf7580f81447e': '初始化数据接口请求成功返回的数据',
  '029e280e119b028bffc51424d909c07d': '选中的项',
  'a6eebb9b4fc7148e2dc41335c74e6393': '初始化Schema接口请求成功',
  '887954cd9bdb290003984fe9a6eb99e2': '远程初始化Schema接口请求成功时触发',
  '8f0064a9cfd7dcbb3c729f1357f11772': '发送数据',
  '6d33298a54888a30753373ca5bfe3bc2': '返回数据',
  '542e06175ff4f7407c467bbde90de56a':
    '如果需要对返回结果中的data做额外的数据处理，请开启此选项',
  '7c583ecdf795ce4f1f40d7960ead9344': '默认提示文案',
  '70941a02776496ec446f21f98ebf754e': '请求成功',
  'f50bf418323ee425eca7208c09a4577e': '请求失败',
  'f3dc08e3617d1e19cf8135be4412a80b':
    '点击后先询问用户，由手动确认后再执行动作，避免误触。可从数据域变量中取值。',
  'faa29265819714253843e23437b9193e':
    '正常状态下的提示内容，不填则不弹出提示。可从数据域变量中取值。',
  'f855f46ce6146aa17a9ed423da16bfa2':
    '禁用状态下的提示内容，不填则弹出正常提示。可从数据域变量中取值。',
  '037becbe8bff2f8838d141cc7b6b2df7':
    '支持例如: <code>now、+3days、-2weeks、+1hour、+2years</code> 等（minute|min|hour|day|week|month|year|weekday|second|millisecond）这种相对值用法',
  '8f7ae284d0039fe05b9f57fd5ae3ede9': '请选择静态值',
  '98229308e2e9484583fde4ae363a979f': '表达式或相对值',
  '6b3c2a07db1bb3c229bbc5df48068792':
    '支持例如: <code>now、+3days、-2weeks、+1hour、+2years</code> 等（minute|hour|day|week|month|year|weekday|second|millisecond）这种相对值用法',
  'dcc94ea1715bd502c709c5d5092e9c82':
    '支持例如: <code>3days、2weeks、1hour、2years</code> 等（minute|hour|day|week|month|year|weekday|second|millisecond）这种相对值用法',
  '13ce82d026daa5a30e50105bd2a204a6': '请输入相对值',
  '31c29c46536a5007522032d2a42db56a': '数值模板',
  'a6a41d1bfb5896210eb527d183a07958':
    '值渲染模板，支持JSX、数据域变量使用, 默认 ${value}%',
  'eadd1d64cd6ceb2c50554281cd2d3be0': '根据该数据来动态重复渲染所配置的选项卡',
  '0d9d899edb456e8806a99850e9c38212': '已配置表达式',
  'fbb96f7ea104d34fc4b7bd625d892c45': '点击编写表达式',
  'e0c7ac5eb397512fdbe71600baa09dab': '请输入静态值',
  '48942ef507ea38d8ead03f8bfdffae5a': '已配置相对值',
  '49041f245018a6d799fee3c6f177c782': '退出全屏',
  '185926bf986c784d03a9a73102da6542': '全屏',
  '891ec6336d4243714c25eecb2f8f774a': '选项文本',
  'fb7ea2b05ca7328ee16a562d90c2eb96': '选项字段',
  '1ca87f0171481e27d94e81b477150b7d': '选项模板',
  'd6ecb32a380c91887a9346653c2427e9':
    '自定义选项渲染模板，支持JSX、数据域变量使用',
  'cb048b2d8426afd464dc01b72c446342': '块级(默认)',
  '3b6e8d54b7b2ae890d5357b7eaaeaaf2': '不换行(默认)',
  '0611733b53e0098e6fd880bd44b2806f': '最小高度即当前元素最小的垂直展示区域',
  '03bfb834c8a5fef58d885e448a4e13b4': '虚拟列表阈值',
  '50437e080edc71ab624c93d419472919':
    '当选项数量超过阈值后，会开启虚拟列表以优化性能',
  '02b9880e1d2df8a07e90e9878080c739': '选项高度',
  'a3f66655c3d2bcfecc6afba0e4424460': '开启虚拟列表时每个选项的高度',
  '183f00df0922a6be371fea58cd46a60a': '弹性高度',
  '9e7c8d1554f6449121a83f951cf21ca1': '弹性比例',
  '99b57d8c9244ff9a695fcd519b4e2e57': '最大宽度',
  'fc2bc4193eea63128961d09497e07dc8': '最大高度',
  'a2b62974f4d7564bb68b570116f25a10': '最小宽度',
  '2bd921d0ea7a73b77ee0fcddb1afcc84': '最小高度',
  'eb7366583485f478e3d8c2b105ea51ff':
    '定义项目的放大比例，如果设置为0，即使父容器存在剩余空间，也不放大。',
  '5d3d48de1ba22368eacdc1c69fb044ce': '组件内层 CSS 类名',
  '72c32b47c5e4dcd58ddabdb8fde761a0':
    '悬浮容器: 基于 CSS Flex 实现的特殊布局容器。',
  'a03384e93b62e3110aa92c9e345111ba':
    '吸底容器: 基于 CSS Flex 实现的布局容器。',
  'f564e3e66dd2aca0e080cb6484c95a5e':
    '吸顶容器: 基于 CSS Flex 实现的布局容器。',
  'db805d4e361ac2d3fc6047eaea1a7c69': '自由容器',
  '9ccbfde404798593fa6fdeac9dbef200':
    '自由容器: 其直接子元素支持拖拽调整位置。',
  'f20ca09e513399510ce34ba2cb05b7d9':
    'x轴滚动容器: 基于 CSS Flex 实现的布局容器。',
  '47ef0cec2c3436377a42390e10de4567':
    'y轴滚动容器: 基于 CSS Flex 实现的布局容器。',
  '8c2bb89f516205027b9ed6609fb17726': '阻断条件',
  '46a3c6ab94da0b16a707bdd3b74c9e09':
    '满足条件时，将会阻断当前事件的后续动作的执行',
  '6e6d4269d0dc3324d551062350a2ae9f': '静默请求',
  '56e13c39822a814ab39b0d5a0867d7dc':
    '开启后，服务请求将以静默模式发送，即不会弹出成功或报错提示。',
  '7f019b96ffb7d72ec8d6ce8d76e5362f': '表达式已配置',
  '699829ed5bab67dfb360764c3bbaed4e': '{{@1}}-事件配置',
  '5ba999eb762f60324033b735e55d989c': '事件防重',
  '7fe94616be0e8fb5ef5ab40a7397f0aa':
    '开启事件防重后，防重时间内多次触发事件只会执行最后一次',
  '91b72e901f1663637157cda638ac4dcc': '防重时间',
  '969e9e56b3812abffa3994f35ea31835': '吸附容器',
  'bb0e1fea25aafbe731886295fcef9a2e': '吸附容器: 可设置成吸顶或者吸顶展示。',
  '7d30297d4e1f310c73b27be88c748026': '吸附位置',
  '2ea76e2ffc1b92911d6f7decfa993360': '吸顶',
  '8cfc818a76662085cb64752d6d592fbe': '吸底',
  'e2f6535e21570a0703c7c65f41b30eaa': '滚动吸附',
  '1c5ea0ffb2b15713cb22c41a02576924':
    '开启滚动吸附后，当滚动至父容器上下边沿时会自动开启吸附模式。',
  '5f9be0002394f0b58952969d5952e24c': '用于设置滚动吸附时的位置',
  '28968f372fe88c0ef855c7f79f42bbad': '均匀分布',
  'd481ed97681365f18cf20ef914473a02': '起点对齐',
  '02f6ac7a3bf6a8b4440a2a1c3e1daeea': '终点对齐',
  'cbac406a3f51abad691702015b0784ba': '高度撑满',
  '5a4e41af91746f8a3905aa9f66048955': '加载设置',
  '6ade3082696deb00357f5c0359093cd4': '合并到上层loading',
  'b6ac896eff6a6502e4ae7079b3e507a5': '不展示loading',
  '0e9ffe1f1cf3f7a620970ea75dba9f39': '使用页面全局loading',
  'b1119174b1beedd2218e0b359ef14aec': '表单提交',
  'c6a65a86cd2bd74a6f72df0a7b81d3cb':
    '配置该事件后将不会触发表单提交时默认的校验、提交到api或者target等行为，所有行为需要自己配置',
  'c207ba29769aca6ffd45db9f80bcb29e': '以零为头部',
  'f02f876ee64cc016d97fa4dc498d4857': '高度设置',
  '091885db07e43ff7cbe60c3b664b0b50': '弹性',
  '363165ccee78341a65f1d42174e8b08f': '适配',
  'eb44269adb6ba70569cd62ea88cb2750': '弹性设置',
  '64561733c68085af3d09315c1d4d7ed6': '鼠标事件对象',
  '052f93928af33d4d7035e7c8e8a73f17':
    '通过 margin 数值来设置对齐方式，其中 margin: 0 auto 用于设置居中对齐',
  'a738a8594bd2b71002d09277b84d86dd': '靠左',
  'fc0f19e9e47e352d36d36cf6eb653210': '靠右',
  'c70638412c6cffd150117ae403dea939': '组件静态数据',
  '77fdd35933c099cdcb64b71f3fbe7a6c': '数据列表',
  'f01c3cd99ecb0c534cc47081d3433c9f': '自定义样式',
  'f7e3d7e3146bb53b5e6f09f7c90dea3a': '输入框样式',
  'a39a2916d17f2b9ba11853066b4225f3': 'AddOn样式',
  '9b6425cd2d496c9cb5a6c6b8ff125d1b': '输入框',
  'b9745a6b870f02a0483a4a4f74814c68': '自定义样式源码',
  'ec8f3e4a298160dface2fda5c0125df2': '请输入类名',
  '7e8eb474f73b6fd60c9aa5082f75e008': 'Label样式',
  'ef55ff12fb0d3efa3491236fee4dabc2': '描述样式',
  '60bac4c0a381a42b320a703227be59eb': '间距',
  '00d1a6230e9a31664c895e95fde707d3': '添加项',
  'cf3f7c0c1e6c77197bb0b6508a9173aa': '添加新的项',
  '3a553b1123f403cf1f81eb28b3e4b814': '最多条数',
  '93bc4888bc13bb6e6c49b34b54077438': '最少条数',
  'e61c32382d879b867938086a50ef094e': '打平值',
  '30d4e9f1c60f2d5851f77463476d568b':
    '如需要拓展自定义的新增功能，可通过配置组件-新增项来拓展',
  '405a48545938c075e62eafb80b732769': '按钮模式',
  'f4b06bd9e4f5ceaac7fbb0e17fffc518': '文案',
  '178bf4dd4b8d56370e2fc8275f9dc9e4': '删除确认文案，当配置删除接口生效',
  'cb8f7758eb03574f9b8402659c0f02b2': '确认要删除吗？',
  'f7894f17d0eba299011d856ce3efea73': '自动填充父级变量',
  '7fc8dab2069004acb8f5c98c27ee0883': '同步字段',
  '8e32c9ffc35059f8ac5254ffaf3c99b0': '标题模版',
  '21766034147682a2435ecc766de5ea22': '多行展示',
  'd8848daffd80dbb21ace876ade2ea8a8': '行政编码',
  '977cfa6e8c7f036674afedfcc19ec7a2': '对象结构',
  '05fcf03ba044a04ce81bdbe6eaf49d17': '可选城市',
  '912beb37a3785e50d6483852be41111f':
    '树型结构选择，支持 [内嵌模式] 与 [浮层模式] 的外观切换',
  '0b52d7d00ed28e41f4845fec1622d056': 'tree、树下拉、树下拉框、tree-select',
  'ea687c59ff7d980afc55427853af864a': '树选择框 - 内嵌模式',
  'ce0e89aa6a8e17562c265714b33b4f82': '编辑模式',
  'ba1a077af717cb3c4788849c2342200b':
    '单独给立即保存配置接口，如果不配置，则默认使用quickSaveItemApi。',
  'cf8774ca07f11ecb14b3b59c3891e9cc':
    '<p>当前组件已停止维护，建议您使用<a href="https://baidu.gitee.io/amis/zh-CN/components/form/formitem#%E9%85%8D%E7%BD%AE%E9%9D%99%E6%80%81%E5%B1%95%E7%A4%BA" target="_blank">静态展示</a>新特性实现表单项的静态展示。</p>',
  '26122d95c72204c83ebdc37cd16a96f9': '图片放大功能',
  'a19769d02b8de60a1e3b46c3ef96f122':
    '放大功能和打开外部链接功能是冲突的，若要点击时打开外部链接请先关闭此功能',
  'd196eb8f65e84003b7ca64d5dd8fc737': '占位图',
  'd98b3f3d11e6dce8eac1a041247fbc50': '图片列表',
  '8e1fee898434093aad55f5888497f970':
    '<p>当前为字段内容节点配置，选择上层还有更多配置</p>',
  '86aafaa75b388deb4a4cbdab2293c099': '头部',
  'e6aa1b827415217c524ae9d9b665cca5': '配置头部',
  'a2ecfd5a0db9c855f59eea75083678e6': '配置底部',
  'fa0b9c20503ee00554e8c86d4a4461b6':
    '返回日志信息的服务，后端需要通过流的方式返回结果。\n                  可参考<a target="_blank" href="https://baidu.github.io/amis/zh-CN/components/log#%E5%90%8E%E7%AB%AF%E5%AE%9E%E7%8E%B0%E5%8F%82%E8%80%83">示例</a>',
  '1d3d7de6b84f4d5d4a4e126e145af132': '文本编码',
  'd55bbcd785be46c3182dcd7663c3c041':
    '返回内容的字符编码，例如 UTF-8、ISO-8859-2、KOI8-R、GBK等等。默认UTF-8',
  '0021bd6b2290ddca1ad6a9c8848377bc': '加载提示',
  'f013ea9dcba3f5ca1278aa850931fec8': '加载中',
  'd7eb1e98e6cfbe38ab3b242c4dda1b7b': '跟随底部',
  'ab2dd531871defe1ef7d8d06548411f3': '自动滚动到底部，方便查看最新日志内容',
  '41058383885336fbe750665044e4e38a': '可在日志顶部添加以下操作按钮',
  '095e938e2a09eaddc8db146b86879f4a': '停止',
  '0cee3cd1e0b0956fb3447d7188553e4b': '隐藏行号',
  'c2f42bd2a149bbeb4627b1e1b5fabedb': '性能优化',
  '45f14d9548decc8a1df4e470efcf8d08': '每行高度',
  'ef744ba73b07d85f877fa8774f64da36':
    '设置每行高度，这时就会默认启用虚拟渲染，避免渲染卡顿。\n                    <ul><li>优点：仍然可以查看所有日志</li>\n                    <li>缺点：如果某一行日志很长也不会自动折行，会出现水平滚动条</li></ul>\n                ',
  '7a5d14c5f61745f08700ba315609cf9a': '显示行数',
  '686e0a9356c1cbbaff2ce3b98bf8439a':
    '限制最大显示行数，避免渲染卡顿，默认不限制。\n                    <ul><li>优点：某一行日志很长的时候会自动折行</li>\n                    <li>缺点：无法查看之前的日志</li></ul>\n                ',
  'bddc8f2d9bdcc083bb4f3dd38eaba459': '展示区域高度',
  '0218cbc88086353118fea07e364334d4': '开心',
  '8167b3dd560a83cdc757d1022ff111e2': '愤怒',
  '8fa28f95c03faa7cc87e487a89a35a72': '伤心',
  'd246bdddaf59436cb2160837f7bef634': '冷漠',
  '2ab01e418dca1500dcb133d50656deea': '一般',
  '3585e4dd456b41fb8aec43f927b6a27c': '自定义显示模板',
  '25e4c39320150bca74b4c05c7740e365': '配置显示模板',
  '9e25d776a57c610940bcc1c19847b97d': '顶部外层',
  '2ec1b0bb189b486945e79c167a4a024d': '底部外层',
  '77ad0879912d0a306724c319eed113e2': '用于标记和选择的标签',
  'ee66d7a2d02188816d633d11cf1a8b27': '普通标签',
  '35242cff1266fd3610f124b0e5e76f9b': '普通',
  '092c4410e162bb3371f2aab804501f24': '前置图标',
  '3e7392ab43c6c76f294f41c3058c2ab1': '值匹配字段',
  '2135be5a60fc3c6fe864e159e289af51':
    '映射表是数组对象，且对象有多个key时，用来匹配值的字段, 默认为value',
  '6a1ca1c113a9b5bee58ea009e40f5954': '匹配字段，默认为value',
  'dc7becbad712786c9e4766636047e509':
    '映射表是数组对象时，用作显示的字段，显示多字段请使用自定义显示模板，默认为label',
  '1140acc9ac4b803b733f70445e7d495a': '显示字段，默认为label',
  '55497c5de8558f1a164e437205cd510b':
    '<p>当值命中左侧 Key 时，展示右侧内容<br />当没有命中时，默认展示 Key 为 <code>*</code>的内容</div><br />(请确保key值唯一)',
  '94124fa72dbfbb81611496cada6ebf4c': '请保持数组成员结构相同',
  'a3221d2d224767df4afa7a8653ded8fe': '内部对齐方式',
  'e8bb313fb86cf474c0e264794bc85896': '是否固定表头',
  '8e903bee4578f72bbecf9eb62d7b875c': '显示层级',
  'c17fef27ea1d970fc66f4c4c3d442129': '默认内间距',
  '236b0cdd2e18418fd17d3cdfcace239e': '插件',
  'defe851634125bb16e762f26dbb6555f':
    '查看 https://www.tiny.cloud/docs/general-configuration-guide/basic-setup/ 文档',
  'df025e01cbbae804f7d720e6b932e8e8': '显示菜单栏',
  'b74c3bbb8ec4f18896cd3b5a20ee9e2c': '工具栏-大屏',
  'e08c3505f9779bf919628166a77d0d77':
    '屏幕宽度≥1200px，参考文档：https://froala.com/wysiwyg-editor/docs/options/',
  '04f91b84d48285162d3e29205a194143': '工具栏-中屏',
  '9ac0e46a361565f0b8be2228bef4b679':
    '屏幕宽度≥992px，如果不配置就和大屏设置的工具栏一致，参考文档：https://froala.com/wysiwyg-editor/docs/options/',
  '70d3894aaed305d2fc67f91122d77759': '工具栏-小屏',
  'a3133ac3b34da77e612bcb1763adae1e':
    '屏幕宽度≥768px，如果不配置就和大屏设置的工具栏一致，参考文档：https://froala.com/wysiwyg-editor/docs/options/',
  '194ab42ad1ca7fca9a6ef84fadf99490': '快速插入',
  '94621f44f98c996e49d5b214aebefffc': '字数统计',
  'b720ed498f054a2539d496d0da68f85b': '图片接收接口',
  '376496a8918c57220159951e24d3b72d': '视频接收接口',
  '555a9859e2c34b015438bdfb59a57c5b':
    '${vendor === "tinymce" ? "编辑器" : "编辑区域"}',
  'afccc17d5d672b10a1292dcd671ef534': '快速构建表格编辑框',
  '1711a82f9b0825015c2c49d9659c9837': '展示类型',
  '63bb911d6392cb2740140d406ab83d37': '编辑类型',
  '6530334ebf5ca810e576858eba168685': '选择框',
  'f25cb224e4543c1dc0228fca8dbfaf1c': '颜色选择框',
  '1145703a07e11b6e680ee2ec8afae0b4': '多选框',
  'a877779dc422c5321057b638d6d520e5': '城市选择器',
  'e2c59e63f751088ad8b43a1439e4322a': '新增单行数据',
  '41c1e36d5d202588511710ff0ccb9e8d': '删除单行数据',
  'f38b7eb6f78af9ae86c449153de2fe1f': '清除组件数据',
  '5e2eb93cf353f5bfa0920553fc8a0087': '触发表格数据更新',
  'efc47e9829b5831e4d0424191049b880': '删除某行的数据',
  '383c125a11b0dd070b04194b9e2e78fb':
    '在新增或编辑单行数据时生效。开启时，输入完内容后需要点击表格右侧的“保存”按钮后才能提交到数据域。未开启时，输入完内容后就能提交数据域。',
  '1d96dc9b36793e242322dd1e092a010c': '确认按钮名称',
  'c507f40ae40ad10fd0b99ac025a6e5d4': '确认图标',
  'e0698c2a2d5c568edfc5a0b1a1d298eb': '取消按钮名称',
  '08f06b06a0db0e978e3acdf7741ccd86': '取消图标',
  'ffa655818f7dd46fb2a767c51618741b': '空数据提示',
  'd173fb23320acba326a4424133969256': '显示序号',
  '26eb498526ba909386befc43466db79d': '每页展示条数',
  '078f9734eeef4ff8c06e0b639ce6bd8f': '如果为空，则不进行分页',
  '52029187eaa09f55193b6a15387e45ca': '最小行数',
  '3d99d1f17ebf865877e681397c61dc9d': '最大行数',
  'aa8b2a821e8e32196a720eaaa41b64d3':
    '为了性能，默认其他表单项项值变化不会让当前表格更新，有时候为了同步获取其他表单项字段，需要开启这个。',
  '2aa56a9b94ee3fde76a15711c94fdabc':
    '是否可以访问父级数据，也就是表单中的同级数据，通常需要跟 “严格模式”属性搭配使用。',
  '53a9db06d0b7e3482dc21e53f150e257': '行样式',
  'd679aea3aae1201e38c4baaaeef86efe': '运行中',
  'e5ac1d2029adff17ec123b86ea07ce26': '排队中',
  'f406ef0ea3f09572835a9b1ec61f576a': '调度中',
  'acd5cb847a4aff235c9a01ddeb6f9770': '失败',
  '391299a1c1722a777991a182a44bccbf':
    '默认支持如下几种状态，无需配置即可使用。自定义状态会和默认状态合并。',
  '33563f1d3d203bc682069a8c85506b86': '默认icon值',
  '2cc90d2f2cd9ba213f9aace88c386f3f': '默认label',
  'aad245582dc9f55cf71e3934bb3b1709': '默认value值',
  '2634cea5a95ece79e05b5a68c38cbd4d': '必填项',
  '2464e9d13bfc84169eb8333b6996203c': '更新目标组件或变量的数据值',
  'c85c8d61a67014c4b5d44f25e49e87fc': '设置变量「',
  'df24d894cd3331f53964bc75f5c192e2': '」的数据',
  '9e1bafbb00018beacc8f579c8ddfaa36': '设置组件「',
  '6c6e12c54723170f214527bedaf81f7d': '动作类型',
  '1b7e6b2dbf3b7f4b1baf2c42e49a995d': '组件变量',
  '2eb4c7ac45befad0f1f9c750bda57166': '应用临时变量',
  '844a7a7aacc5be82d0fd6225edc6bf63': '请选择变量',
  '85451d2eb59327a23e8f745161066d4a': '请输入变量值',
  '3d4d83f05a12364e2522fcfb265d8ce8':
    '开启时，新增、编辑需要点击表格右侧的“保存”按钮才能变更组件数据。未开启时，新增、编辑、删除操作直接改变组件数据。',
  'c18169dd6fceab2f023216fa6f7d22c1': '确认按钮图标',
  '5720057e62e80f7a04489dc4c035b4f1': '取消按钮图标',
  '8985ea173dce8f9bee667b3cdf0b7bdf': '此配置项只作用于表格操作栏的“新增”按钮',
  '3f64a567662a24714768237a3a6d0de7': '表格下方新增按钮',
  '9dd651411c1cb25e19249bb4ea8878c3': '动画间隔(ms)',
  '46bc66b19c2b589ebd24d1c583325080': '动画时长(ms)',
  '9cb33a16b57ef10b79ae76a66379d66f': '箭头一直显示',
  '0bf60b32f9db93b87e08763b1c815469': '数量',
  '98e04bf7cb91497e4225d272e3a331c8': '自定义箭头',
  '7076ef56f5c4f13d3c9bf87d3536352f': '左箭头',
  'fce3880b7a24a47f02a16331a294b255': '右箭头',
  'f4f965513462fcc9fe6fe896a9c249d8': '多图展示',
  '522cddc343d72db3db80cf3d71f99210':
    'API返回格式不正确，请点击接口地址右侧示例查看CRUD数据接口结构要求',
  '9b39126b20e519bb1c6e9054f4b55784':
    '<p><code>ids</code>: <span>用 id 来记录新的顺序</span></p>\n              <p><code>rows</code>: <span>数组格式，新的顺序，数组里面包含所有原始信息</span></p>\n              <p><code>insetAfter</code> / <code>insertBefore</code>: <span>这是 amis 生成的 diff 信息，对象格式，key 为目标成员的 primaryField 值，即 id，value 为数组，数组中存放成员 primaryField 值</span></p>',
  '5323ab3e5c12066101244f0577c30e22': '自定义容器区域',
  'b34422e637c90181d3fca4485a97c712': '分数',
  '95e0d70d1809d5267c2419eda58e78ca': '等级',
  'c13998e4c837dc40b8e90828d99561df': '为空则不支持编辑',
  '738b58219dda4a849e293c0f75d06438': '操作栏新增按钮',
  '306abb77f96a1048cf6e61bfe6e7bae4': '如果为空则不进行分页',
  '136ecd1380f3fa10c1fd99b93c63fc9b': '是否新开页面',
  '9778ba8e0cbbae9471485dfc28df8948': '初始是否展开',
  '18eae9f567a9c425bf59147a2601ee6a': '菜单点击',
  '12b91237057ca7d81d50cca9873c8267': '菜单点击时触发',
  '0a974408aab362095e939e5364315971': '菜单选中',
  '4b5f4893cf06a9d9ea5b8486bef87c26': '菜单选中时触发',
  '9b317a5d43f72026b0c0699b1d596436': '菜单展开',
  '9ef3c85c39e1e686f6b8c58292352ce1': '菜单展开时触发',
  'afce1df2e30a4674f507292d642fe270': '菜单折叠',
  '4015091668f286adf085d60a040f89a1': '菜单折叠时触发',
  '2631c12c40aa18fff4c6a2793f03f95b': '数据加载完成',
  '26a92da0738fed6a6178be565cfb3a59': '数据加载完成后触发',
  '67d8dc76a8c8a3f0f52232969d72e8bb': '更新菜单项',
  '12fbc0729d69e4243c4818093e96de8c': '触发组件更新菜单项',
  'ee621e1f1429f1150a69bd02eecf6964': '触发组件的折叠与展开',
  'e0e2b0cf2a3c8379b1d9144b10b3824d': '横向摆放',
  '8dd27bbe64ec245ce449caab5cf7f12b':
    '默认为内联模式，开启后子菜单不在父级下方展开，会悬浮在菜单的侧边展示',
  '154a7ec36c43427f73705ae834967703': '子菜单悬浮展示',
  '9dcb6b749d6682f1ac51b2dcdefd3208':
    '点击菜单，只展开当前父级菜单，收起其他展开的菜单',
  '49a84da9b39148af54903ba4c6ed257f': '默认展开层级',
  'd4b70ea3a756ec4d06c8ecb2f27330ab': '默认展开全部菜单的对应层级',
  '96198d14846a941a60824bd6ebda4d9e': '最大显示层级',
  'cac013d011fe1ac71c3b9f0bd0594d65':
    '配置后将隐藏超过该层级的菜单项，如最大显示两级，菜单项的三级及以下将被隐藏',
  '7419d6bab78d959d5c3b7a38f9888258': '菜单项',
  '1a9aaf05889002e65708c4950012a652': '对，你刚刚点击了',
  '243e91d5fbc5a03a5c54da6d8e662e4f':
    '页面渲染器，页面的顶级入口。包含多个区域，您可以选择在不同的区域里面放置不同的渲染器。',
  'a7999d1390b7dc775e4287511c395d6c': '角标类型',
  '31175366279c15232e27b6736ccb2fd4': '仅在文本内容为数字下生效',
  '58f966670529f4753fb3c5a0647606be': '大小',
  'b3c1b71ed42c7f2fe55f3c64346f1ae6':
    '此处配置角标样式，需同时在菜单项中配置角标内容后角标生效',
  '515e8b11c3c24c6c3b03c6c76b1da496': '默认选中菜单',
  '4f80ea1e761598ba9fe393e7c745468d':
    '优先根据当前浏览器地址栏URL进行匹配，未命中时将选中您配置的菜单项',
  'e3cd6fc0612b22f7c7e209b8184378c8': '菜单名称:',
  '4cd5629f32fc9710fbb0291b51adc34b': '请输入菜单名称',
  '9e0b4b20e7177b83916ab7b017c4162b': '跳转地址:',
  '2e507449ccaac4522f503f0ec3dfffa9': '请输入跳转地址',
  'ec3cc7a898eb5eb6f87a1d70a22a05c7': '是否需要新开页面:',
  'a93a3970825296478d40658ad07af43a': '初始是否折叠:',
  'd5004744a86589d72041b0a3fa8efa7b': '包含子菜单:',
  '14794add5446201274dd148086bc0361': '添加菜单',
  'aae5ccb98564e19c48f19c740c3c10b7': '默认(static)',
  '5d721446605f21bddb3b8e2ab2a3841c': '相对(relative)',
  'b55b525d56fd0d4d3dcb9291e59e3433': '固定(fixed)',
  '25ece43050dda20ad4d8dd058dd590d1': '绝对(absolute)',
  '22b47452f52254ce07507287d137d167':
    '<div> 当开启数据映射时，弹框中的数据只会包含设置的部分，请绑定数据。如：{"a": "\\${a}", "b": 2}。</div>',
  '68419387f5bb8487a848b818d78424ae':
    '<div>当值为 __undefined时，表示删除对应的字段，可以结合{"&": "\\$$"}来达到黑名单效果。</div>',
  'cb65841ea7dec5ae0af20b3f5e52abfc': '原始数据打平',
  '6922790f45faf064e063069816e4d2ec':
    '开启后，会将所有原始数据打平设置到 data 中，并在此基础上定制',
  '9791b05a4df9d72f1a01b81fa695fbc6':
    '当多列的分组名称设置一致时，表格会在显示表头的上层显示超级表头，<a href="https://baidu.github.io/amis/zh-CN/components/table#%E8%B6%85%E7%BA%A7%E8%A1%A8%E5%A4%B4" target="_blank">示例</a>',
  '7441768e25f67a80f0f2173c2a618c35': '日期值',
  '8036cf5e8dbf62ee4f4e896f7354ce5c': '日期时间值',
  '598f69a9b640508d8e124fd7d33131f0': '选择图标',
  'ef79da787ad206e5d5f8cf62e195c836': '图标大小',
  'ff58428ef8221c4c1bbf532dd3c77113': '图标底边距',
  '88f109195ad926bcd436f0c56198240d': '图片上传按钮',
  'd825ba2b8ea0c1b0737b0dd5ca9bc128': '上传图标',
  'bade9c4e0b8a75a251c1a2760571d3c3': '数字输入框样式',
  '53069052573de671c6e5108de745b036': '数字输入框',
  '5c48fa6f337d2b5dd168a0887c6baa2b': '表格表单数据',
  'fb7b49ff7f85f6e3f995b5eaae42d084': '标签选择',
  '0aefac04b467ce313ca6b05e33c6145a': '最大字数',
  '188676cb26835b8e4d8ea568467c55cd': '限制输入最多文字数量',
  'f1ee660e12ef0b76c10cccc3d923e605':
    'AK不能为空，请访问http://lbsyun.baidu.com/获取密钥(AK)',
  '4d01bb9f50eb1480e0e9995a2a003181':
    '请从<a href="http://lbsyun.baidu.com/" target="_blank" class="text-sm">百度地图开放平台</a>获取',
  'f1f7a5f821ca0ee4caaa9324eda48d91': '静态表单项',
  '4602761ee85e2e6e5360cd13fe642a08':
    '<p>当前组件已停止维护，建议您使用<a href="/amis/zh-CN/components/form/formitem#%E9%85%8D%E7%BD%AE%E9%9D%99%E6%80%81%E5%B1%95%E7%A4%BA" target="_blank">静态展示</a>新特性实现表单项的静态展示。</p>',
  '6ca92e3386f9e392584367df5054c27c':
    '用来展示一个图标，你可以配置不同的图标样式。',
  '47fd366b711a0567646854f541449f8b':
    '比如：\\${listVar}，用来关联作用域中的已有数据',
  '98204720c30a843a9234bdf22dc05d51': '弹性布局设置',
  '3212f1de74130815d12d982437fc6919': '文档预览',
  'a7199769ae58f8a5c35ac0e5f8804abf': 'Office 文档预览',
  'e414473c886072e393710563f201d7f3': '打印',
  '6a62d33c838524f0609a624aa59ee9e7': '打印文档',
  'f26ef914245883c80f181c4aade2ed04': '下载',
  'fc856cd721f5b5955f8c4be2767a1cee': '下载文档',
  '270c7dfc38ec1f8eb7b211481d26659a': '文档地址',
  '2bd4fa4fe6637a09add46354f52ea9dc': '是否渲染',
  '010787d733c97c5f7a8f9bda438af9e2': 'Word 渲染配置',
  '9a50cbc2f0c7115043a19c3b1db5776b': '忽略宽度',
  '927ed823f52a6d3bbceed4436636a7dd': '页面内边距',
  '755ea661684e7bffe9f97fb07b8d4921': '列表使用字体',
  '9b87dc5a019f749722a1d3a9c854a2b9': '变量替换',
  'c6adeba660df8e19ac6cd3b8c57416ad': '强制行高',
  'b93ea0046d63e2df7cf24a7a63bf5c99': '字体映射',
  'f6e9b56f97af64235bf81f4ddc2288ab': '是否开启分页渲染',
  '4c71a09da7ce050d45514b68bb15b4ab': '页上下边距',
  '3651c159e19c05a3bdaa7036dac91e4e': '页背景色',
  '91bc444339545a7785f3aa3055d9ba05': '是否显示页面阴影',
  'c856051e8c80913ff6607dc880341a20': '是否显示页面包裹',
  '6a6772a0eae27591ed8763b6e616e988': '页面包裹宽度',
  '22bca073daae505d7fc9e7d7c8ee047c': '页面包裹背景色',
  '2839785a190e062058635add192f961d': '缩放比例',
  '9723e66141840db4dc6bd1db9b165302': '自适应宽度',
  'b7dd79307fb7bcc921aa1b94ef904fe9': '行为按钮',
  'c3e817974de836e80c95cc6ce5718eff': '搜索框',
  '83fe6a5db780fcfa68f3336c7d86f25b':
    '用于展示一个简单搜索框，通常需要搭配其他组件使用。比如 page 配置 initApi 后，可以用来实现简单数据过滤查找，name keywords 会作为参数传递给 page 的 initApi。',
  'bb50b53491c2c43845c58b16e48c27f3': '搜索框内容',
  '68a896ef9627fea8cd84f3fa4f7269aa': '点击搜索',
  'a7be3c702997f49cf9429240fbbc5e36': '点击搜索图标时触发',
  '1c113a8c88ba15fc1ff04ea410e63f33': '搜索内容',
  '0796ba76b4b553687e5ffaeb78512ccb': '基础',
  '218bcea849478df7335ac31072860e8e': '清除后立即搜索',
  'a48b511d5da79faf6f7f58c5470738f0': '立即搜索',
  '71c198baa12405e56705a3c68f66e3ef': 'mini版本',
  'b814fb5782f733a22ee561397ad376fc': '加强样式',
  '87d88a457161f2a09f95f6aa29b38051': '步骤条',
  '2a9a1329b191c2787b1a70c289e3bbe0': '竖直',
  'd517acb68fbed2331b57d1a11ca21dcc': '简单',
  '341fe804cc8b65dc17a31c7a25a90444': '标签名称',
  'd04f139ee0fb6fac19ccaec0f7b323df': '点击关闭',
  'fe7967a547915be9ae4083ed50c3b94a': '点击关闭时触发',
  'b624985146c759cfeb1be80325eccd65': '文字提示',
  '717b9f738e2da460071b1b5ae7cc0e06':
    "{\n  url: string; // 当前接口地址\n  method: 'get' | 'post' | 'put' | 'delete';\n  data?: Object; // 请求体\n  headers?: Object; // 请求头\n  ...\n}",
  'd1bfe86cb1776358c5ed50bc137a2b7a':
    '{\n  data: Object; // 接口返回数据,\n  request: XMLHttpRequest;\n  headers?: Object; // 请求头\n  status: number; // 状态码 200, 404, 500..\n  statusText: string; // 状态信息\n  ...\n}',
  'd3b13672e0e24d1490b2564ae7f6da4e':
    "// API响应或自定义处理后需要符合以下格式\nreturn {\n    status: 0, // 0 表示请求成功，否则按错误处理\n    msg: '请求成功',\n    data: {\n        text: 'world',\n        items: [\n            {label: '张三', value: 1}\n        ]\n    }\n}",
  '36d7adab7769ff6741b2860f041e56d2':
    "// 校验成功\nreturn {\n    status: 0\n};\n\n// 校验失败\nreturn {\n  status: 422,\n  errors: '当前用户已存在'\n}",
  '0228c8f19830732b523a58a2ee0bbcfd':
    '{\n  "status": 0,\n  "msg": "",\n  "data": {\n    // ...其他字段\n  }\n}',
  '6d8e3115be41a8a5690d6fefa637dac7':
    '接口返回数据需要符合以下格式, status、msg、data 为必要字段',
  '09212e946a4d9b0f775700c46ef0dcd5':
    '返回 0 表示当前接口正确返回，否则按错误请求处理',
  '7391774e57425e5d8e83de64100b5f2e':
    '返回接口处理信息，主要用于表单提交或请求失败时的 toast 显示',
  'fa385c23820ee9999c82035086baa772': '必须返回一个具有 key-value 结构的对象',
  '6494bc042d99f2f5de34a858b8a699c6': '校验接口返回格式字段说明：',
  'e6246c03148f553e5d6a66adbdabb9f8': '返回 0 表示校验成功，422 表示校验失败',
  'b8079b9d1e6d3e8a457787910a75cce4':
    '返回 status 为 422 时，显示的校验失败信息',
  '9d49f04a3affb34a31bb157e4887cbe7':
    '可基于 JavaScript 语言直接录入发送适配器的函数体，在该函数体内，您可以对 <span style="color: #108CEE">api</span> 进行处理或者返回新的内容，最后需要 <span style="color: #108CEE">return</span> <span style="color: #108CEE">api</span>。<br><br/>\n    函数体内可访问的变量如下：<br/>\n    &nbsp;1. <span style="color: #108CEE">api</span>：接口的schema配置对象<br/>\n    &nbsp;2. <span style="color: #108CEE">api.data</span>：请求数据<br/>\n    &nbsp;3. <span style="color: #108CEE">api.query</span>：请求查询参数<br/>\n    &nbsp;4. <span style="color: #108CEE">api.body</span>：请求体（针对POST/PUT/PATCH）<br/>\n    &nbsp;5. <span style="color: #108CEE">api.headers</span>：请求头<br/>\n    &nbsp;6. <span style="color: #108CEE">api.url</span>：请求地址<br/>',
  'a59c65bea7d5065f19eb9c918a716c33': '必须将修改好的 api 对象 return 出去。',
  '05fb5edb84e41c19b0f5429fff20b834': '返回适配器',
  '44af993b124817085dc2579a9f842d55':
    '可基于 JavaScript 语言直接录入返回适配器的函数体，在函数体内，您可以对 <span style="color: #108CEE">payload</span> 进行处理或者返回新的内容，最后需要 <span style="color: #108CEE">return</span> 接口最终的返回结果。<br><br/>\n    函数体内可访问的变量如下：<br/>\n    &nbsp;1. <span style="color: #108CEE">payload</span>：接口的返回结果<br/>\n    &nbsp;2. <span style="color: #108CEE">response</span>：接口的response对象<br/>\n    &nbsp;3. <span style="color: #108CEE">api</span>：接口的schema配置对象<br/>',
  '98498eb59e87ec9a0eaf98ac55628da9':
    '当前请求的响应 payload，即 response.data',
  'ed33c46d1d69336bb011813e8352fa01': '水平方向',
  '963e9ff4252cdef288063c41eb6d4998': '垂直方向',
  '4117e80d2c2e52f795ec64160f399364': '垂直居中',
  'd365d58d281508b9982f6a0370649ae2': '水平居中',
  '21af94c1abc5891b2703c9321417a1a9': '间隔分布',
  '849b9b944a65eb0685f3e6af60a0c523': '水平铺开',
  '50334fc77fc5a2c2636f14f158d3c417': '上下文变量',
  '7f05bea37729325a6cc84eb26bb9f8c8': '请输入角标文本',
  'bc3f5a690d8c3a47d27ef8a1b127bafc': '隐藏选项不能设为默认值',
  '240a19929878c26f5e4c41c08f63cd1c': '接口校验',
  'ae7ca6f3dec57a73ddc145a7094adc97':
    '配置校验接口，对表单项进行远程校验，配置方式与普通接口一致<br />\n              1. 接口返回 <span class="ae-ValidationControl-label-code">{status: 0}</span> 表示校验通过<br />\n              2. 接口返回 <span class="ae-ValidationControl-label-code">{status: 422}</span> 表示校验不通过<br />\n              3. 若校验失败时需要显示错误提示信息，还需返回 errors 字段，示例<br />\n              <span class="ae-ValidationControl-label-code">{status: 422, errors: \'错误提示消息\'}</span>\n              ',
  'c6f30c2f084ddeacb7944235348bdaa4': '内存变量',
  'fa6b01f51cc2b8e16bfbb914b6c08ace': '确认对话框',
  '4cb9c4bc5cb960fcd03fceb2d2e62f3a': '抽屉标题',
  'fbaa94ca6b6e6e76a07124e80733f109':
    "/* 自定义JS使用说明：\n  * 1.动作执行函数doAction，可以执行所有类型的动作\n  * 2.通过上下文对象context可以获取当前组件实例，例如context.props可以获取该组件相关属性\n  * 3.事件对象event，在doAction之后执行event.stopPropagation();可以阻止后续动作执行\n*/\nconst myMsg = '我是自定义JS';\ndoAction({\n  actionType: 'toast',\n  args: {\n    msg: myMsg\n  }\n});\n",
  'fd2bed5e9a84273b22f79950a0a1807f': '悬浮态样式',
  '1daee167a43c72dbe9e31e955b670b4f': '点击态样式',
  'd117954f3769008ef22b864060913c65': '禁用态样式',
  'd37c4140bd531fc117e91e727d7e576c': '常规态样式',
  '0371c0e05806c01e5ce2f26e9e2e39c8': '编辑样式源码',
  'bb7654f7c2768614e95a0da7e94f4045': '应为冒号',
  'af26fdb215bad5b2296529802b129c12': '应为属性',
  '3955b2e2e06144010a1142d3624b17b1': '缺少分号',
  '052f7eec7ca35a6b4d72d169ee1de494': '请输入表达式',
  '10710f1c01d960a3ffde384115296026': '块级(block)',
  '26b10072a4e0c8c9a3a1142db3d7b3b4': '行内区块(inline-block)',
  '2c7fe494c99c94ba5965f963fd7d3a4c': '行内元素(inline)',
  '39353c2b258e4bc73d8dd6a46f0a7955': '弹性布局(flex)',
  '553333a72dec41b54e8ed18d49453a76': '默认选择第一项',
  '86bf16f0cb7fd8b1fef2e1439e06b632': '样式源码',
  '7893f221dae53be8e3bfe72d2eb8a240': '图标尺寸',
  'f36616e35765ac3d5c9c7646e50a305d': '初始化数据接口请求完成',
  '48c43999cf3908d9f5100a6d5f0b4404': '远程初始化数据接口请求完成时触发',
  'aa6070a7f454f554fc1c7d8b1d2d935f': '响应数据',
  '23d861e4b6635508eb877a50a9333a9b': '响应状态(0表示成功)',
  'a303669d47440ed05558efbf3d5d0592': '响应消息',
  '31769d6063c5ab0cfee842b395916fd4': '总行数',
  '092798b39c21e4f73dea5107fef3a61c': '响应状态',
  'a556b5535b6b0e5925d59bbc54b58946': '初始化Schema接口请求完成',
  '2cfbb19c3e801c5f4a11dafa1ec7884d': '远程初始化Schema接口请求完成时触发',
  '7bb4a8abc423086dbd1edeed4cd3f718': '当前行数据',
  'f74fd69ce55e3f96fe9a032c0da42330': '当前行索引',
  '5db079b140feffa84c84d1b51d824dea': '鼠标移入行事件',
  '90a5608bcd85a3cfcfbb5263a229915c': '移入整行时触发',
  'e8bf039de5dc6751c4045f55930e0c83': '鼠标移出行事件',
  'f1ca0d305f3251a81f361a2dc8e11fff': '移出整行时触发',
  '41c5c859a80b635c23b3b4d1d8b44efb': '已选中行',
  'f87912f19be48f36e4e261e585764d6e': '未选中行',
  'f97f8b90f09fb7df39c5c9ae3554b4c7': '更新列表记录',
  '0449dab523020003eee89a0751e1c003': '内容区域自适应高度',
  'e406488d1b9545168eb1896e35139bf2': '确认按钮文案',
  'd919d62d7e5ec5cb63ad47e5dc347a7e': '取消按钮文案',
  'b12df8decf62700970e08b9b2ebce1f8': '确认按钮样式',
  '7f29931020c2d8ce97b8043a933db151': '取消按钮样式',
  '1b43b43e692a5b6b2f6ec1417791a550': '远程请求轮询结束',
  '0de773dedbe5875ca4529e7d0ce9d2bf': 'asyncApi 远程请求轮询结束后触发',
  '9bf078fb5589f2c09031f1f2b5a8d2b1':
    '设置此属性后，表单提交发送保存接口后，还会继续轮询请求该接口，直到返回 finished 属性为 true 才 结束',
  'f63ba7e3c9d8de0b8b4f769055a36d2e':
    '设置此属性后，表单请求 initApi 后，还会继续轮询请求该接口，直到返回 finished 属性为 true 才 结束',
  '58477471b3bb8ad53fe9ab18a244868b': '添加行',
  '6070fdf451b08e4302a7dab560771222':
    '点击左下角添加按钮 或 某一行右侧操作栏添加按钮时触发',
  'c9f63503ee66082fc00c3e6eac57a95f': '列表记录',
  '7b25d5ff93da66fe95b5409731bb9838': '新增行记录索引',
  '5e3640e4bc8efbe00a7b7c6ba169560c': '确认添加',
  '008ae858112dcc7739985045e4326f3a':
    '开启”确认模式“，点击添加按钮，填入数据后点击“保存”按钮后触发',
  '1fb1cbe49c3e72c2b5f4e0151a4cb5d9': '新增行记录',
  '3fdaeadf0e8a3154ae62784f04138c28': '添加成功',
  'bd4ef227fe8aae88ae6aa97236c0e9bb':
    '开启”确认模式“并且配置”新增接口“，点击“保存”后成功添加时触发',
  '6452a05591d7402a8bcd9fadc950c449': '添加失败',
  'bd7c39d10dbf6985d2be2d9a4fdcccf7':
    '开启”确认模式“并且配置”新增接口“，点击“保存”后调用接口失败时触发',
  '37122b9e4499da38258b3d51b09d68a1': '请求失败后接口返回的错误信息',
  '88ebffeee507da0f0d039eb2d05f7e2c': '编辑行',
  'daf24d1f83e1b4fce2f57a6b041ddba6': '点击某一行右侧操作栏“编辑”按钮时触发',
  'a9e009a9936bf5bcc9d4eabcb3e952e0': '所在行记录',
  'e756ec22a0f897c4dd886c5f4a95b7a6': '所在行记录索引',
  '4271f29faca65d7840ad6bb2c4a7b8c6': '确认编辑',
  '562f54a2fec7a68f85ff192a68cf69f2':
    '开启”确认模式“，点击“编辑”按钮，填入数据后点击“保存”按钮后触发',
  '3bb47b67994cb374e601fab35f63bc8e': '编辑成功',
  'b394745a8a77179da0000293638e8a56':
    '开启”确认模式“并且配置”编辑接口“，点击“保存”后成功编辑时触发',
  '9304e8f4c324b5882b550caa971b64b8': '编辑失败',
  'b9d277c3ffab7d4b955ad10308c7ae0a':
    '开启”确认模式“并且配置”编辑接口“，点击“保存”后调用接口失败时触发',
  '650bccdd6f99fe5fc4ca6cb8788e7cb4': '请求错误后返回的错误信息',
  '947fa341a6d676d7f25bae6bef8342cd': '删除行',
  '7508f6d66d920323d87a9f9d58487a40': '点击某一行右侧操作栏“删除”按钮时触发',
  '0007d170de017dafc266aa03926d7f00': '删除成功',
  '13640e78822f62b7b71bfabb4604025e': '配置了“删除接口”，调用接口成功时触发',
  'acf0664a54dc58d9d0377bb56e162092': '删除失败',
  '14d36ca583bcbfc5516a000eb06ccedd': '配置了“删除接口”，调用接口失败时触发',
  '692dc28abc42931daca8fa12d69d5c99': '表格数据发生改变时触发',
  'f8692d409bb7f122a374872e01efd03a': '添加行数据',
  '459fd144ee129b501545d19c169269e9': '插入位置',
  '196e71fa869b8410088b4ceb54aa7988': '请输入行号，为空则在头部插入',
  'a26858cccbc451fd53515416a5968550': '新增一行',
  'c520ed9911d349c7974116d3d1e1423e': '新增字段',
  '344c1c96f9cb3275f972929755b4e238': '删除某一行数据',
  '8910acd418e45a30f01e7e531b90b61b': '删除方式',
  'ebf62450c3fb11c0b7e06da2d7535d6c': '指定行号',
  '4bf7636a84714fac140e42b4cee242d4': '条件表达式',
  '7708fb1394ce722ee73326437a66c77a': '删除范围',
  '015f108b96c70bba6511091e159db0ac': '请输入行号，输入多个则用英文逗号分隔',
  '63aeb2082d7cc0a316fc1e44913d7749': '删除条件',
  '4622a4ce221f9b79aa3396cc461adc75': '清空组件数据',
  '800dfdd90200bd47bb4bb83def4fea56': '今天',
  'a6a93b404bc039cded728683af5d625d': '快捷键名称',
  '快去添加事件，让你的产品动起来吧': '快去添加事件，让你的产品动起来吧',
  '50f198f07fc820a4911d1c97a0ceb8c2': '上下文',
  '6142a89066ca7dd6a1ce9493462c5aca': '已选择行记录',
  '21bd0846bd8aa2296c597a1c1ff8e1a7': '未选择行记录',
  '8f98291c9fa89c0bfce463c0a2eaf97c': '列名',
  'c35c1a13309c6f9da9837857517e65fc': '排序值',
  'e125986c2ba6783c4297ffe5405cc8bc': '筛选值',
  'caafbcb52c70ad0bbbbf127ee7b08b89': '搜索值',
  '64ef585f778c9d1b010e86b032398ab6': '已排序记录',
  'db9df54392e408520ca12c6a56113b5a': '当前显示的列配置',
  'bf2a4fd8ecd654982e2d466f10f54d3f': '当前行记录',
  '85f1708454f409855d552f702ac27b19': '当前数据域，可以通过.字段名读取对应的值',
  'e48d65cda774019d9a6677354bc781f2': '选中的值',
  '10d23d1f68ee1facb03b1f86678aa2ba': '状态值',
  'ed85c3f659acc9e89bcf6f0dbaa02a62': '当前代码内容',
  'e26f6832d586f9e73d2361573bf5273f': '组合项的值',
  '91190195405845950230616929d852cf': '被删除的索引',
  '1b5cf3e354142cc1cdd6f56b6afaba49':
    '当前表单数据，可以通过.字段名读取对应的值',
  'b06216eac0df52f6072a8adb095f72b7': '当前城市',
  'a610ef4a4fbe8f0c8b756162acfb6186': '当前日期',
  '05606badb4b14ffd3c38c278fb0f3c9f': '当前时间范围',
  'de3ad0cd57153f799f7538dd1e4fd441': 'excel解析后的数据',
  '6d829f061ed82a688f2669c54dd83301': '当前数值',
  'bc0689a4c353e9c95c5b7fc5aa49b59f': '当前滑块值',
  '9b0c6dee9b5f48734c37901d4a430b71': '当前分值',
  'd84017fa76584f7475e26f79767df28d': '新增索引',
  '9d776ddd9dd2d8d85ea225df9c27e929': '当前标签值',
  '4ed30a5be1b6680e6cc9fec0965d0f4f': '选中的标签',
  'e01315f74dee36831d93a117cbc47c8f': '标签列表',
  'd5c135b5a4aed5dc39ef846a6f502d4f': '当前文本内容',
  '2eda8e3f67e2c6e02e63d27978530ec2': '变化的节点值',
  'b4e54cb84d448952a4aa1a17ceaa6ad3': '选中的行记录',
  'b2a18e08b0b0e0fd7e80554b89244aa0': '选项列表',
  'c46f27dcf45a345993f1cbb63380fa98': '激活的索引',
  'f7daf85b4501d9d2aa048f85618b3f1f': '当前的文本内容',
  'e5369f1a5e8d2b3e64eeb627e69c4e9b': '选中的节点值',
  '28387ec7d7fd160541e7901d9f0a900d': '当前选中的值',
  '94935dfa6c9b908515a593956ee7d07c': '已选行记录',
  'f10b94a4ac77878be53fad599a761928': '未选行记录',
  '6c200daeb748ecce2c730d01837d3508': '初始选项卡',
  'bd749c7a75af1236325d8d669e9bc5fc':
    '组件初始化时激活的选项卡，优先级高于激活的选项卡，不可响应上下文数据，选项卡配置hash时使用hash，否则使用索引值，支持获取变量，如：<code>tab\\${id}</code>、<code>\\${id}</code>',
  '8b4de52c23ad472b9ece9e30d8750c48': '初始默认激活的选项卡',
  '7806807651c37e4467f9d2fc1c18eb2a': '激活的选项卡',
  '8a59e0a5705fea1751d77a97b7bf5d8d':
    '默认显示某个选项卡，可响应上下文数据，选项卡配置hash时使用hash，否则使用索引值，支持获取变量，如：<code>tab\\${id}</code>、<code>\\${id}</code>',
  '27e0d57c4412bcb89e6aaeeb1e5935fe': '默认激活的选项卡',
  'e83caa4c6ef82543603609c916cd804d':
    '可基于 JavaScript 语言直接录入发送适配器的函数体，在该函数体内，您可以对 <span style="color: #108CEE">api</span> 进行处理或者返回新的内容，最后需要 <span style="color: #108CEE">return</span> <span style="color: #108CEE">api</span>。<br><br/>\n    函数体内可访问的变量如下：<br/>\n    &nbsp;1. <span style="color: #108CEE">api</span>：接口的schema配置对象<br/>\n    &nbsp;2. <span style="color: #108CEE">api.data</span>：请求数据<br/>\n    &nbsp;3. <span style="color: #108CEE">api.query</span>：请求查询参数<br/>\n    &nbsp;4. <span style="color: #108CEE">api.headers</span>：请求头<br/>\n    &nbsp;5. <span style="color: #108CEE">api.url</span>：请求地址<br/>',
  '8d1903162d2a50d6321819c3fcc1f2f6': '{{@1}}({{@2}}动作出参)',
  'd5fb02425d3b8586d8d7b98971d63e68': '事件动作',
  '5e3406cb54f255dc1be5edbaa6f87389': '排列方式',
  '60e237a1b5e9a4cc3633898d527d5a38': '标签宽度',
  '813a5158d9f7171d20e7df340c5b48f9': '组件上下文',
  '8b3fd9147a07d27ad95c0ba2594fb67a': '当前数据域{{@1}}',
  '0383d6f467ed0dd89860a7b8cc793ce9': '上{{@1}}层{{@2}}',
  '45f3a444853a11667f97e941b53266b0': 'API接口',
  '7658a7c761a4296d5526426d29ee7c68':
    '用来保存数据, 表单提交后将数据传入此接口。<br/>\n        接口响应体要求(如果data中有数据，该数据将被合并到表单上下文中)：<br/>\n        <pre>{{@1}}\n}, null, 2)}</pre>',
  'e7173f5a0462ffb0eb9dfc02e44155f8':
    '接口响应体要求：<br/>\n        <pre>{{@1}},\n  page: 0,\n  total: 0\n}, null, 2)}</pre>',
  '319ec20dfe05d268928076e0cbb8e45a':
    '接口响应体要求：<br/>\n              <pre>{{@1}}\n}, null, 2)}</pre>',
  '419b0a74c438b81124992616dd0a36f3': '快速编辑后用来批量保存的 API',
  '152e8c553fe6fbc51df7c72a45917107': '即时保存时使用的 API',
  '91aa2166ee4811414381c8d94e6567e6':
    'API返回格式不正确，请查看接口响应格式要求',
  'c00a96da01a3cf0445df3b0d05918317': '查看数据',
  '7695a3b5bfbcfdb3e7b085f8cd6455be': '编辑数据',
  '6f2f7e3794aa8502d02cb20aba881df1': '批量编辑数据',
  '40f8f022c51542d282edda5b9ed4b512': '删除数据',
  'c1c29e445748fa076c5fee45274bdd36': '批量删除数据',
  'ebf98d77b1a002935ad5c41446f257b3': '确认要删除数据',
  'd00d3377afe33a0f1b63293f3a3e3a79': '确认要批量删除数据',
  'a094e5b7699ea4b61094cc4120170423': '数据来源',
  '6f99b6eed37795cb97d5f6370c32113b': '基本样式',
  'c8b9dbab4a3e3ad283d3d95a1663cd68': '当前行记录 : {{@1}}',
  '83c9828692e1bb250a069bbf37807190': '表格2.0',
  '63ddcc28ac20f6cbd4197671ae7e628c': '启动自动刷新',
  'd6ba60b5bbf5df4cc2959dc897c2f792': '停止自动刷新',
  '16ea2200bfba281fdf5e6870498790cc':
    '[amis-editor][CRUD2Plugin] dynamicControls的值必须是一个对象',
  'd45a439fa6e82798bc1e98d738cedea3': '开启后支持选择表格行数据',
  'e821ce185e41eac2ab846ef5cfde2363': '分页设置',
  '4d7080ff1405a1f08c5415a0f942c336': '分页模式',
  '290026b0b40b637e774c6af435b897b5': '过滤时刷新',
  '67584675004a48be903e9f61c733cb35':
    '在开启前端分页时，表头过滤后是否重新请求初始化 API',
  '8cc3589c442c478dde8ceb60aeb29e03': '翻页后回到顶部',
  '9cb14f1355b3a312ebd62ebff5e1e06b':
    '无限加载时，根据此项设置其每页加载数量，留空即不限制',
  'f300691f823aacea572e63bb7fb7ce8a': '点击编辑分页组件',
  'd81ad681a447abae7cba38779ffc0c9e': '未找到分页组件',
  '86b17bba54b65c6a62895357095f63a3': '接口轮询',
  '983a8ab6a97da0a0dec7b5c751cf02b1':
    '开启初始化接口轮询，开启后会按照设定的时间间隔轮询调用接口',
  '4cbec5cb54ba399ea81381a75f5f1f93': '轮询间隔',
  '3a5d9512f474ff7c2a017a13e7f8a9af': '停止条件',
  'fd649ca959662306b734f03438869bf0':
    '定时刷新停止表达式，条件满足后则停止定时刷新，否则会持续轮询调用初始化接口。',
  '0516133b87f03f859e23bf014d71ab57': '模态窗口期间停止',
  '52dce697d35795d7835e483d69f72419':
    '当页面中存在弹窗时停止接口轮询，避免中断操作',
  '6d5b1e5a235fa839c759d2362654d638': '静默拉取',
  '8b34b22a4da1b9b855a5efd33434f5e7': '刷新时是否隐藏加载动画',
  '32aaf2f04e983290deceb0674fb0d159': '表格区域',
  'e3e100dab1d8d13a2e3f9c391b0b108d': '顶部工具栏',
  '2aee96dd788b815f83b3d118188e7fd9': '底部工具栏',
  'cc6a173a3601403f2d96cae2180539b3':
    '用来实现对数据的增删改查，用来展示表格数据，可以配置列信息，然后关联数据便能完成展示。支持嵌套、超级表头、列固定、表头固顶、合并单元格等等。',
  'b58da2d7e87937280042e1deca1153fa': '流水线任务实例 ',
  '93b824b57d4c22085c1035f7254619db': '单元测试',
  '2a8249ce9e52240855dfabc0efc83a3a': '通过率',
  '09eb6d258fd00deaf350c2d1a0fd6f46': '任务实例',
  'a5cd4ea1820d5c17c35e86885b1ef10d': '报告',
  '3791ba5c2962a42a251489872342b7d6': '组件名称',
  'fcd70206ed8109b7ed361c7bce0922c4': '每行个数',
  '3ccfcb4c0daac7bb3ef1399b4389e3e5': '左右间距',
  'ab57255c391c3732b4b38ae78f55e058': '上下间距',
  '390d9ad686ba5622d49443d1e3659d51': '当前列表项',
  'b271e427962758c71e342a2c06df493a': '索引',
  'd63b707be8a6feb914a3f6899c38770f': '左切换图标',
  '911f1640e858c362bfb3ba9a55e8269a': '右切换图标',
  '8a0b967b90e89dd8e7311065c1de720a': '切换图标大小',
  'eabda74dd2ac5b4e6eab9229e0e63f79':
    '接口可以返回echart图表完整配置，或者图表数据，建议返回图表数据映射到 Echarts 配置中',
  '59688d1a484179aef8edc0ccbabb9fc1': '跟踪表达式',
  '3fd81a573ea309b203ab019c1aa95a2c':
    '如果这个表达式的值有变化时会更新图表，当 config 中用了数据映射时有用',
  'd0d852432ce09f627e6ae471b3f86b0a': '数据映射（dataFilter）',
  '06e89ed3cb501da6d57e35bc28a37089':
    '如果后端没有直接返回 Echart 配置，可以自己写一段函数来包装',
  'f25c9fd2852b85502157decbedd19082': '原始数据',
  '2695f318db820c2bfe4c886fed3697f0': 'echarts 对象',
  'b76e25de5689ea51af0688d0ea6d7b39':
    '如果配置了数据接口，接口返回的数据通过此变量传入',
  '45bc998df29cf75725df6141098d5cec':
    'debugger; // 可以浏览器中断点调试\\n\\n// 查看原始数据\\nconsole.log(config)\\n\\n// 返回新的结果 \\nreturn {}',
  'e8f96ea47cb322f4f1a4c938c7b155ac':
    '默认为追加模式，新的配置会跟旧的配置合并，如果勾选将直接完全覆盖',
  'e732f4c0d8075670b6c75cc52bd1f7d5': '图表下钻',
  '00958a92c5804d56047c18fc206382e1': '宽高设置',
  'b3e991d11b9f0ee04f55627c62d4fcba':
    '默认高度为300px，值单位默认为 px，也支持百分比等单位 ，如：100%',
  '8e5ff4f20463c03f7935e0627888c03c': '折叠状态改变',
  'bcfcd9d31a7a469fa1f3a2ea9e3e3f89': '折叠器折叠状态改变时触发',
  '0e8638286319f6efb0afe2616714e8c3': '折叠器状态',
  'dce3879aaf11920ab97c94781ddaaed5': '折叠器展开',
  '0f680e944e33feb4719bd0dfe618aa50': '折叠器状态变更为展开时触发',
  '0469f19533c2fa1d63418b86ec203ab7': '折叠器收起',
  '3ffa0c7285daa4c39f7b7699a845860c': '折叠器状态变更为收起时触发',
  '578125c5d7586aefb797caca9111ed1f': '组件展开',
  'ae772db91ee1bd385ffafed3e9b8f4cc': '组件折叠状态变更为展开',
  '5882cb6b5133d35488c386965321c60b': '组件收起',
  '04e36bd57e0e1623da86ee2c19c2a885': '组件折叠状态变更为收起',
  'd2fa917958506736ea39edbef5e1cea5': '折叠面板折叠状态改变时触发',
  '58164864ad00f5d134f304d309055eda': '当前展开的索引列表',
  '44732aa2566399b71483e63252d3dbc7': '折叠器索引',
  'abd55237a0df8da6ffda1ef377982707': '列默认显示',
  '67a2d9746956b631dd3ae9d13b6ae9ff': '点击遮罩关闭',
  '36f5b682310bd52f19c63b077ec054d1': '隐藏按钮区',
  '6bdc97671296112658e3a1cd369c0686': '遮罩颜色',
  'b3b287010ea79586507a77e0580d9cad': '底部区',
  '38cf16f2204ffab8a6e0187070558721': '确定',
  '40d39c3bc7bebced7d63eace0191a0a8': '角度',
  '5d606821df2528b127c07333f5f403cd': '回访数量TOP1',
  '796011a5b11f78292d93a73ff284de50': '北京分公司',
  '7232042d4e1b0c8c7172d68048358619': '最大显示个数',
  '64095ae27232995731f776f12bf66d8d': '当前循环项',
  '23b3169341314eda02d860be90ebf487': '全选文本',
  'e2b67a7f51c977887d2b3c2de2727509': '操作区',
  '4f2ef285e13e20f551f7111f535cde11': '表单创建向导',
  '7efcb0ce09e8842951c5cfd298b4e7ee': '使用场景',
  'b4f035fa6f9faaa78707b72c352c6a8b':
    '[amis-editor][FormPlugin] dynamicControls的值必须是一个对象',
  'cd8b367599d0c748e74029da8c10d295': '本地缓存',
  '09a94b2400f7f05fbf2fc37a20b097d0': '提交成功后清空缓存',
  '69445b3c5ddb606c7d51fd941e4aae10': '自动填充数据域同名变量',
  '86f056f1cd4e25d5bd2bebddc971032b':
    '默认表单是可以获取到完整数据链中的数据的，如果想使表单的数据域独立，请关闭此配置',
  '5cd08fe4cd86fffe7cd23d934d4d3b32': '提交设置',
  'e59e1cb3f366d6e7012fe64748355b0a':
    '如果底部按钮不是自定义按钮时，可以通过该配置可以快速修改按钮名称，如果设置成空，则可以把默认按钮去掉。',
  'd3458f4ee2c2203bb51f9d138dfd0c05': '提交后重置表单',
  'e89bb79f0a0cb4fa4693c4a5ace130f4':
    '表单提交后，让所有表单项的值还原成初始值',
  '59c1b37b7ff983385a2521541cbcccf8': '阻止回车提交',
  '794020840d754ea7fb58ea3bf4394e1a':
    '默认按回车键触发表单提交，开启后将阻止这一行为',
  '87a6f142d80987db2e31569e403619f7': '提交后关闭对话框',
  '2e799a2e68a3303201d89752ae201a88': '组合校验',
  '585293753528cd2f74501f41fc2e2c30': '在表单顶部显示当前表单的数据',
  '86ed196f63524be7775e5a01b0152793': '标签对齐方式',
  '6bde56d911167d0f47aae6bab762cd70': 'Panel包裹',
  '019078f52f120a70fc23d94ccd364200': '吸附操作栏',
  'f9e5a5377c1f56f1d09ae2bc58bd2d5b':
    '开启后，滚动表单内容区时使底部操作区悬浮吸附',
  'cc6c35a3e0f97fb9747905dc13e9b625': '编码',
  '936b62c222fcf6ba10fde069212fcf97': '省份编码',
  'd7009d07f04d208f84e6c343fbc80ab7': '省份',
  'e982852e24060d47f29be600837b675a': '城市编码',
  '0dad46b34f0ddc802bce0351dc4745eb': '区域编码',
  'd3ce40d862f1c7e4748e6c28ffb0a007': '区域',
  '716c3dc1bd990ccebc4fee34eb9171cf': '街道',
  'fd3913adcb81427f1895ab5b79ebe0b2': '远程上传请求成功后返回的响应数据',
  'cfe5917c83c8c31d5e57ddeb1f3460a5': '请输入行号，为空则在尾部插入',
  '9a3af29bac4ff8dff3305e283acb2516': '根节点文案',
  'f273cc663180d6f62497ff3a28f4fdd7': '新增文案提示',
  '897deae9c4c3dc0baa4a6989bb8727b6': '添加子节点',
  'aefacb210d5fa5eff897a302269ed920': '编辑文案提示',
  'f9886b3adb1ec253d24432615b62151c': '编辑该节点',
  'b8079d1411b21dbc48749154a827a680': '删除文案提示',
  '176a53a7fc4759482d71312551d868ec': '移除该节点',
  '7650487a8758fd50c87d6c9cff0aa5ac': '地址',
  '3d18ca01ddd1b95e982ec44ffcda8165': '经度',
  '6acaee71fe6a23c17f18625df01bab23': '纬度',
  '40d58bb6ac9888b0c672f5bcff74da25': '地图厂商',
  '542241c52bd2efb24dc37b32cab2329c': '列全选',
  'd94ec7663ac9ad2d09fca5c86928b434': '列级全选功能',
  '7020fa7949a5ae24cc8eb696772d97fc': '行全选',
  '227c24282ff52f3f6f52dfdb853cc1ad': '行级全选功能',
  '83a60f8b752a1ef3ce6a240388d635aa': '默认当开启全选后居左排列',
  '316a639631f712780829a202258ec3cc': '居左',
  'e2aaec83377244c9d15f78f51cccfe6f': '居右',
  'b7fc7efe26efb867838223936b7fc467': '左侧选项展示',
  '505e204cdd98afd08c174d6dcec0dc09': '已选项',
  '9b1ddba55066e0f329ca3cca2e58909c':
    '可以通过接口获取检索结果，检索值可以通过变量\\${term}获取，如："https://xxx/search?name=\\${term}"',
  '14079611c014884bbdffce4d0e5f4d73': '可以通过接口获取动态选项，一次拉取全部',
  '1fd65acd90f99791d70ca70e046c9f05': '假数据图片',
  '5789be67da4a1dc0fd9600bd626776a1':
    '只在编辑区显示的模拟图片，运行时将显示图片实际内容',
  'eded9ed93a453c3f790126e30b776a22': '标题文字',
  '11d938eaa50cff1b7e59c64b891de73d': '标题边距',
  '36285b8c01571203859d6b8ce7af0cba': '描述文字',
  'a7dcee68c68f8f19f39c9788e08fac31': '描述边距',
  '80dc1f6307e4acf29ece7ac2f6e04334': '放大图标',
  'faaadc447b1c2b1adc920d9c9aedcc25': '序号',
  'c4767bc3ad5c6812a8ea302e6126d988': '3月',
  '499d02b4034d1234b407042ccce614a8': '列表标题',
  'a302da1d8160ed0c72719c13346042a9': '这是内容简介，可以设置显示行数',
  '4e74ff55b36a0f35d511a761c264fce3': '内容区样式',
  'a67496c9ef1b1951fb9f104d1ef17997': '标题栏样式',
  'af608093f1d3c25e85bdb4ed17f8b947': '工具栏样式',
  'fa898e5ae4c0315061129c6cef47c326': '边栏样式',
  '087e631da111edc6db508289a7b5a95b':
    '分配不同的值段，用不同的颜色提示用户。若只配置一个颜色不配置value，默认value为100',
  '88f8919dd239f2018d267f79d71a672b': '搜索框、searchbox',
  '********************************': '清空输入框',
  '985d97bfa88cbaed452ee5900ad26d45': '服务Service',
  '6f6f1e6feb9fa966acaddae627b73948': '接口配置',
  '270ac7e329c21eb9a5e4c342f202bbb4': 'Schema数据源',
  'bfa178f875f63957378fc2d0bd8d0f59':
    '配置schemaApi后，可以实现动态渲染页面内容',
  'dd22d42f40442605fbe0c5da03203ffb': '是否Schema初始加载',
  'eb1d029d107422d00f55c8b76e66ec75': 'WebSocket接口',
  '470e0b39b2486883a10c1048f2df9f40':
    'Service 支持通过WebSocket(ws)获取数据，用于获取实时更新的数据。',
  '3bfc17446f0456f9692f25fc90383ec1':
    '对于复杂的数据获取情况，可以使用外部函数获取数据',
  'ec9b680403aab2f0498badb090f04364':
    '/**\n * @param data 上下文数据\n * @param setData 更新数据的函数\n * @param env 环境变量\n */\ninterface DataProvider {\n   (data: any, setData: (data: any) => void, env: any): void;\n}\n',
  '642d94fa418e15a3997bcf7488315e4d': '原子表格',
  'dad6519c7c75a23bee8ccb576e4609a2': '数据源key',
  'cba09997ca646e67544f880c80ab97cb': '可拖拽条件',
  'cbe89f133fda6e0d1bec31eaf6aa7853': '保存排序',
  '413c6b52c23a1bf5cbc9fba1485f88ff':
    '[amis-editor][Table2Plugin] dynamicControls的值必须是一个对象',
  '5b11953888d7c376458b397f222d4533': '绑定当前上下文变量',
  '5e1872b4afc82fc5114b32aaf4477500': '选择类型',
  '443f46c76ebe6ec4eb502f95c451e4b0': '多选',
  '9fd1b7cb41cfa3b83bdfd2a44381417f': '单选',
  '9c5c1f96ba29b9c0a8915be950e91cb3': '固定选择列',
  '5fd9a061aa8d25137801caf78d8d0f42': '选择列列宽',
  '107ce5ae5c46f0e63565c593eb09a312': '全选所有',
  'f3993a00b12133950b96199dbf08fc43': '反选当页',
  '0cf1882623b2b50f0416030c980c3179': '清空所有',
  'b5a34b813ffd5d7d776eb8ca56a1b45d': '选择奇数行',
  '0c2a525c7e183c05b46caa6b52a21b9f': '选择偶数行',
  'df5a3392ee995cfaa13787b11a1e2652': '行可勾选条件',
  '76417db4eeb031b0bc15f4cf8178ae46': '最大选择条数',
  '4d250d2754fbdc9cbd131bf48d445894': '展开按钮位置',
  'ea7026a83bf0b63ecc31fd8e215766de': '可展开条件',
  '9e9b4716c08d4f15d9d52e00281f4265': '配置展开区域',
  'f09e007fa1c2e7eb9ec01f8481104d94': '嵌套字段',
  '1b4b8d809a7d253bb650d0f104d24ea2':
    '声明数据结构中作为子节点的字段名称，默认是<code>children</code>',
  'f53160d07e516a3b0d38d61822944b03': '行角标',
  '8952f4e24070a79741a505dc20bad8f0': '高度自适应',
  'dd7befc8e7c124f6f4ba9bbf3de9dc53': '缩进尺寸',
  'eedd7279409d000114e27194f3c8a5ea':
    '嵌套结构展示时，设置子节点的缩进值，单位为px',
  '4bf8b7a0b3385fcd34a8e0cd355d7964': '行类名',
  'a193a0f0b38ea06f46b1d3051c433cf5': '弹出框',
  '3c87936b95f844aa21605c75d217c8f9': '弹出框内容',
  'a0f14a16feaece37026df27feb9070a1': '配置弹出框',
  '44c9d7cb031327029dd0b1bc92689542':
    '[amis-editor][TableCell2Plugin] dynamicControls的值必须是一个对象',
  'f4a3780b66d65a315a762976ab5e781f': '操作按钮',
  'a0dbb2b0a000cdb3a265d096d4e5ed8d': '固定当前列',
  '00ed921de3ababcafcb0594ff0e9a997': '左侧固定',
  'a5b48f7807e3c1ddd80fa160f46f9cc9': '右侧固定',
  '5cbd7ad8a3ecf059b62219c17fa58cae': '标签内容',
  '3b4bcc788ee92004cae962801b471b8d': '自定义标题显示模板',
  '2328f91f1e160de6a06c2004d49ec53b': '配置标题显示模板',
  '11e6dfd7d3fcb0892c2cecb6d7549102': '请编辑标题内容',
  '3d95415ee75837db2660255ea1ca1b9b': '时间区',
  '2fe3c5280ea644639bc6378bcecc8b27': '详情区',
  'e1445cd3c4710c8abf7c1b3b1ce060d8': '浮层样式',
  '1f9ba0bdeb0cf0d3193cac269dd2f708': '最大显示行数',
  '2043742930f9833b4405aa0314a496b1': '填充假数据',
  'a903e51cb1915b56f94bf54a81816cf5':
    '只在编辑区显示的假数据文本，运行时将显示文本实际内容',
  'fd8f406e39d5c4b1f1f2251156eb34ef':
    '{\n  // 上下文数据\n  [key: string]: any;\n}',
  'ddca9c0f0e3d07c3341701b80f139cc0': '是否静默发送请求，屏蔽报错提示',
  '6de215632addbb664d254e00532d92aa': '最近n小时',
  '5bc676c0d274d9a4674f832ae07c6757': '序号「{{@1}}」的字段名称不能为空',
  'aa70a469e4ceb0dede9d73cb8d8d953a':
    '序号「{{@1}}」的字段名称「{{@2}}」不唯一',
  'acb3aec4d12f6ca06a1e45302030cdb1': '数据处理中...',
  'cdfd25057876424324682b5bdde38a3d': '请先填写初始化接口',
  'a9fea442707e26dee478b34a2f2ce263': '请先填写接口',
  '4cc6a76c146c0360a41ceaf5e212c891': '基于接口自动生成字段',
  '4484fa04e7b71db4c8293e5bcb53eca4': '添加字段',
  'eea3ebc33e69694e0c12d4ab2e07a553': '字段标题',
  '404f38ae7ac36860c0b3af6f2f4a13f9': '菜单名称必填',
  '822be91778b5ac22d31681f7256b849b': '不能将菜单拖入其自身内部',
  '088b54ee8f10a43977afa9d16ea5350f': '自定义菜单',
  'cd1e63aed43df0827cc09fb26521936c': '编辑菜单项',
  '648c5e847b923bdd51bf5c72436169ba': '添加菜单项',
  '8ee9f276a6356aab65f8178c4f30fabd': '菜单名称',
  'e3cc5bd7fc92d7287a14bf5398c4ecc3': '父级菜单',
  '24ceb2a06b1962b396b75286fc0960d1': '请选择，不选择默认为一级菜单',
  '9483042d09dbad731addc1791b5d207d': '菜单图标',
  '6107b3c4fd8587589210cb9fe2fcdad9': '请输入地址',
  '76b5162d1b7a16b4b6adf1b79231c96a': '跳转方式',
  'b2d1bffc689e4478519d8a010450192c': '当前页展开',
  '80fb2db8d3f212b3dd130d24da1c970e': '新标签页打开',
  '70b4d0676f9a9640c5a7b1d5f66faa64': '角标内容',
  '9f3cdf6aa12759fab68a5a88179462c4': '若为空则不展示角标',
  '07c60b53a84fd7751095864a5310cc7d': '自适应',
  '81522afdfef2e4121c9240d00583f531': '百分比',
  '9a8aed590bb7fcdd9198daca349b055a': '固定列宽',
  'e0dbf4a939e6c84610d28b8a9b1803c1': '百分比列宽',
  '41be5ce31e28742d0b259fe734e49c28': '至少保留一个根节点',
  '49198f84cea6c212cd5d463037827cd5': '添加列',
  'fb4c95bae088e3f216ea9312bcdb26ca': '列类型',
  '8eebb8ae809cf1ceaa23cd2fa2c73898': '字段列',
  'b302476563937e24190496ab668ebd26': '未找到对应列「{{@1}}」',
  'f9e759ac21d528e7d1e84b5feae4560a': '空列',
  'bcba3b2e1f7b4fb528213e999333d331': '确定要删除列「{{@1}}」吗？',
  '7202bba23492811d2964234485a909e5': '容器列',
  '4c2dc6491d29ef77f421daa19541c8ab': '字段加载中',
  'e11e888f47bc6cd5816a1147be05e792': '未找到目标组件',
  'e6413abdc2fd3ccbbf15327e4004b1b2': '去编辑目标组件',
  'fcebd5cda006253365b4453596290f48': '暂无字段',
  '161f53cdfabe18451e4067f2426cca0f': '未找到工具栏中对应操作「{{@1}}」',
  '4357955c64f836dd7f36af048d857f9b': '确定要删除工具栏中「{{@1}}」吗？',
  'dc858fed3c66a1194d5f9aec81faee3d': '暂无可添加操作',
  'fb3304d27d85c79d89ce6cac22a174b8': '自定义按钮',
  'e40c411876b4a57e24fbc6b4b4fd5027': '操作生成中',
  '0e7bb2c7879f0a34c02f547820b0b0b3': '错误忽略',
  '5d9fe78268b03e8aa41ac316ef610d9a': '动作发生错误时，是否忽略错误继续执行',
  'c0d5d68f5f1cc399311e92905ed2fa80': '忽略',
  'a165be161ac250720f6f25820dd2a5b3': '不忽略',
  '9cdfce42ef0fa346511538131e51328f': '预设',
  '17817a4d2da41f4261f4155ada59e395':
    '<%= data.ignoreError === false ? "找不到组件和动作执行失败都中断" : typeof data.ignoreError === "undefined" ? "找不到组件容忍，动作执行失败才中断" : ""%>',
  '0db9e779f5cd9ad8bd3d16d7e8a16b64': '弹窗消息',
  '4a502e748d1335385c2c05bf30e582e7': '打开弹窗，弹窗内支持复杂的交互设计',
  'c1b3a483bf057f5afa118f96644dc8a3': '弹窗来源',
  '0c5cbc9d345936876230a0b09aece2ff': '选择页面内已有弹窗',
  'a781b5903a4013c147783e1047f42e08': '新建弹窗',
  '53bed22bc03c0fd61fe4fb81101f712a': '弹窗标题',
  '953131d14e66fae5e3611f8b419b7ed5': '请输入弹窗标题',
  '7cc53692d650e049802d808b81efe7f5': '选择弹窗',
  '939402f3ff754d8d815eb7f6cc991bea': '弹窗类型',
  '139c619a4dbfc26fb61d76dc388e3e7d': '请求结果',
  '0eb4e63db88e158600dab0e723e8a213': '重新请求数据',
  'f16654604d6cb2f62469e5aa9db19871':
    '如果开启发送数据，会先发送配置数据到目标组件，然后重新请求数据。',
  'a0c117d927c6290bab55ae0e848a4d4b':
    '开启“发送数据”后，所配置的数据将发送给目标组件，这些数据将与目标组件数据域进行合并或覆盖',
  'eb6cd21b9ed45ded3ecdb12f62b590e1': '数据处理方式',
  'ac24ffeb131a7a9d2465b3ba7b14e10c': '提交结果',
  '0e3517fb21e2c4066bd0ab75c51bc6fb': '请输入存储提交结果的变量名称',
  '0e2ba6becfa8760853cfa31c9e15a94b':
    '如需执行多次表单提交，可以修改此变量名用于区分不同的提交结果',
  '4604d5023479171cb6e901dbeccf62c0': '错误信息',
  '71f6236494bfc8b023804abb0cca1639': '错误详情',
  '4e6bfc5ad98f719f9bc6b2ad3b6440ee': '提交请求的响应数据',
  'bf0f829689370b36d01ce871324e0bb6': '校验结果',
  'd23157205c994bde20bb2605c193fd27': '请输入存储校验结果的变量名称',
  'a422eb12ebbfdd3347c4deb5ec6b4b54':
    '如需执行多次表单校验，可以修改此变量名用于区分不同的校验结果',
  '0552d08957a270160d5203e0611661b2':
    '自定义样式仅对当前组件生效。示例：\n// 当前层级\nroot {\n  color: #000;\n}\n// 子层级\n.text-color: {\n  color: #fff;\n}\n',
  'aaf9656ba493b58fbab398d52efa9f7c': '编辑样式',
  '3ac7b489ba8cb811386c9f4fbfd1c6cc': '结尾字段名',
  '9da561f34a93e0275c56b84717ac9cf0':
    '配置了结尾字段名，该组件将开始和结尾存成两个字段',
  '475cdfcaf614f2b69d88e1e34ba76079': '主键',
  'c28f86f11fc814ea5696af5aa9464cbe':
    '每行记录的唯一标识符，通常用于行选择、批量操作等场景。',
  'fa66d1acaef7cd181f21f5fc2895becc': '启用严格模式将采用值严格相等比较',
  '9e3790244299ed296601d79e0bf43a5c': '新增按钮名称',
  'bbbeba31bfc391bd0741ac62ade78c5a': '编辑初始化接口',
  'ba62070c3a350918d542e990d3278b07': '新增{{@1}}',
  '06e2f88f428b7e26f7da3cd4d40ec2ed': '请输入名称',
  'c630aa278a251b72bebf09284095112a': '配置新增表单',
  '52830af276fb186ff93f16a562acb2f6': '卡片组',
  'f27f10e4660cba730189cc73bcbec0dc': '线长',
  '35633d4bcf019258d4a5b927e8644bed': '标题样式',
  '3ec2bba02a859a90b023ee793a2381d5': '距离',
  '12ae2cd5a178cdaed37967ec4226c4d3':
    '标题和最近左、右边框之间的距离，默认值5%',
  '7eac9d196c43693b0820c7bc0ab504c7': '循环渲染器',
  '21157967162b6206ec88d66789bb1eb4': '对比编辑器',
  '6d36d88078b28530bd4a236b1033c1db': '表单项集合',
  'b400e476d7cc8a1b42744661f57525f2': '隐藏字段',
  'a0e6ecd1eba5673e16218e559f549112': '颜色选择器',
  'bb712c16299683ab1af22258740a537a':
    '日期框、input-datetime、日期时间框、input-time、时间框、input-month、月份框、input-quarter、季度框、input-year、年框、年份框、年份选择',
  '0bbad229b14369f667abae7fa8453d69':
    '日期范围框、input-datetime-range、日期时间范围、input-time-range、时间范围、input-month-range、月份范围、input-quarter-range、季度范围、input-year-range、年范围、年份范围',
  '912e3cf5ce8720c39e7a778e916898b5': '输入框组合',
  '9462f8cc8d65d991be1593493039b85b': '重复频率选择器',
  '224c9f857217e09f7c244093d4f08cd0': '输入内容变化',
  'ce369362c3ae69c27d717d3210303e27': '富文本的值',
  'de0302f6f891653ac2d8417a46249ebf': '列显示开关',
  '142184926b0ae6169a9b7bdefb9b42ff':
    '是否展示表格列的显隐控件，“自动”即列数量大于5时自动开启',
  'a0d2d27fe320721bd5b0157fd5466b9d': '标签选择器',
  '997a27d508152023fd04b7227b531681':
    '文本框、邮箱框、input-email、URL框、input-url、密码框、input-password、密码输入框',
  '98cb62edf3436738df2561d01ee19ab0':
    'tree、树下拉、树下拉框、tree-select、树形选择框、树形选择器',
  '010b98c12fe130496016ea515b70ec32': '当前选项',
  'd8bab42dd5fad3f25760e190b552fac0': '配置默认态模板',
  '70864b156cd3a9c8d982074feb01f587': '配置激活态模板',
  '369f7b53301a54c2240a618827ee2f83': '选项名',
  '70aefc6ad8dffc85d083aaa38aad9d28': '厂商',
  '3fe044338b0be3a388c1a00fb9fb0592':
    '传入参数格式应满足如下要求。<br/>\n                    <pre>{{@1}}, null, 2)}</pre>',
  'ab7c4bf8e1f3d2779d62d0e5e038e2de': '列表选择器',
  '828476b63e65ac62976920fc753a3071': '多行文本输入框',
  '895013340b5cba65de69aefc9f4f0dff': 'uuid字段',
  '993844afdf1cd0bd368cda41fe2d39d7': '水平分栏',
  'a94180c2142df3122cffcf1900c507f9': '实时日志',
  '319c7a1a88910df53d6cd1b408f5894d': '当前页码值',
  '52f3b7dd57c2cc44b645afe6d944d07c': '功能型容器',
  '7c39d9e0d516e260fc3513eb8e5243a5': '状态容器',
  '39282fd22802ac1d9f99cdd0ffff253e':
    '根据状态进行组件条件渲染的容器，方便设计多状态组件',
  'f449bcac0f09a349882e74800f20d724': '状态一',
  '3b5a5cee1bd8775463be15d96eac23a1': '状态一内容',
  'f7361c5ad849477766b12e970bbd71f0': '状态二',
  '0880f36708cd0408167f318936cc6df1': '状态二内容',
  'c887c83f280bd63012d32179ff5273ed': '状态名称',
  'a30fd0a2a17e3dcf701b3334341aa39c': '状态条件',
  '7829a5f84414f02562a5b17b1c301046': '状态{{@1}}',
  'ca035ede21ad993ba8f29a2e615b61e0': '下个状态',
  'b7349a87c4d838d1a6047c2844b1744a': '上个状态',
  'df7f223af52c3261a3b23a152bffe26c': '状态列表',
  '8256d18231287d07406fbf019e81bb01': '新增组件状态',
  'bd863f35feb02c40cf14493b1dc1b198': '显示条件',
  '93553f4588b0f0c782f58182db0490d7': '状态内容',
  '7cb7a4480adf1c3e13378201684dbd5f': '表格展现',
  '22efa960b935516b3016b4a5bd7f1160': '任务操作集合',
  'bab9f8592e1b8e286ee17ddf5dbea4b3': '显示文本',
  '97d43b5b163c9299f70b76d12541828c': '新状态',
  '01ceb3edde98440157f199c292011dfc': '无数据',
  '8191e975f70edd63fe65476dd8bc0ddc': '组件id',
  '7c80a8d7f7af4e8eeef653c0af82010d': '输入组件id',
  'c619838a5e3c9641a80fc5f1a9001a9e': '持久化Key',
  '78979b40f53cb6510c6bc1e1a2473fc1':
    '使用静态数据或者变量：<code>"\\${id}"</code>，来为Form指定唯一的Key',
  '72f4712f23226b5e01442ab9b6ae90bd': '保留字段集合',
  'bee8b35158dc5ddd44ebbbd9f29de422':
    '如果只需要保存Form中的部分字段值，请配置需要保存的字段名称集合，留空则保留全部字段',
  '5755fa70717929d1cabc4f4d9778d08e': '请输入字段名',
  'd149ac9b09b2aec63ffb58dca98fc281':
    '传入参数格式应满足如下要求：<br/>\n                    <pre>{{@1}}, null, 2)}</pre>',
  '8ea6d154a7c25f8802406719c2ec3c02':
    '内嵌：以平铺方式展示在页面，其它两种以弹框或抽屉形式弹出展示',
  '361f434f3b82973d6526efefbb538118': '标签收纳',
  'deab2747ddf5310781cad655c4f7a50f': '当值数量超出一定数量时，可进行收纳显示',
  '8c019e522d7bfbf02d6600f91f3b4edf': '最大标签数',
  '5404c803438f1027131cb6fe35037075': '选择器收纳器',
  '5d16dd78f1b9194d063322d117c75162': 'CRUD收纳器',
  'ba3a8f747de8c6dfe2859c4ce2105c3a':
    "当在fx中配置多选值时，需要适配值格式，示例：\n                  选项值为 <br/><code>[\n                    {label: '选项A', value: 'A'},\n                    {label: '选项B', value: 'B'}\n                  ]</code>\n                  <br/>选中选项A和选项B：\n                  <ul>\n                    <li>开启拼接值且拼接符为 ‘,’ ：值示例 'A,B'</li>\n                    <li>关闭拼接值，开启仅提取值，值示例：['A', 'B']</li>\n                    <li>关闭拼接值，关闭仅提取值，值示例：[\n                      {label: '选项A', value: 'A'},\n                      {label: '选项B', value: 'B'}\n                    ]</li>\n                  </ul>",
  '00e44cfe38bdcdefa40ad63dec4e9bd4': '标签模板',
  'b2f3c4387bcabafaf39a7be9c624b584': '已选定数据的label展示内容',
  '988d7b545c09108379a54a1e6060f563': '图标颜色',
  '7278e97a3360f7b31768b86b8d22173d': '图标hover颜色',
  '9df3213a5f8211cf2626f4c5c646d71d': '新增数据项',
  'a6354302d04750ab5e28fac72be054b1': '数据项',
  '1a873cce402693b4c3b4bf16a4a04523': 'Option中属性的Key',
  '75208f0b7fa9dd1633fa2dded76a6e8d': '父级数据项的值',
  'dcc957ddbe7fd921ea03d008f7ddae4c': '请输入父级数据项 valueField 的值',
  'cd407112daa4d933aead47835bedafb2': '编辑数据项',
  'a3b6fcf7cfabfc83543bb00375235915': '数据编辑项的值',
  '0f2dd97ecc370524fd8ff0a9ffb880a5': '请输入数据项编辑前 valueField 的值',
  'e70edec390d0e602208f6951e1b5f01f': '删除数据项',
  'd16814fa14c14dbeca8d7e8600852255': '数据删除项的值',
  '6a2e7e69d5bcf2e2ab0c3e288d7fd2ab': '请输入删除项 valueField 的值',
  '6e065eee612393b1d6a16e7696e4c911': '树组件',
  '555fbc1714ad0d899939b12b95dabe09': '树组件 - 内嵌模式',
  '5c52d3e283d197588e29ca31bc613ffe': '选项搜索接口',
  'dd52923c45686a6f507854e438d4880e': '懒加载接口',
  'a386f65cf2c47acabfab13781dbdd89f': '树组件 - 浮层模式',
  'b5d9da39175bc5bdcd2cc7dddeaa232a': '懒加载字段',
  '31ff316f7b6aa169b2575a50e0342be0':
    '是否为懒加载节点的字段名称，默认为defer，可以用该配置项自定义字段名称',
  '37a3bfd0cf20cc17c53396a1077c0852': '自定义开启懒加载的字段',
  '6c1f091d6ee0780a6f836d212b8afa82': '已配置新增表单',
  '9de4041a80c32ada7fb8b3f212da62d2': '已配置编辑表单',
  '7eca7159d5a09db3a0008280faae395c': '搜索匹配函数',
  'e95903f4c15aab31ea66ecd51b76e1a3':
    '自定义搜索匹配函数，当开启<code>loadDataOnce</code>时，会基于该函数计算的匹配结果进行过滤，主要用于处理列字段类型较为复杂或者字段值格式和后端返回不一致的场景。<code>matchSorter</code>函数用于处理复杂的过滤场景，比如模糊匹配等，更多详细内容推荐查看<a href="https://github.com/kentcdodds/match-sorter" target="_blank">match-sorter</a>。',
  '483cab6ec2367302b037208475361860': '/* 当前列表的全量数据 */\nitems: any[]',
  'dd19e649ee3fd61e0eeab6b67a6eae7b':
    '/* 最近一次接口返回的全量数据 */\nitemsRaw: any[]',
  '2512e82e75dfe0f6f499db4ae9f715af':
    '/* 额外的配置 */\noptions?: {\n  /* 查询参数 */\n  query: Record < string, any>;\n  /* 列配置 */\n  columns: any;\n  /** match-sorter 匹配函数 */\n  matchSorter: (items: any[], value: string, options?: MatchSorterOptions<any>) => any[]\n}',
  '4f54012c824bfcd8f9ca9ded82051d94': 'Mock配置',
  '3566441483da64f6bb1dd44cfd6b0184': '数据Mock',
  'fb677689a875ed3078e386ad88273e73':
    '开启后，当数据源为空时，会使用 Mock 数据',
  'af192f42759b02f6fd256776b12a238f':
    '设置后，会按照设置数量展示数据，可以提高设计态渲染速度，降低表格高度，便于布局设置。设置为<code>-1</code>则不限制',
  'a15e0eac4fe49346a744fa7943c46890': '日历日程',
  'b13bf2bdf8a7abbd8cdf85683214be45': '展示日历及日程。',
  '0c6482892e9df6505c8de1eabca68266': '时间戳',
  '398e51caf9e8fabbeb075833f0c1e701': '日期格式',
  '2ca9949e2a05e63f735b713260307c45': '时间格式',
  'e63e4cf37bf0af962b6ad66243dbe0f4': '日期时间格式',
  '69a79a0e5083305255d191f708a41860': '是否可拖拽',
  'bc48a7871960f7a395fa3ff20774f81c':
    '{\\n  "status": 0,\\n  "msg": "",\\n  // 可以不返回，如果返回了数据将被 merge 进来。\\n  data: {}\\n}',
  '1385df54facf9564658f05d0e9e2c3c7': '行双击',
  '1c00d9cceec92084b116a382f2714ebb': '双击整行事件',
  '4f0a3e65cf67ca5a117a3f28745ae930': '展示底部新增按钮',
  '2424c96966b9c38c7d094a2d40f23a5f': '展示操作列新增按钮',
  '2e2c57b664358061f9f20aeda43b53af': '自动选择',
  '3fd78aa49302ff2634badc2ef49828f7': '开启后，自动选中用户当前的地理位置',
  '1acee853829433de3ece301ee365bc99': '限制模式',
  'ce8b1e6f3819ea068921a4fa9ead6968':
    '开启后，限制只能使用当前地理位置，不可选择其他地理位置',
  'e28da3f03af5367e299b937643a22e49':
    "当在fx中配置多选值时，需要适配值格式，示例：\n                  选项值为\n                  <pre>{{@1}}, {\n  label: '选项B',\n  value: 'B'\n}], null, 2)}\n                  </pre>选中选项A和选项B：\n                  <ul>\n                    <li>开启拼接值且拼接符为 ‘,’ ：值示例 'A,B'</li>\n                    <li>关闭拼接值，开启仅提取值，值示例：['A', 'B']</li>\n                    <li>关闭拼接值，关闭仅提取值，值示例：[\n                      {label: '选项A', value: 'A'},\n                      {label: '选项B', value: 'B'}\n                    ]</li>\n                  </ul>",
  'fb58b1b692e6cc562764f6969269f802': '已配置选框详情',
  '645dbc5504e722a30896486085a06b32': '预览',
  '9aa70276e6d364abdc0543deccea2dac': '预览图片',
  '893a9d45e4df161d0b0c57340631bd2f': '调整图片比例',
  'e8aaab64292d8b17db25a1ab44bcd8e0': '将图片等比例放大或缩小',
  '28a7e3af290b229c490da0ad4e9b700e': '调整比例',
  '7f2081cebf9bd4b10b953b4008c4b568':
    '定义每次放大或缩小图片的百分比大小，正值为放大，负值为缩小，默认50',
  'cb90ac33f6b3bf684635d4f57cb290a0': '放大极限',
  '7ab252f7aab256f1785c793049d01e13':
    '定义动作调整图片大小的最大百分比，默认200',
  '7d645933c48d5061c2a13dd5f8b9b4fa': '缩小极限',
  '164f3e6ced78578c534477da66e0b008':
    '定义动作调整图片大小的最小百分比，默认50',
  '8e9f5f321ac9b39dbcf0f603190815ce':
    '悬浮容器: 基于 CSS Fixed 实现的特殊布局容器。',
  'ace080d5d9684a05aa5ec0ca38ec2831': '每页显示的记录数',
  'e52664b945c685d43d6ea81953a33a4a': '触发二维码下载',
  'a605077feb2a94f1f72627703ff48e39':
    '当前表格无数据，使用Mock数据用于效果预览，组件面板Mock配置中可修改相关配置',
  '014b4de0dd3108fc912547e4a1ed19fd':
    '当前表格仅展示{{@1}}条数据用于效果预览，点击顶部「预览」查看真实场景数据，组件面板Mock配置中可修改相关配置',
  '8bb1910d5bc3b1b32c1e037978ba9d3b':
    '开启后修改即提交，而不是批量提交，需要配置快速保存接口用于提交数据',
  'df23e8db7baa38e0d9db41384788a40c':
    '默认使用表格的「快速保存单条」接口，若单独给立即保存配置接口，则优先使用局部配置。',
  'bf4bc760ad07eccb701c8be8579005fc': '列表项数目不能少于{{@1}}',
  '0e69a56857c65181f96cd272d20455d6': '点击新增表达式',
  'd846706b91f99cd12545a78212fa5984': '常用动作：',
  '1889907dcbe32f9e8ac2c9df6603f12e': '对单个表单项进行校验',
  '1a04578671f6c3bfa2d35a8cbd9d1a9e': '校验表单项',
  'e4092e07c4613278048c91064f4bfdf7': '校验单个表单项数据',
  'dd39bd1bf78f1b2aec6ed8a05ced6884': '校验的表单项的值',
  '0a2680def3df6912d7c96d95a047b680': '校验成功',
  '136b339ae7f40edc05251da87dd8a233': '校验失败',
  '14a265b60d169e9d9c11b9a286de199d': '事件埋点',
  '5c1024a792dfc345b4d8a3b34255691b':
    '开启事件埋点后，每次事件触发都会发送埋点数据到后台',
  'b420567f7b473ae1a68d1754a70ff32b': '弹窗入参',
  '0b6bad3bc224e893a3972a32b2a5daed': '添加入参',
  '87e2f37db14a6d201805063d0257fb84': '弹窗参数',
  '466cbd9ddbfbb53ad6753c74ec5f6775': '头像地址',
  '5402f86a1c7ec56ed792225a5aad9fa1': '默认头像',
  '5f7b62a975fcb8b8a493b66821aa18b7': '按钮基本样式',
  '69ce077907d7e7cf54adc92bcb8d1ab0': '按钮内容样式',
  '6dd8962fbf03abd87c75e55505c38992': '行索引路劲',
  'd6c6e029578569c4d0c3828ed0b6ae52': '触发',
  '4793ea431592921524fc31117e9a662e': '加载',
  '365bce0cce7816191593eb7680abc0e3': '更多数据',
  '9ff126c0c5cf17d836a2f475e2f10297':
    '无限加载时，根据此项设置其每页加载数量，留空则默认10条',
  'fd68baae75f0dc2efcc94e52764eb2c1': '将值重置为初始值',
  '0e3b706136f235f9368e41858f91ee86': '标题区样式',
  'c409adcc9f7ef3789c2f03c4da4295b5': '展示风格',
  'e7e07e58639b91dba2531381c9882f3b': '简洁',
  '74ef6bf722dc30b7a927f0c2737e9277': '下拉菜单对齐',
  '395e591c46c65bb082408c63aeb55d87': '插入一',
  '2d5aef4f2443b4694bd1dcc434636c6e': '行',
  '9969614487f9bf68da5b5f46117356e7': '弹出方式',
  '86dbfaf290d218967e17a8e526da5dd4': '触发确认操作',
  '7513dd4382015cf5d874f1f05635f32e': '选择框样式',
  '49b81e7a87ea36f001b16903fa0e221e': '下拉框样式',
  'fad7c8a21fe3d7b3eb1db3d8a3212503': '选中',
  '9116f77b722c5e5066f7294a5eab3258': '选项样式',
  '7d9550e4650922a116d0524199fd4ba0': '选中态悬浮',
  '056be0efc6da4c479842d8bd0e48ce44': '选中禁用',
  'a1ff16babff54c3a5130d924fab66077': '勾选框样式',
  'b9295cb46f727ebbf428dbed7ea591df': '隐藏勾选框',
  '9b30c781f9ee4bbf8fe17d9c5230f960': '拖拽结束',
  '4a6052ca42be59c3ba9d4643c1219820': '当组合项拖拽结束且位置发生变化时触发',
  'db3b15e5f4a62aabbbeae5d97831865a': '拖拽后的索引',
  '7d1578f87dcc598b587bbef905df1a60': '拖拽前的索引',
  '39f0e1740b5a1ee35861ed3d95f47160': '被拖拽的项',
  'f92c83fc847f44f5bbec68093823b642': '拖拽前组合项的值',
  '29075779cbee0247af40dd1faef9d8c4': '拖拽后组合项的值',
  'a464ee5fc18625f4a3705371a62980fa': '配置唯一项',
  '16bf955c22835b6acbc202bd2738b50c': '网格',
  '98cf14f371887d369548aa7323eee85c': '仅对PC页面生效',
  '5f35e6afa1712206c8ca6358bb0d8974': '表单样式',
  '6a01ded8bc34fc12c2fd3db9d53fd31b': 'Panel样式',
  'd4e08f2d68b6a5e424f0c588e74e2d79': '表单项样式',
  '2798cd2f320e1f47a19c98409feddc39': '上下布局',
  'e2e0f5e01ebe60cbdd5d088fe3aecda4': '水平居左',
  '7c010e13bc2da0b7654f575ce180f8f0': '水平居右',
  'e6b03b4b253a211cae522ba2ddd25ef0': '表单项边距',
  '0457e75acf23d1f08df052ce76392f10': '静态展示文字',
  '2698a98498f2cbfd2981abe114d98d88': '操作区样式',
  '82dc7569ff9b0d6fc5cff3c16ebd1b2f':
    '支持<code>hex、hexa、hls、rgb、rgba</code>格式，默认为<code>hex</code>格式',
  '07e8860ca70f31dfdeefc423fbbe71cf': '显示上下按钮',
  'bd13c6190081411bfc68f1d2dedfe66b': '表单项基本样式',
  '86c705e304b94409e504c0816cf705b6': '数字框基本样式',
  '7d5cb12cf591bad370620d44db1fa147': '手写签',
  '0937b57f4520b147cf1ca55212073961': '手写签名面板',
  'be2525ebade48dee835e25c04f130725': '签名',
  'ca15cf7f2255339b2c76472661ee1cad': '签名面板',
  'febcf824278892810c15874ef8091c54': '签字模式',
  '3c37b8f85d2956314157313230a65a23': '功能按钮配置',
  '1dfce787bf34fc9ebeaf2c45a01f1e74': '确认签名',
  'e6690b4a5eeb5fe0e80a9a686528b68e': '取消签名',
  '6d5a04648898247773df72222485377b': '撤销签名',
  'cc3a5a4cdcf5c351dc2cf6a30fbc6c25': '清空签名',
  '5bb67fb82af68a0fc68051e010cd34b3': '签名按钮',
  '6fa55bbbd57143589f07456b6490ca37': '索引路径',
  'c61fd58caebbe493bfae4dc8970a870d': '取消排序',
  '5d247589c2361f7eecc34ab2f05bf170': '取消表格拖拽排序功能',
  '8424ef1246fde86185c86f29e76b7705': '最大标签数量',
  '990056ee43b48c3757c1a42fcfecc32b': '最多选择的标签数量',
  '8b76adb277cb335ecc55f4742f3ae3f8': '到第',
  '5c419515e88f2528de8858879fe8825b': '收起层级',
  '8214a59b9ded0f512ee313b1ba09f42f': '搜索当前数据源内的选项',
  '16cca0782ad018a5aa116125bc74d76e': '的选项',
  '9699a50e47f6c25c62851c06c5f05191': '关键词',
  '5f8e53e94d4c96150845f8f8f04c64ee': '子节点反选取消父节点',
  '0f11ffe65cea25ee815350349a4af981':
    '取消任意子节点选中状态的同时取消父节点选中状态',
  'f95191a22264512fe771a97126358f8f': '节点行为',
  '645aa00b95df2c0f133cae9b8eaeba9e': '操作栏位置',
  'e787d249c3861cff191cf89f7ff1271a': '自定义操作',
  '50e7872c5ae24a71d7eb7a525ed79606': '配置自定义操作模板',
  '7c893acb8cd7e7ed6ba47cac83a9d1a7': '树基本样式',
  '3c7a5740603788647438ed460548ab1a': '树工具栏样式',
  'f9bad394c0cc6ce790b7da387d25da7a': '当前节点',
  '5cc731040b5d6c7d92f826f9381c9cd2': '节点索引',
  'b1785ef01e9b97956eec7a7266a3fb52': '节点名称',
  '94ec9e18c7faa520231cccecf4f4a77a': '节点值',
  'b86224e030e5948f96b70a4c3600b33f': '节点状态',
  'df19687fd0e738ab705bece977dd7efa': '单选框样式',
  'ee9a0585c8cd603e6dd49cf266fc98c5': '选中tab',
  'f66aff3d8be711b30d15560791684444': '多行文本样式',
  '39c853dcd16e8f93bbc26b2c79046fdf': '仅包含子节点的值',
  'a6fadd83bd384afc9c2435350de2602a': '仅在autoCheckChildren=true时生效',
  '0f3c63b3680c5aff632494685ad0615c': '选中父节点自动选中子节点',
  '7cc032aca90bf4d2d5032580053fc3b5': '弹窗大小',
  '0d7234df4bc9931b75f5cacbdae88a91': '穿梭选择器',
  'c47fc67905aecc2c0fe2a7b32a5a1f95': '穿梭选择器组件',
  'b6417c8c8429de8091117a34b4cd3ec8': '节点选项',
  '1827f06ea993ef2a305bf10ff5e6cca6': '调整',
  '507417dce637412e4f67cc9c651418d1': '图片比例',
  'dc9c1c19fe2da23caf8c839cb305032c': '快捷版式设置',
  '7ded21833b89a2da13b6cff3b3414ada': '内部对齐设置',
  'dcf6ca3fe486fd851d344fbe2b2a0327': '行间隔',
  'e0913d8f0238e2dbc497b97c256e08c2': '垂直排布时，内部容器之间的间隔',
  '8ecd2ee16695bf2bc997286b7e5d0391': '列间隔',
  '3f5abfd6b6b56dcc490e328bf8d2966e': '水平排布时，内部容器之间的间隔',
  '32ac152be1911e9360047dcb0898cf70': '更新',
  'cbfd78693bac7c74c0f0065e40984233': '表头对齐方式',
  '582a5fd6bee616a51533a0e2f966eb11': '复用对齐方式',
  'a305a0dac61dd6d2776f158e3309f6ce': '文本超出处理',
  '91b314d626f639f47de564bb2c2deee3': '溢出隐藏',
  '5bf278e051a29ab3333e6026f555fb13': '取消换行',
  '117265698bf794951a0d5b07533ddd09': '允许任意字符间断行',
  '50993ca7f4e7717bd8d9e51bd6176c85':
    '开启此项，换行处理将在任意字母处断行，长英文单词或长英文字符会被切断，如url链接',
  'a9d23531ba618e11b467f5530c59dfb4': '边栏位置',
  '6ffde2843ddeaf536a3451c863ac67ca': '页面基本样式',
  'bae2bbd65546c06101495548c58f1ef2': '页面内容区样式',
  'b6ceddd4553ad4f0b7af0e46cfe82441': '页面标题栏样式',
  '67df95d649c47f6aa3957bc1df2026b3': '页面工具栏样式',
  '530269d9698d01a147cc1a12c404c0ad': '页面边栏样式',
  '0de9cf51289089dbcc3aa250fff0d533': '多页跳转页数',
  '5cab3e445ecabdd21be267f4b0654ee2': '微型',
  '7fc0c1364e6078cdb4b2a6d545b6a411': '底部区样式',
  '3bb48fbf9a96b72f434d87ac767e843c': 'PDF预览',
  '682c47a594e7ed21aac36de207e19abc': 'PDF 文件预览',
  '9d577fe51b38070678cad01e994931a6': '文件来源',
  '5a878b8b32da24a3467d49a437d16936': '文件链接',
  '7cfa34aea62c19462ee2074f58a74aa4': '本地文件',
  'bcf03e212f6351c863bb8ada2014aaa5': '链接地址',
  'feea9211d32cb4ed1cccbad9cc773083': '下载二维码',
  'd09c4dd6414ba34c26fee8ce6ec1716a': '图标文字垂直展示',
  'c252de4695f13a88d7092629eef62e8b':
    "支持模板语法，如 <%= this.id % 2 ? 'bg-success' : '' %>",
  '703cb4e22041256b9060d77a662ae22c': '表头',
  '17169fd8c2ffa82c4e1d08e7267725f4': '行高',
  'c4f3fbc6379d062e230e5590e913bf51': '选项卡删除',
  '8739b49403bc65abd95f85fcdcff248e': '修改当前激活tab项',
  '83a991d77ce351f5edbbf8c0aa878d11': '激活',
  'ac007746080a48f195815c62f8c1570c': '第',
  '1d56565b64ef592403ab7de9c83e49fc': '删除指定选项卡',
  '9c4b40ce474b1ff0897e392636bff81d': '删除指定hash的tab项',
  '4798e120ee7568c397cd17b8ec4e8c25': 'hash为',
  '7cca05cbe9c6298a42957abf29040812': '的tab项',
  '4149ee3efdffed299e4b517a508b0469': '删除项',
  '29a7f9c52775d3668cecb3dcb01b1633': '请输入hash值',
  'a069859a7b209ed546008c3906548592': '选项卡基本样式',
  '7f434fad0e2eaf5ef1937063012864a2': '选项卡工具栏样式',
  '41d064f868823c433dac65ac62755b77': '选项卡标题样式',
  '4df40591212dcc3b7a0a4b114d77ddad': '选项卡内容区样式',
  'f8f23b5d6a0724f940650b93d2f6df91': '自定义标题模板',
  'f4c4d3e31442c8c605be328d16b1c057': '配置展示模板',
  'ce811fde00a65108c19dad52fbfc2d78': '配置标题显示模版',
  '50f34990b0f1147f0e5c7f35f620926a': '当前步骤数据',
  'c76c602025a0d7bacb0096bc1cec7396': '滚动',
  '5f411223ca5a01a5adf20fe735a433d0': '返回',
  '28c35aafea77fa3d92e70a0ad0260d86': '至上一张',
  '7875754736f0fc077a49d2a4937c22ba': '前一步',
  '2b9239697fc5e1d8a2b7b5858fdfadb2': '至下一张',
  'bec7e4d621a66bff059edb31816fbf52': '切换',
  '4cdf4ce158a0adb3468608aaa7ce5d61': '条件设置',
  '73148d5908d22de26cf65b00dea2ffea': '点击编写条件',
  '1943442b4ebb62ba60ec9f364a7232ec': '快捷变量',
  'e3c45feea8c90fce5e05f4dd4c91b5ca': '函数计算',
  'f9968e1893e7a2fd940a00776b6c56ca':
    '每个选项单列一行，将所有值不重复的项加为新的选项;',
  '9d4ce7f085df6c8e70ec8efc49894e4b':
    '<br/>每行可通过空格来分别设置label和value,例："张三 zhangsan"',
  '20fe75797aeb27230d7a46d797d3bc49': '自定义条件',
  'dd46fd79e0e9b5917bc9b65553189483':
    '每个选项单列一行，将所有值不重复的项加为新的选项;<br/>每个数据单独一行，时间与标题用空格分隔，例：“2024-01-01 提交申请”',
  '85f3189774581e5dcd697cfced4251d3': '添加一项',
  'a5d07592917279b17941531a3d96e8a5': '设置表格列',
  'adf692b7cdf447c6afa8deccd238f54b': '设置表格行',
  '270e6864d08bdb3537b6a275bb0bb006': '上下文数据错误',
  '0cb74306e3873be06f679d0324db3b0d': '程序异常',
  'e9aa3a35103faf8ef66d1c2994cefa95': '请选择一个弹窗',
  '404371742f29489a92a49ad6990093ac': '参数不能为空',
  'afdfbc5f36063757e50d1d71636ed7ca': '参数中存在必填参数未赋值',
  'bf86dc21effb6c836c6d2d64ee4d772f': '未命名弹窗',
  'fb35e6699c4e260c27f9bb569353c5e7': '<当前动作内嵌弹窗>',
  '48f1f6b60f4b63df17e055a9084e9429': '<内嵌弹窗>',
  'c96df65b93a6a6cf30537b836958ada7': '确认框',
  '60cdeeb33a3bedb6232fa35fbb9f6304': '抽屉弹窗',
  '0f136747da0492424ae0f3a5f8536cd7': '弹窗内容',
  '391a66bca64718f749d70ce9ecf0b6ec': '编辑弹窗',
  'a7a42528f0fa1819259397e64cd9cdc1': '切换弹窗后原来的内嵌弹窗将会被删除',
  '111e2cc37bd69e6118662049de03b68f': '编辑选中弹窗',
  '839353031488319956d1a8d4626e7f6c': '参数赋值',
  '3f21ce6f1561647add9c21d91d44e875':
    '弹窗内参数赋值将优先取此处配置，若关闭配置或无配置值则会透传上下文数据。',
  '52288dd0fd7a1a96109e28742df9eb1f': '添加参数',
  'ba1470102e66d11d7133942faf589065': '等待弹窗',
  'a39785220eae7064a5afce72af6b23ee':
    '当前打开弹窗动作结束后，才执行下一步动作',
  '15d5fffa6a4cacd866ce3a1134afed49': '响应结果',
  '297d92523f813a6d28a33b5826a66e17': '弹窗动作结束后的出参变量名配置',
  '55dcd671e5416b769d6d973e000999e6': '请输入存储响应结果的变量名称',
  'bb47cf4a90893b1d3255c1027d8e91e7': '动作描述',
  '2bbe5a93eeb01ef5970fddf84c101b0e':
    '<%= this.ignoreError === false ? "找不到组件和动作执行失败都中断" : typeof this.ignoreError === "undefined" ? "找不到组件容忍，动作执行失败才中断" : ""%>',
  '7b7312051cd7e29209225f561ad37541': 'actionType 不能为空',
  'b250b2f9c038cc2dbceeb3c9edeb1293': 'label 或 tag 不能为空',
  '0fe0032cfb95f8b822bfa6a0ee9b15a8':
    '存在actionType：{{@1}}相同的动作面板，将会覆盖原有的动作面板',
  '23f01311516d41e5fc87fbd7d241fce2': '设置变量',
  '637db2ac48c5b47f42dacdefa4e59533': '设置组件',
  'c6df636df2f58d6a1840461bd6fc14f7': '切换为静态展示',
  '2c00fd772a5165fcde0f8b57160991c3': '打开&nbsp;',
  '2a2eee02daa6fc08c038105ab35d55a7': '{{@1}}，点击查看弹窗配置',
  '82708671c66054145588b0ccf33b589f': '全局广播事件',
  '8aa6c7f0ea9b90540d42439a62bdcd05': '触发全局广播事件',
  '5895a6b62e90730d7069f8f492980c0f': '请选择全局事件',
  '3d502eb9a597bf467517d5653e3770b2': '参数映射',
  'e73d88fd48b62fefeddb2a0ac4bddfc5': '请输入参数值',
  '8fb2a370be5ae9fce3ec64924ef38b47': '配置参数值',
  '2660dbeb80c1273cfccabd13649fdadc':
    "/* 自定义JS使用说明：\n      * 1.动作执行函数doAction，可以执行所有类型的动作\n      * 2.通过上下文对象context可以获取当前组件实例，例如context.props可以获取该组件相关属性\n      * 3.事件对象event，在doAction之后执行event.stopPropagation();可以阻止后续动作执行\n      */\n      const myMsg = '我是自定义JS';\n      doAction({\n      actionType: 'toast',\n      args: {\n      msg: myMsg\n      }\n      });\n      ",
  '50d41f10522686a3d297ffbe076d87cd': '请传入actionType',
  '32039746caad9fc07f464af73315b7bb': '{{@1}}，点击锚定到该组件',
  '6b1fafce7b035e7d6ef582c56fdd6560': '温馨提示：未找到该组件',
  '0e0cf27195aa443a6c77ff289b3da2e6': '该组件在弹窗内，暂无法直接锚定到该组件',
  '8e90b820067c81922e0585e130ff830f': '所有选项',
  '8dc09ebe97e0de817d74acab93088c0d': '获取',
  'e0096ff3f908374978ad2b166b0eeac3': '焦点',
  '6eccf8477beb3a509bbfe833c3207846': '{{@1}}(全局事件参数)',
  'eca9acda905705b2e161257e0c4aa0de': '可查看哪些组件会调用当前组件的哪些动作',
  '878f48025dcd724ce8480da94c12c798': '查看调用关系',
  'ad6d665d1a6ac68d64d630504d344646': '您已添加该全局事件',
  '8b30d6b1df387b743772d949b66cf7fb': '全局事件',
  'c3ae33aa8d7ae462c6376301e0f83acf': '排列{{@1}}',
  '28f7f1bb2bf5c0cc8f5319db7e063ff0': '自定义分隔比例',
  'b9241e371f7fae571b1887e893eea9cb': '例如 1:3:2',
  'b9a3db6a1a805c8a5324de48df3b7439': '可关闭选项卡',
  'f41ebe896c38e85342201dc3ec3ca3c5': '选项卡内优先级更高',
  '8230519f7c697911f9c1e92ba3b9ea8b': '禁止输入',
  'fea2358bd4486d4420487174a32e58f2': '相对原位置定位(relative)',
  '09a2682f7e9084eef44c64effadbf798': '视窗中悬浮(fixed)',
  'dd02cc5a07221639a51096b005d9ce77': '绝对定位(absolute)',
  '8b0cabbb1334cf35f326a4830269e1a4': '水平内容超出',
  'a1b66f536630c4456a552a593febf1e4': '水平滚动',
  '8c176b5df33ea7b8c0c68c1fc494a17e': '垂直内容超出',
  '1215dd0689aa1fdf89adbb9b7530a90e': '垂直滚动',
  'a6ba771dc7e0c778952b2c2c6b075fa1': '淡入',
  '340480f5796b020c4060efff34929807': '从上淡入',
  'f8519f45ebc32c93b93707c97ea80c57': '从上淡入(加强效果)',
  'd1329a02546ca79e10dde3497537c454': '从左淡入',
  '3de47f181cf84d96e16ad17ebf74cb29': '从左淡入(加强效果)',
  '2438838b29ab59e5c192f18785f85ffa': '从右淡入',
  'df5b72f4a6ba70e3afb3816689501425': '从右淡入(加强效果)',
  '39377ae457c7b617ed7c5a9d8709aad6': '从下淡入',
  'c92e19fe08d566eaf0a36dd93732147c': '从下淡入(加强效果)',
  '285b0d56e364277b24b72df4a1679427': '回弹',
  '85025eac2602d5792bdfd7abc5a149e4': '从上回弹进入',
  'c9403db4e39beab29f38f99b58ce6f49': '从左回弹进入',
  '7cdd1e7fd3bea2a89be768eb51c7a344': '从右回弹进入',
  'd223c08efde7802938d8283645c557d1': '从下回弹进入',
  '79b5a65645855d1e066e17648d320002': '旋转',
  '18e5859e5f0bbc60afd14da0dd788d9e': '旋转进入',
  'de9e534c129089c34f0f3d2a3abc6302': '左上角旋转进入',
  'd496ec3e33e1c55c7830153bf03c5006': '右上角旋转进入',
  'e1363364b3e6a75f4b171ec033b31dc6': '左下角旋转进入',
  '3b1dbd7b02841bc23c3eb535fb774598': '右下角旋转进入',
  '367f6ba4391cb049015b931c8da81037': '滑动',
  'ad240914679d522d125ac1a203762feb': '从下滑入',
  '217a27c317d559b749396e1e9b07a5ec': '从上滑入',
  'f5a74e26709b963f843713260d8afba0': '从左滑入',
  '40747a59ff4121f8264c372dd5c0cf18': '从右滑入',
  '369662b059423885b010c09ccf4885d2': '翻页',
  '7eb53077f889447c8e91730ec0fb6ddb': '水平翻页',
  '5cacc7f3c3688dc600ff4350e28323da': '垂直翻页',
  '198ee35b44d88a9d68259b7d8deba2da': '弹跳',
  '0679561b8354b4ae830876a7de5b78a8': '弹跳进入',
  '194046fb163c32bac4636cbc1d5f0e15': '从上弹跳进入',
  '1cd7936be0f7fe6f3b95ce06530c47a7': '从左弹跳进入',
  '8b7430f252e8017ee8ff796fb9e86838': '从右弹跳进入',
  '8dd64aaa73906e5580e09d04475e3710': '从下弹跳进入',
  '05853d9ceb108fc5434cdb1a34ab3fc3': '缩放',
  '22ff588ab903f554b26ddc3ac6e5a2f3': '缩放进入',
  '60105a62f5bb45db82a2af1cbfa49de7': '从上缩放进入',
  '529568bf3f02080f77b4acdd98b225c8': '从左缩放进入',
  '4be6fb1f3b77b092c0b97a128d400328': '从右缩放进入',
  '64ea0ec38ac25c5aaadae965d6e964b8': '从下缩放进入',
  '9b916bf3adff39bbeb6d5b68ed3118d7': '从左光速进入',
  '891b421d61cbc5526ca2ed579c3726e6': '从右光速进入',
  '70e344c162554d439887d4186a348837': '滚动进入',
  '1548bf2b4a1f3c3937bc62a8b2fad739': '闪烁',
  '91bd8bdd6599511fad9f7c38cdffc71c': '摇头',
  'f3d7a0cd14258418967f731370993b52': '心跳',
  '61c275a8e526d2a3fa0bc52fda4e1894': '果冻',
  '0308bd6d07412dc4628b0fe2cbf11eb8': '跳动',
  'eb8186075ac943b645b5646c9ac893bb': '摇摆',
  'e660a1fb4de45e3bf6e6525ccd041821': '震动',
  '9f6012f3eaff11a8721072cd5bfd7dbc': '晃动',
  'f1b8e0c6053ea917fbc702380bff9b73': '抖动',
  'c1754c2d14d1eb4f0d18eb4f3e6df78c': '水平抖动',
  'df83af49facc0e41864bec77c3aeb052': '垂直抖动',
  '78a83c170c9ebceeea30dda253b8a9f9': '橡皮筋',
  '8c90bea6b1f0f200e47737df611df0e7': '淡出',
  'c31bb885b514fc3670af717575f45e07': '向下淡出',
  '494226348e54f0757e9f5acae3e0f69d': '向下淡出(加强效果)',
  '115a2f5e84c129fa14a46477991ef707': '向左淡出',
  '7b8b99d894fca4adaa4cb7db7de88b44': '向左淡出(加强效果)',
  'fba93f5b3a172d74f947fdb03b614b76': '向右淡出',
  'ac5fb0d7a285ff37046d4a49389221ab': '向右淡出(加强效果)',
  '3a9ccb41592a1d9af3c3a0a31b0bb940': '向上淡出',
  '5615ffa3af206322fbe4c7ccb91a3fac': '向上淡出(加强效果)',
  '548c9e55448ef954b3a39b1731a8196c': '向下回弹退出',
  'ddd0c226af7b15b72605c5737b937f2c': '向左回弹退出',
  '127d7a2c25a46ed6c1502d652ff705cb': '向右回弹退出',
  '9e67d753c7ee78ef752bdc2759d71799': '向上回弹退出',
  'a8a73aff502d78c90a5c20eaf257e411': '旋转退出',
  'd6b661719a464b61cca75e161ea44d23': '左上角旋转退出',
  '167cd96d6da5f49e82f3caedf4da507c': '右上角旋转退出',
  '9a38bfebab98787ffbf54e7b9e16f16f': '左下角旋转退出',
  'df3a9e899eb6cf4531a2c01742d53964': '右下角旋转退出',
  '370f59195bc9427f4cb05ebf4f1f056d': '向上滑入',
  '8c6f065535f9a34e7bac0fc86f7dbf57': '向下滑入',
  'aed469d08234bc2522696064f53e220f': '向左滑入',
  '6d891408fa37c301c88732d991dbba23': '向右滑入',
  '25ce3b051c233c20e60e09e68a27aae8': '弹跳退出',
  'c7d4cc3f68e40032e82a9060a624075d': '向下弹跳退出',
  '3a5543863562663a0b74a8f518b4a7f8': '向左弹跳退出',
  '4864869dd574cb2757271c984a69e221': '向右弹跳退出',
  '81a1635facb5e25802e63c0ec38d736f': '向上弹跳退出',
  '74e98545bdd4f179a7b618438e972d73': '缩放退出',
  '30217532652eed9cd1c0683c8930d5c9': '向上缩放退出',
  'cf9da34220fc2d63f801ddbb32553759': '向左缩放退出',
  '792782766ea1402a33787b57df79da4f': '向右缩放退出',
  'a76d8ecbbeeb08ca8fc9ea37098f9ca4': '向下缩放退出',
  '4183baf0de66292097ac18c4b12e08f4': '向左光速退出',
  '7d90a511ea18060bace3357ec3c68493': '向右光速退出',
  'cd35750918e16b38f8d2d6c817363bd7': '滚动退出',
  '9d10187a72e379ac7c6e66a9bdf7a320': '上间距',
  '37ae4f181d4f139be90fb80cae3fc493': '图标样式',
  'a68e1efc4cfb1c098588ba307a83e5b7': '图标边距',
  '9f553e501dd2d1cfffa42ae21773da00': '持续',
  '0c1fec657f7865ded377b43250a015fc': '秒',
  'db732ecb48b1a9cfb3ebea9fcfbf0980': '延迟',
  '82683e3be20c6b7163fef820c660b809': '进入动画',
  '4c1b6f4b9ab9a5e2d9ae472d02d1f002': '强调动画',
  '33e3c0d279016d61870b21123bef105b': '重复',
  '9c7d7a67aaa0026807c22a5a5c59c456': '无限',
  'b77a11aead7c0b84307ee2bd33a60c51': '退出动画',
  'dd1c01bd4b36b5f4793a41bda20b334d': '身份证号码(18/15位)',
  '5c20c316de54221879cbef3c01bdb1fc': '身份证号码(18位)',
  'cd393d01a120aae11de4e3015f1e4334': '确认新增',
  '76d898a22c0a44a13482a9d9a32b088e': '新增提交时触发',
  '69f9962f200bf6bbcf3be61e1ea5adf2': '编辑提交时触发',
  '631cd22018a47a2171c01d1c1c703261': '确认删除',
  'a89e51f8d0e84d2c96d2950c07693c1a': '删除提交时触发',
  'a4a667ccd3f4c87ce1a02f6f0278ba94': '确认新增(不推荐)',
  'c1decf80d615094aeabccb94965c7d6b': '确认编辑(不推荐)',
  '0ea333785c950e92761898a19ba166dd': '确认删除(不推荐)',
  'b4e23d11bddd3135f3ecc614e5e183ca': '选中项集合',
  'c5393293444e7b6e2535fd40f88ee2a6': '节点点击',
  '9d5df6103865f50a2974ff7dd56acae6': '点击节点触发',
  '7a9fab192455e65d6265b1978a32c5f2': 'deferApi 懒加载远程请求成功后返回的结果',
  'c1cecab6116bfb14ce9f9d84d6c4150e': '懒加载完成(不推荐)',
  '67d5946c409872ab453390a49a405838': '目录管理页面完成之后，增加返回',
  '6e3541884d20e95ad039b9b3c894cac0': '用户信息',
  '30acd20d6e6115b769020b8bc395e4fb': '用户ID',
  '819767ada1805b0f0117f10967b94d4d': '用户名',
  '23eb0e6024b9c5e694c18344887c4fe7': '昵称',
  '8098e2b4e82c9fff83ec2d91bda6f1a6': '手机号',
  'f3c144991c6d4631fd31067199658918': '应用信息',
  '3751f65ff0252d27275b472a5c7888e6': '应用ID',
  '27c3862a86325ca60b6306825379e6fa': '应用名称',
  '2eb6f32efa500ca53578b48a99f5a22e': '应用Logo',
  '0359cf618fd6b710375353cfb2b60f32': '当前运行环境',
  'acfc983e79f7bc74837d353878b52a0a': '组织信息',
  '1eb4bf1b121d558c4b13a8021e8eb8ce': '组织ID',
  '4c12d831e36386981052eebce255278a': '组织名称',
  'e42b01933b2d2f9960941764d4906487': '组织Logo',
  '9d24d229baae271f3cdf1b3ace2c9681': '组织标识',
  '10c645d2523e160068638a81a8a2806f': '浏览器变量',
  '8863d6bd178bcd28503b5716a7974174': '页面入参',
  'ff6855279eeccc6d6f1ff32f40a8e4d4': '产品名称',
  'c9452aeeb55874f01c0bc221f06e42e3': '账户余额',
  '82330ce88fe46e7fd37a4ea971f0a8bf': '产品数量',
  'ebe7816542f5838cb9bedf9d7c3bcae6': '是否线上环境',
  'f836f49bd38a4ec3c37831d34c4a906a': '产品列表',
  'eab1293c98ed5068dfa21a2507603f2b': '个人信息',
  'a01a9963238df560a9b974f45c7e253a': '年龄',
  '63385eb63e8a246739dc47987f9094fe': '街道名称',
  '4d8e094ac4913ab43009b365cae194bb': '邮编',
  'd688a3a4d07d16ac647e799e472bff2e': '简体中文',
  '68f3880860c3de1a7b0fd237b9a500d8': '全局事件A',
  'd8ac01bb2d794d5f422d22faae2dff79': '全局事件动作A',
  '6b128d2d7e40de95d6253fc8284fdd2a': '全局事件B',
  'b2014cfc0ff281d075946e3020894dfb': '自定义渲染器',
  '606c8c7399d5c3b86c88a25b6748bd1f': '这只是个示例',
  '6fce1c9aa3654ae2f0eedcc068a7e189': '自定义组件',
  'f8a2de5e15f7524480a904de65b8ef7a': 'PC模式',
  'bb1be951fecf7bfb45ba1891c941eec9': '移动模式',
  'df5b97b65e12438c5f86826767c13974': '点击测试国际化按钮',
  'c0c4a2e6c8e71769c21fb44849872fe5': '切换语料内容',
  '53cb1a5b565d2e94c61a26e089571472': '左右均分',
  '0eca59595d401d76fe464fdb310f4617':
    '常见布局：左右均分布局（基于 CSS Flex 实现的布局容器）。',
  '0b6d1a0fc4bcca3a316e86c8041920ba': '三栏均分',
  '5bf0bab26f838aa4bccfb3da33066f73':
    '常见布局：三栏均分布局（基于 CSS Flex 实现的布局容器）。',
  '4e66d1a5829f4e16723adbf93d61852e': '上中下',
  '4a32e12cf434a580d16e0d82284ac9a0':
    '常见布局：上中下布局（基于 CSS Flex 实现的布局容器）。',
  '17bc41719cc7a4aa88787402d3fef3d8':
    '常见布局：上下布局（基于 CSS Flex 实现的布局容器）。',
  '2de76c22e8c6952000cd9c0104e9c4ac': '1:2 布局',
  '5962c11edae5093617b77277e38ace1d':
    '常见布局：1:2 布局（基于 CSS Flex 实现的布局容器）。',
  '1712b9adb39c5f03cd9fc9e18ee31316': '1:2:3 三栏',
  'cc864704f2535633f2c76462b9026631':
    '常见布局：1:2:3 三栏布局（基于 CSS Flex 实现的布局容器）。',
  'de3d5434b3ec146e56fc618e22e7ac49': '一拖二',
  'f4632577e5c07f9c54ff3aa741395e1a':
    '常见布局：一拖二布局（基于 CSS Flex 实现的布局容器）。',
  'd929350236d58607bd0f3aea90302ab9': '左一右二',
  'cbd4886dd4935c67984677441afdf09a':
    '常见布局：左一右二布局（基于 CSS Flex 实现的布局容器）。',
  'dad2ae63d3f4f415f17193b00f804aef': '1:3 布局',
  '38cde83c212e38fd807848be1bfa1eda':
    '常见布局：1:3 布局（基于 CSS Flex 实现的布局容器）。',
  'ec9f8096ab5b23ed9a93389b954c9951': '二拖一',
  '76fa2d38561a834fb2eb464dc02d8570':
    '常见布局：二拖一布局（基于 CSS Flex 实现的布局容器）。',
  '95b2440c1264b8a52707714c0bb3a4f2': '左二右一',
  '9e7ec97f41b35e0d9ae38b1762c9f4cd':
    '常见布局：左二右一布局（基于 CSS Flex 实现的布局容器）。',
  '056f41e5ecf54f5fe8fb18707b441cc5': '多语言',
  '766959d419bcbf6463dc55081c6ac640': '压缩质量',
  '2f67a22a0696dfd6bdc6520e06a6cb4b':
    '裁剪后会重新生成，体积可能会变大，需要设置压缩质量降低体积，数值越小压缩率越高',
  '5f0eec58f36853e40a718b9f250881ab': '可视化',
  'db06c78d1e24cf708a14ce81c9b617ec': '测试',
  '99929330245c293d6ee7ba29838bdbf3': '客户信息',
  'c38aea521be9eda79369b59e962b0270': '下一个页面',
  '240f6f9f1dc4812dd332d73b68ac21c8': '页面流',
  '6bbd0a3bc0d4e680561f35ac85aa23a3': '前往页面流中下一个节点',
  '23d0eed8b1d828aea8040adbd622d949': '下一个',
  'e84aba2a7ced97a4eca8531fe64bd306': '事件名称',
  '0ecb267c0be471118549f19a84e0fc17': '事件编码',
  'c52ea850ea91ef3999b78329101aae98': '页面流参数',
  '5d2a0557aaa340ca2936093018b0c630': '符合Pad Pos项目UI规范的基础按钮',
  'eb7a017dfec58837ba26c90a0b7dfc55': '统一底部',
  '56988fadece52d99e83693392edf2460': '二次业务page模板',
  'b13830d1f27d66093042e59bd46e224e': '客户搜索',
  '931339437c6b324c7940f00483821d68': '主页菜单列表',
  'bf388d9e0f50fb9bed029908f963d532': '菜单项组',
  'e416671d35a4612e17002a90be28c3fb': '主页广告',
  '32ce6fc48a9211c41caa3112905f4573': '主页搜索',
  '4ce8061939d1831915d8e8f7ac8b2cd5': '客户列表数据源',
  '51bf01601bc7790434555be3657aba12': '业务签名',
  '5ee791c074edd8d49d78c16c20f36862': '标题3',
  'c4fd4f9595ba755eb8adb0db15112d76': '符合Pad Pos项目UI规范的Icon',
  'cabafa50d58210f437a63c46005f0bae': 'Customer Case 背景图',
  '87dfcbdd3ee44fb3950fa37f5caaf8e3': 'order entry 客户搜索',
  '32de092b611bcaeece4a39bbfb1b6643': 'pos头部',
  'a718237d51da29ab951632bdc07c033d': '热门套餐列表',
  '02323e73b4f9b872d54c595b8a96e84e': '套餐过滤',
  '46f3e97cb774978266c3b75c0432bdbb': '套餐类型',
  '0790d1cc29bef1edf327ec8aea7108c1': 'iconfont 图标',
  'e489325f46666d8864c8d3b1dc53ccc9': '图标类型',
  '02e0fbc65369f1d716a665dce24d24e7': 'Antd Input 组件',
  'd34952ff329297d2d4b07eb5dfd4f85b': 'pos左侧菜单',
  '83e774125172bb9e5fac5b54de948c0f': '登录组件',
  'c89cbf5fed462ec0f9966a793919ca34': '登录组件表单',
  '7ae7269a52d44c96d2cd0f6f76cd09df': 'order entry 搜索界面',
  'd8ad116c24483fe748a3bcb011ed6b0d': '菜单设置',
  'af0cea2d439f88ef17f2371d296420a0': '用户标识',
  '588fc8225ce6b7940cebdbf810206015': '用户名称路径',
  '6b06edfe33e766f900abd2346b79d909': '用户证件路径',
  'afeb8e0f887043d444e4f89a66361014': '表单提交成功',
  '89c34d6bb87ee79c4044a7c00e516c7e': '用户登录信息',
  'c29e4097806f93f9c0722e7d3da7002a': '合成',
  '83b0d23429991afa9781d554eb77c633': '客户名称',
  '506139cc209fdd8f47e20fbf6d21c9d9': '客户编号',
  '2eb47fa7e3ee097a673065e2b8ca75fb': '点击增加',
  '2e9519bfe71b77c43c7e11d726e2ddde': '点击增加Icon',
  '0ebd349602d17729afe915730996284c': '订单入口',
  '2487f137e37b09d705d46ae12bc43c88': '更新全局数据',
  'd409158feafd4634d31af1c723b1b4cf': '客户标识',
  '06af6f6db617710af7a5a39702f7522c': '客户默认语言',
  '603c6eed9b5d2ba5c1985294b6145752': '左侧菜单',
  '8d8784b51a62b2af1628cdc3a1c36869': 'Shop 标题信息',
  'c8042d8dc70c31c87181948de6225f8c': 'Shop 套餐展示',
  '34169690466d261c88ae2edf2d2ff427': 'Shop 套餐类型',
  'ef496d670b725a10e96bf4f3aede3f5f': 'Shop 搜索条件',
  '5bf004397ae096a80aca649b2c94b35d': '展示排序条件',
  'b8d1bb96cba395c09f79ce848e8660c8': '展示筛选按钮',
  'aa492b01a15daecad547fa72d8afefd8': '全局Mock数据',
  '2fdfffe4f998fcb5564f5b2dd2adcd06': '账户列表',
  'ae579b1087d0bff38f7947232ee06781': '账户列表数据源',
  '7e12f4823d6a975a19baae7eaa2e31b2': '可选套餐信息',
  'c673e84da92a66bd7a8c29949882e052': 'SIM 选择界面',
  '5034b67f7df65d2413a6535b246c9d98': '登录成功后的数据源',
  '43a4d021eb7c5b1ab066682a48673575': '登录成功后调用的接口',
  'c5473851ac3eb19a28a366062c3a6b43': '登录成功后的local数据源',
  '92c4f987349e7fdc8f042f3cb6c0423c': '登录成功后的job数据源',
  '1d28ccd1b65f60c0df14a42dfcb8957d': '套餐确认界面',
  '135381a31b44b2597136f1d49fbfdc97': '订购成功页面',
  'abc93ee157ead39e06fe0d81704bd7e2': '支付界面页签',
  '99f57a75ba95f5d06a4686ce55ca69ca': 'Order 按钮点击',
  '0755c5a08541b4e47f4776dc0cee5cb5': '点击card的order按钮触发函数回调',
  'b6804f5d59b6478ac5fb39edddbe81e6': 'offer 信息',
  'e75e90135e558d219bd31bfc9fb47db3': '查询offer列表数据源',
  '956aafa01c7920efa0ed699fab248846': '添加收藏数据源',
  '1e47e5a6c4982fc0907a9328bca90142': '删除收藏数据源',
  'a62fbc492ada923716aa57c62ef18c70': '页面步骤条',
  '0127f318eb22077b75b54698089ea3b7': '订户列表',
  'd748497cb56bcc9f6d37e7091451d75f': '背景图片',
  'c7bb11ade8e0da823ec76ad9f406c9c2': '客户通用列表成员',
  '3362142c607404de7d4f984e811121d4': '前缀是否是Icon',
  '1428a7d67039ad05ebe1089719ca05a6': '是否展示子块',
  '314dcc7a0d942e96254fe4d6f0985be0': '客户通用 tag',
  'bb34b4a5d24b3f5415ca40c0eb2571d0': '客户通用panel',
  '8c7e479dd35d2813dfbb1c4078fe36f0': '是否切换为tag模式',
  '47591f9cce55f65ae69021f752aa3971': '客户card信息',
  '9e5fea52973647473280af3c97d78e3d': '数量绑定key',
  'aa65df685f5650234a87e9936ccd310e': 'Icon 图标',
  'c767c4b2ba3322c3cbbef99f829dd7d4': '绑定key',
  'fd8ca24641927752e2ebce8fb810c4a6': '客户card信息列表',
  'fa4d6cb0f733eb44621902e10151e4f8': '客户文本信息',
  '648e4585ea26a7632eeb4f0fc395a13c': '客户文本信息区域',
  '5dc794d2ddba9a5b938e749b286bf9a9': '创建Case单',
  'b6e28374cf6f2c98297e784f36498012': '定义接口',
  '46cb212c547e923cc922d88fa8c7d186': '页面输入参数',
  '98ba97f09a6201fbb4be4484481bddca': '页面输出参数',
  '283b02659ba1fcdc95760bb05f951c4a': '客户类型',
  'f022ff766e1585a8eea2d96fd6df3587': '客户母语',
  'ae55769bbc87764404ea77be3ef6b7a3': '证件信息',
  'f2474f9b83b97b7491589c22c70d7144': '证件号码',
  'fc0fd628a2e618a39b07573da9681f14': '预付费',
  'cf0377d9954dc612eafb36cafbfa4df4': '后付费',
  'ae17525c1d5dfe018fba921b4bcbbd53': '是否展示前缀',
  '5ac6749398d6250ba0c87436c6edf890': '是否展示后缀',
  '7bfc7a4eac22144cfff03e2570a56a5a': '客户通用 tag 列表',
  '9634adba95636da563ccfddd4f586e8c': '客户选项卡',
  'c3c12b4639a46579f7ed4e94d38135c4': '客户选项卡面板',
  'ad35accdc61ed40bc04bd702e623fb0d': '客户组',
  'e6f6a08827e402b58cfdfbb94c46d72e': '客户特殊名单',
  '16f473d67315f4787a3bbdd9b1a1069b': '订户tag资料卡信息',
  '4874ad17bf1a42df8549214f6359bb8a': '客户Tabs',
  'a9d809aa105d881e1b89ea4f08bacbd6': '当前 tab 的 key',
  '49bf25707aee855f7b13cc6b838e3643': '客户时间轴',
  'c5eab2fb506e1d16366710557aaa7bc2': '客户时间轴 Dun',
  '1974aaf2bfc14fe1660b45ec790e53d0': '客户时间轴成员',
  'c7b755e0ae89e0c8cbc61be5af7c4618': '是否水平展示',
  'b197fb5664a036ab97c204973126c0a6': '样式是否分离',
  '380f87759d91afb9202705432e5b7e43': '商品预定',
  '3c108fa74d3a872cadf5de323c4a4c80': '套餐推荐',
  '4835c676f9e86a54346544928b512ff0': '处理中的订单',
  '99c64459072a4b6d7da59ca8fdbc81fb': '订单成员',
  '843d0322295eff8f31f01bd20b6715c6': '订单成员按钮',
  '4c7d77362f61a84c374f0ba34461eb76': '文本内容复制',
  '06f7b155ea570fd2001d91925ab66162': '加入购物车数据源',
  '6234d8dfefbd1c5208acec9b893c26eb': '页面内容有改变, 请重新保存',
  '5f3a1f11daedbbda850c0876bbf9f580': '跳转下一个界面',
  'bc6ed944c3a25f1ac48ff611ec39c666': '跳转上一个界面',
  '6cb1692a13a117802b7cb53c72b04ad7': '新增版本',
  '83611abd5f806cb2db7cb992e91186dd': '发布',
  '4805dd77c6f7af3ae511025a666646b5': '下线',
  'a05e4eecb8dcf594dc92f894b66966fc': '版本名称',
  '84fd8ef5ab92b704c6af98ea76f7f16b': '复制版本',
  'f4abce256a9e6615f7a2cd1e0c1db625': '版本描述',
  '15b3c6aaf23d140ab8c294b46ab9a812': '选中账户',
  '3ad511e40f864f84a5e810d6970a3dad': '账户信息',
  'e3840ea344d19e0735d3fbfdf63cc479': '成功新增账户',
  'a99b083f04e17ba589d7feaa69742a30': '号码信息改变',
  'cb4ba4e06e297ccb79065f73b5ce1737': '号码信息',
  '86547f4141237fb14fded53fd26f12f8': '是否展示分割线',
  '072b4e2bda1bcd39190718a6cfaaf2e6': '订户成员',
  '6ad26b925b49a57e26f8ee42dbb455d7': '点击事件',
  '2253650dbefb51d9e60c136da54b4dfd': '选中容器时触发',
  'c5ebb7875503d433f459ca2c083cfe23': '订户点击信息',
  '065362c2990f223cca9e36d53f937ee7': '客户订户列表',
  'd34e4114809396952aaea92455ea16d1': '实物预留',
  '13aacb98609ada554a5569bb13ef0e9f': '套餐详情信息',
  '66757e7ed00cc9a8c6d4594b54aa9f0a': '占位组件',
  '3b9e4205f65ef167779de4d2f0d572dc':
    '开启后，这个组件的body会被替换成设置布局的组件',
  '4a0df9c4798e6f5d99f1a00911214396': 'Fish组件',
  '14c54c6b41cb5f54959e2d5047f448df': '开启布局',
  'e605deb06ebe66d616243c62eaa4e4d1': '更新步骤数据',
  '2c2b212031ad6b5e331b9164030eb592': '可选包',
  '601c150040e245ed7a328648e09f0295': '可选包new',
  '97c29eb2cae077f80df111bd7a0d36f8': '可选包列表New',
  '9e5ffa068ed435ced73dc9bf5dd8e09c': '基本信息',
  'bac8eef8df435320c0fda9b3df446b15': '订户信息',
  '0536318253320bd95497256fd0af59d7': '查询套餐详情',
  '2feffaae305089ccb80287692271ae18': '查询套餐详情数据源',
  '033b7d67e28dd1c594d534cc3b756efd': '查询订户详情',
  'dbe638d827cf83b936597c642a52ccec': '查询可选包属性详情',
  '48227160dff2811c5bc1c74414f9ef0e': '查询可选包属性详情数据源',
  'b9ed6640c73619fb7b305bb2cad30588': '查询租赁单元类型名称',
  'f728eb30d7abd76ba25960a8bdbbd085': '查询租赁单元类型名称数据源',
  '58ade282be83b008e35660b215164885': '所需数据',
  'a2fd136a035abe5f6d360a430d09a12e': '可选包预览',
  '3c721873e4d88aeec1dbe86738fba804': 'Addon信息',
  '130dea7f0ee340c846405dcae2dd9699': 'order entry 搜索界面',
  'ec3fb565b83597729ab29694808195db': '选择客户',
  'c62d5c820e44e79a6bc95b82cc3629ee': '选中客户时触发',
  'a5739f04a794c6c782d0dd03a0a5ee2f': '账户标识',
  'ded73665f2d336be598335b0d9116734': '后付费标识',
  '1047d22a649472e74a15c44b7295e954': '查询账户余额的数据源',
  'bbc4b07317bf60af11ee165322c5f902': '查询欠费的数据源',
  '909b67fa65d1a64424a56a08c05ebddf': '余额信息成员',
  'fa5edb835228eedba72c3138e8546713': '余额信息成员列表',
  '959d18e353664212b8a5750463e8d9f7':
    '的技术和市场佼佼者之一。 在中国,西克中国(广东西克智能科技有限公司)负责工厂自动化',
  'fa21edc00bd961863f41dfeb2203b1ed': '文本前缀',
  '5a90f57702f5ec67d1c1727a3ae69406': '已选择订户',
  '40e1f94ac881583fc30477ca6b3f3c5e': '查询订户数量的数据源',
  '634a2d37f70e47fed88dff65d524a954': '查询订户列表的数据源',
  '8a08f7bb793fe22294328eef985138ab': '查询订单总量的数据源',
  '3804a4729c9ba7a4941300e489211e65': '查询订单列表的数据源',
  'f04f189563a29bb4f72b40d955de1cb1': '客户订单信息',
  '6488c71bc2507681c5c28aaeeaa716c4': '每行展示个数',
  '130255f2df524a0241090d56e83fcbe1': '客户订单设置',
  '70eebf090da2a1f1f3da9169a4c090db': '客户订单内容',
  'af3931e10fc9f17b64f21d32a86b3e0d': '成员名称',
  '7f126577ce54346a8e09fc8852aceafb': '成员标识',
  '772d69760de57537303295d4565e7357': '元素横向间距',
  '4d81ff8464680a9f7692304236124d27': '元素纵向间距',
  '2325a5fab66d8dba69e94c585e789e98': '客户订单信息成员',
  '4eb88f22d7c81d3b887320eb3ceb2a1a': '页脚',
  'd42348d8b7655733f67da4a29fe0423e': '费用项展示数据',
  'e8f0ea944ee4d735187a2638c696018a': '可选包列表',
  '7cba654ebedb420aa641dae4942ffa1d': '查询可选包属性',
  '3d879d0dcefeeef8ccb7015a4ad61e6a': '查询可选包属性数据源',
  '9eabbc78e77824a814ce1dd3167f6658': 'Offer 信息',
  'b685d3dcded195102fc96ed161b92449': '金额精度信息',
  'fb5a7ee3f384adc26a0ed5925dd94cbe': 'Gsm 布局',
  '1f6b3deb84439dea442a56cf88c1529e': '点击图标',
  '69be3672ecd4b979bcf6d6ba989c4324': '图标信息',
  'e657132e682189e1a386a865c6dba435': '激活菜单项',
  '91e661879a78463c3c34f86fafb7b0c6': '左侧菜单项',
  'ab60642e4f15e700f67cee39c5dd5a3d': '点击菜单项',
  '3d391b2bba14fba14998cbd6097b6356': '菜单信息',
  '8d1330f9d1cfc83f8043a7853e8bce70': '菜单项标识',
  'd7aa7fe6b27e06f4f468d276ff7f27a8': '订单确认界面',
  'b492aa617017a788890e8b4984a92edf': '可选包信息',
  '587a4ab6a23f67b4426ee7aece934e30': '费用信息',
  '60fd6a52523568a487735eccaf867d21': '侧边菜单',
  '1c38344f373a726ec01fc717aa3d05e6': '签名回调',
  '99d692b945bb6af9456ffd01bef87e9c': '签名变化时回调',
  'ab09ad555696a0b7f85216eeefdc1786': '签名信息',
  'eb4b7b6d399dd49834827645274752d2': '订户信息卡片',
  '0f0c77117328fa615c4264fb0549eddd': 'icon 图标修改',
  '76f88923edda515a12812cf69dcc819e': '二次业务点击',
  '152c6c39efe12e4a1ea821c001ddfb60': '订户详情',
  '59c5febebe8277495aab2e8ed5cc5adb': '订户事件Id',
  '0d992caf57f6926884ad1168d30736cb': '订户详情数据源',
  '0e69e26e445149bc9a6374319a2cc0e4': '查询订户详情数据源',
  '46da65b134cf4c0e725a53153085dcd4': '数据定位',
  '08364b78ff3bce209d3ba67514f8d0c2': '保留页面容器',
  '7afc91d8a47319919941c75b1708905c':
    '开启后，页面装入布局组件，否则页面body装入布局组件',
  '4f996391a1a21d540410794d2ad1703c': '保留页面数据',
  '9709167f654e8e8976b05bde55a97743':
    '页面数据会和全局数据合并，页面数据具有更高的优先级',
  'e0eff20edc25f711171c57b7372d3312': '全局加载动画',
  'b1f76db9c53927d8136752ee043fc9b1': '在接口发送请求时打开全局加载动画',
  '27e9ea84565c0b8e6fc7586863f404f7': 'Url跳转',
  '46f3912df125c9d88c9b6d6faef6842e': '页面类型',
  'e455bbdfd3abfc6f97ed7b6b8d65e9cc': 'url类型',
  '29c8bb662316911a05f49223857b4603': 'url地址',
  'c283c644d3be852a4a8ac8748fcfb5a4': '登录信息',
  'ba66b9ffec6f72b1d0fbc331ecfa6fbb': 'job信息',
  '98efb6af528fa2788eeff78ce3bf4ebf': 'local信息',
  '10c6755b6f804a1bd7bde0137094c2f1': '精度',
  'f53e291fc5dd473eabc4cbb41a8e126c': '用户编码',
  'ccc70a664dc3e9dc7672e50e2ab0201e': '是否展示额外区域',
  '67e1f34294865a8e4caf5dd5691fd2f0': '下一个页面',
  'c2e45f82d34fa53148cf222fe14fbbb6': '路由跳转',
  '84fd3fd1865f1056b8b890f7c876899b': '更新步骤数据',
  'c795e2cb45259f9ca4c7190e208ecf24': '更新全局数据',
  '599025cac31d971550f89c26596757ba': '下一个步骤',
  '5325b0e36967ad4b012342a90b17114a': '跳转下一个步骤',
  '1b34930e05cce48990ec473906845469': '上一个步骤',
  'b0b161b6d8aff2b8661fb48e9e7e555d': '跳转上一个步骤',
  'ae9cf16870cf3b14387e9effafd6b024': '内容区域23',
  '89cde8fd3e955888342389bf3e6e4c8a': '流程启动回调',
  'b3a8a1451ee00f26e8081dd08ec4ba42': 'Fish Checkbox组件',
  '********************************': 'check 选择',
  '7e078c52d1f1c4c813133475dddd5949': 'check 选择时触发',
  '867d37ad38682fd7c8f46d6d7502376e': 'check 状态',
  'b4f6bff3f0150515944dddf26557f1e5': 'check 标识',
  '8483a8ef48c0b0d391a6edb524d249c6': 'BO 数据标识',
  '9967969d9b50948ea437cca0aa1486d7': 'Fish SIM卡类型',
  '207816e6c17b908039efa554fa95954d': 'SIM 类型选择',
  'ad5e93e83758f121a1651aed33877447': 'SIM 类型',
  'dfb1500bb9448ae06113d2d00f9aadab': '数据绑定',
  '4c3d18a04943b3bc25114e8e548f6067': '选项设置（优先生效）',
  '11c4cf314d9af56ccf6a89d45db821bb': '指定选项 label 值所取字段名称',
  '12938481657dcb7dd6d33cea8b0b023e': '指定选项 key 值所取字段名称',
  '2c530775e3aaa98cde7777985e774440': '自定义选项设置',
  'd0c8ef9fc518ac94bcca2138713d67dc': 'SIM 卡类型',
  '72af9529003667b3cc7446a956ec157f': '查询回调',
  '844393f752c5f980a1684408beb1e490': '查询结果',
  '07166e9a9e36893d5736abb4df3e5982': '订单列表',
  'a4049c474a598de77a1f98b8669baf22': '号码选择',
  '34c0dc1ec33528d2368aea70622a8055': '被选中的号码',
  'eeabf0db5bfe9d3d3bbefac3ea7f8e16': '动作完成前禁用',
  'e0be7cfdce198406596f331d739ee705': '重新加载或查询重置时触发',
  'aff69cc0f8cf108586caf28831bd5abb': '已选择行索引',
  '2bc80b42059f1e21092e9fff01276251': '开启选择',
  'a69516665bbd5dd4bfdc4b61a3bd0210': '开启后即便没有批量操作按钮也显示可点选',
  '7a99893930bd8e69b536344e8127af6d': '开启多选',
  'bdb5ae35d79c8b6d972d16f60e9c96af': '控制是单选还是多选',
  '047d7d986d09f9a1c6f33063644a1c2b': '清除校验状态',
  '82f734a76a21cd2ea9484634a6155d9d': '清除表单校验产生的错误状态',
  '29cb280f3bc6d139ef99b5e83ec21795': '文件上传路径拼接',
  'cf349e79f5765bd7956321f283e2bf73': '查看密码',
  '9d262cc24e9f899d853d1c900a754a70': '点击查看密码图标时',
  'dd909adbe395bdf8439d6afcde19d8f3': '隐藏密码',
  '388ae7e2b90cab255311e1f5300b257b': '点击隐藏密码图标时',
  '234dead0f8b58b6ff1772baf16025df8': '密码类型时触发查看真实密码',
  '4894c9cfc59cfaaa117a352c3199c32d': '密码类型时触发隐藏真实密码',
  '127b557c6292a72ac2b877246c338ac6': '文本框基本样式',
  '1afed738ac92796f8e1eae9fc449fea4': '输入框外层样式',
  '1b7e164ef4ba164086666f31b6a72093': '弹框尺寸',
  'd61c5ae1b2bf84c4043534a1e5428617': '选中项可删除',
  '36b4942ce8a1770d6835c58d26cfffef': '列表选取基本样式',
  '96abd5a4a11da305223654eeb26bafad': '配置编辑模板',
  'fcfffbf5d7dd5c491645e2e0947eddb8': '全局变量',
  'f2196f7e3a68af97302d2e0df46df38f': '是否显示序号',
  '61f1ea1bba6d7fa642821d52df2a4077': '节点标题',
  '9cd700df625adcd5450a4dde0de1af38': '客户端持久化',
  '9fea607976a818e5ccbe7c93455bc023':
    '是否在客户端持久化存储，刷新页面后依然有效',
  '0cea6b668196bab5c0fb9297c75dec28': '数据作用域',
  '9f589edf9c1772c1d5f79c69855f8473': '页面共享',
  '32a7213c48ebd1a2a82bdd737caacd2d': '全局共享',
  'a5ead186e12a531148ff637a55fc404b': '基础变量',
  '2b8bdc1349eb750d33f67e2293b02ba4': '系统内置的全局变量',
  '11eb1a8e9254458f195c6833773bd2c8': '未找到对应的变量类型配置面板',
  'd78f93a5ad4a40e8a72e15f454199288': '确认删除该全局变量？',
  '9c3d1f05074276628a9949cf1028035b': '变量名已存在',
  '52cbb59656bb7e6d38011eaad8ef14d1': '变量类型',
  'f61f4cf6d0d544fad4507868b74696a1': '暂无',
  'e2e4c374e59fa5acc981c0a0ed6ce96f': '编辑全局变量',
  '6c95367d14682115f83b647c482b4a2f': '新增全局变量',
  '28eb5b4a25ecf8c0c2da703de3b9fa02': '放大效果',
  '121e212b7e9b6972aa98d7f982013c55': '缩小效果',
  '1563ef9dc3edbe3f2e887b1b846bf4b4': '阴影增强',
  '42969502d9d5a359774481cdc32dcea8': '发光边框',
  '6b978922ce4748240bc00ae30e3a0d70': '内容上升',
  'dd9476eba02d98354bbafbda8f604c01': '内容下降',
  '680f410e0e95bfe2406816243c774aae': '内容翻转',
  'c54405c1a79eea023f3032068edc1621': '可见时触发',
  '980c1b4180bf5096a35ea7505fe59ff6': '组件进入可见区域才触发进入动画',
  'c18063462332b341f4cc674761577d70': '组件再次进入可见区域时重复播放动画',
  '933ec2e002276dda7d8940d426de8df7': '悬浮动画',
  '4f91513e8383967d5e4797bd81445fd7': '不可见时触发',
  'cdbe611aea802f0fc55099161af20256': '组件退出可见区域触发进入动画',
  'b1271049f8a2d3235bf7873093c24215': '193环境登录异常',
  '7116e7ec169a0780408f1ccea427b9ac': '账户',
  'f0adb293772218222754d86f8bcd54f7': 'CRM 订单列表',
  'd19ba67ea375011c1305dbbef53bc6db': '客户基础信息',
  '469400d03177246bd62d77a9568850aa': '显示索引条',
  '917c4d67e1d3a4df20594f2e0ccd7ad2': '顶部偏移量',
  '48da289c738e3964d66553b1875b51ce': '点击索引条跳转时，与顶部保持的距离',
  '4c4bcbc24bf6782ee6db46fc33b8e19c': '索引字段',
  '9ff6f96b5ec659bdbb5fd18041df1ce8': '请输入索引字段名',
  '86efd2ff39d92845c9b1d684e38b0b67':
    '根据指定字段的首字母进行索引，默认使用 title 字段',
  'eccb240e994c6ff148eda9df71b260a0':
    '支持 fontawesome v4 图标、iconfont 图标。如需使用 fontawesome v5/v6 版本,需设置 vendor 为空字符串。默认为 loading-outline',
  '6c375a6511bdfb9705be7ce8df499140': '最小加载时间',
  '4f3ccece83adbc3d6bdef56a5ebedaa3': '指定加载时显示loading的最小时间(毫秒)',
  '3328813d737f9f5c9d24b39ca6e4a3c1': '数据追加方式',
  '51af6b853b376aa6fd5523aedead90b1': '指定新加载的数据追加到已有数据的位置',
  'dfa0274d6b1b7e255644db357ec85a47': '追加到底部',
  'd5af2ba9df741bfb42a483be55968df2': '追加到顶部',
  '63f3359c10f5638708442a6c45dcc7ea': '文本配置',
  'd3d862e14c7983a91544159052d943bb': '加载前',
  'fe8fc61a2186e6373adc87f98fd5623c': '点击加载更多',
  '26b5bd4947df8be257c59dde927b400c': '加载中...',
  '977b11fe9834e54432d0c6724df89e04': '加载完成',
  '32452712d4d2a3d29865bc764a22ecfe': '没有更多数据了',
  'dacb05cb2e948e47c19df9b1cf63ab24': '总条数字段名',
  '5b3fda18f1c058decd9b46122558eccb': '禁用加载更多',
  'd316f65c78ec9f4c18d5f72aec83d2cd': '默认为loading-outline',
  '35d0c48adc62e13848d9292f110d5c93': '文字和图标颜色',
  '4f48aaa0bf4350ff76fe1d98a8acb6cf': '默认为#777777',
  '40e2afdab75788307ef3f957ce1909b9': '新数据追加位置',
  '8c9a33c47ba8e3942e5c60c061d38f9b': '最短加载时间(ms)',
  '89581548c692d58a4050c869a000b0db': '手势方向',
  '00d5a294ef43ae80ebd85a5f3e40f68d': '向上',
  'c4557f2bf8fcaa91c5041ed1b30c1540': '向下',
  'c822d4676b97a4cbbf2c3c8d14e61b62': '默认文字',
  'a9cd27b182577c9c3a0dbb74a0c02df7': '下拉过程文字',
  '5db6ea087a57ee14cf642e89e0c26a23': '释放刷新文字',
  '5cf222112a486ae6d161ff444f6199f0': '加载中文字',
  '10bb1f20fe35208d4a3b082f25b9603c': '加载成功文字',
  '99976bdbddcbe3c1c1054b990566a1ac': '加载完成文字',
  '33b3f095fe1b51cf19b6f03be4270fdc': '拖拽组件到这里',
  'b85cad54c102b745d5f5121a5e9ed6a8':
    '[amis-editor][EasyFormPlugin] dynamicControls的值必须是一个对象',
  'ae0f39d985efa925da4875780c958555': '表单项设置',
  'fad42585b0d8d40878f053c91df60040': '操作栏设置',
  '5e8424a6d968e376854b203d76ae3a6b': '图片集模式',
  '82f003bc378f64a46ad41d3cb093a3a7': '缩略图模式',
  '326d6be0ee560f2587cdb0be7affc850': '大图模式',
  'b07edf2b5be42a9e7e82a967e5563943': '瀑布流布局',
  '44620090d2e45c0d7c7e1ed503dadd59': '开启后将以瀑布流的形式展示卡片',
  '4aa775b3d9625e71b250b26327e198c5': '列数设置方式',
  'd757d94682043ab0589ac55f68417ece': '固定列数',
  '91815d7713fda9690b8b9c725a1f4bd6': '响应式布局',
  '8c8dfb78cb78556d33e89a986b25679d': '每行列数',
  '8b1a324a876f3df5962667272c01a1db': '响应式列数',
  '5c6395fccdcdf4bac189f0535e7222a8': '超小屏幕 (xs)',
  '7baf78ae7bd99ecef5e58472978d2391': '1列',
  'fa10ffc6dce7ee8002a987c84a41daaa': '2列',
  'b767f61bd7fcc251bd97f017082a73d2': '3列',
  '75a5a7039854862c57ecd42c9cf4d6c7': '4列',
  '6b16e5cff6dc092c38638fdb8445a6ba': '小屏幕 (sm)',
  '47bfb85ddc7968a5a1ba6109f44f0d6a': '中等屏幕 (md)',
  '0e5ed5199217d3c614b58d5790b4eb6a': '大屏幕 (lg)',
  '98bc1be32af1632ddfef5ad27ebd78c2': '滑动条',
  'bff6bfeea70f890f1ff9dfc1b3a1f734':
    '主要用于移动端中支持左右滑动展示更多内容，在桌面端中更多内容展示在右侧',
  '0c49df7faeea0255f92adc7401ad5407': '主要内容区',
  '162b302043b4c92848aa6f806bdcf984': '左侧内容区',
  'e667354d2eead09934e594c200954f51': '右侧内容区',
  '84a16bf350e47bcd5ad797a04cf19be2': '内容区宽度',
  '66224f9dea39d73429ee8a51c2291830': '主要内容区宽度占比，默认60%',
  'd34ecf817b317b4d4b37c8467eea6b05': '显示左侧内容',
  '98900b460da433d5231f4931d59873d6': '显示右侧内容',
  'd96a7b078404d54eac580f831e155966': '步骤状态',
  'a7591bb6ef77e85980fb440f7557a2ef': '隐藏图标',
  'd099044ace39b79ace126ee5080db444': '副标题样式',
  'ec2d2128688b123b379b42b4e9b319ef': '隐藏副标题',
  '8bc54ad0651d37b92e79adadd3a8673d': '隐藏描述',
  '1c53ee677c52f2ddb523d58f02e87818': '列设置123',
  'fe8189070b8c6c7dd52ebd0c9e7c26b4': '开始播放',
  'd4c2e981c7f6c5b47ab36c261e6d2b0c': '视频播放时触发',
  '18d9f2dc2a0b0c2ca8dfa9d34a241028': '当前播放时间',
  'b063ef03f52de4db3a66bb26f65f6d7d': '视频当前播放的时间点（秒）',
  '296ed483e27ee65aa2d02b64a2d1902d': '视频总时长',
  '8dc6348f87ad56ddf71aba58846c83c3': '视频的总时长（秒）',
  '431b991d358babd875697eb0c623ae87': '当前播放的视频资源地址',
  '30019156e9340207c4cccd34245a6250': '暂停播放',
  'da2e1c73167313ceea55293b42031c66': '视频暂停时触发',
  'cffe326796a53ecd533a7c28d7c228a0': '视频暂停时的时间点（秒）',
  'a7d200f4422e1ff49f87fa254951fe04': '播放结束',
  'ad68db856822a4509b563ee97c1a9522': '视频播放结束时触发',
  '39b75e75d433cba4c57c4af1768f499b': '视频封面',
  '5094d4404b88877bacd12d94911e6f95': '播放速率',
  '9ee2f29eebe6e88f5f8e55b1367f49cd': '视频帧',
  'd4f32ef4f29f0b00c68838bc5ae0bfe8': '占位',
  'de205e9a976b0f4a85657c2aceb26dde': '修改表单项',
  '00023a2bbbee434389efce546514e9ac': '编辑表单项',
  'c9f871580f6691e0b67b2d57a2eb1849': '增加表单项',
  'e40b505be9beab346df0435af11fcf73': '添加表单项',
  '1388cfb1dddacc6e92d80b6bf2b9638c': '${label}不能为空',
  '129afa9b6da9340d94f789bc0858edff': '自定义列数',
  '75e3286810ea80c00a5d26b0fb475775': '修改按钮',
  'dd21259e1f4ce7d6d617d84a5cc76d77': '增加按钮',
  'c5896f48d30f9546751422973fd942bf': '添加操作按钮',
  '3628db5934417569d5be86cf89d44564': '操作栏是否居右',
  'aa05fd09a619ddbf07d8d903e6d32c5e': '通用',
  '0048314e09131553fd8add1d4bbb47be':
    '常见布局是基于Flex布局预设的几个常用布局样式, 可快捷设置',
  '4f38e93f6a62690cae448fb7531f6ab7': '快速构建页面布局',
  '68e4171aba5105f0479c6889f207cd95': '请选择一个常见布局',
  'e26de184a681bbfaa1885d8b0e5b790f': '专业模式',
  '64b67d07ce0121ad66df27ccee06d765': '简易模式',
  '866f7aee6d1655c06fe9eaef6d49042d': '订单目录',
  '8962e82adabf4b10dc92f545128211a3': '订单 Options 信息',
  '4a01b938175101e3e080528813789d33': '新增账户',
  '41f2b97e9c41dde90a2c7981a96fa688': '上一下二',
  'bf986aee29b821bb50e7981733c3ee93':
    '常见布局：上一下二布局（基于 CSS Flex 实现的布局容器）。',
  'c52630a44bb1ecbb4f32c9e2ffe36d07': '上二下一',
  '4bde33b908b1b3e0d17db7cbc253563e':
    '常见布局：上二下一布局（基于 CSS Flex 实现的布局容器）。',
  '171e87ae25fc4c47e8c0a599f371ca2f': '上下结构',
  '828fa1360014cd8d1dae1b3dc005f600': '左右结构',
  '3eddd43fe13e0b5cedf623da98fcef65': '上(固定)中(自适应)下(固定)',
  '08bed6a642f812ad59e1568c9a98188f':
    '常见布局: 上(固定)中(自适应)下(固定)(基于 CSS Flex 实现的布局容器), 上面高度固定, 中间高度自适应, 下面高度固定。',
  'ee583527e8fb68abf6687e159f9b1deb': '左(固定)右(自适应)布局',
  '1484a923503256702cddd5adbe15cd08':
    '常见布局: 左(固定)右(自适应)布局(基于 CSS Flex 实现的布局容器), 左边宽度固定, 右边宽度自适应。',
  'aba9ee8a18c73e1c6d183b71d7f5e5b8': '左(自适应)右(固定)布局',
  '556c8025b943ead4ab75ee574fd1a340':
    '常见布局: 左(自适应)右(固定)布局(基于 CSS Flex 实现的布局容器), 左边宽度自适应, 右边宽度固定。',
  '66344def1e4a8d7a13aa9ae76d5cdf7b': '上(自适应)下(固定)布局',
  '43488082d2476fdf63275d7edd344029':
    '常见布局: 上(自适应)下(固定)布局(基于 CSS Flex 实现的布局容器), 上面高度自适应, 下面高度固定。',
  '6005a351b653b2ef4415edd6ff168578': '上(固定)下(自适应)布局',
  '5571c79bad2234c8cf3f8609ab874acb':
    '常见布局: 上(固定)下(自适应)布局(基于 CSS Flex 实现的布局容器), 上面高度固定, 下面高度自适应。',
  '0248b98d5f4ee9941dce4b6a0d46547a': 'Order Options 数据',
  '03e630d8de0fe9e9225cdbdd0d9f506b': '安装地址',
  '7ffd3c32e4e3d3b9c081e22cb83c664c': '设置的地址',
  'a620db4dad94d95befbbdbf8fe4e5527': '额外信息的数据对象',
  'fe5ce6e90794f94101c8bf1658252a83': '维修联系人',
  '3ff96fb1ea36834f70125ea6a9d1b19b': '被选中的维修联系人',
  'ab592135d04deacb5e60d888bdffdd06': 'Options 数据',
  '54b39f0144713cf79c0b57f6a723a52b': '加个描述,支持自定义输入 px rem %',
  '76e0113750bc18feb1a013039894d359': '字段列设置',
  '1dcaaa566049ecd72795bc09911ce9e2': '操作列设置',
  '153fa67a7fb6ada66a1fcccabbbfab72': '选择',
  'c1b990c22e9f681c8d0ce8fb0bc33c78': '行内所占列数',
  'f463b49764443d609d81db9d3c499b8a': '操作栏居右',
  '4ba5d34b4005419a71fe177b2bf0b825': '所占列数',
  'd32e02d19cb3673f5fbf2271e5c66abe': '表格列设置',
  '2a6090089f4a26064e1ee1e37c3251c3': '增加表格列',
  '4e0b8fe26d34aa9c3c3238be5294f715': 'Layout1_1_fixed_left',
  'e45a4d3fdb8c7a4982e135061f7ff872': 'Layout1_1_fixed_right',
  'f51a32dc5a204cf27af71e0d0f767f4b': 'Layout1_2',
  'd94e7d096b87807167a021e511991aa6': 'Layout1_3',
  '4445ecd5efeec89369efa52bf854ac04': 'Layout1_2_v3',
  '9f0ca5bac3fdf54abbeafe65f5dd8806': 'Layout2_1_v3',
  'ef05c86efbab5537d7724efe3bc22771': 'Layout1_1_1',
  '7feb319b49c0974e386de86c1f8f4169': 'Layout1_2_3',
  '6c59c892ac763cd22323f1af8ba5b4da': 'Layout_scroll_x',
  '73f5a078133cfd17d3031532b27985a2': 'Layout1_1_v2_fixed_top',
  'dacbadf972e129adb2ae25f8bc44d5f0': 'Layout1_1_v2_fixed_bottom',
  '700011877c7f7b121a56b42ff26e6389': 'Layout1_1_1_v2',
  'ba974ea97e557ad35cce2ae62f9218f1': 'Layout1_1_1_v2_fixed_top_bottom',
  'cee0e7611ae31e1a5a7a4fcc9c32503c': 'Layout1_2_v2',
  '705c7190693f198d2d14523ef2764b5d': 'Layout2_1_v2',
  '878520066650b91dbd3dde124dca3ff9': 'Layout1_2_v4',
  '82e688864e5b6aba9b33809cc3a4f0bb': 'Layout_scroll_y',
  'c44c6c179c44e794b103053886847dc4': '没有预览图',
  'ac1695a2bdf7fe467f7f8e2e7513f9b2': '账户选择器',
  '589897d84e0176e36620ac2ddb5cb8c3': '超出省略',
  '2f36359e06018b483b13c5aa7e6d57cc': '优惠券',
  'a6876608f3f85ec7582cc3db9f5d92fb': '隐藏选中按钮',
  'bbfe8621f7a31329324d52aaa72fa448': '资源加载失败:',
  'c315c74af2c6fc0d37324dd0a107d5d5': '检查 window.portal 已超时',
  '0c92a7ef8ad3be57c5e02adc13846314': '检查 window.require 已超时',
  '95d4523d73e4f2922dd1998780a4dfbe': '193环境登录异常，正在重试',
  '1edc52cd8c142f1bbc3875a5fe70012b': '安装地址信息快',
  '41b0a41ed47aaf927cf54bfee90eeb18': '在一行中占几列',
  '015db4d97e7e4b76eb36b2a4952908ce':
    '检查 window.portal 和 window.require 已超时',
  'fcef5f8bbaf824d160a97ee29a4c01b1': 'window.portal 和 window.require 已存在',
  '33345ae42f5248db461a95e526ba797d': '权限组件',
  '943403d3d978758600a82ef660c727c3': '选中账户事件',
  '813ba7609071f8fbac674b685ae5991c':
    '用来获取初始Account的 api, 返回的数据可以整个 page 级别使用',
  'a94470f534c5833de1f8332fce6c3517': '账户信息组件',
  '12c4d53ad9a8e4c21946506cf8681fd8': '查询账户未缴费金额接口',
  '7dba90f6f7e3138d46058e21d676eedf': '用来查询账户未缴费金额的 api',
  '2afb3b8b9f2871d0a193b08e18beed13': '查询有效的账户级账本',
  '1e2702907ad8ff536f85efa5e20b0a3d': '用来获取有效的账户级账本的 api',
  '1d020b5b1daafb7c3ab88c2ac8d9bf50': '查询账户信控接口',
  '62b3f5fddfa7199571abc8f84db56f93': '用来查询账户信控的 api',
  '25dc962894011a00a9d1a8e922f3357b': '查询账户上个账期消费接口',
  '7120ab0048f525183dab2776d6134123': '用来查询账户上个账期消费的 api',
  'e4b40e3d2bf1f917fb6b8c27531566a1': '客户统计信息组件',
  'e0950f8a8253616c0b9a7d2bbb708971': '展示项设置',
  '5df67db667a1939a9ad52390a284d6bf': '展示项',
  'bfd6b39ad2ef2d783f83f1e49368997f': '查询客户订单数量接口',
  '187c08c1f7f61bec8e2430990de5527c': '用来获取客户在途单数量的 api',
  'e9021f4fd419042c6e8993940de48de2': '查询客户账户数量接口',
  'dfcefbd3988e282291c0fdb2857e458c': '用来查询客户账户数量的 api',
  'b40217dfaaed71ef4a22900ee4594331': '查询客户订户数量接口',
  '3c0300be02fa674f3251cb981d943e1e': '用来查询客户订户数量的 api',
  '0e43f4d72bb6153638f291ab3c8dbf67': '查询客户tt单数量接口',
  '5951d1e9dec381650b65df183556229f': '用来查询客户tt单数量的 api',
  '0c3c738e824cb8d4c11494f86afdd337': '客户信息组件',
  'bfd1addf6789727c887514af6403125b': 'Mock数据',
  '67682c9b02007d79c852d734190fd244': '查询客户信息接口',
  '7d9148d804815b8f7448ae61b0029157':
    '用来获取初始客户信息的 api, 返回的数据可以整个 component 级别使用',
  'a3f79989b509598c240c112a8eef31f8': '设置企业logo接口',
  '4da89dc6c79229f98ae2c50b24561302': '用来设置企业logo的 api',
  'c79f86e183f352daf38dd7cc89336f46': '设置上传logo接口',
  '5bb663f9833709bd43ec9168f259d9c1': '用来上传企业logo的 api',
  'e26c3d19bf33b23ea286b1eacf95d667': '组件标识（字段名）',
  'e479e2df5827207178a70e21ee279844': '标签名称（非 FormItem）',
  'dadac2456735b0ef132d265da6e42894': '标签宽度（非 FormItem）',
  '02d00e47f9650bf891a17a0b09ea4636': '添加Bundle成员',
  '68aefd8f6db6b83e74e3488bbc914e01': '区域内容标题',
  'ffef4df1f16586e3f3cf803f5a29610f': 'fish 标题组件',
  'e1e315d1a93fa6860e358259b42f2036': '动态表单',
  'fedf405884ca8b19a539316f71901884': '动态表单标识',
  '13c3e5e134f0962f224597a6277baeb9': '属性数组',
  '3f86e0d288ac9a6ae65df711a550d7a6': '选择订户',
  'cf076dcdee94e726dc0316e76c888940': 'GSM 客户订单信息',
  '1fb0ca80d7afa3b784f9db306cb67d92': 'Options 对象数据',
  '90ad07b0a901fb8d4f8080a7741745b6': '订单录入计划信息',
  'a5201f9f480f2f3a90460636e9452f12': '是否隐藏组件',
  '9e4848adbfe20121682b1f1abdf4c76b': '是否隐藏360视图',
  '82819b726c3611b7357c4a133da9b701': '读卡按钮状态',
  'cbb05213cae5baba7ba42d5eaf094b34': '客户搜索回调',
  'db26b496ebe6fb40babc988f0fde79c8': '高级查询按钮点击',
  '957b98b08fd59031d9dcafd19302da19': '选号',
  '5c705be129a1e9a11e828ccdcd50d8f4': '独立产品规格标识',
  '59b0173583aed5c86a3a1966e6c70328': '订阅方案标识',
  '924f67de61fc9e07fff979306900dc6a': '服务类型',
  'd4eaaaf051a5027fdb7231895fac6651': 'GSM 号码选择',
  '58ba1baa4d39e50debed2a7eff9d069f': '社交媒体',
  'fb20cdd33c419fa440a9fac36ad17473': '订单项',
  '7e611050e4e9772a35609e7e1513ba88': '请选择文案',
  '8ace6b57ea7764104dcc5b41dac3e34b': '未绑定文案',
  '7f87e02f5fb9577a7492c0bbd6a2ba47': '已绑定:',
  'f2992715424a4eeadbce85560195a6ff': '点击复制',
  'f7d7896fa29613e78674e5014025bc2c': '取消绑定',
  '02afc31e325152cc47cc8e7012dfaa56': 'Amount展示项设置',
  '828d5216aaca380fff8c7e83abd11da6': '绑定Key',
  '7d86892e15fc98bdd412835a57d47f69': '客户数量',
  '4b4ab48b032c9ad8d08d2c2288092120': '客户信息展示项设置',
  '818279bcca08d700b3c5705f3b16e4a8': '返回上一页',
  '286f047c9187daa51a038a39aa69ac09': '返回页面设计器',
  'a44afb46f963e10047f918c240cb53cc': '页面流设计器',
  '98d0b1b0ac58199821be7d3eb42b2536': '流程设计器',
  '151ec3dc757d5beb308788d18c80146b': '描述:',
  '22b4334f915541755261249f38d0df9b': '草稿',
  '0044f62f5599155c3578c6110f7a52f0': '废弃',
  '8428a3096a2c8bd8c81c369896820364': '不复制',
  'a496e15c79cdecae1c001c228baddcce': '逻辑编排 API',
  '11b1a77a90b1ca5534f0c16cb6254308': '原生 API',
  '5e4863b5bf13c09673738a8e362d8a54': '逻辑编排服务接口',
  'e13030ac144576fed24ae2a67716b982': '变量名:',
  'eaaa50407ec2b696143350bd33729b4f': '标题:',
  '3925f08a9614c06aa6401ffd34f3b2be': '值格式:',
  '8b393188c8be2173483c2fc5780f6957': '默认值:',
  'f8d38a48716370be187d524911da4f9b': '客户端持久化:',
  '9d65105f27b89f0a2fb4184382e02211': '数据作用域:',
  'f120a0cd2a1fc8231020ca0a83b3afbc': '保存并更新',
  '1008677b6a86f453b384a68223df5283': '更新页面',
  '9b601b8efa642f1b3c8159cd20d061b3': '当前版本',
  '64922b0a2e653fe745c50f0cbe3756d6': '切换成功, 请注意保存!',
  '08e7e3bcde6e3b97b4777c868b39fbb5': '数据回滚成功!',
  '13667f31c58766d157ef45c4580b87f6': '修改历史',
  '14cb7d323bb5d4cbf1205505ae91c1ab': '历史记录回滚',
  '696b48ca6b2ec7e61084f127ea0f1d24': '确定要回滚到该历史版本吗?',
  'd00b485b26c28f3df105a5a48bdde2b0': '回滚'
});
