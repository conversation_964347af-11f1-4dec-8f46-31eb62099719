import {extendLocale} from 'i18n-runtime';

extendLocale('en-US', {
  'f7e68bde2caa2cb5696d6a37fe4a23a4': 'Common',
  'ea15ae2b7fba76c83eec6d0986d15197': 'Option',
  '3fea7ca76cdece641436d7ab0d02ab1b': 'Status',
  '24d67862f87f439db7ca957aecb77cce': 'Properties',
  'afcde2611bdd13c1e65b4fb6a2f13425': 'Appearances',
  '6d0034a2419e1f394dedab07994b9665': 'Label CSS',
  '2cadb6621afe19333b142faa541b0f91': 'Control CSS',
  '0e627e6a0ff773ee76bc4cc0871cb48d': 'Description CSS',
  '10b2761db5a8e089049df39675abc550': 'Events',
  'dc19704991f1476fa4dcbb80c50bedd6': 'Prompt Title',
  '55713166f8bddcc0aefc3a32464746f1': 'Enter Prompt Title',
  '2d711b09bd0db0ad240cc83b30dd8014': 'Content',
  '59b9e2022323a63079c6ddab63fec112': 'Popout Position',
  'af767b7e4ae069d54f9ea839858d4c6d': 'Upper',
  '3850a186c3235bc646d4c2f79cebac36': 'Bottom',
  'd2aff1417831aa621c16cd5b95306b4b': 'Left',
  '4d9c32c23df5d234e629c922c58d8e12': 'Right',
  '5ef69f62dc668c1a3e68b51c50a2530a': 'Icon',
  '4434b33a8731a73613ba5fa1eb984efb': 'CSS Class',
  'f10b676db977ae808af8d96b327be7f3':
    'Which secondary CSS class names are available? Go to<a href="https://baidu.gitee.io/amis/zh-CN/style/index" target="_blank">Style description</a>. In addition, you can add a custom class name, and then add a custom style in the system configuration.',
  '159dbc2fafd57b9d3652f16659b1b519': 'Triggering Mode',
  '45a51525391d4a3771b22f2cf1aa96b3':
    'The floating layer triggering mode defaults to the mouseover.',
  '728c7cdfa431821d291b5108394ec65a': 'Mouseover',
  '4363c17ebb346b646af55bd8c8075915': 'Click',
  '5632c68dac780bd766f740830481f4b0': 'Click the blank area to close it.',
  '9efb0ce5a4510ef29345b6edb3e58bc2': 'A td must exist',
  '3ce57bd19e37d2b27145dc6fcfff3520': 'The first cell cannot be found.',
  'f7d205072a2ceb63b4f48a8b6f32fd25': 'The number of row lines is incorrect.',
  '852228c640b1daefe6b0853390e66791':
    'The number of column lines is incorrect.',
  '26526c3354307798dfa84f17decf5140': 'The corresponding id is not found.',
  '38d2ccdde0ae0c2329defd3c75c59d8b':
    'Due to the padding limit, the height setting that is too small is invalid. Thus, you can reduce the default padding.',
  'd59379f4227af3b2c60214e2f4f903ba': 'Merge cells',
  '02d9819ddaaaeb1b7b22b12608c7e5ca': 'Prompt',
  '4be3194e93cdd2899d06b499c184195b':
    'The prompt is used for special text prompt, which includes four types: operation prompt, success prompt, warning prompt, and danger prompt. Combining the <code>visibleOn</code>, the prompt can be used to prompt the error information.',
  '6f2b01db04cbf7e460b5c6f4e37a5e76': 'Prompt Content',
  '132a78bdf68d6d17bde00aa807bbf032': 'Content Area',
  '4092ed98e9035652d4c9ca9441701ed7': 'Basic',
  '226b0912184333c81babf2f1894ec0c1': 'Type',
  '330363dfc524cff2488f2ebde0500896': 'Success',
  '900c70fa5f7dbc014e6f762d5e0e885c': 'Warning',
  'e2e27a87257599f83c817c43e724b6aa': 'Serious',
  'cbda486dbec5bdacb593294e240c5e63': 'Close',
  '7372dc9f39a173dd0c75a185373245b1': 'Custom Icon',
  'bede211909a1022b13e9728ed162b77e': 'Anchor Navigation',
  'f6da8aa86fa3e399da95dab399a67985':
    'When multiple lines of content are displayed in the anchor navigation, the content can be displayed in the form of anchor navigation groups. Thus, you can locate the corresponding content area by clicking the nav menu.',
  '22c799040acdb2601b437ed5449de076': 'Container',
  '5879dec0aea52871e0ae6969893a315b': 'Anchor 1',
  'ce08bc4247c040bac13155befc7c1638': 'This is anchor content 1.',
  'd89a42cb938819f67172ea64037c19fe': 'Anchor 2',
  '96f05e63c6f606925636d6810167e7ea': 'This is anchor content 2.',
  '31327b9041b825a0b455b3ff9ddd3909': 'Anchor 3',
  '68284dd430993f495a6f2254ae5480ae': 'This is anchor content 3.',
  '9ad515106f02f3909974b75d22625b0d': 'Anchor Setting',
  '40c6e9ed3d9949a326f5a216d936324d': 'Add Anchor',
  '8cfd149e7d73ebae6a797d21728292ff': 'Enter Anchor Title',
  '6be15e9949e4be7fc485e1eaae472942': 'Anchor',
  'c624c875ea37f790665d0cae8959d4e2': 'This is the anchor content.',
  'f612a2f944af178fa07a719c79e8438b': 'Anchor {{@1}}',
  '42ff02f6763799ebfa5ce8dd5f99913d': 'This is anchor content {{@ 1.}}',
  '39f4fbc5b5ba681f7e8c4d4a4ddb3e2f': 'Default Positioning Area',
  '9959b2ad2d4f75f7a6be46872642df6d': 'Navigation Layout',
  '4cde06e6162ed66720e3133cb83bc059': 'Horizontal',
  '75ac842f8e77305846f1d776f97dfaf8': 'Vertical',
  '056f2d7df6e6b64625c3a2d27ce07b05': 'Nav',
  '696754a8b2b23e30b11187303d1238f5': 'Area Content',
  '33be689a0f0de129ce37f7a96052002e': 'Content Area',
  '32c65d8d7431e76029678ec7bb73a5ab': 'Title',
  '6bd854c27cd4c2e97dee65cf3f3f8551': 'Anchor Content {{@1}}',
  '726dd5df4319e9e158e2ca3c22177b6c': 'Audio',
  'e7a707f9fc7da36828db940ca2960f4b':
    'The audio control can be used to play various audio files.',
  '997c7a5d34f6fc628893f509d0df32e0': 'Feature',
  '22b777e6fcb613b8ba83ced9594cd07e': 'Properties',
  '37b12f2666b9e4e37f33eb5e83533d5e':
    '<p>Currently, this feature is configured for the field content node. If you select the upper layer, more configurations exist. </p>',
  'f8f176147db276063e7ec15f076e39e0': 'Audio Address',
  '91d3cd46d6b6919749e56056d5acc9bc':
    'Allows you to get variables, such as <code>\\${audioSrc}</code>.',
  'a945269af10da66c82cdb7336bc490d1': 'Audio Speed',
  '7ccd84ca5c16cd03d26f5ecd5e6f6bd2': 'The acceleration ranges from 0.1 to 16.',
  'fc03b83d19e2fd12f1e7c56a11d7dc18': 'Internal Controls',
  'd37d357dee041774b993daaf5c8bb752': 'Speed',
  'b85270cd3c06d8eb635eadcffbb10119': 'Play',
  '19fcb9eb2594059036dfede5f4ec53e8': 'Time',
  'c7bff79d059a0b7ff9b02441959d8be2': 'Progress',
  '09b095d8fc867cb968673be9dcc00a93': 'Volume',
  'ad751bba0aed43a673c40b652a239fc3':
    'After you choose the speed, you need to configure it in the ordinary selection bar.',
  '54c6bb48170611ec995f634319312156': 'Autoplay',
  '56e6ecf97176d30c06b30cfa428ef832': 'Loop Play',
  '2fb0853874c2cc8cc42f0c7520af662a': 'Inlink Mode',
  '33bf801796fd255b5f6147e33146669b': 'Display',
  '4c50eef3bdaf0b4164ce179e576f2b2d': 'Portrait',
  '18dc831ec12d358d05902bef1f00e1f1': 'User Portrait',
  '027446c2f9070b0f5b16a18208bf5fc7': 'Display',
  '20def7942674282277c3714ed7ea6ce0': 'Image',
  'ca746b1ff10193a3ce20878dec04a733': 'Text',
  'bfe68d5844f8e54602760e18f45954f7': 'Link',
  'b54f4a65cd257c87db32ee1023e1daa1': 'Fill Method',
  'def423db04dd24b226911b9e4cf5dc9c':
    'The image processing method that the image size is inconsistent with the control size.',
  '74735df86a8e1c15bce50e2c1dd42e98': 'Proportional crop of long sides',
  '9d7f3e170404f211a4f95c214f044b05': 'Proportional padding of short sides',
  '9854f491213784118614be4a1970bcf9':
    'Stretch the image to occupy the space fully.',
  '3d2b60dda894eba9a042beddf7daf3cc': 'Crop the image to its original size.',
  'c7fff1a6f585d7fb22c94bb1ef82707d': 'Border Distance',
  'f1b4c635cdb39c91461f181d27d06f8c':
    'The text is centered. When the text is too long, keep it at the minimum distance from the border.',
  'dfd0cd7f2cba96917484569a646bdc8d': 'Length',
  'c1df04eec5fa0857bc0df2d68d8e953c': 'Height',
  '0103eb2d3dca70270d1a74e9ec987ac9': 'Rounded Corner',
  '9a233b241eef54521cfe9365bfaa7b2f': 'Padding And Margin',
  '961534b4ea37e4e88aada736b299d063': 'Border',
  '8e1b944f4389bdaab6f11d5bc83190c8': 'Background',
  '803205e38834280d9e6a75993ac00764': 'Shadow',
  '0d98c74797e49d00bcc4c17c9d557a2b': 'Others',
  '34dac4adbc96afd65f060cc4cfff1feb': 'Opacity',
  '169b227aff15b834b64205d0fdcb0f33': 'Bread Crumb',
  '3576258acd7269da9f44859a2edec1aa': 'Bread Crumb Navigation',
  'db1c89e0f6e62f9642018cbb531bbd4f': 'Home Page',
  '2e8bf3c87641fba59c2a9cb6636d7e88': 'Parent Page',
  '12d358955755488ff3790bbd6d75673a': '<b>Current Page</b>',
  '894b94fbb15650da7f9290e7106446f3': 'Separator',
  'a38100f22f59f7cd89e36139faa6fd4d': 'Dynamic Data',
  '66ab5e9f24c8f46012a25c89919fb191': 'Add',
  '97d07614380da93d257f9fbf81aa56fb': 'Text',
  'ba7f916a39c0beb545388ea3b7f6b3b7': 'CSS of bread crumb ',
  'e3acc9857c852dae27e064ace5e5688e': 'CSS of separator ',
  'fa966345577ba81af19408f203db968f': 'Button',
  '89de611b2d759a1802542b5d3a06829f':
    'It is used to display a button. You can configure different display styles and different click behaviors.',
  '7af5e3ef39ff71d39fe3f645c8079124': 'Trigger this option when clicked',
  'f6d767f39ba3bf955077a3c0ce81e581': 'Mouse-over',
  'bcdd89d453da0dc0622a2f3189728357':
    'Trigger this option when you hover over it',
  'e272b0b8c7fedc670a87075514d9b49f': 'Mouse-out',
  '727309bc724ff237c5e2cdf7a90cf28e':
    'Trigger this option when you move your mouse away from it',
  'd7ec2d3fea4756bc1642e0f10c180cf5': 'Name',
  '939d5345ad4345dbaabe14798f6ac0f1': 'Submit',
  '4b9c3271dc2f299dc3aeffb369187513': 'Reset',
  '5e64227f46b221001e3b151d72fa6412': 'Close It',
  'd5bb99590ef447a1af8e9782786c751d':
    'Close the current {{@1}} after you specify the operation',
  '1e7196ce05260faa55e5b7ea5a1667c2': 'Second Confirmation',
  'ed2f135144b4e138cb29419c1f245f4b':
    'After you click it, ask the user. You can perform the action to avoid false touch upon the manual confirmation. You can use <code>\\${xxx}</code> to take a value.',
  '0d0c6ff7b65e62eba3ddd523e2976389': 'Confirm Content',
  '7e9646e2db5ce66dc2b4b922ece483ba': 'Bubble Prompt',
  '5daaffe964aee9d884e0a681b2818a17': 'Normal Prompt',
  '2fd82aa9dd7fedea2c16b7dfe93b6d0e':
    'Give the prompt content in the normal status. If you do not fill in it, no prompt pops out. You can use <code>\\${xxx}</code> to take a value.',
  'fb37b983a93aabdcdbbd59ae48c519fb': 'Disable Prompt',
  'f4f168a3fec79443d2ca8fd1955edee8':
    'Give the prompt content in the disabled status. If you do not fill in it, a normal prompt pops out. You can use <code>\\${xxx}</code> to take a value.',
  'd420160a9e04c481e55a9686ab158caa': 'Mouseover',
  'd6763cb7640bed39caa058f156007a86': 'Focus',
  'd586324c6d6b45cb78a4172d836dab3e': 'Prompt Position',
  'c182ad6b97f0909596a523b1f04c28d2': 'Leftside Icon',
  'ad7e6f016bc1d9a9bbc6e18224d73247': 'Rightside Icon',
  '39003734d1d700d5bd97bf1e7a2fcf73': 'Style',
  'c12ba7b658a9fccf0815f3951bc759b6': 'Highlight Style',
  '6aa6b9e2dca63d27dc74eb155020271d': 'Block Display',
  'c8339fd2a85af4ba66084d28df808de4': 'Size',
  'c8caf94205105bac5833ab31cc2129d7': 'Button Group',
  '66ae9ce23b0b2e243aff087d906a2489':
    'It is used to display multiple buttons which appears as a whole visually.',
  'e9d2f66bbd44c96a3e03494bf6b1ebf0': 'Button 1',
  'ce35a17d2ba7caac95092a7a66ac9a0d': 'Button 2',
  'de26e1294acedb55155a418630d57924': 'Layout Direction',
  '8eb18b36f5a27fa8e6d32bc66546ce05': 'Tile Mode',
  '2e28645c67c5742e473888a27aab7bd6':
    'Enable the button group to occupy the parent container. The width of individual buttons is adaptive.',
  '66774850742a81e8b2393195290b7330': 'Button Management',
  'f9f2b9cc91cd332db6b2b732c3869233': 'Add Button',
  '40f3bc0a4f4d0f4230fc7fa6f4fcec92': 'Child Button',
  'f05dd80af77a441216ef940e7887a8db': 'Button Toolbar',
  '433e2e80ec74563daf4368e59b525e34':
    'It can be used to put multiple buttons or button groups. A spacing exists between buttons.',
  '6651fec0511e3593d3438a113dff23d6':
    'The buttons or button groups are sortable and removable. If you want to edit the buttons or button groups, edit them in the preview area.',
  'd7213304d1a8a02a73a2f4010839e061': 'CRUD',
  '7a9f1ec32752de8b69ef21138970f64d':
    'This button is used to realize the addition, deletion, change, and view of data, and supports three display modes: table, cards, and list. This button is used for data fetching, paging, single operation, batch operation, sorting, quick edit, and other features. Integrate query conditions',
  '48c68eb5a42e2b3e679c53b00f6e203f': 'Rendering Engine',
  '64ca9bab920a2983bcf270320d850d00': 'Reload',
  '9ef5597ac0b4da662bcd967da37eceb4':
    'Trigger the component data refresh and render it again',
  '95b351c86267f3aedf89520959bce689': 'Edit',
  '607e7a4f377fa66b0b28ce318aab841f': 'View',
  '5b48dbb8dc710cffe6313bb56a7f6d47': 'View Details',
  '2f4aaddde33c9b93c36fd2503f3d122b': 'Delete',
  '6c546666aab964c39cd8bfdf4fbd46b8': 'Are you sure to delete it?',
  '7fb62b30119c3797a843a48368463314': 'Delete In Batch',
  'e73cefac9d030927da1618c7b15c98c9': 'Edit In Batch',
  '19c6b7463e1bf73bb4b12ba07abd5444': 'Field 1',
  'cf12e55021998a8328201800ec356773': 'Query Conditions',
  'cfb5f18c43753ad5329348d626bd3739': 'Keyword',
  '4a1e3c50547e61503a2d1c356005eb08':
    'Add, delete, change, view, and quick start-CRUD',
  '85624c8e8b0fc98954eecbe508e8b59d': 'API Address',
  '3dd674542204724eb5417efc7354ec73':
    'Verify the format and automatically generate the column configuration.',
  '3266535dc49863d976b9960adad29fef':
    'The API return format is incorrect. Click the question mark in the example on the right of the API address to view the example.',
  '07b59fd09f6007bac246d9a73b793a49': 'Enable Feature',
  'bee912d79eefb7335988c4997aa9138d': 'Query',
  '0f61da949d2b45534967e197cc2eee29': 'Modify In Batch',
  'aa85b3cd6aa4cdfd45bfe5a96678ad2f': 'Action Bar-edit',
  '653eb2792d3126a60caa6982f89a906b': 'Action Bar-view Details',
  '8a4d6dfbcd8072555b5951091f171000': 'Action Bar-delete',
  'f4b368051b455e386a314688c7c75c1f': 'Enabled Query Field',
  '23c7ea8ee9519459598b2de65fe2a2eb': 'Display Several Fields In Each Column',
  '41a344642681efaaa418c228ba7fb45c': 'Bind Field Name',
  'ffb01e5bcf4c00447f5150d3cba81371': 'Pure Text',
  '59cf15fe6b8d659c9bd2f86143534a06': 'Template',
  '4ff1e74e43a3586339251494117185ad': 'Date',
  '9da188491dd34c4382a5b9f006194e41': 'Mapping',
  '8abc564260a1564521e0c3a1d5419b4a': 'Action Bar',
  '2b6bc0f293f5ca01b006206c2535ccbc': 'Action',
  '07a88fae7dd11f87891af91fb54a74bb': 'Enable Query Conditions',
  '7f7c624a843b0d539a4ea59a696702f9': 'Batch Action',
  '46e6edaeb9968e7f7ab549d4f2f82d6d':
    'You can manage the batch action buttons through this button. A selection box appears only when you set the batch action buttons. Also, you can configure the position of the batch action button in the appearance. ',
  '8347a927c09a4ec2fe473b0a93f667d0': 'Change',
  'f13a0697d58d975d14eb3f3c72c2cbf2': 'Single-entry Action',
  '3674ea51498e7118732e5e1c53d4bc80':
    'After you set this button, an action button appears when you hover your mouse over it. Meanwhile, this button appears in the top action bar. When you select a member, it can switch to the batch action button intelligently.',
  '4916771d080ddf6d0551de4d6d2f42a4': 'Hide Mouseover',
  '569343b4fe5e48131b78611c11eadbeb': 'Synchronization Address Bar',
  '6dbee29a8c54eef9d042ef3280999ad9':
    'After you open the synchronous address bar, the query condition data and paging information are synchronized to the address bar. When multiple synchronization address bars appear on the page, it is recommended to keep only one synchronization address bar. Otherwise, multiple synchronization address bars tend to affect each other.',
  '2171d1b07d045e796cba4a05bcf7d13f': 'Default Parameters',
  '01e7eb2256821085a14708d6ddf36814':
    'This button is used to set the default parameters, such as <code>perPage:20</code>.',
  '58ed0a7a5a91996dbb4c7d6dc7679364': 'Keep Selection',
  '36ac0011faae6f88ee0ec3e642328327':
    'After the default paging and search, the selected entries are cleared. After you enable this feature, the user’s selection is retained. Also, this feature allows you to achieve the cross-page batch actions. ',
  '2a0b47ba76e886070c3bd6abeae3d1c0': 'Single-entry description template',
  '6ab3e5b6c5d5d2cf621e911226495433':
    'With the [Retain entry selection] enabled, all selected entries are listed. This option can be used to customize the entry display text.',
  '987b04af242bb2dafaf32d890ab952ff': 'Designate primary key',
  'a270e70be12fb13a24ca3e4ac70fa838':
    'This option defaults to <code>id</code> and is used for batch action to get the data at the row level.',
  '54ea89b497ec3bb319c68844dfa3687f': 'API',
  'db7ee36de8063c2d5a6c123eac65641a': 'Data Fetch API',
  '0951dad1723aa1191ce1a2e96db76051': 'Enable Initial Data Fetching',
  '0a60ac8f02ccd2cf723f927284877851': 'Yes',
  'c9744f45e76d885ae1c74d4f4a934b2e': 'No',
  'a9400c408441f1f7f6d6954deb05ae9a': 'Expression',
  '55b22f5d136e73b26cef65aedd8ba86e':
    'You can use the JS expression to determine it.',
  'bedc70d448b656d828bd3ed7926b3e4d': 'One-time Data Fetch',
  '559fa334442f0f75b0343bbf38b7ff05':
    'With this option enabled, you can fetch the data only in the initial time. No request for subsequent paging and sorting is sent to the API. Instead, the subsequent paging and sorting are achieved by the front-end device.',
  '1af68c43e1780249f11b9de1eeaeb281': 'Enable Scheduled Refresh',
  '9800f1ce2f78a23b81c8d944ebf9cce9':
    'After you set this option, the timed refresh takes place automatically. The unit is ms.',
  '19c5410b23fba4bbfd1a58bbd5268c9b': 'Silent Refresh',
  '04f840b0772f4b5d59954a29a76f4e7b':
    'Whether to display the loading after you set the automatic timed refresh.',
  '6037dae99e9446deaed45f7e408f47ab':
    'Expression to detect when to stop the scheduled refresh',
  '32e3a79e80dcf7f7304e8092dd7acc6f':
    'The refresh takes place constantly once you set the timed refresh. The refresh proceeds unless and until an expression is given and the conditions are satisfied.',
  '154ef40e477c031f6c1ec15caefb570a':
    'Disable automatic refresh when a pop-up appears.',
  '50f7c85bf60a9f43522789644566c62b':
    'The automatic refresh is disabled when a popout appears. The automatic refresh is recovered after you close the popout.',
  'd8905a70e93a33c7a86240f467c653d4': 'Draggable',
  '040a78b24eaff47d4fa7e266473635b4': 'Sequential Save API',
  'ee850a0e326b217bdeb61d936c521177': 'Quick Save API',
  '8343f619879fa79bc8ef1a35b2fc3e78':
    'After you set the quick edit in the column, you can use this API to save the data in batch.',
  'd891e79d4a8718a7dbd47ac68aaaa5cb': 'Single Entry Quick Save API',
  '38db6e045e214ffcd03ede695002271c':
    'This API is used to save the data when you set the quick edit and the immediate saving in the column.',
  '522110866c19dace2ce38336617405c2': 'Default message prompt',
  '3b69c2e540c05e9ca530ed30cf224472':
    'It overwrites the default message prompt. However, if the API returns a msg, this msg is used preferentially.',
  'fb24383a41f23196349548b5d0cb98ce': 'Success Message',
  '62e3e15c8fb9038f2780329bc26e8bab': 'Failure Message',
  'c62a1b7f314be10aead10475e7543f6a': 'Sequence Save Success Message',
  'c8035507b7a576d43e9f227c91c7a7b5': 'Sequence Save Failure Message',
  '7cb0932b806559be232d2a69453224e7': 'Quick Save Success Message',
  'fd79a193a487b8c9d5a302d0d88c1c2c': 'Quick Save Failure Message',
  'ff57a3bf69a1065707e77c18c51f7bbb': 'Display Mode',
  '20aadc3f9b7edb564dc58898898e0dc8': 'Column Information',
  '72cf373be86a38b29f6d2f15900b0da1': 'Sub-Title',
  'f26225bde6a250894a04db4c53ea03d0': 'Details',
  '3bb4d608c6bee2b7b6d788417cde04e3': 'Simple display data: $a $b',
  'b339aa87104709397ba68e7ebbc6e5ba': 'Table',
  'd87f215d9ac688b1d3399bf575a0ef6f': 'Card',
  '3712972d84adf48acbd6ad24b4d75ad0': 'List',
  'e4fd8d64804a4f3d743eff384a6eb20a':
    'You can select and edit the non-built content in the preview area.',
  '14555503d6e09ecd66661d04c882e79b': 'Top Toolbar Settings',
  '3862626c138ce5945e0e273a1bdfbad0': 'Paging',
  '439a19857be1fb8d3e6017258e32c486': 'Statistics',
  'bc908986a4e4eec3dca3530afd1d4306': 'Change Page Number',
  '77281549955309c49f5eef77838a85e5': 'Load More',
  '1add12ff3338a990b60154b75019c348': 'Export CSV',
  '7d57852b946d9d4d64fb2a48ca2b4a44': 'Export Excel Files',
  '16c6099bc0efaa669d8de698c105e333': 'Column Selector',
  '073694928db5b0b5423ebe095ec62d39': 'Switch Query Conditions',
  '5b9c3e6ce478196f802722cb09d61f0b': 'Drag And Switch',
  '66eeacd93a7c1bda93906fe908ad11a0': 'Select All',
  'd5bc35360607472de4525358af126de4': 'Align Method',
  '413f48cc71f71083ce532a86e3efdc21': 'Left',
  'fd8e9572cc6bf87653c4d8f8b8dd0db9': 'Right',
  '1325969c143a639294c1c6ab370b35a3': 'Bottom Toolbar Setup',
  '440a3a2d7f1b123718be75e2baee8391':
    'You can select and edit the details in the preview area.',
  '34da7e4a0ecdb63967427f43adf2de3e': 'Toggle query conditions display',
  '25c0db3ddce9bfffd497d0c824cf3c72': 'Default visibility of query conditions',
  '259d11c300a365b6767228c12d29ce53': 'Hide Top Prompt For Quick Save',
  'fa9417bacb7714e82663655345ca213d': 'Always Display Paging Controls',
  'e3d2a85f20608a5bde7d539969d03167': 'Hide Select Button',
  'af1af0a7fad9e8bdcd21694d0e036e12': 'Content CSS',
  'c11322c9cc43ce3c004cf03f5ac0acd0': 'Data Source',
  '62569fcb0fc8314daea97989bba3877c':
    'This option is left blank. The properties of the items or rows returned from the API are read by default. If anything else exists, set it here, e.g.,  <code>\\${xxxx}</code>.',
  '606bc931d2b3ebba2569cb1494719e2c': 'Items Per Page',
  '004d01f56242e07cbdc97256bb11c75b': 'Keep Selection Across Pages',
  '3d330edb46341a26ccc9aaa7f7938a8e': 'Max Selected Items',
  '95e68bea2d8c8e41ffa619d4364a0d6f': 'Page Field Name ',
  '537f8b09437bdb7fac429dc4e8666763': 'Paging Step Field Name',
  '30c7bd5acd4564057bd89e0846f01728': 'Sorting Weight Field',
  'dd8d217677e4c5df28b7f46aa99b22a8':
    'It is a field name that is used to determine the position. The new sequence that you set is assigned to this field.',
  'e04e8f570e2fb43a533b224f3c48523d': 'Number Of Switch-overs Per Page',
  '0fc1de8f71a7470213fc68f981abdbc2':
    'Configure the expression that you can select for each item.',
  '7d5fefd589000879088063dceb4b2552':
    'Use js expression. If failed to set it, you can select all items.',
  '614ec7801e03f7ee009e4448e6ed4001':
    'Enable a single entry, and click and select the entire area.',
  '530ab79908eabe5b329ffe17695079e2': 'Automatic Return To The Top ',
  '9092afb1ccb692308ef06d8001da2208':
    'Whether to return to the top during paging.',
  'c8cf39b24bb52d0562472c33b86824fe': 'Synchronize Query Conditions',
  'd3c172700af4f3f3afb028d6bb4a90a4':
    'After the query, the returned data is synchronized to the query conditions. ',
  '5db6b2160b9169b9d89de247d14ab740':
    '"Add, delete, change, and view" edit area',
  'f49d40842a3c66c4de2e57a48157c707': 'Display A Single Card',
  '0212e8c9b113143a031d1f3e25167794':
    'Click to add a button element in the left component panel.',
  '6312e80e416fa79ff3383e1577c243b8':
    'Click to add a content element in the left component panel.',
  'a3cf7c9ee581ae71eb51d18b610b05b6': 'Add Content',
  '00a1f644f34b9ee43adf82cb3449158c':
    'It supports the following template syntax: <code>\\${xxx}</code>',
  'f7a82c9758acc4ff4c5350182f2ca378': 'Image Address',
  '3bdd08adab6ea90b9164b20a0e4151ac': 'Description',
  'd85562778760b00b1372af642c4630e6': 'Highlight Expression',
  '4011e5fb23b422f755d81269a5242d22': 'e.g.:  <code>this.isOwner</code>',
  'd4f223e0619836d03d488c89558f38e7':
    'Maximum number of buttons put in each row of the card',
  '620868e5e60e5c22249c3277c971bb13': 'Title CSS',
  '1fee6fa022c97f5dd7f48e362cea5acf': 'Highlight CSS',
  '2a3c7d647a29fb7dc5aedabac216575e': 'Sub-title CSS',
  'a5680444d449b2099b067e9963fe43aa': 'CSS Of Top Image Layer ',
  'b8e1e46cbdea20de4e5fc130d31b7bcc': 'Image CSS',
  'a31119cb2fddee3366163a311f317cf7': 'CSS Of Content Area',
  'e82eb2350b4283822aeea21aff9d97b5': 'Field {{@1}}',
  '9caecd931b956381e0763d05aa42835c': 'Field',
  'bcee820bc20342a4d388a35ed32a35fe': 'Column Name',
  '093c340f7e1fbde1928ca56b5c7f9cc4': 'Card List',
  '85c17b016309a3f867a1803049b3bcd8':
    'This feature resembles the table, but it uses a small card to display the data. You need to configure a data source for the current component. This component does not have the data fetch feature. You need to take priority to use the "CRUD" component. ',
  '6223c41373004e3111e768225450b4e8': 'Card Set',
  '3983b9f5575ae146d2d06f8ec5447a4d': 'Configure Single-item Details',
  'a3f38735bf211edb2066ac4e51b55cb2': 'Open External',
  '7ea26d0cb93e59339daf6a1ac68624f3': 'Bind current environment variables',
  '21efd88b67a39834582ad99aabb9dc60': 'No Data',
  '35ba83e053cef95e55dfffde279822b5': 'No Data Displayed',
  'e18d033cc4baab3ebb329f6b29eb3cef': 'Whether To Display Header',
  '412593f58b9d062a43cbe2ce38f4dc65': 'Whether To Display Footer',
  'e494f1aa112068688ca863db7f39a0b5': 'Header CSS',
  'd267231d2d8b60e267acc7d7d9955ae2': 'Footer CSS',
  'b2d2593bfb7a627817c0bd1ef6a254a8': 'Card CSS',
  '58e78d512d9ff40c73a263ab616cc652':
    'Number of class names displayed in each row',
  '3cf0da9fe51f92842e0a6d375fa5c605':
    'When this parameter is not set, it depends on the card CSS class name.',
  '953e91f3df59837ac2965cc04dec4b0d': 'Enable Waterflow',
  '39a6853b109ae98f1aabca90283d7edc': 'Configure Member Renderer',
  '6cb01692eea2fa7066d20fe2b6ccaea3': 'False Data',
  '0c0180cb06a322199a67f10d4ec41cd5': 'Carousel Image',
  '1007c97dbf952e032ce13be3cb811f23':
    'It is used to render the carousel image. It allows you to configure the content on each page (in addition to images) and the transition animation.',
  '0a2907a421b8f980986117e4f3044f92': 'Associated Field',
  'e760ec18028fc075c5705bf184589e70': 'Static Setting',
  'd314558953b3c76adb7e131aaec8bd86': 'Field Name',
  '793a763e73f1f742e3a16ddc2ed95ccb':
    'This option allows you to set the field name and associate the data in the current data domain.',
  'b9994cc749b4cfbbac0a9b140addd242': 'Carousel Option Content',
  'c6c7456d446d62a906c2809b6ba19ce1': 'Image Title',
  'ab8a46ccf46acbf82d020d11468291b1': 'Image Title Class Name',
  '098c3d959911b48b4d912cb85ccc4942': 'Image Description',
  'c6fc4066471664a8602c636cfe1cc766': 'Image Description Class Name',
  'cfb6f6e4c92a61ed0e0717abc8d0eec7': '<p>html segment</p>',
  '97cc997910b99083bd23c6ac39294ff3': 'Automatic Carousel',
  '8c2a12c5dee794b8b9608bc1f8087947': 'Animation Interval',
  '6265104f900789dd51d75b449c3b9f89': 'Animation Duration',
  '5bc37c57ee54d407f441b222f02391db': 'Animation Effect',
  '8a0d716ded7baa7ee356ff39c3cf0bec': 'Control Button Theme',
  'f7471313dce32bc3669d338764a0d036': 'Control Display',
  '1c0fe943329339003e9e3c1b12a27fe3': 'Bottom Dot',
  'e030190fd1c10b0c967f48e789fa86b1': 'Left And Right Arrows',
  'c28479019e24e0e4745f4948e9e97ee7': 'Width',
  'ad5a36ee5f4054594c30b6dc481e2d81': 'Previous Card',
  '49e0f68798e3c69b7990076bd8f1f47f': 'Next Card',
  'd09504750ebc1d75c38d86e16060f83d': 'Chart',
  '10e14f791d73c7c0f4113787f36f1626':
    'This option is used to render the chart. Based on the echarts library, it supports all echart types theoretically.',
  '67b6bec14c39be3f2602271e2fe3bcde': 'Refresh Data',
  '0310aa5a05fe07147d8b7ef20616f659': 'Trigger Component Data Update',
  '3d6abfdea70af544603da70f93ed3d24': 'API Fetch',
  '03677b68a73eb59e5faf281f49f3c130':
    'The API can return the configuration or data. Recommend that the return data could be mapped into the Echarts configuration.',
  '1396ebc166bd49c1e3b67549a1b4caa0': 'Fetch API Initially',
  '2af32ab13a9dece315cec2764d6aa7d4': 'Timed refresh interval',
  'bc827aaffaa35a95607346cc104c0785':
    'After you set this option, the timed refresh takes place automatically. The minimum refresh interval is 3000 ms.',
  '57c7d1125d2803b946a5b25c3f80f249': 'Echarts Setup',
  '3d3fa75d8b345b22a5fbd14a65a0af71':
    'This option supports the data mapping, which can fill in the data returned from the API.',
  '43ddd80698198791d4a738bb20a2d7f4': 'Set Up Drill-Down',
  'c8da43fe6ad1c537f86cecb353250145': 'Delete Drill-Down',
  '4fd9621d4facc101aba3afec055e14d1': 'Data Processing',
  'd98ef182637b4d10e16e8073c1101e51': 'Replace Chart Config',
  'f1f13cb0ca4720a727cbfba4c82e5890':
    'This option defaults to the append mode. The new configuration is merged with the old one. If selected, the configuration is entirely overwritten.',
  '40128a51e9667fe6a20a0454069368ba':
    '<p>Content<code>${value|json}</code></p>',
  '7e3f6245e2a6adb903cf85c77cb1bbd7': 'Configure Drill-Down',
  '38bbd995a790f5a67211e034b007c145': 'Highlight Code',
  '86e38e6425f722ba725718af2366ac08': 'Fixed Value',
  'e90e6ff080f179215c3566a61ca62367': 'Folder',
  '452b62e9b7e650fa163300da2893654a':
    'The folder can unfold or fold the content area to keep the page clean.',
  '81d2b9f20fb2083c75a5b052b84e897a': 'Unfold Title',
  '7349194c139069b32889101768aa7428':
    'The title appeared when the folder is in the unfolded status.',
  '3a8647306ee6456517b7bf9c8bc7de23': 'Title Position',
  'c949729cd1a1e425595c1a297649c7c6': 'Top',
  '12c4c5e8abda77e1fdc93653d6414187': 'Bottom',
  '731f9b470e0948cbf56341a53c423213': 'Display Icon',
  '96c0cc844a06e0850c04f7c44b6475fb': 'Foldable',
  '63b67eead04256e42ea9f6f7218731ee': 'Title CSS',
  '66d88b3e01aff17c7973181e53fc8c0c': 'Content CSS',
  'd09980a88568f75e9267ca7b531c74eb': 'Fold Panel',
  '0d571a7ab19e098820e8cea4d5a80f7d':
    'When the information volume is huge and there are many classes, you can use the fold panel to sort the classes.',
  '17dcbf1f144607d4af0bb718e008682f': 'Title 1',
  'f7fb20f6cacd5e40c7f5732cb377d0bf': 'This is content 1.',
  '72d41bd9eb3882f7da6f55d0ff0a39f6': 'Title 2',
  '0431ee7033364800e261d1e560808231': 'Icon Position',
  'a7eaff29603a9c40927f726013d2c016': 'Left',
  '128d58f066a18ddb2ddb701921d5c77c': 'Right',
  '47b4e22880eb59ce9989b8419222e88a': 'Accordion Mode',
  'bb3548f0bb97ab11ee92284ecf37ec16':
    'In the accordion mode, you can unfold a single panel only.',
  'f24544b06700857ec11b434cb2916692': 'Panel Management',
  'c5ceab33d3e275262b4992a8cb43317f': 'Add Folder',
  'b839e579e920068bd817d34cd7927069': 'Add Folder',
  '030a54b0afb54fc7f90e1a0f64eb8205': 'By default, unfold this panel.',
  '39b066b81835fd66bd4529d1220c9dd3': 'Title {{@1}}',
  'ded228f9173b241dd8df2a4811ea0e98':
    'A simple container allows you to put several renderers together.',
  'a823cfa70cfa46c788e1eedae043f6e5': 'Container label',
  'f7d64e5e79994c3c8853f2608d7b2d25':
    'Because the HTML label is invalid, re-enter it again.',
  '473d2078518479669823205110842376': 'Custom Code',
  '8b9c77fa4e646b9cb270f833252e511b':
    'This feature is implemented through the embedded code.',
  '0b13dc6251002bf556263fb3e4675b11': 'HTML content',
  'd6b917c76b92aa9b92b6bebdcab993f3': 'onMount code',
  'e64739dd24bb0bfcb6f6e1ee2cce5413': 'onUpdate code',
  '0601b7aa5b53cbc3616e24719bcd2aaa': 'onUnmount code',
  'b82231f254baf9a28bf752683d31b169': 'Custom container',
  'e5b5798a8bab7dc8a578431991731040':
    'The container component is implemented through the custom code.',
  '96ec95de2d7da5b16465eb980f74deae': '<p>Custom container area</p>',
  '749f710d280419b1da031c9bc79b3b07': 'Custom container area',
  '********************************': 'Date display',
  '2bc6d101e5701a70f2fb9e0b67581594':
    'It is mainly used to associate the field name for date display. It supports various formats, such as X (time stamp), YYYY-MM-DD HH:mm:ss.',
  'a2344febfc246ddc7281f62217ba42c0': 'Date Value',
  '84ff80a2dc4717cc01acd486040a6763': 'Display Date Format',
  '6eea1b15be458a6999c9259aa2280a70':
    'Refer to the format use method in the moment.',
  'a7032449ae8761aea61cc30e32d3be10': 'Data Date Format',
  '4c1cff4d8c05daa6ed9352a241ee628c': 'Placeholder',
  'b54e0f0a60f8e2c4c31f3b1ad7d5a613': 'Date And Time Display',
  '2a898869829eae8adcfca290fd34a67d': 'Date And Time Value',
  'b0d6f2d882adc2163e6a08a121d18677': 'Display Date And Time Format',
  'ab3aec075a09d055b2a28c8b61925ee0': 'Popout',
  'e83a256e4f5bb4ff8b3d804b5473217a': 'Confirm',
  '773ddc154f1e9b80f04e8bc9d83d2caf':
    'Trigger when you click the Confirm button in the popout.',
  '625fb26b4b3340f7872b411f401e754c': 'Cancel',
  '08ab4ffcd1bddd249a193e6353bb52bb':
    'Trigger when you click the Cancel button in the popout.',
  '4708bcefff645287c8781a1de2a0f20b': 'Trigger the Popout Confirm Action',
  'af17a4e37e5c6d68fff33c084192801b': 'Trigger the Popout Cancel Action',
  'dd10fdec63a2224aa3d28b48d428cb98': 'Data Mapping',
  'abf68809d31c8eabb3eb5ae9e00abfcd':
    '< p class = text SM text muted > when data mapping is not enabled, the pop-up box will have all the data of the environment where the pop-up box button is triggered by default</ p>',
  '1fab180b92bddf03ecf7da009f86d826':
    '<p>When data mapping is enabled, the data in the pop-up box will only contain the set part. Please bind the data. For example, < code > {A: \\ ${a}, B: 2} < / code > < / P > < p > if you want to customize on the basis of default, please first add a key of ` & ` and a value of ` \\ $` as the first line</ p> < div > when the value is < code >__ Undefined < / code > means to delete the corresponding field, which can be combined with < code > {&: \\ $} < / code > to achieve the blacklist effect</ div>',
  'd6f555510792c9f3cb9e8087af037898':
    'There are errors in the data mapping, please check carefully',
  'c14ebcefcb02c97b925b762d8bacd1ee': 'Press ESC to close the pop-up box',
  '********************************':
    'Click the area outside the pop-up box to close the pop-up box',
  '********************************': 'sm',
  '18c63459a2c069022c7790430f761214': 'Default',
  'aed1dfbc31703955e64806b799b67645': 'md',
  'ab18e30c0dc4093ceeda8961fac3d1f4': 'lg',
  '949934d97c42801151673a51d3adc421': 'Extra Big',
  '5831b836c8132033f90b3f530fa433db': 'Display Close Button',
  '79e5818b205352166231217ea39c4f1f': 'Top CSS',
  '7e1eb2c588aa1301f4aa19395ef0a177': 'Separator',
  'bc43ae8e61f1ad4be2b0a9e70501e490':
    'This option is used to display a separator which is used for the visual separation.',
  '33f1fc330b325469b43614c9d96f590e': 'Drawer type popout',
  '0c5a0448b07419ad2900c36867e8e4e0':
    'Trigger when you click the drawer confirmation button',
  '57f215184a2fb83541f7cfa22d039feb':
    'Trigger when you click the drawer cancellation button',
  '97b6bad87c4320faac2f6a5cf556c26c': 'Trigger drawer confirmation action',
  '909ba2872b2d670ec0ecbcacc4c8c833': 'Trigger drawer cancellation action',
  'd4d2a66820d30e07b44c850eb3f116c0': 'Position',
  '81b8345bc49ce2aa550ad9792acec3e9': 'Define where the pop-up box is called',
  '********************************':
    'Click "external" to close the pop-up box',
  '********************************': 'Press ESC to close',
  '6d7f8b31caaf80deb65eb2c8bdd41cd7': 'Very Small',
  'b90f6c7021e444a34a00fcba6a428762': 'Show Mask',
  '07e8695e6142a6e50b323c3d5aae2101': 'Draggable',
  '0fad5a56c8b4643f24797eaa616bef6f':
    'Define whether the pop-up box can be dragged and resized',
  '********************************': 'Bodyclassname class name',
  'cc70a816b7d61e7212d57335c0a15af5': 'Drop Down Button',
  '74bafe23b0be1a14aa218b396cb33bd0':
    'More buttons are displayed after you click the drop-down button.',
  '213a4e49568569943403ff31fff31ee5': 'Configure Drop-down Button Collection',
  'b5b57c711fd2d2309cc390a8b44d2b69': 'Button Text',
  '********************************': 'Mouseover',
  '4a757588f5aee8cd039b1d166b096d1a': 'Click External Area To Disable',
  '9951d740257c40978c238a683b1d4a80': 'Click Content To Disable',
  '205cb6cc6c8d37f3bed62d9c8bfae976': 'Unfold By Default',
  '697eb55e1c6cecf43e63a26232dda5b2':
    'The drop-down menu defaults to unfold after you select this option.',
  'b2aa282e908597d1d700c1f4de17b8aa': 'Menu Align',
  'e57996d3d771141f1b3080bbd8ad605b':
    'The button occupies the parent container width after you select this option.',
  '1ce673c48f29162208e75bc210307bfc': 'Display Style',
  'c9e265ec462b61887af6f58928923925': 'Hide Drop-down Icon',
  '938ac86e738246ccd0ca0224031f96af': 'Drop-down Menu',
  'ff9f6c2d74c413daa3cd6fb12f8dfd3e': 'Each',
  'f34111ff3694a6c6de6e31bef8ebadcb':
    'The feature renderer can be based on the current renderer for the loop output of variables.',
  '874268022baac239b06c40600d3ce080':
    '<%= data.index + 1 %>. Content: <%= data.item %>',
  '69bdc66bb88ac5b63053e2bb7db41801': 'Loop',
  'b7c16dedc4291d333fba7628ec9eb073':
    'If the container has a value, you do not need to configure a variable name. If it has not any value, you need to configure a variable name. It supports multiple levels, e.g., a.b indicates that the b’s attributes under the object a are associated. The target variable may be an array and can also be an object.',
  '4726ff4e62d3fcfa4b090aaefc393229': 'No content is available temporarily.',
  '5d5f9d49fcb2109f94a43590ef796ca7':
    'When no variable is associated or the target variable is not an array or object, it displays the placeholder information.',
  'f549581bf93c72ed69c37e906e04191d': 'Flex Layout',
  '6ab8332f3da284b137d54f6ba901e93c': 'flex layout',
  'ef5abdfc944546ddcbe10e9884cf5832': 'Column I',
  '874fdb7b3a5730910a4de1c58220c337': 'Column II',
  '3a76596e73fa265257ce90b7bed684c7': 'Column III',
  '5aefca559c5a41d10078e21e6d616825': 'Layout',
  '2fdc3722b88a2ba5077e0d11156ede6a':
    'Horizontal Distribution Method Of Child Nodes',
  '4ba6c2256050d805ae5cd1e0e84737cf': 'Starting Point Alignment',
  '56c17ba6a56c01706ae00a31611deb03': 'Center Alignment',
  'abeb360ab1e66534a041fb8b44e1a00e': 'End Alignment',
  'd5a3cb1cc31a0469b011abdbd3e947f7':
    'Even distribution (leave a blank at the starting and end points)',
  '85530444a72a840ee657e2df99970136':
    'Even distribution (alignment at the starting and end points)',
  '9aad08fbd356fb4279f0efa81b3d016e':
    'Even distribution (equal spacing between elements)',
  'ae558cbf4c35d381b6542f517f2e8dff': 'Even distribution (automatic stretch)',
  '70d39e84bc1ecefaf6e19cf78c9574fe': 'Vertical Positioning Of Child Nodes',
  'ed97c73866617b40a7b1215867e0f489': 'Baseline Alignment',
  '7ac1519928de413cfe36f5d2e0610430': 'Automatic Stretch',
  '8e15f51c9512fdbf4287794d6642a90b': 'Child Node Management',
  'bc78248b34b7bf18691e6d385e0f544b': 'Child Node Content',
  '197af5d5971778e3b80deb25182d63e3':
    '<span class="label label-default">Child node${index | plus}</span>',
  '023c4bfc60545a2668c2d5111171b5d8': 'CSS Of External Layer',
  'a4611da51ffee9140976d01668e45d45': 'Child Node Collection',
  '729a4cca5ed3504793c1f3a87d2b48b9': 'Button Group Select',
  '29513434492e5d19a9660e0a918befd1':
    'This option is used to display multiple buttons which appear as a whole visually. Meanwhile, it can be used as an option selector for form items.',
  '6edda84461bf13d38328cb401c8c23db': 'Option 1',
  '39692081e75ef73c6479fc25f8f10dfc': 'Option 2',
  'a457872a51628ccadfb9bcfa23428a98':
    'The button click and selection can be used as an option.',
  '755955372bcc0c7ebf161a656bc389b3': 'Value Change',
  '2fc76872efce1eabd3b74a3e4fd5b976':
    'Be triggered when the selected value changes.',
  '528609917710d813a55e5a6cecf1e458': 'Selected Value',
  '288f0c404c4e546aa3683ff5054e85e2': 'Clear',
  'c374a44713fa5ff5da2565beb398c7d3': 'Delete selected value',
  '8db57ba01ffb9bf29c1656de5f0208f5': 'Reset the value to the initial value.',
  '944908c981a86bfa0cfab9360ab38184':
    'Allow the button to occupy the parent container fully. The width of individual buttons is adaptive.',
  'ac3880323853de9adc4f66bc06d438ff': 'Button Style',
  '0b98b0bea3db6ae5b67a09c7bb2d032b': 'Button Selection Style',
  '55b45c73ae417c4dead67905b1550e85': 'Form Item',
  '3b49c8cece3f495f0e76b73823c68bfa': 'Button Collection',
  '012f602372cd2dbd639cd966c63e1f90': 'Toolbar',
  'b6872877e1eb5ddedd904c170db26024': 'Chained-Select',
  'fdf1664c0790d25f236bd596aef1acef':
    'Through the <code>source</code> fetch option, you can add the levels infinitely as long as there is a return result.',
  '556988a9dc1816dd979e96eb5cd19a85': 'Chained drop-down box',
  '********************************': 'Default Value',
  'da3ca8191fb919fb34e8e78fc6f2fc78': 'Enter Value In Options',
  'bc8d09093edd98769d5cb39e759aa498': 'Concatenated Value',
  '2646ee1ebb6922a5c9359de6cd3b3639':
    'After this option is enabled, concatenate the values of the selected options to get a value of the current form item. ',
  '1395eba8d9efe27aa1ecd1a45e3e5dcd': 'Get Option API',
  '4ea50507bf8b9ceb908677f30fb20e68': 'Option Label Field',
  'fe4c9c2eed1ad213040d84036c675454':
    'The default rendering option group gets the label variable in each item and uses it as the display text.',
  'be43687d4ed1d9e663c729e12618166d': 'Option Value Field',
  'f1e6b60c4b6df555a6b03f91033091f4':
    'The default rendering option group gets the value variable in each item and uses it as the form item value.',
  'f411d0f1f925d9b48d8c1d451bd809b1': 'Description',
  '2ef0fb6299da5954f5ea84a088684ee5': 'Option Description',
  '454e60f5759903d7d3dba58e3f9bd590': 'Checkbox',
  '********************************': 'Select Form',
  'c75fde0e2d329ce62f55cb1a207181ae':
    'Be triggered when the selection status changes.',
  'ddd6650e02f5a266c3df2703daf37270': 'Selection Status',
  'db0258df1ddbd88749b335aecdc8425e': 'Value Format',
  '53235c46364db079356d57da5870f967': 'Selected Value',
  '56f3150f1713a5e5c6e7c55fb0b79b75': 'Unselected Value',
  '7c7a88eb1bb4b40206c6c680bd8995a8': 'Default Selection',
  'db98f889ce6bc235e66bd4b2a788d137': 'Checkboxes',
  '********************************':
    'You can configure multiple select boxes through <code>options</code> and can also fetch options through the <code>source</code>.',
  '05f87b331e1c97691776d93a6598373f': 'Option A',
  'f38c0a46797523b11051e35ec0f82a42': 'Option B',
  '9c541222ced2435288c24b34f8ad1fb8': 'Select All',
  '05bef457e8350e1a5d8007cad41b70e5': 'Select All By Default',
  '84f31b24ffc8ea2b81d61a6f054b5bb6': 'Code',
  '08346c5bb99d8e3bfa406995b8c4f067':
    'The code editor is a monaco-editor, which supports {{@1}} and so on.',
  'ab0710b367acefa1d6a78e2338291e86': 'Get Focus',
  '4638e799b95e1b71edd55f278a6f707c':
    'Be triggered when the input box gets a focus.',
  '********************************': 'Current Code',
  'fc96a5f1b79cb734afe08e401b6ba5e7': 'Lose Focus',
  'c776ab86eb24f6b3db35114e43026f75':
    'Be triggered when the input box loses the focus.',
  '********************************': 'The input box gets a focus.',
  '********************************': 'Language',
  '44fe0e1bcabcea83d6a30e6af0fd42af': 'Full Screen ',
  'b3b97a293baac13db6367aba5539a09c': 'Control Size',
  '3386da5f56fac758ed0f18e024ecb943': 'Extra Big',
  '254bb8aa6b92d588d957a383df24db1e': 'Combo',
  '4db731c7d73988e40a01b4bf1a7f00d7':
    'Through the combination of multiple form items, you can configure whether to add and delete the initially set template.',
  'b58c7549c0246c55b9cac96383200338': 'Add',
  '8575b828c7320de82b9f99058aa1f55f':
    'Be triggered when you add a combination item',
  'aacd80d2c978abae7b955510a938788c': 'Value of the current combination item',
  '4933bd64bb23de03ca8ed246fa5509c5': 'Delete combination item',
  '0410242a74850f010a9a8061bc0cd891': 'Delete item index',
  '74333901ae9ad27a93487dc850e45609': 'Value of existing combination item',
  '95e09290c4e0f01323bb5abf301c950b': 'Change Tab',
  'ba2bd765f6c2e2b717139c5453884e14':
    'Be triggered when you set tabsMode to true and change the tab.',
  'b04c6cf9cb1212b9c173ddfeec129028': 'Tab Index',
  '055f2f284d2bdb15bd9e542ea9f93285': 'Fixed Member Type',
  'e3b9236d585eb9c93a074f264737cb65': 'Multiple Branches',
  '66e867eb73a118649800c0a064d0b5aa': 'Type Description',
  '5aa528690fd771f89683a7f00868f39e': 'Branch Management',
  '35b65a5a3f8d721e12cae310463d69d6': 'Hit Conditions',
  '7341e991c8e8cfec68d31ffe0e06e429': 'For example: this.type === "text"',
  '85485d70be6b380294428018e54fc9b9':
    'Judge whether to use this branch according to the member’s data.',
  '50bfed6ada3e7d0ef4d20eb727b3d7df': 'Configure sub-form item',
  'd68162ea1904f627b033fe3953da295d': 'Configure sub-form collection',
  'aab09f676645f2651655a711d5e3327c': 'Add initial value',
  'dda5ffc9ecbad13d424346890bacca6a': 'Whether to change its type',
  '8a0c77e91392d70df522b55eb4d403e2': 'Multi-line Mode',
  '26343b8bcb694fa5e333b47d8b663d1c': 'Whether to go to a newline',
  '86032735a191d117b02111e447494380': 'Whether to ',
  '28a59fee1a4714493a8d6cec05c1417b':
    'The data structure in the default array is an object. If there is a form item only, you can configure the value. Thus, the value contained in the array is the value of the form item.  ',
  '7f2579cabd4d654458a720eed517a37d': 'Whether to add',
  'ea1e5695bf682ea3b31aba0c35198ae3': 'Add button text',
  '1ab31449faaaeeeb7200d724eab9a7ab': 'Whether to delete',
  'fa3e9f9e49f5a81c998f949155f86b50': 'Request sent before the deletion',
  'f962922d46ef18e68127642e5c00165a': 'Deletion confirmation prompt',
  'fc763fd5ddf637fe4ba1ac59e10b8d3a': 'Confirm to delete the previous request',
  'f41a714bc8b26dc27a93a07c44e329a8': 'Drag the sorted prompt text',
  '91396e9bc25c9e8b63907fe22408e2bb':
    'You can adjust the sequence by dragging the [Change] button in each line.',
  '8ee004bdd55b578acdb10b1bcd3fa4f7': 'Remove Border',
  'e9cbda74a1ffc06228fca68e4d16c4dd': 'Limit Min Number',
  '********************************': 'Limit Max Number',
  'b8ccbc166c72b2eb54aac1332c99fb49':
    'A prompt appears in case of failure to limit the sub-form item.',
  'b34cbe877b2c8464c625858fcf19f4eb':
    'A prompt appears in case of failure to verify the minimum length.',
  'ea2474ff679195d9b54bd5ff3384fdfd':
    'A prompt appears in case of failure to verify the maximum length.',
  '1c5b9cb245f04413a2d888bd59442097':
    'Whether to fill in the parent variable with the same name automatically.',
  'e673084b4261d10104d27ae9e4d014a4': 'Adopt the Tabs display mode',
  '6e7bd650f763085de3bddd51a8d6aa88': 'Tabs Display Mode',
  'fd6e80f1e0199d6ecc3ee81ae04aa9ef': 'Normal',
  '2dd25b8c21efbfee4a198787810d65d8': 'Inlink',
  'fc6c9fa3af230165c39fb314c086be22': 'Template for generating tab title',
  '9ff4713f6b17e96e9cd76650fd5892be': 'Lazy Loading',
  '93dc24d4ad01981d5994ba7f8ffaf345':
    'When a lag occurs if there are much data, you can enable this configuration item.',
  '98d52b737d6bc171b6d5bad9a42f6e23': 'Strict Mode',
  'cb5ca128b6c78a90f26673e21b0b3f28':
    'If you want to transit the value of the environment variable into Combo, disable this option.',
  'bc91f4844843d6c8ec1acb78a1f1aba4': 'Configure synchronization field',
  '07bf6c08bb5f0bdb32dbfecc7d3fe290':
    'If the Combo level is deep, the data acquisition at the bottom level is out of synchronization with that at the external level. However, configuring this attribute of the combo can achieve the synchronization.',
  '3f2176cdae8a4ed6a4c4eaff002a3b24': 'Allow to leave this field blank',
  'ead7156521ca11acb7ca7bdf4c9c87f6':
    'If the sub-form item is configured with a validator and is in the single-entry mode, the user can select to clear it (leave it blank).',
  '7ab968b8219f6c348478da255ebcbcb9': 'CSS configuration of individual columns',
  '6e87bfe16db746db13966f0d7552b052': 'Condition Builder',
  'ab7f2096d3ea8aa85f453b204bfbc493':
    'It is used to set the complicated combination condition. It allows you to add conditions and groups, set the combination method, and drag the sorting feature and other features.',
  'ae5e21c7b57aaaff2071b757f35dbb3e':
    'It allows the user to enter the query conditions. Thus, the backend device can generate query where according to the data. ',
  '55d4790c5d819cd0462cbe89561b0dd4': 'Number',
  '97b0b6499334ed889b372513290a2a52': 'Boolean',
  '0c3bf4fce50589b1073baf15f8a00d36': 'Date And Time',
  '6d6b4f2bbd2fd0b5faee33673d7f06ae': 'Quick Start-condition Combination',
  '22ed9ec0e00b5deab2c335ad1fd2e343': 'Condition Type',
  'e996419dedc2f1ffd96e7a0e808127d0': 'Field Name',
  'c322edb884724d04842fc35f4d29a24e': 'Min Value',
  '5da893141114a59da868052b3a17a79a': 'Max Value',
  'd26404c10871481ab6bbb4837a34ae95': 'Step',
  'dbd5b99c34260412f10835d82b59919c': 'Date Display Format',
  '94575fbef23460cb02524d20d152d728': 'Time Display Format',
  'f20a86701d87369e5166c28a71b8b8cd':
    'You can fetch the field option remotely. It supports the API or data mapping.',
  'ed4ad0f9e6374d6915ce3d2c0dec7c2c': 'Operator',
  '61260d9386fd95a268dfc93d977c2706': 'Form Item Container',
  'c5739a29e7c403fc212608cefe70cf29': 'Element Collection',
  'a00f44e570f896de5490cba9d2462951': 'Insert A New Element',
  'a9a8efb2541ee6f89ea7b83e610ebf7f': 'Add Element',
  'bac53d3c739f7fb3327704efd5b40eee': 'Diff Editor',
  '7b4fe9415d80d3694d2f630411ac7e9c':
    'Compare the codes on both sides. The supportive language includes {@1}} and so on.',
  '0517b74cbe247a903faf40991a5a335f': 'diff editor',
  '42f04184315801c372989820106cc4ee':
    'Be triggered when the right input box gets a focus.',
  '********************************':
    'Be triggered when the right input box loses the focus.',
  '********************************':
    'Get the focus which lies on the right editor panel.',
  '2a69150aa382f6a309c03a96145d4266': 'Left Default Value',
  '04b8c503707c34f9c275d349275787f2':
    'It allows you to use ${xxx} to get the variable.',
  'a71e655ab56c9962742f72623f67ca76': 'Right Default Value',
  '460bc46ffeb31b737669e2312c5bae72': 'Field Set',
  'a89cd8150a1dbc60ac7063580e0852e2':
    'Through the combination of multiple form items, you can configure whether to fold',
  'a13a2fa224ca5b6f44d5aee33ec29d58': 'Text 1',
  '37dd6f28ffb87d8907a286e0ef4dc7fe': 'Text 2',
  'd6c40a2ee219c010edbcdaa2eeb94ddd': 'Sub-form Item',
  'ec2a8ec81d1d2588db8c7827ba99e7d3': 'To Fold or Not',
  'f315bd4984fd09c30581674d28287f12': 'To Fold or Not By Default',
  'ab2d2b13794ae1e2d7bf3bcd5af55dce': 'Control Style',
  '23ecf42cada8bf2715792d718544d107': 'xs',
  'e5a226534fb99ab260865b936d3c85ba': 'CSS Of Content Area ',
  '********************************':
    'Click to add a sub-form item in the left component panel',
  '1297c46c0ea697a0041c3899b15d69c6': 'Add Sub-form Item',
  'cd948961f71f87ecc72b251147d96144': 'Control Type',
  '0766a6467bed7f2840871a5f0638669d': 'Single-line Text Box',
  '********************************': 'Multi-line Text',
  '829abe5a8dcd0579a340d20636b59276': 'Group',
  '9597dcaf432ceba92a160d61cb1ef65f': 'Number Input',
  '9913107b19cb6012250134ff91377430': 'Radio',
  '006ded9fa277cf030592021f595a07d5': 'Select',
  'a6beb974cc0b50eebd18120b8110a88b': 'Switch',
  '481e034e6026969aae4ce7ce7c8a7b6f': 'Input File',
  '6bfb9bb2218ff32b6139e98bc93707c0': 'Input Image',
  '24b6d4c0892a8f3ee2a982e3ab0afe38': 'Rich Text',
  'fdf6f7f6497556de440fe434b721ee99': 'Display Name',
  '712538d3e674792ec94feb9a5eb2cc0a': 'Submit Field Name',
  'eee1e2258d7ea163fec625ee44be9637': 'Form',
  '10b3d542748da2043f79395bfa2ab35f':
    'It is used to create, edit or display the data. To configure the initialization interface, you can load the data remotely. To configure the submission interface, you can send the data to the remote device. Additionally, you can submit the data to other components. Thus, it is possible to build communication with other components.',
  '5ac57ce6df8c2a19668b7429aebd9f33': 'Input Text',
  '1b6f9adf1e6a41a18b9a76407d945618': 'Create form quickly',
  '380c80efc8d38510d31f50578690b781': 'Submission address',
  'ffc2c1671eb7e3f6751006cd7a9961f4': 'Text and input box display mode',
  '********************************': 'Top And Bottom',
  '3720b9ef8053b7b8a54c7d8ace051912': 'Left And Right Placement',
  '9d68944682609cb230534195ca7c62ae': 'Form Control',
  'cf993a1d9c0352215055d180aca60b97': 'Control In The Group',
  '3673ed1983c1be059126e3715fc34922': 'Form Collection',
  '741e41f5247b1541bde22c00e65f4401': 'Initialization Completed',
  'f75e31cd2e3bfb79be420b0e61a533cc':
    'Be triggered when the request to the remote initialization interface is sent successfully',
  '211f02318e3cceff5ee50d4949a5c8ed':
    'initApi Initialization data returned from the remote request',
  'b2a5322c8dbc0d8343315cafbd39b7ce': 'Value Change',
  'd6fc04abf4889a864bea240d6b67963a':
    'Be triggered when the form value changes',
  '66f1115691b0a3e434dd3e8a6e733730': 'Current Form Data',
  '7233c9cadee5096097673c9590eae9b8': 'The form item is checked successfully',
  '6611594c527756e23d4044f908fedfa9':
    'Be triggered after the form item check is successful',
  'e00a32d415785d5a5d43a593d26cbaa0': 'The form item check fails',
  'd4c12cea41e1595329358edb365c5f5b':
    'Be triggered after the form item check fails',
  '368f9bab722b255f1fdb669a89f0c594': 'The form check is successful',
  '35e69ab84129d721229bc5b48afdedd2':
    'Be triggered after the form check is successful',
  '641fc404690a43cb13e4666ce272974f': 'The form check fails',
  '5d7dfa5bf9bcd278f06fa37e482a2c35': 'Be triggered after the form check fails',
  '23b62e9cbc868e024b80d2e3fad80ac7': 'Successful submission',
  '8c9d9d2594b9cc39294c6939bd08a5d5':
    'Be triggered after the form submission request is successful',
  '18344d8a27aa678e401d5e575d4efe99': 'Data after the submission is successful',
  'f5d008dea7d2e953195a5588dea3c8e4': 'Failure to submit',
  '86555672b37841b639311e7d49f0f593':
    'Be triggered after the form submission fails',
  '17aa713bc661f98e585ec3725d6d4f0d':
    'Error information returned after the submission fails',
  '4e34003861eee3de1e0c9c1222249bbb': 'Submit Form',
  'a360c5d4e723ad78a5e52eb1f5f3f2a2': 'Trigger form submission',
  '1b6f5cc49e71c90a5b85a796285e3135': 'Reset Form',
  '35de8c264c2a87836ccbf302b4ee673f': 'Trigger form resetting',
  '4a3deab45c0a7218b8ae58a33fd24c28': 'Clear Form',
  'c18255cd6a048da86045c59a65fdc92d': 'Trigger form clearing',
  '17f2bf425eeb7d20d79c595344e9dc94': 'Check Form',
  '27f35bc7086bf54e27e254f5d77c3168': 'Trigger form check',
  '60ad7d0d170b973ab9cdb0b23e636704': 'Submit button name',
  'a834505b13627a2e9eb4e0a0c2746e5c':
    'It is valid when no button is customized.',
  'cbef9ad64297efd7657d5b67b2112751': 'Automatic Focus',
  'ded8caee55c05aa0f1a4e6a197c480bd':
    'After the setting, the first form item that you can input in the form gets focused',
  'c8801f299681b3080968effcb13a57fe':
    'Disable the "Enter" key to submit the form',
  '4fe8162504ae5fb955c30863d88834fa':
    'After the setting, you cannot submit the form through the "Enter" key on the keyboard',
  '6232c762a93aeb3c89cc759c06802610': 'Reset form after the submission',
  '7d61784cd115d333f01a3c0c46408a1c':
    'After the form submission, restore the value of all form items to the initial value',
  '49086a13c74f262de398e60f448ab056':
    'Submit the form once after the initialization',
  '1babd035cabfeb997ac3eee3f938d794':
    'With this option enabled, trigger the one-time submission after the form initialization is complete',
  '0d1fb15904862f5fd2d81d2fc8f371d6':
    'Whether to close the dialog after the form submission ',
  'e91209a4a78c0b34c26b681b49e0681a': 'Submit the form to another component',
  '4133d2c3613ece9792c90d185ec32306':
    'You can submit the value of the current form to the target component by setting the attribute. Instead, the value is not saved by the interface. Enter the <code>name</code>  attribute of the target component. Multiple components are separated with commas. When the <code>target</code> is <code>window</code>, the form data is attached to the address bar.',
  '7653297de32f34fdec0dd0653aebf358': 'Redirection',
  '6d00e21637c382cbd4d949b7735a2e41':
    'When the value is set, redirect to the target address after the form submission.',
  '672b63d7523095b949f5fad233fa0cde': 'Whether to enable local cache',
  '8a0544ca2f7ddaf8768ab599858315a6':
    'With this option enabled, the form data is cached in the browser. Changing the page or closing the popout does not clear the data in the current form.',
  'ce2457fe99197e0fe81b6fb08e3eaf0e':
    'Clear local caching after the submission is successful',
  'bc01ee1a28f980c298679610fe4d2d66':
    'After you enable the local cache and the configuration item and the form submission is successful, clear the cached data of the current form in the browser. ',
  'f5e55fb88f5adc71beb0b1fff60face6': 'Form combination check',
  '8101a0aec7eba32e633e3cc29f4b7ede': 'Check Rule',
  '80ce5ea9ac2c3001e6e8ea3175ecc12d': 'Error Prompt',
  'ff7cc75cc43c25c823d05d87cb8190b0': 'Saving API',
  'd325cfafec323a62463af3d3da9b6ede': 'It is used to save the form data.',
  'dd1b9892e274b16540aeda961437870d': 'Is the asynchronous mode adopted?',
  'e638bd32b4342dfc7a15990a79f588ae': 'What is the asynchronous mode?',
  '6df230c8f18709575777172f0a9a588c':
    'The asynchronous mode is mainly used to solve the request timeout problem. After the asynchronous mode is enabled, the program periodically polls to request for an additional interface after the request is complete to consult whether the operation is complete. Thus, the interface can return the result quickly without the need to complete process actually.',
  'daf3aec137ac74941adb1b1d3e3dd1d3': 'Asynchronous detection interface',
  '0705e4aba9f22ae70d906e2201a4a68d':
    'After this attribute is set and the form is submitted and sent to the saving interface, it continues to poll the request for this interface until the returned finished attribute is true.',
  'b4bc91701b86fe8543d649e97daea602': 'Initialization Interface',
  '258c437ef67e5ef12f3a766ff1802f85': 'It is used to initialize the form data',
  'e8c46074d8432532cac25eba56bca354': 'Is the asynchronous mode adopted?',
  'd2af24c0f76cf325f1c8fa939576c379':
    'After this property is set and the form requests initApi, it continues to poll the request for this interface until the returned finished attribute is true.',
  'ab94e2c30b3cf0fd11eea477f70dcbaf': 'Initialization failed',
  'b64292a1903bd969d0e3a558c334f5bf': 'Default message information',
  '8a5e590f69e1ae52d86396410ceeee5a':
    'You cannot set this option. The msg field returned from the interface has a higher priority.',
  '3b108349b93f7c8c4e2346f8d48c092a': 'The saving is successful',
  '6de920b4e4e08b261cda928d9beefab4': 'Failure to save',
  'b66ef8966dad62d377bc5310d8b88e7f': 'Prompt for successful saving',
  'cf538bbe1fb431f9e2668da4d84cfadf': 'Prompt for failure to save',
  '6509e435d66db2a105b2444b1d3d0db1': 'Prompt for verification failure',
  '02e977ba56a7ccc59c277d2d5be43ba0': 'It is packaged with Panel.',
  '1cb01363e2463443bc8105f544ce2736':
    'With this option disabled, the form can display the form item only. The title and action bar does not appear.',
  '1fdadb49bcabfdc36a4a9e020f597f4d': 'Display Mode',
  '57d348e83d929dd4cb3bab04debc93a5': 'CSS Of Panel',
  '9b26fa2b5943c827eea294e79b1427fa': 'You can set Panel--info.',
  '85ca671c1bb6de5a15456a9692d2edf4': 'Enable Debugging',
  'f24b7483069b44490a6379be5b90f4e3':
    'Indicate that the data of the current form is at the top of the form',
  'eb332076d766c2e817285f0a6d4860b9': 'Formula',
  '000cf7353ce03857c671e89c91b35464':
    'Calculate the specified variable value through the formula and apply its result in the specified variable',
  '9da7578f9329ccaee1bbdf6a766744fd': 'Calculation Formula',
  '77562ec3db28683ec71612a11e6b39ef':
    'The formula calculation result is applied in the variable corresponding to this field name.',
  'c80c42a0be1b39ed899b1f5560875cf8':
    'It supports the JS expression, such as: <code>data.var_a + 2</code>. That is, when the form item  <code>var_a</code> changes, it sets the current form item to a value of <code>var_a + 2</code> automatically. If it is set to a string, it should be placed inside quotation marks.',
  '89a8549c2ed7fc23a683599731d92b22': 'Application Conditions',
  '8add6799ceff24eb041c5a6a92bb1694':
    'It supports the following conditions. For example, the <code>\\${xxx}</code> or <code>data.xxx == "a"</code> expression is used to configure the application conditions. When the application conditions are satisfied, you can set the calculation result to the target variable.  ',
  '3df193f5d4f652a4bac331a69761041b': 'Whether to apply it initially',
  '86cebf86c66c6a4e6731b840c3967ab0':
    'Whether to run the formula result during initialization and set it to the target variable.',
  'aaff9b44fa3c84058d6bec9888b7b39f': 'Whether to apply it automatically',
  '677568b4f6505bdd63387137bfcbf35a':
    'Whether to automatically calculate the formula result. In case of any change, automatically set it to the target variable. <br />With this option disabled, you can press the button to trigger the operation.',
  '222c7bc60f06552b019d71bce57013ed': 'Feature component (formula)',
  'bb09a378529edac439abc2edf05c10d7': 'Form Group',
  '39d36f049a794eb8df305b5ca7710c36':
    'Display multiple form items horizontally',
  '32f6f7f8164c7f78e4b46eb50c158ab9': 'Sub-form',
  '400fbff5e744d3b7317dd130eaad723e':
    'Click to add a form item in the left component panel',
  'bd1aded1c983ab7fcf3990f0dc31047c': 'Column width configuration',
  'a170a375b264f7fe0c02a7ca8c268784': 'Width Setting',
  'daa0f354e189c0da577ea25be13f874d': 'Adaption Width',
  '4db804afe5c99f7ca4fe988ada35c77f': 'Adaption Content',
  'f1d4ff50f3828f9b73412e7d94e6dd6e': 'Custom',
  'd5d885add2551454955bd70411769c88': 'Width Ratio',
  'ff7e66f1feaaed3260b6e8ef432efc79': 'Spacing Size',
  '1b3408880b73544a3fad501dafbb71e6': 'Configuration of column CSS',
  '551481accddd97e18d7152f511fb8987': 'Divided into several rows',
  'd27d7b1542d91641d1d232c9f58b96d1': 'Hide Domain',
  'd50b9a7291d45d1da8633439427afaef': 'Hide Form Item',
  '4f6595b5030e171fcead42d321ba767f': 'Feature component (hide field)',
  'b6946d13d670fc49b721b626bca426b7': 'Array Input Box',
  '********************************':
    'The array input box allows you to customize the member input type. In fact, it is a method of flatting the Combo flat value, which can be replaced by combo directly. ',
  '02cc4f8f5a9aefbc03c778f7a5c989c7': 'Enter',
  'a4b72cd5d7c953c57b00e3597b9ef666': 'Array Box',
  '********************************': 'Enable Drag And Sort',
  '1362211a6bfc8cc4130d54643e8e9732':
    'This option allows you to drag and sort the prompt text',
  'ba6a6f2cdb46e8f80fbfd4fed20eafac': 'Input City',
  '20700607ccdb7b6c83f3b5bc525e6975':
    'This option allows you to select a region or city',
  'ee167d4c74e600248aefe9d0ba474705': 'Change in selected value',
  '770fe9e7899cd310b035ef50a39ab2ae': 'Reset to default value',
  '9633e6be5ecb44fbd69b3e8328cc8430': 'Allows you to select a region',
  'eec86dd33ae6186937da563fcde3555e': 'Allows you to select a city',
  '95892a76bc26c2db31087c9914e442b4': 'Whether a search box appears',
  '********************************': 'Input Color',
  '5a9e72d006165ae3dacdbd96f931f70e':
    'It supports <code>hex, hls, rgb, and rgba</code> formats. It defaults to the <code>hex</code> format.',
  '6b36c6f7ec834692ec6c8e3816349fdd': 'Color',
  '277daf93adca7889605057f566b584bf': 'Change in input box content',
  '********************************': 'The input box loses focus.',
  '********************************': 'Clear input box content',
  '********************************': 'Color Picker',
  '1b25b3b1b5076f0c2e6bd12d73c56f79': 'Hide Color Palette',
  '9b161db0e2e749c1106c702c8097d380':
    'With this option enabled, disable manual color input. Thus, you can select a color among alternative colors only.',
  '05a64e0df1490a5db391e7a43eced6e0': 'Alternative Colors',
  '6cbabc9a4cc07c1e26bb131c02833f8d':
    'Alternative colors at the bottom of the color picker',
  '79d7c8cd739a1f94c7bb30c74323eaa7': 'X (time stamp)',
  '84c7af622906c4e9d62bbf905d6ee517': 'X (ms time stamp)',
  'aa2fb1b6cffd7c9783c11047adcdbae4': 'Select date',
  '8935dbb864f032bacc618b0938887ad7': 'YYYY MM DD',
  'ff91b28a49de6fd998dea31598198a73': 'Select date and time',
  '3e253325914594e1dc483c0b64ea7643': 'YYYY MM DD HH mm ss',
  'fbd05a89ca037ca9e5a6af4c2206d488': 'Please select time',
  '90696835bfa70d38b02ef5533d33c88d': 'HH mm',
  '6154f88c0ac259ace41b0a5be45d753c': 'HH mm ss',
  '904393383755b88e61f5c242eafabdda': 'Select month',
  '55546b74d8819d49cccda6277f3a3282': 'Select quarter',
  '13ef7828361c8aebbf6ebe09370f5e41': 'Select year',
  '4be42a8a2cbb9088b4d051cfd824d68c':
    'It supports the use of relative values, such as <code>now, +1day, -2weeks, +1hours, and +2years</code>. Meanwhile, it supports such variables as <code>\\${start_date}</code>.',
  'be28cd64f978dd70c1cd345ebe751bca':
    'Date box, input-datetime, date and time box, input-time, time box, input-month, month box, input-quarter, quarter box, input-year, and year box',
  '********************************':
    'This option allows you to select year, month, and date. It supports the setting of relative values, e.g., <code>+2days</code> later.',
  'e54ca3223a36c6489de0fb8a39d4049f': 'Date Configuration',
  '96f9d9fc9cef8b18e3cd1cf9077147d1':
    'Be triggered when the time value changes',
  '0a72b3858efffaa1bab685fa840b701b': 'Time Value',
  'f6db3514c72bdc34922f137a8a92b997':
    'Be triggered when the input box gets a focus (non-embedded mode)',
  '********************************':
    'Be triggered when the input box loses the focus (non-embedded mode)',
  'e02d111d524de97e8622121f7ce845cf':
    'Before the data submission, format the data according to the setting. Refer to the format application in <a href="https://momentjs.com/" target="_blank">moment</a>.',
  'ecd1a3cadcf1d55250afafbbde767250': 'Display Format',
  '182503224cfaf1f63c566f13da56a2a4':
    'Refer to the format application in <a href=https://momentjs.com/ target=_blank>moment</a>.',
  'bf8f46b5c3b309f666965a74d57ac093':
    'It supports the use of relative values, such as <code>now, +1day, -2weeks, +1hours, and +2years</code>.',
  'f0789e79d48f135e5d870753f7a85d05': 'Mode',
  'a553741d5ebb9c80d7d2a63b202cf4b8': 'Floating Layer',
  'c6e1b91d329a61b691d0d5d2eb343ddd': 'Embedding',
  'a2847d82fc998cbe25447b14d113234b': 'Select date range',
  '3f9c3a9eb55b7116bcaa6b614ecb38be': 'Select date and time range',
  'ddc4a982defd88cd164c61da914819e1': 'Select time range',
  'c899221db27c8b3606ce7c807f0765f2': 'Select month range',
  'c09ddfc72d3c34ae6aa76d5a457cb403': 'Select quarter range',
  'cb6deedf9cd4a0b65abd70798cfed85e': 'Select year range',
  '7866226eb814f681dcc4037e7489aab8': 'Date Range',
  '28de3d73d839b616bd617b3636669780':
    'Date range box, input-datetime-range, date and time range, input-time-range, time range, input-month-range, month range, input-quarter-range, quarter range, input-year-range, year range',
  '9024ff398faf8340b92bf0208431973b':
    'For the date range, you can set the minimum and maximum dates through <code>minDate</code> and <code>maxDate</code>.',
  'e7271563debf3c7bcb85e23816c35acb': 'Min Span',
  '6f44927b5ffddc499e4dc26889169143': 'e.g., 2 days',
  '6da95498bea887b2ea7c6c2bb4b2fdc3': 'Max Span',
  '61fd7e3e86b168be41ac2e37237e1788': 'e.g., 1 year',
  '2f8d6f1584b73bfc6dada44526abb502': 'Yesterday',
  '0dc86a275145ad5a7774e594f0d94a06': 'This Week',
  '79abd4ee3661ff70c7d79716c8aaed83': 'Previous Week',
  '73bef6f0c997ffe317c868c4716baab0': 'Latest 7 days',
  '8f2a5a5f6e3777c7a0e1ce9484a2f7d7': 'This Month',
  'd5578d93388a5b2552316418cd1124da': 'Previous Month',
  'ffb2b7fbf227d9d21e03d0f160fb2a34': 'This Quarter',
  'dd657784cc0d5511d2f25459e49ead1a': 'Previous Quarter',
  'd3dbc7a7fd9fc5ccd168084c8579c1ec': 'This Year',
  '2f92fc7bf6ef3dd57c514d0797fe2f1e': 'Start Placeholder',
  '592c59589144ddc68d05d528da17dcdc': 'Start Time',
  'a04c4894d83323d187f65cc357fa646e': 'End Placeholder',
  'f782779e8b5d709462c8e71e0d9019f2': 'End Time',
  '59a81f21a829e24e9a765c4d6e509e68': 'Select year, month, date, and time',
  '5eb8cb70e4dc97622c967d7c7efd6566': 'Date And Time Range',
  '7f4466c0a16a1808b5ee398ce55997ab':
    'For the date and time range, you can set the minimum and maximum dates through <code>minDate</code> and <code>maxDate</code>.',
  'ab37cc3baa3ec204bd7ebfa450568c40': 'Email Box',
  '********************************':
    'Verify whether the input meets the Email format',
  '3bc5e602b2d4c7fffe79258e2ac6952e': 'Email',
  '899339c1133a6100a1b223680d26692c': 'Upload Excel',
  'b0e4a21f29af38986eebbbf867eee31b': 'Automatically parse Excel',
  '7caadb59b5892d107a909816b6571c66':
    'excel Be triggered after the upload and parsing are complete',
  'eb3daf37c93971703187ecbacb00c74e': 'excel Parsed data',
  'd9435aa8028acfc660276c4e0af5536a': 'Parsing Mode',
  'b14494137c805dc66bdc9ed88d7fd2de': 'Object',
  '0e67d4b0e351b00f4bea9840aa6b99d7': 'Array',
  'fe5c59cbac3d45314ec6397d9f75789a': 'Parse All Sheets',
  '50b10b178196378f4359ce11bbc31372': 'Parse Sheet To Text',
  '1647e4bfb548f2e8468d10b4b3bfbe21': 'Include Empty Content',
  'ec1f230a0181d79b37967a455b1f3515':
    'You can upload several files. Also, you can configure whether to automatically upload files and to upload large files by dividing them into multiple parts ',
  '8dccb3d6633a85edb21fa52599211654':
    'It is be triggered when the upload file value changes. It can also be triggered in case of failure to upload.',
  '5560230c43dd8d8c2b4a62d829f152b3': 'Uploaded File',
  '7dcb3fb3ccc93369798d4b6950e96058': 'Remove File',
  '03d585240162dad662a0a6b5d90a4692': 'Be triggered when you remove a file',
  '6775136a73e41c1b2cb4ab025773e326': 'Removed File',
  'a7699ba73144aad544eb9ac49f82749d': 'Uploaded Successfully',
  'e309c24c2b07e5970208a82388f1d88e':
    'Be triggered when the file is uploaded successfully',
  'a27518f50ea00aaacb2c4e52f113eeb1':
    'Result data returned after the remote upload request is successful',
  '54e5de428ca9d59119d4624706215a4d': 'The Upload Failed',
  '4855bc3f3d06b9cf58d14b61839c5a51': 'Be triggered if the file upload fails',
  '236dc056d6800bf423df47520637c52d':
    'Error information returned after the remote upload request fails',
  '36d40a48f3da92af9fe55ee77cfae46f': 'Clear Data',
  '18843505278637e44a29e7e1f39e0b06': 'Clear Selected Files',
  'cf6e87cb9edfa398ccfc3db377e34ca4': 'Button Name',
  '0ec0e6e04b9e918939ac8e0daf407b75': 'Max Number',
  '0a9dced8856958fbd05508d1218f8090': 'Max Volume',
  '********************************': 'Submission method',
  'd28879b6a8a4ddb62bf6f2ab59303df7': 'Submitted together with the form',
  '3a76423b7ae40b0fa8b0bedb470cce7e': 'Separate Upload',
  '7c6722203327e8173be987f36fadf610': 'Data Format',
  'fde1ab2f504097f3f717acbb653b4f09':
    '${formType ? asBase64 ? "You can use it for small files. By default, the file download address is submitted to the form. After setting, the base64 format string of the file content is submitted to the form." : "The File control does not take over the file upload. Thus, the file upload is directly completed by the saving interface of the form. You can choose either of this option and Base64 (什么和Base64 ？？？)" : ""}',
  '6168fb08fe64663a502a132c5589b73d': 'Binary',
  '7245fe895fa1cfc42b5217a3de314565': 'Automatic Upload',
  'cf9e4c80962e712eaa55551cccff317e': 'Enable Block',
  '454dbf9b835af8627d4dfff2903298b7': 'Block Size',
  '2af0c8b5999a1d243ec5fe503da20026': 'Block Preparation Interface',
  '5548252bd28fc1d217d13a9e5771ecb0':
    'It is used for the block preparation. A file can be called once. If an error occurs, the subsequent block upload interrupts.',
  'd7832abe5b9ce8e41537b4964fd7cf70': 'Block Upload Interface',
  '14008d63c109cc0d0c4ba305671800d2':
    'It is used to receive each uploaded block. A large file is divided into multiple blocks according to the chunkSize and then each block is uploaded by calling this interface.',
  '70c935fa7ae03aeb1ff87b878e16841f': 'Upload Completion Interface',
  'c88aaeddb5aa95c7627d84df33929e3f':
    'After all blocks are uploaded, the `eTag` information collected for the uploaded files is merged. Then, you can request the backend to complete the file upload. ',
  '1e4dc4d5f4a3a95ddc349147d4d8cd39': 'File Receiver',
  '258d9e27231b06769dd584a3365545ba':
    'By default, this option is left blank. The files are uploaded to bos. You can set your bos address in the system configuration.',
  '28b988ce6ae6dd62d24bcb8a52119179': 'File Type',
  'f0a37d6f810c73a2f936f33035d99056':
    'Enter the file suffix. If there are several types, they are separated with <code>,</code>.',
  '58892b7a6a785706712761d5aebb4639': 'Template Link',
  'af4b910df15b00ba7fb5163558389cfe':
    'It applies to the scenarios that have upload format requirements, such as excel upload format. Also, it provides users with a template download entry.',
  '26d384ebe61b6ffe0e64310331f9e998': 'Drag To Upload',
  '644d03767c8148de6651cc6b00b0173f': 'Selection Button',
  'd1b06a828d05b0ff72328d50b16a8443': 'Upload Button',
  '0d4ebf2f4a10336011cfe0466b29ba5c': 'Input Group',
  '5b7363cba6bad37b6614809e9bd90746':
    'The input combination supports several types of control combinations.',
  '13370c4a5c95eff126068be5cfd2a9df': 'Input Combination',
  'ff10d1d90be63fbcdc7377435bf18f96': 'Input Combination',
  '0a7f8e17f8487c3715a154bea698778e':
    'This option allows you to crop the image. Also, it can limit the width, height, and size of the image. It supports automatic upload and upload of multiple images.',
  '295afc11c5fad1a6d97ae174abfcc5bf': 'File Value Change',
  'd4633ba7a30303a59891d051a7715cc9':
    'With this option enabled, you cannot enable the crop feature at the same time',
  '22616e32a895c56187921ac6cc673f51': 'Max Image Volume',
  '13446481eaf0a047d8fddf159d981a7c':
    'The upload is rejected when the file exceeds the size limit. The size is expressed in bytes.',
  'aeae3dd09f5cc972944a892802244483': 'Max Number Of Pictures',
  'eec48d0e729f7260544646c60ef6580d':
    'Upload is not allowed when the number exceeds',
  'b582587147013afc52c58de5fe34d7d7': 'File Reception Interface',
  'd24bada35447c81d7d7ddec13c44b576':
    'It is a file reception interface. If this option is left blank, the files are uploaded to hiphoto.',
  '7c5e7ad8954effd97cb78dcb0e8f99d9': 'Image Type',
  'f816b3decb401dcd0c22db7f104beccc':
    'Enter the image suffix or <code>MimeType</code>. If there are several types of images, they are separated with <code>,</code>.',
  '99e6b6011bacaabc18aeac70390252a3': 'Placeholder Image',
  '886df74c6a38c09891f46bd029490ae7': 'Whether to turn on fixed size',
  '037805d2f16cfe69e3145c9f436c06ef': 'Hide Upload Button',
  '80f7e6960804df47856301e7ad8bef6d': 'Enable Compression',
  '2c4a34adf66f12d0a08716383304a66e':
    'It is implemented by hiphoto. For the custom interface, it is invalid.',
  '5d8ff202988e28795bc72ab60d67d79d': 'Compression Configuration',
  'd4efa8f599c1effe67bf6a7f38a9c40d': 'Whether to display compression option',
  'f8a68f636880acd097317f16aeacaedc': 'Turn On Cropping',
  '********************************':
    'With this option enabled, you cannot enable the multi-select mode.',
  'e09f3466f78e62c1494fb36816950501': 'Cropping Ratio',
  'ea172e50aedd5afb561ef02ca119821c': 'Whether it can be rotated when cutting',
  'ae948ea02f5abf3c6409b1c3fe3a2295': 'No during cropping (scalable)',
  '4697838df60efb48d857c12387c6d795': 'Crop region limit',
  'bc436447f54b10a9ac3f0ee3e1b863dc': 'No limit',
  '7ccc0313ac4c40b364f482341ecc3bb8': 'Drawing Area',
  '4db4c540f05524c38e9431173736517b': 'Image Limit',
  '93f0c741d286a0353f95afa4a8aeff64': 'Width Limited',
  '5738784edf25dd7c3e40e0c1af4dc80a': 'Height Limited',
  '367e853038270c93e2c35a2bfdf0bcb5': 'Limit Max Width',
  '********************************': 'Limit Max Height',
  '********************************': 'Limit Min Width',
  '484abb6891d6ea842c58e5ada6dbf411': 'Limit Min Height',
  '9b43b6d003635fbf2e02f9bab12929a5': 'Limit Aspect Ratio',
  '2e81b2a5e6d523a70db93481f01af875': 'Limit. Limit the minimum height',
  '********************************': 'Aspect Ratio Description',
  'a7b7da38fcb8e246e910e178c5fe4ebc':
    'When the aspect ratio does not meet conditions, the description appears as the prompt information.',
  'ed55564c631322fa3042c77286d6562c': 'Input KV',
  '746dd83e6891ccc9a19804c23c2c4443':
    'It is used to edit the data of the key-value pair type',
  'b3747d09769d3a2ea8aa664edc901212':
    'Be triggered when you delete a combination item.',
  '654ad5670efdd25f1417958a7026a355': 'Value Type',
  '9648d874d516a87965066342394e86e6': 'Key Prompt Information',
  '4dae7425b21494a318cd4a69ce24608f': 'Value Prompt Information',
  '233662283039ded8c29f070d1a807029': 'Sort',
  '4f9c849b6b7f048cfd242cef3e707a58': 'Select month',
  '7c3885e3c3eca4d95521b3e220586628': 'Month Range',
  '5b4ad7ffa6eee985e72e002bf18ac09a':
    'To select the month range, you can set the minimum and maximum dates through <code>minDate</code> and <code>maxDate</code>.',
  '1e65b8181e9a40e76b86e2c261cafbe0': 'Input Number',
  'c45782c425bfc31824af8129bd6e0282':
    'It supports the setting of the maximum and minimum values and the step and precision.',
  'acc945d0c8449dce7dc1441f466ec413': 'Input Value',
  '5f914e36c49db618d06981df7b3c4d81': 'The number box gets a focus',
  '********************************': 'Current Value',
  'efdd0d8923130f281b54506eef2121cf': 'The number box loses the focus',
  '********************************': 'Clear the number box content',
  '********************************':
    'Enter a number or use the <code>\\${xxx}</code> to get a variable. Otherwise, the configuration does not come effect.',
  'e284e64008fd8b066da04bca335d032a': 'Decimal Places',
  'b317cbf67c94f986fc44cf4cbc2280c6':
    'Precisely retain the number of decimal places according to the rounding rules ',
  '4c9bb42608b11278a5d9a2471b74eb40': 'Prefix',
  '0c8fe8b3675b0c7f5a896a153461ea46':
    'It appears before the input content. It is not contained in the data value.',
  '242d641eab57223af01fb29940a096ed': 'Suffix',
  '7915dcc78c28ed3cda8fc949a86e4806': 'Unit Option',
  '8c80ed1f85135cc9153d0b7406ac5b38': 'Quick Edit',
  '0a8c852e27763a18ce5b72a87ba8b5ba': 'Single-side Button',
  'de93563a076f72b3e919870c9dad9935': 'Two-side Button',
  '5dd92ede50cc183f0f067dd29be5c325': 'Password Box',
  '********************************': 'Password',
  'a483bccf85587055ab31314ad1d2f82a': 'Quarter',
  '549a2a421951fc8c304c6223f346e024': 'Select quarter',
  '39891e1760c79287985adece9a107fd6': 'Quarter range',
  '6036c91d6b0b6d1a2468684e597d9f70': 'Input Range',
  '4c65f10eacbfaf580236b5cbe2de68de': 'Select a value or range',
  '0a6a4aee139530801791c556e7177a7f':
    'Be triggered when the sliding block value changes',
  'f34b1a3e26aadb6f60c546dbe9c62232':
    'Be triggered when you set showInput to true and the input box gets a focus.',
  '********************************': 'Current value of the sliding block',
  '7c9ec5db3bf23f367c1a905d356aab5c':
    'It is triggered when you set showInput to true and the input box loses the focus.',
  '********************************': 'Clear Input Box',
  '********************************': 'Method',
  '9ec172e2cdff6146d623087d567c8574': 'Single Sliding Block',
  '753a8c54d3944e252be1af928b8e7afd': 'Double Sliding Block',
  'f2996845b6bf0a07fe26f74f35e42ebe': 'Units',
  'e48d95a94e49b81b12a0113a5c253d8f': 'Value Label',
  'a465db53b8c98f42caa15ca5662f9c90': 'Direction',
  '3aed2c11e95a9c0ea1d853d4aee72e8c': 'Auto',
  '1d1a0bd85740653320d80f0d6e0918e7': 'Inputtable',
  'd22aa5f57ff8679ee7b7d473ef31417d': 'Resettable',
  '15628d1d85aee23c229c528a70419414': 'Track',
  '108db12ed4454cf1ab4242ca98442b7a': 'Scores',
  'f11a0f49123c2099ca6c6162ca37f0ec':
    'Support the read-only and half-star selection',
  'ce9201dd7d5816945fbedae79927518f':
    'Be triggered when the score value changes',
  'cef3580fce1b3156908cc852dadef892': 'Score Value',
  'e2c6470c3f2e76cb98ba951a64e41c3d': 'Clear score value',
  '09bbfb387dce6201df1ccef2aab161a6': 'Clear',
  '5ea244a79d480632f635a254c95c38df':
    'Whether to allow you to clear it after you click it again',
  'fa6f66437097fe23c948f4f9b78ecec0': 'Half-star Allowed',
  '1a10cd6599219eafb615b34234cfc0b5': 'Description Content',
  '9c07532d0c9acfecfc4ec2eb265c3e03': 'Characters',
  '2a3d1f6d5b0afdbf04033e2f2c1fa11e': 'Failed to select color value',
  '497562b3159756834c7adfe9525bd7b5':
    'The default unselected color value is #e7e7e8.',
  '4dd547945b10f672a7e48d7ffc42496e': 'Select color value',
  'f186d55a48868a88b026d48efc1cf36f': 'Score Description',
  'd38056d1c6aa9456f4b6b2c20aab06a2': 'Input Repeat',
  'b3389af540d99fe9e51fb2995dbd6dff':
    'Select the repeated frequency, e.g., hourly, daily, weekly, and so on.',
  '2d842318fbd931286be387aaf5b2a7c3': 'Cycle',
  'a2d92b5adb07a4bf8a45e55643bc39f8': 'Enable Unit',
  '42c036311993897680ef37f11e4e20a8':
    'You can customize the configuration bar of the rich text.',
  'e2591e971f4c28db7c80a5f546084a1d': 'Rich Text',
  '70209fc05c1a5b21f05b186a0f7ba2ee': 'Editor Type',
  'eca97e97331bd76aab9f6160991e02bb': 'froala setting item',
  '39624e820b79bbd62561357f6179c8d7':
    'Prompt language displayed when the mouse is over the configuration in the configuration bar',
  'a7bac2239fcdcb3a067903d8077c4a07': 'Chinese',
  'f9fb6a063d1856da86a06def2dc6b921': 'English',
  '81fafee54baebeb9591b5e7840d7e650':
    'Configuration item displayed in the large-screen mode',
  '0b6eee152cb2553ed4298ca2fe82d3f8': 'Screen width ≥ 1200px',
  'd85a80d177db696b29b7338af1501055':
    'The configuration is separated with a space. You can group the configuration bars using <code>|</code>. <a target="_blank" href="https://www.froala.com/wysiwyg-editor/docs/options">Reference documentation</a>',
  'f126c4368fbf51cfd470684e7c3d31c9':
    'Configuration item displayed in the medium-screen mode',
  '3b8c320d14dba586c581ceb951917397': 'Screen width ≥ 992px',
  '89bc688b5b497e515ce3c026a1d92669':
    'Configuration item displayed in the small-screen mode',
  'a849d8a0f59f3ae146b149b7b4fc7480': 'Screen width ≥ 768px',
  'b8c8f1c6a56e902bd837420da0f554b3': 'tinymce Setting item',
  'e4d0e65de0018b63698ff22d683d6dd5': 'Whether to display the menu bar',
  '63068211669d69b4028eebe0052425e7': 'Enabled Plugin',
  '8cfcd52cebdc2555b202f4b03de93662':
    'The configuration is separated with a space. <a target="_blank" href="https://www.tiny.cloud/docs/general-configuration-guide/basic-setup/">Reference documentation</a>',
  '1a920d9999206a9fa5f588c7405fe081':
    'The SubForm allows you to configure a <code>subform</code> as the current form item.',
  '18c113b99afa964ee988f30e81baf12b': 'Name Field Description',
  '4d930a9c13fca9d74e1a77496abfaa58':
    'The button description is displayed by using this field value when this field appears in the value.',
  '307ae20f587910da3e9bb4e885334b6a': 'Button Label Description',
  'e366ccf1556c0672dcecba135ed5472e': 'Setting',
  '70c04a0f4522e39fde5f18ebc85b6232':
    'The minimum number of buttons is allowed. ',
  'f58ea00f6a84bcb1cac174d1f68c88bd':
    'The maximum number of buttons is allowed.',
  'e41fd1934b82f93f5737827be7323119': 'Form Edit Box',
  '********************************':
    'If it can be used to display the data, it can also be used to display the data of array type, e.g., <code>multiple</code> <code>sub-form</code>.',
  '4ba0387a2daf11ad9c67a75b52819eb3': 'Table And Form',
  'a80f36ea893b8135df8d9266bf13afac': 'Description Text',
  '1f9794dd5634220ed0a498c666cf46fe': 'Column Collection',
  'b2c712c788d3a143206eee22fe24d9f1': 'Green',
  '6b93fcfc1a5795189c6072fa6e86d4f6': 'Quickly Build The Table',
  '0c14e431f1b5ecd163f8fa010a0654c7': 'Add A Column',
  '13c250c68608118463871ce7cd8b292c': 'Form Edit',
  '6a2f67fe2d8bb1d389eb934dd8a72b1f': 'Add Button Name',
  '44177f78c596be113d4fe7c6bd7bb46a': 'Add Button Icon',
  '********************************': 'API Submitted During Submission',
  '96848841ea9b2a7d692aa6de57aa5e3e': 'Delete button name',
  '34f020715121478c6a8e4654954c84fd': 'Delete button icon',
  'a497289d862c6aeff8e61b6479b1081a': 'API submitted during deletion',
  '748f181258041bac1fe7346e970dfebc': 'Whether to edit',
  '412e319fdd893e6f3b26a1975d21b0e0': 'Edit button name',
  '********************************': 'Edit button icon',
  'c128de224b606b094bd8139890fbe215': 'Whether to copy',
  'd59abc7020b4a6d943a0031da4518422': 'Copy button name',
  '5d04af0e9d59ab3f1265cd8345c2517c': 'Copy button icon',
  '681bdd6184b526ade9665e6d74976863': 'API submitted during modification',
  'c29e632a658ecb98edb874833b070e98': 'Confirm edit button name',
  '24c39bb36955b3113baefcfc20c5f60b': 'Confirm edit button icon',
  '9c68c27e8939af40897d1b0bca0360a5': 'Cancel edit button name',
  'e54289c1f0e9311185adee89af24e618': 'Cancel edit button icon',
  '1205e138ba64dddf61c1f8e6eb3a1aa7': 'Confirm Mode',
  '********************************': 'Get The Parent Data',
  '5831c1f77c5acefa6dfe9c64d06ca78a':
    'Configure "canAccessSuperData": true Configure "strictMode" at the same time: False Enable this feature. It maps the variable with the same name of the parent data domain automatically during the initialization. Note that it is mapped only during the initialization. Once modified, the data in the current line predominates. If it is of form item type, it is used as the initial value.',
  '677fb705e57897710d28867b7628307e': 'Input CSS',
  '14d342362f66aa86e2aa1c1e11aa1204': 'Label',
  '1b4996b29c7b4d5fb45ca701e6438d14':
    'Configure <code>options</code> to implement the selected option.',
  '52636511861a0e08cbe6a0eb1c27d816': 'Red',
  '9c9aabab3f7627ff4bb224b2738b26ea': 'Blue',
  '9888468550779b1777260b8fafe6abc2': 'Option Prompt',
  'a2cbb42e488dffe644bdb87c26afbc07': 'The latest label you use',
  '6f6fa31a91b516b28ebee7a86a9b13e2':
    'After you set the option, drop down these options for your reference when you enter it.',
  '379b5486e7860a9f38f37dddabbb094c':
    'Text box, email box, input-email, URL box, input-url, password box, and input-password',
  '********************************':
    'Text input box. It supports the input of common text, password, URL, email, and other content.',
  '9306b956ca5950203ee49a2680cac63d':
    'Additional widget on the left side or right side of the input box',
  '********************************': 'Automatic complementation',
  '424cd425082360322678a5c8d6910b80':
    'Call the interface provision option according to the input content. The current input value can use the ${term} variable.',
  '7e9c83e86beb612377a94e6e8d1fc644': 'Display Field',
  '26ff46d82166741297ce666b2792af85':
    'The data field corresponding to the option text. If multiple fields are merged, configure it through the template.',
  '6d4ce0631f37676a887c9599691fabec':
    'It is the field that corresponds to the option text',
  '2e01f5f5889e33d003bec7857cd38445': 'Value Field',
  '959c27193eb0a41d01f4b53dcc4b9245':
    'It is the field that corresponds to the value.',
  '82a61b32c76fba3dc83e2611624e93ec': 'Time Box',
  '********************************': 'Hour, minute, and second input',
  'c166d8f9804ecef74b3b74190599a7b8':
    'To select the date range, you can set the minimum and maximum dates through <code>minDate</code> and <code>maxDate</code>.',
  '479728c411bac59bc44d5ab8dc3cb4f0': 'Tree Select',
  '15ac5d800c0163b4b806622739478e9b':
    'You can select it in the tree structure. You can configure the option through <code>options</code> and can also fetch options through the <code>source</code>.',
  '57a6105deead3fec79028cce7bfa2004': 'Option C',
  '9bc04a190ce0a5ba1ed473cb628b5ed4': 'Option D',
  'f77f634e6892c6447c5d9df623e42aff': 'Tree Selection',
  'b35014ec330e91860bb188200fba12db': 'Select node value',
  '65d76d0590aa6e05d61fe8db4c6a11ca': 'Add An Item',
  '0761b99481b1bd10c1d6aad6028a8281': 'Be triggered when you submit a new node',
  'de2392357fc78e9e0f0946bcc0af87b6': 'Added node information',
  'cf965b232227a0e9d14f4f7e31b01c62': 'Option Collection',
  'cd994c38456676f5a55c5593b6a652bf': 'Edit Option',
  'e385c7c6f726dc2641866d9050777efb': 'Edited node information',
  'd015e18748f42f53bb6ab213e9b06187': 'Delete Option',
  '58cbd355c5bdb80653a8ae3d3b316c37': 'Deleted Node Information',
  '58995b490ba67e5d29dd87f422b14861': 'Lazy loading completed',
  'ec2fb7a5db150690b14b63f83d9d4b30':
    'Be triggered when the remote request from the lazy loading interface is successful',
  'e6b94af26512b3f7ea8eb1433d4a2aaa':
    'deferApi The data returned after the lazy loading remote request is successful',
  'e2edde5adbdf33f6dce59a299cbf5fad': 'Unfold',
  '38747bcbc3c47924098076ee87e59933': 'Unfold specified level',
  'a148ea1749eaf611df5cc95b533751ec': 'Unfolded to the level??',
  '37252d3a5eb0ebab17bfce14968b47c9': '?',
  '01356df4561f9da87d2876ec9c0dacfe': 'Unfold Levels',
  'def9e98b60e3bfc493bcd7693e702096': 'Folded',
  '3ebb9059b6abc8f80a212b5795270ec2': 'Fold Tree Nodes',
  'd81f0b9079d5a38bbedffeacaad8b905': 'Delete Data',
  'ce5e449208bb568eaf722bab9a20380e': 'Reset Data',
  '25ae4ca8d4b8a67b273066a97a516327': 'Options',
  'fe7509e0ed085b86f07e3e9975cc5b3d': 'Value',
  '72453d792655604f1fab821146133d7d': 'Sub-option',
  '210da23d108e85b2f0bbfa85846cb792': 'Add A Sub-item',
  '7dae2e2c470a12011e0c8b831506f656': 'Hide top level',
  'feec2a81557811b7fc38b0e6b758c3f7': 'Whether to display icon',
  '018d13580184c6ca3df616940ebaee66': 'Do not automatically select sub nodes',
  '210bc6834481c05ed90875d87a262ed5':
    'Whether the child node is automatically selected when the parent is selected',
  'b39c6fb1cdf629d3f3032d6c7d4694a8': 'Whether the value contains a sub-node.',
  '1fa482fed3042cac9f96c6f43c13f84a':
    'Whether the value contains a child node only.',
  '55b50a43f58fa746600d6bafdf405336': 'Top-level Text',
  'c48d9f48ce8a798772f17d1f47560529': 'Top level',
  '945da00807804cb992a3aeb0ed6ea698': 'Whether to display single-select button',
  '13604cddfe74bc9b9078dd61f9fb94ef': 'URL Input Box',
  '********************************': 'Verify whether a valid URL is input',
  'b233766d3fae72574d3f9ee98c5be120': 'Select year',
  '32a750b4fd61f1cfe37cac7147f05b3c':
    'To select the year range, you can set the minimum and maximum dates through <code>minDate</code> and <code>maxDate</code>.',
  '973b69af999dbdf4fa124df8c928ca6e': 'Read-only Mode',
  '710ad08b11419332713360d2750cd707': 'Disable',
  '7abf78a41095c6d21a1cc89b4a876233': 'Delete Value When Hidden',
  'cd8992b644e6c18367861a4d913fd116': 'Verify',
  '1040279cf7b8dbdb842f597c30095f62': 'Check API',
  '133886b10fd9721e6cf91b76f2df5b6e':
    'Separately check the API of this form item',
  'b95c6e14a4d8f6a6c6d99d583916f327': 'Merged Into One Row',
  'd22dfe420e4b00e000b93f94db3c856e': 'Select List',
  '5e391af52da238f13c78b471a7cc54f3':
    'You can select a single list of multiple lists. This feature allows you to fetch options by <code>source</code>. You can configure images for the options and also customize the <code>HTML</code> configuration.',
  'd1f923b8e23b66b3e48953ba1ce17839': 'Location Picker',
  '989ea9acbab9b17d2c15e2946b5365bb': 'Select position',
  'dffd9d86d2003615897b12ce7597d77b': 'Map AK',
  'a269e7de6c7735290733eb3e574c2129':
    'Get Baidu Map AK from the <a href="http://lbsyun.baidu.com/" target="_blank">Baidu Map Open Platform</a>',
  'b472ba224a7d132c487ee6ec4798f835': 'Coordinate',
  '36443b53c845b197db8d39eeda433ab9': 'Baidu coordinate',
  'f58cb611aec0998a44ef104b5c950b40':
    'Coordinate format by State Bureau of Surveying and Mapping',
  '076a041dcf45a36b29c5cdb8b8eca922': 'Rectangular Switch',
  '183aae5eec1ff547833dc338aaeffc9a':
    'You can configure row single-select and column single-select, and single-select or multi-select for all options. ',
  '7583b85ff3579d60a9f9d323c2f6674a': 'Row title description',
  '3fc35bb610852289cf718f07a5b79369': 'Column 1',
  '50987924540e6e35c78246031499e75b': 'Column 2',
  '08a6996be7a86af5692cbca41922a5d1': 'Row 1',
  'ce31d858c63395098c5e4cd892bd473b': 'Row 2',
  'e9b376d39966c8182683019f95ff8cf3': 'Row, column, or cell single-select',
  '0f244785fd9f555aae9797db4d14fb09': 'Column Level',
  '5a57bd526cc3170b6c86f920fc24cdee': 'Row Level',
  '45dde5879e77d7f7a15d04b8fed26ec8': 'Single Cell',
  'dc7558211f2990370954c2e7ca498ee9': 'Column Configuration',
  'f4c807fd8453f4b3fdaab02fb9c21ba7': 'Add A Column',
  '39886861ea5d8b526e0ac5ecc78d110c': 'Column Description',
  '34ad26bd1fb448c7f2384252d856c02b': 'Row Title Text',
  '48baa62373a2f90d6aa743d7821be956': 'Row Configuration',
  '854af3c2cd9c275ac70fc5121ea4fb2e': 'Row Description',
  'bf306308e467aeb7b00df0e2dd127d06': 'Add A Row',
  '75bf5fd49520bce97de632700fc8a129': 'Get Matrix Data API',
  '24d9de25721d1fb0ecf89ef81c43d877': 'Nested Select',
  '10d1cfb7219a5445ef1e70aa16e4451d':
    'It applies to sub-items contained in the option. You can fetch options through <code>source</code>. It supports the selection of multiple options.',
  '03cd388fe29a4fc116c0021e496a113a': 'Option b1',
  'fb00d1c1a65d3739c03a0b2715168327': 'Option b2',
  '353ae08afdb3d0a3587e9b27ca239c33': 'Option c1',
  'bea7ad76f0f321124ff614a099cb9a85': 'Option c2',
  'dbdae74eb12668e2b9568b013bf27d45': 'Retrievable',
  'ae45cdb51c795df3b046f71afe3611bf': 'Blank Prompt',
  '601bb29fe72e56930dea03ae3e1aa555':
    'The text appeared when no result exists during retrieval',
  'e3be7b8a459a08fec8f378a0660b642b': 'Multi-Select',
  '6a7f7d89c8f1f3e6aab0962ff920b145': 'Parent option as a return value',
  'c23eb92d1f249452c3ff3ee13738b47a':
    'With this feature enabled, you can select the parent option but cannot select all child options. Also, the parent option is used as the return value.',
  '8fcbfdc1a9403f6339d81911473806da': 'Picker',
  '819776e8d96b1438eca3594f47bdb1c6':
    'Through the <code>pickerSchema</code>, you can configure the data source available to select the data you need. It supports the selection of multiple data sources.',
  'dc0c50a5c9832b393df34835111c34a3': 'Enable Embedded Mode',
  '1ac065ed64b81380384a0b371d5b404f': 'Configuration Option Details',
  'd64b585847f015eaa1443a3a03562350': 'Display the style of the selected data',
  '0861915dbac25ccb573b3bb72ffeebd7':
    'It allows you to get the variable using <code>\\${xxx}</code> or write the template logic using lodash.template syntax. <a target="_blank" href="/amis/zh-CN/docs/concepts/template">Details</a>',
  'c2c23b4fc7f695c58e947ad413f2c5d8': 'Checkbox Type',
  '********************************': 'Drawer Type Popout',
  '5a7af8d3e471d98339c84b07a844547b':
    'You can configure the options through <code>options</code> and can fetch the options through the <code>source</code>.',
  '1e890599eec94efccec337cd474f7f1b':
    'The first option is selected by default.',
  '46110787e4774b81418b274e9b06127e': 'Single Row Of Options',
  '83a00680e0872e2a35496be7e12c1309': 'Number of options in each row',
  'c30264927c3c170efd2e7763becf12fc': 'Single Option',
  '8a0c1f35cff334fc105b6d52b195369d':
    'It supports the selection of multiple options and can give an input prompt. You can get the options using the <code>source</code>. ',
  '75cc2a992ea150d3a6c68ac4bc486637': 'Added Item',
  'a13b85bddbcdab63ef2b2d98dd46afe9': 'Edited Option',
  '8aa4d6aedd7957ebc6b87fec655695ef': 'Deleted Option',
  'e19c0792886a147d74fc662d7af138bb':
    'Display the selected value in a single row',
  'b28aa9c36d0b506a71aa78b628e796c6': 'Number of Labels Displayed',
  '2b23767de575e27fc9e4e0949e885f81':
    'Maximum number of labels displayed. The floating layers are folded in case of exceeding the maximum number. All labels are displayed by default.',
  'dfac151de712ab0b3618072c8a7f0a0f': 'Advanced',
  '406af2b98e6210cd42d7f824cb6dfd16': 'Option Value Check',
  'fa8d03e8b5458c1a1d742736bc26e25b':
    'With this feature enabled, the option text becomes red when the option value does not match the option among the current options.',
  '33e7b7433fdbeafa1ef71105ee28c061': 'Static',
  'c99e0a675933de39693b7e80d5b70405':
    'It is purely used to display data. Also, it can be used to display <code>json, date, image, and progress</code> data.',
  '134492cd10a0646da6f23a743feee6d4': 'Static Value',
  '04f5f12c49c2a6fdc43da049591328ad': 'Static Display ',
  '663a93dacbba9f7860dca783cf772419': 'Quick Edit',
  'abd733d00ec656e7b0cfd88deedf102f': 'Quick Edit Mode',
  '78f395c15aaf8c92d9223f6ca69b41a4': 'Drop-down',
  '5e2d1e872682befd5350687f94a6b417': 'Save Immediately',
  '2d2eb2ac28f074d1caef0d332d61cfb0':
    'With this feature enabled, the modification is submitted immediately. Instead, the modifications cannot be submitted in batch after they are marked.',
  'eccba4475f3144e417e55fd96e831e09': 'API for immediate saving',
  'c8e5c062d5ad38e54413abd9c7cfb2f4':
    'Whether to separately configure an API for immediate saving. If no API is configured for immediate saving, quickSaveItemApi is used by default. ',
  'c3ed36e4abb96c18a6c83350994cdea7': 'Configure Quick Edit Type',
  '45a6c55d8d2d92af84e219defef084d5': 'Configure Quick Edit',
  '3f337f436989e9847790e4435423f77e': 'View More Display Options',
  'f7247cd14bd964b883bbb481892c440b': 'Pop-out Mode',
  '2a2924380dfcaea998bd8a49703545a9': 'Drawer',
  'a06e8df4e9232cc606e0202e06a198d8': 'Floating Layer Position',
  '68b54e7b408c4fb83561c931aa668eae': 'Middle Part Of The Target',
  'e18459c93769a5afec01e1ce60f9b9fd': 'Top Left Corner Of The Target',
  '1fc3cdd8437f5057774cde2f2c51f97c': 'Top Right Corner Of The Target',
  '63dd9faca92bccfd436ff21a6b4b3151': 'Bottom Left Corner Of The Target',
  'd01c239688b9c8fc145191ee642dc080': 'Bottom Right Corner Of The Target',
  '8fcf9802436282672a8e28ebd6262390': 'Top Left Corner Of The Page',
  'aed25160b4e8cfc613a743c4549e9222': 'Top Right Corner Of The Page',
  'd4a4ab1916187e09b9c037705fd49ffa': 'Bottom Left Corner Of The Page',
  '921fccbb84c829bf8c6f0b9957029f44': 'Bottom Right Corner Of The Page',
  '1d53f83df1d889a70131b7a93c819575': 'Content Details',
  '8dda45360dcf9ca501fd7d0eb53045b5': 'Configure To View More Display Content',
  'fd389de167730ba8542217ad31b95562': 'View More Content Configuration',
  'f9a9fcc3bf6a3c8ff1e99fa48ed6d03d': 'Copy',
  'deb65aca8dba2ff9d0cfaed0a3441068': 'Copy Content Template',
  '622e14515c4fd5ca6fe6946e3a1bfb4a':
    'It defaults to the current field value and can be customized.',
  '261bba7ad82914e477f4b37f6a83874e': 'CSS',
  '16a20243f9b741c08216dc9548de2968': 'Overall',
  'e1b2f870112bd38f8d7e14a0ad589930': 'Control',
  'd38b7fc2d31e0ae21cb4bc7d7df14e92': 'Switch Control',
  '6e17d8bb70df1c1e379fa86cb235ac82': 'Switch Form',
  'ddccb436305b0a984c77d4ffa0725375':
    'Be triggered when the switch value changes',
  'a75c768fda740b2c8f6e2dcc76400f23': 'Switch Value',
  '7e1eec8349c4998d142960009305f27a': 'Fill Text',
  'c580bacf343343f04a1b551e46d02c4f': 'When Enabled',
  '9ff352ae39cdaeaa4fc54f83575eedc9': 'When Disabled',
  '48433deca382e67b26af31c6ff967d04':
    'The default selected value is true and the default unselected value is false.',
  'cadd676e2710e50ac149211078f8a306': 'Selected Value',
  '13f19e1d0dd528aafd6263fafdc35e82': 'Unselected Value',
  '617a63f1b19b5aad029f973479bac917': 'Tabs Transfer',
  '4f26f1edebcdeea90f6e4247a501dbaf': 'Tabs Transfer Picker',
  'ab5dea29793d933fa7b5487a7309df6a': 'Member',
  '71f8043aefd52572b172e7d1fbd5af57': 'Magician',
  'fda93c79275b812a6c1c189fbebf8b08': 'Zhu Geliang',
  '573cb0d34bd1cdc7b368c59db9b7bb7d': 'Warrior',
  'e37a86d1a1dbed0cd53c95582977f075': 'Cao Cao',
  'ccddd2de691ff2d56b651877f72d90ed': 'Zhong Wuyan',
  'c0db8e7b42528eeae96310c6629e53b3': 'Jungling',
  '293040fc607f40caf6d6e16042012182': 'Li Bai',
  'b5256ec780343c4e20e397b43cab96a9': 'Han Xin',
  '49dcf9f88e7b7b8ca7448631021d7d12': 'Yun Zhongjun',
  '1fd02a90c38333badc226309fea6fecb': 'User',
  '8b3a9a5c912610c384bc8dc2c8514386': 'Select all options',
  'e1112a529dc969a03bbbb409905ff2ec': 'Tab change-over',
  'd202bc660c4d2eeb58e194b6320bd235':
    'Be triggered when the tab change-over takes place',
  '9e7a97a3d043f566f2435329e01e09f9': 'Currently activated tab index',
  '91208131116f2823993daf99f15e1325': 'Clear selected content',
  'dda222620b789d07c2c5c279475caaf1': 'Reset selected content',
  '2ac24a383a1faae33f81772b757b2817': 'Modify selected tab',
  '91f894b900f593c848e5b21f2b414b05':
    'Modify currently selected tab and select other options',
  '791959f9b90734dce08da79f4ac27a41': 'Search API',
  '6eaeb8ccaa8473e2b985095be2bf3cd1': 'Select display mode during query',
  '6541f1702af367c41a3127ed8511eb50': 'List Form',
  'd58ba4b5e94680fcb08300e176502fb8': 'Table Form',
  '406573cea6af9b0c6462295108e1f5c0': 'Tree Selection Form',
  '6c5358b981a475da2a83e95e4170647a': 'Cascaded Selection Form',
  'b51b404751554341aae342bc5a6e9b22': 'Title Text On The Left',
  'c7840604d79e814f02f8260bc2ba6544': 'Title Text Of The Result On The Right',
  'c6a16ef980efc2ac48c58727e5bade81': 'Textarea',
  '2af7e3952d7430d1e4c86ea5aca4e4fa':
    'It allows you to enter characters in a newline.',
  '5bc28acd4afb712dcbc234927357cd87':
    'Be triggered when the input box value changes',
  '********************************': 'Trim Spaces',
  'd3a8fe0d542476cf7989ef9e69cdd6f7':
    'With this feature enabled, you cannot enter blank space at start and end positions.',
  '829d96cf23f19759e4ef988fb5320032': 'Min Lines',
  'c0afd6005e68838e37e26dc7c34cf368': 'Max Lines',
  '685e5f4815e87c4690dda7d7aa163904': 'Shuttle',
  '26b4a55f458275a108c1ef213a354ced': 'Shuttle Component',
  '1b09b6621ebf0d10ce98f0178fa1bda1':
    'Trigger the component data update. Several values are separated with ",".',
  '0ba83d9fe027d8386b814f4966f20d4e': 'Check Display Mode',
  '847fe7c3978764607631845b0a43926b': 'Association Selection Form',
  '9037f708f0d9750505aa741399768ac0': 'Option Set On The Left',
  '3dd8d112997a8d0ae0641b8bf69eb7ff': 'Left Selection Form',
  '7980e24e70a7e56490fa397d5bc6b86a': 'Right Selection Form',
  'baf916e9b0de774b78e716e91b963690': 'Show Statistics',
  'e40d15221a911e060b8a05ec7aa9533c': 'Tree Select',
  '26aa42c81825364d2d691261017b6797':
    'Click the input box to pop out the tree select-box for your selection.',
  '********************************': 'Tree Drop-down',
  'ac34e635d05de0ac2de78737ea313995': 'Be triggered when you add an option.',
  '5f5e2d89e987fa64d56471b2e3d50cda': 'Added option item',
  '8784005bbfb2b66592cc0c5cc0a62f2d':
    'Be triggered when you submit the edit option',
  '25a4d7b5ce2585aada1205e1121e9967': 'Edited option information',
  '91a6d3a760184e581eccd52bfa3f7a18': 'Delete Node',
  '3c72d934764bb7205f593903c63ac7ec':
    'Be triggered when you submit the deletion option',
  '7768e7d47fa6e9b811d1a1f684703cfb': 'Deleted option information',
  'cfd5faa079bd9d7092e7cdc92f353c30': 'Autocomplete Interface',
  'a9f97c7c1bec32a0912f1dd36b2c3b90':
    "After each new content is input, the interface will be called and the update options will be returned according to the interface. The current user input value is in ` \\ ${term} '< Code > please do not set it at the same time as the access option interface</ code>",
  '72cd009894ef3ea735d5e2a9b4656c04': 'Expand sub options by default',
  '0770f4a4a4f769d778ffe228675e8c3a': 'Options: default expansion level',
  'd1b65ac6a715ec342d7b738c64b55aa0': 'New option interface',
  '07e2cb845d6b6c3f91e6960c209b36da': 'Edit options interface',
  '8c05730667f91c371ace46c17f46c875': 'Delete option interface',
  '43474db97aff2ed0a876a47b6f4cdc7d': 'Automatically generated UUID',
  '43e1548e15272e1007051d7c8b70adf6':
    'Automatically generate UUID in the UUID v4 format without the need to configure it',
  '844d72db7e57be4d77881ee9b4294e75': 'UUID (will be hide)',
  '2820712cac089483cf2b4a0c939fc780': 'Column',
  '9e17d57a0ba39254a75c0265aa3063ca': 'Column layout',
  'e63907bf0db529e84866d1ae737bfc0d': 'Column',
  '9bdb07e72d3a9a6084201a7398523f5a': 'Insert',
  '3c43c5860b4dfaced296d7a63eae1513': 'Insert Below',
  'decaeded2b6f2c66f191ff34b868b1eb': 'Insert Above',
  'eb22d47f16f92e6f621c2b9d87119303': 'Columns',
  'c288b519484207bea1d51884a5e9acaf': 'Column Spacing',
  'd81bb206a889656035b929cd8bb1ef10': 'None',
  '98d695d6a6a48cfb4bc2f6396ee64787': 'Horizontal',
  '1cc9a6949b47913462ff832cb684bdde': 'Middle',
  'da1b972efb29f850b50e219ad4d98ba5': 'Justified',
  '11b7fc50778c89572391ec4c6c4b55e1': 'Vertical',
  '2a6ad292447e6354ca39ee7f40d2fcc8': 'Top',
  'd68c21b6b65e7a2e361762b65b8a5032': 'Bottom',
  '0d9eb53682343f1cbd562c9a944ef5bf': 'Insert Left',
  '814276d30e338eccbaee96c3e6bacb61': 'Insert Right',
  '2a3e7f5c382c56faf261a87573658325': 'Manual',
  '19432e481cefd1bf88fc707f20ea62c5': 'Column CSS',
  '5ea5dbb698afe7ca566b94e92f6a3835': 'Column {{@1}}',
  '9cc03fc4b3e87e8410e10357a219b44e': 'Insert Left',
  '6b12fc99e2a46aed982d46b93ac191a0': 'Insert Right',
  '57ff158884582ed498a87d03aed46146': 'Insert Above ',
  'bd63eab86ac0e0b35a06d0f24a098096': 'Insert Below',
  '69a519f6b7fd6d0bebba72e7572ea1d6':
    'It is used to implement the left and right typesetting layout. It defaults to even distribution. You can configure the width of a column through columnClassName.',
  'f9c91bffab8b1202cf32ab508879e805': 'Fixed width <br />w-xs',
  '8c187c1862900db487c8d47e19490fac': 'Automatic Fill-up',
  'b6f2fcb279241fe5ff9a8052a444266a':
    '< span class = label label default > column ${index | plus}</span>',
  'cb2f68c9c24e85d21e6b090b6e5657d8': 'Column',
  '92e2c6523449dfad4350f58908260266':
    'Press and hold the right block of the highlighted box and then drag it to adjust the width ',
  '129b16a89a82c6d5e03ab075633b3892': 'Column CSS',
  'd34cc10492e80595a901a292d0c16bb8':
    'You can add a width class style to adjust the width. By default, the width is equally distributed.',
  '84b620405949e278f214a811a3a19e2b': 'Column {{@1}}',
  '4bfd6e7e1ec0fe485aa0e7741d1670db': 'Insert Left',
  '9d75cc0b193601391a139285d55a3493': 'Insert Right',
  '713315591970d7c8b49d1c732fe54fde':
    'It can be used to embed the current page.',
  '8f1c078c6d42759e6ccb1a9bf35f1629': 'Page Address',
  '51ad722113289f70b6d77c78ddf0c64a':
    'The default width is the parent container width. The value is expressed in px by default or in percent, e.g., 100%.',
  'c78d9b9ab9db5951eb5526e8a46677d9':
    'The default height is the container height. The value is expressed in px by default or in percent, e.g., 100%.',
  '9ef4425332e5f8bcad86de483b4faedf': 'IFrame page ({{@1}})',
  '67997ccf7ea846c3c2d278b01ed9600b': 'Image Display',
  '6dcf991e992a0b765df0933411fe9bb2':
    'It can be used to display an image. It supports the static setting of the image address. Also, you can configure the association of the <code>name</code> with the variable.',
  'e18aa5e376437da71083a29c4cddaf46': 'Thumbnail',
  'fb43d5ffa21f3c4055c29fad350f27e4': 'Original Image',
  '582570bef8c57c5af7658c4a4eea45ff': 'Thumbnail Address',
  '1193e1aab7bea094279ae7b4288ba848':
    'If a field name is bound, you do not need to set it. Also, it supports the variable.',
  'f5865bf2d791d293374af4aa76d27c4d':
    'The image displayed when no data is available',
  'e1110b854bceeed0e887cb951bb3d2f3': 'Enable Image Enlargement Feature',
  '7ffade593e390a2a2cc43e6663461b71': 'Original Image Address',
  '214953c5f7557b1a5f1310c87238ee03':
    'If it is not configured, use the thumbnail address by default.',
  '6d45b56ee94ea873a554ec41b9f2074a': 'Whether to display the image size',
  '00cedb73310cc531a43d23cfa5ba0e5f': 'Thumbnail display mode',
  'b0267f4aa776e75443b7ef6e8dad257e': 'Fill Up The Width',
  'c30b1b6f29debb05449aa3cb40268e7e': 'Fill Up The Height',
  'e13556bb3580ac3746e1f8663eb15896': 'Included',
  '47303119ba97a66d168ff042575b9de4': 'Fill-up',
  '319501b9acacdd6f94a9bdd0637a3cd2': 'Thumbnail Ratio',
  '4d080f1e18d86051b8d651c68685f319': 'Thumbnail CSS',
  'e040fc4bf539dd1cf6ebca6b4bff857a': 'Image Collection',
  '93f46deec83b8f9005233a1467498d52': 'Display Several Images',
  '2382c315a6ba396be1932dd8dafaff52': 'Image 1',
  'ce6e2814f207c9333f10785606c57df3': 'Image 2',
  '45a4922d3f42d0b137c6845c4c77031f': 'Associate Data',
  '2d4247be13246794180897b40fcdbcb1':
    'e.g., \\${listVar}. It is used to associate the data existed in the application domain.',
  '81a2b634d7ddcffc21b787613673943c': 'Image collection data',
  '0f088d8f579c362068d7a3858e207280': 'Thumbnail',
  'ebb2073c604e72ffd9ae1b796665b702': 'CSS of the image list',
  '5acc93183b7fe3816a845aca52baeff2': 'JSON Display',
  '5a4b81442789f3b2e7b665bd430eeabf': 'It is used to display the JSON data.',
  'f891291cbfaec7ba3754520e2a5227d8': 'Default Expansion Level',
  '6dfe63d8c039df37787c87afe4c68604':
    'You can use it to display the text address.',
  '6ab20dc4b64021b85886ad9c12b6e0cc': 'Destination',
  '78ce29fdc55e6d0980b591a264c537a8':
    'It allows you to fetch the variable. If a field name is bound, you do not need to set it.',
  '5d809212900f3bc3ba122fe93638394d':
    'When it is left blank, automatically use the destination address value.',
  '88a5126f6a1463fc9986b590ee4ab99f': 'Open It In A New Window',
  '39a2cb79c6d9762783e20522ea86dcff': 'Left',
  'de2a774bf98944b8f0ec8755d5f59d64': 'Right',
  'e370757f933a8ecd87bf0255c3ce45d0': 'Advanced Setting',
  'e35dbea2b0c097d7fb76173da0e0bba1':
    'HTML &lt;a&gt; the target property of the element. This attribute specifies where to display the linked resource.',
  '9f556fb46ecef854282d17e631578b1c':
    'It displays a list. It allows you to customize the title and sub-title, content, and button group. You need to configure a data source for the current component. This component does not have the data fetch feature. You need to take the priority to use the "CRUD" component.',
  '84c04f1e9ce6a6a228228dd3fb96b99e': 'Configuration Member Details',
  '1ac0e1626be43287983fe3e5559320eb': 'No data',
  '4e53bfe03e30bb3eae63d90d1f7f2a1c': 'List div CSS',
  'd9eac94850a74ec881198f4ffe4edcfa': 'List Item',
  '3ed7b428165499a1b91ed1eb287ba343':
    'It supports the following template syntax: ${xxx}',
  '456d29ef8bafd5202547e50d3e64d4ea': 'Log',
  'd06f8476d115020496058363a32bc40b':
    'It is used to display logs in real time.',
  'a2bc1edd7be9348e5a998feb0886c55d': 'Log Data Source',
  'ddeeaa33ae62ead1a77a859fb79b78d7':
    'It displays the mapping of the current values. For example, if the original value is 1, 2, or 3..., it is required to display offline, online, expiration, and so on.',
  '844318bd1d58ba0bf7dabbb771174b12': '<span class="label label-info">I/span>',
  '2fccd71275cdf5d6c0c677ef79dd84a0':
    '<span class="label label-success">II</span>',
  '198a96c4cfc64d5feba81b3f931f8289':
    '<span class="label label-danger">III</span>',
  '626a0f2fdf13bcadf11581006ab9eba3':
    '<span class="label label-warning">IV</span>',
  'ce3fd44456123f571e9d083b98da9fcb': '<span class="label label-info"> V/span>',
  '8b139ce9fa196b602bb1ee3bd25b25df': 'Mapping Table',
  'a9de5901385d644363cd056482be7517':
    '<p>When the value hits the left Key, it displays the right conent. When the value does not hit the key, the key is the content of <code>*</code> by default</div>. Ensure that the key value is unique.',
  '2b10dfa6dbdf8775ab2b38fc67e7ea49': 'Wildcard Value',
  '87cac3a9c9576d9b5bad6639c891112c': 'It displays the markdown content.',
  'eb99816b4a216b2eb884cc6194f00ec6': '## This is a title',
  'f6a5891819c67a80c1179c7cdaa00935':
    'It is used to render the nav menu and supports the landscape and portrait modes.',
  '4cb18f42ea06a97b382397c40ed64fb2': 'Page 1',
  'a004407ac524b6d331671fb7a013c3fc': 'Page 2',
  '310c10c1fcd37bf760bc15c30f54e722': 'Menu Management',
  'ba750e6e302292402a4c61ed72331cba': 'Add Menu',
  'f7a63e1e77fac1bfa05a060d55e27692':
    'A configuration error exists in the menu, so you need to check it carefully.',
  '7fa141f341e173e2339dcf0ce6869b5c': 'Redirection Address',
  '720fc47e20be9b7a2e67a4ed808b3bf2': 'Whether To Highlight',
  'a6ed2640c41d0e3df4acb4f15d37f6e3':
    'You can configure whether to highlight the menu. ',
  'dc55fd2e990771fd670743ca5dd59275':
    'Automatically analyze the menu address when you leave it blank',
  '78b7cf23a2b93db1ec36e6c6cfe170db': 'Include Sub-menu',
  '710b20ff1d85f7965bfaac44ae26344e':
    'A configuration error exists in the sub-menu, so you need to check it carefully.',
  '02f925f6a5136c2b65b5da8d1216b5b8': 'Sub-menu Management',
  '59ad4734917af165482774de9c2d50cc': 'Add Sub-menu',
  '4d25cfe4cd14270af9420acd25526691': 'Get Menu API',
  '9fc8c79aac0302a60ebc0777183cd181':
    'If you want to set the menu address dynamically, enter the API address here.',
  '737669a8ef038ebd37fd7b2f3b5f389d': 'Whether to arrange vertically',
  '8d79a8cbe0ed37accbe0739024d5d896': 'Action bar. It is used for tables.',
  '6c0fe599b36c2a55efd8705681783ee5': 'Button Set',
  '975903dc39691813530e108986e49ac1':
    'Click to add a button in the left component panel',
  'c641fe0ae06e6c0547d89fefa91e7f75': 'Add Button',
  '88d1257b0cf667319085f3e0033b9607': 'Button Behavior',
  'f10f0be4aa9684eef9d78234072fe08b': 'Drawer type pop-out (Drawer)',
  '4f02d2efe05a20232ab9da63c090595c': 'Send Request',
  '5dfd5a78e2ba1bc8afb482a8745454ea': 'Download File',
  '4e58f9c94d345e14e2d69cc8496b7b5a': 'Page Redirection (single-page mode)',
  '982db3084a2c470d1a9b34efa024511c': 'Page Redirection',
  'f20d9579ebdc9dfc30a212ae6cae931f': 'Refresh Target',
  '6a086902a84969a835423002718e86b4': 'Copy Content',
  'a56c9f13b1566009fdc7640bc551709e': 'Reset and submit form',
  '30313d6fa06603604db18086bbcad9d3': 'Clear and submit form',
  '0fc26855080a4219bbfad638029a795c': 'Redirect to next entry',
  '8ba8a1bead7ca55554cff1c85246ae09': 'Redirect to previous entry',
  'c28f4d63beabc4833b17aaa10ca550db': 'Copy Format',
  '21fa07f18f80bee50695686831ae1286': 'Designate refresh target',
  '8a089992349df754f182a5d02de8e8e0': 'Popout Title',
  'b680997c50ca749acd4e6075cdca244e':
    '<p>You are right! You click it just now</p>',
  'c3c8422fcecd8c1cc922cba15ab5cbc0': 'Configure popout content',
  '262c7c7b9874ae5607fb51da468d0e8c':
    'Configure content of drawer type popout ',
  '5eb694a4252528628929ced97ca95823': 'Target API',
  '84d38979bed546b93c4b3a399844419e': '<p>Content</p>',
  '0cc0fff6eb667b140d3fd06e34a8c69f': 'Configure feedback popout details',
  'deb9089ed7ebcacd712117fc4204c65f': 'Configure feedback popout content',
  '215f64480a93893fc56c51aeb5d40f11': 'Clear Setting',
  '7984d95c01b725a2709fb8f5ee330fb4': 'Whether to pop up expression',
  'dfa07586a471e24b23fe68e11f5dc41a':
    'Use JS expression, e.g., `this.xxx == 1`.',
  '47186f00df86d3edad3b5595ba8c2a0a':
    'Whether canceling the popout interrupts the subsequent operation?',
  '4f21e04fe35d39c79e7779cdf2f4e232':
    'Whether confirming the popout interrupts the subsequent operation?',
  '0bbc3ec26c36a87c9df3183def6ca9e0': 'Whether to open a new window?',
  'd0c3025a64b26e5fbf22005f400c06d7': 'Whether to close the current popout?',
  '0c15a924dc3bedefb79c958972bef2b9': 'Confirm Text',
  '06b13b11740f7663af325bf5426930ba':
    'Click to pop up this content. You can perform the corresponding operation after you confirm it. ',
  'fa9a0a79f29fef72e3060ea1af93c305': 'Refresh Target Component',
  '437d629f00e62cf99b3ad288f84ade46':
    'Specify target component you want to refresh after you complete the current action. Support data transfer, such as <code>xxx?a=\\${a}&b=\\${b}</code>. Multiple targets are separated with a comma.',
  'b01f08bf5b9f8e3ef9d49e31d89bf770': 'Specify Response Component',
  'f667748a8e9717498da714d4e5087af2':
    'Specify action executor. It defaults to the functional component where the current component resides. The action executor is specified in terms of the target component.',
  '80ddab8a52f74d707765501b0caae21f': 'Customize Click Event',
  'babbd439bc04241ed3536f892668c250':
    'Transfer two parameters, i.e, event and props.',
  '867ade50f0bbb10bac65a5c3bc7895e9': 'Keyboard Shortcut Key',
  '45882ddedb42c1a38462949750bc8a84':
    'Display a prompt icon. When your mouse hovers over it, the icon shows the content.',
  'ff88d5db9d61f14bce6e3397fd4652a5':
    'When no value is available, this prompt is displayed in place of the value.',
  '348097cc50579e489f0bcb5433637d3a':
    'Enabling this option allows you to sort them by the current column order (backend order).',
  '9db64f772c11c614ee00bb3cc066f46f': 'Column Group Name',
  '19c4f5e98ad302574202de30dddbaf66': 'Enable Quick Edit',
  '15c3796e07e33afc7252df751f610c5d': 'Whether to save immediately',
  'ba5a0a1ff2c438ae7719ca48b0ce3af7': 'Enable "View More Display Options"',
  'd689e3c38fdb32c98fb27f8f35a26552': 'View More Popout Modes',
  '6e78b595d6a296938201a3c80660bf35': 'Enable Content Copy Feature',
  '65f7e01d58cb5065f49e0e8f48cc16be': 'Fixed Position',
  '9ed8a4c5d1b3726121175dc986268b0c': 'Unfixed',
  '0a5ac2f5c327e28c58d51db967a0e603': 'Display By Default',
  'a3bd2104e3df81df713de636b907462c': 'Trigger bottom display conditions',
  '986a5f50e946674bb91c9408fc974497': 'Always',
  'a165f0fe5fb904049f6b6961105e433f': 'Mobile Terminal',
  '03ee8b948c9b34daca9584811bcca17d': 'Tablet',
  '82d9f1f96084674e2b0101ecc04d5d58': 'Small PC Screen',
  'f4166de371b5dfb87efce228b17a3fbb': 'Large PC Screen',
  'f8cffd4d3fcdca390a3a3de24d209bb6': 'Forced wrapping of content ',
  '4619988f1c5d9093dc00d1430633b2bd': 'Internal CSS',
  'bdd9d38d7e2929024089363dc8f48b7a': 'Column Width',
  'b198805e7a6b35830ba813f51db2fdc4':
    'Fixed column width. Setting the fixed column width is not recommended.',
  'abb58b3bac0533ab4359ed977fa3c073': '<{{@1}}> column',
  '044892c0c637f2d9e78e78956b1ded01': 'Anonymous Column',
  '59ceff465ad16932d8972191ad815dfb': 'Page',
  '54002bbf7eb3da8346dd4be61d642bca': 'Sidebar',
  '49400a573b9f7a7bd693f84ec59379d7':
    'Initialization data returned from the remote request',
  '5d758dc5e33ba0122c256d80c1572e88': 'Drop-down Refresh',
  'd6fdfa4f989be6586a7a29ea85522f24':
    'With the drop-down refresh enabled, it is triggered after you stop the drop-down action.',
  '56e6db657d4775698984f883b71cb379': 'Toolbar Content',
  '4f9fa9ee5b0604d97da73e77fdbc281e': 'Sidebar Content',
  '40fd4b2a194b2b1284a7f7f738b69640': 'Page Content',
  '0e82bfaaec104a9f0eeb14820b42e7c8': 'Area Display',
  '685fae7809f86f9edee4338daaa212a0':
    'A prompt icon will appear near the title, and the mouse will prompt the content.',
  'ac82acdf2f38faaf293c8690b2ea627f': 'Data Initialization Interface',
  '9d191f6126f21da8222755c14f49707b':
    'Set the default prompt information of ajax. It is useful when ajax does not return the msg information. If ajax returns a msg value, ajax returns the value predominately.',
  '4e5640b31caf3326c27026bd1ed8f4cb': 'Header CSS',
  'e656f261cac36cc658e8d69973d7611d': 'Content CSS',
  '1444bd7ae7b7301b6edf930c2ab8b175': 'Sidebar CSS',
  'dbe33f485b394a3e77240c530b9965e4': 'Toolbar CSS',
  '674be25c9feffcf78eba48d53a944148': 'Mobile Terminal Pull-down Refresh',
  'e24bc5fd094c7c272725c6340d8aeb8e': 'Drop-down Process Prompt Text',
  '717b23399e04873441478fef1cc16d43': 'Prompt text in the release process',
  'f59b11ff84daeb60b027dc02490b627a': 'Paging Component',
  '7cfb5e222a5954e891fba6e3802c7ea6':
    'The paging component can display the paging of the list to improve the page performance.',
  '9ed7d3adc032f6b78808e7f3786ec9cc': 'Total',
  '8e60090c332693095d5852d92ee149ee': 'Number Of Entries On Each Page',
  '47c62ec10a240c35b8446cc923c5e8ef': 'Pager',
  '97b8cf6ae269b6f6d75063073cd565cc': 'Paging Change',
  'c9fc9b668c08d0e54415c7054d414651': 'Paging Type',
  '35242cff1266fd3610f124b0e5e76f9b': 'Ordinary',
  '40e49965594422b15ddd789eef4dda7e': 'simple and easy',
  'da184573d7eac6362e01e6fb202d3c91': 'Pagination Layout Display',
  '7c94d517ef4d6633144d5c719d0019d0':
    'Select to render this item. You can drag and drop the sorting to adjust the display order',
  '65a4ce155100cd7fa671b0f85c1df2cc': 'Next Page',
  '442d73ebc936d3b74e41b901cce44a6e': 'Current Page',
  'fe00f5ed371bdec5801ac07f8bb564be': 'Last Page:',
  'eec5bfeff8012238efbf611fc22c32f6': 'Total Number',
  '71fa2ba926cd032c0ebe0bcdd5d3eb10': 'Number Of Entries On Each page',
  '04519bf3bf428bb1a75938ac65dba040': 'Default Entries Per Page',
  '040f05137eb37e880873a763ff653fe9': 'Max Number Of Buttons',
  '2ee8eb6b1a80a4b025f97fca6afb800d':
    'Display the max number of paging buttons. The minimum value is 5 and the maximum value is 20. ',
  'cd6f79e7bff1337c12c86a15ceedd6da': 'Panel',
  'e04a2f1662121e5a3c397d496114185b':
    'Display a panel. You can configure the title and content area.',
  '4e9bb0326ab4d3a2af94d901c7f1b6a7': 'This is a panel.',
  '5bf5f1fd54476671bd6fd9d97e3e6b6b': 'This is the content area.',
  'cac3ba71180c97b1b6432833b3417d2c': 'Add Content In The Content Area',
  '1fae678397df046c1754092f15a71d98': 'Fixed Bottom',
  '68744acedf015b8cfc445af30583815e': 'Display Content Area',
  '5a0fbcaaeb439684bb4ae5be579e4cd4': 'Form Display Mode',
  'f99d7e5f15906ca78c45753ee3b04a8b': 'Horizontal Ratio Of Form',
  '9970ad07468267e2f309f1467c75bb80': 'Theme',
  'fbae87bcc352f6933541fb77a07418ed': 'Primary Color',
  '540f84ddc0883866b229f71c2844199a': 'Danger',
  '5e5d3f13111593b2710673006d4c8297': 'External Layer',
  '65810a32a98f09be550b0c421df6c540': 'Header Area',
  'f3b9889baa6d17ec63f05ea7d326bcfa': 'Footer Area',
  'da71dcbb13405815476cef28a8b9c4f6': 'External Layer Of Button',
  '6c5b1f0e8e361a801fa75da070d1cba5':
    'It is used to display the pure text. The html label is escaped.',
  '67e77a196826a8880e47ad949ce08ac0': 'This a pure text.',
  'f8fc21a9fd40881e8fd3d7f15919465c':
    'If the current field has a value, do not set it any more. Otherwise, the new set value overwrites the old one. It allows you to get the variable using <code>\\${xxx}</code> or write the template logic using lodash.template syntax. <a target="_blank" href="/amis/zh-CN/docs/concepts/template">Details</a>',
  '83077805e05ac6bedad69b47fca4462b': 'Progress Display',
  'c77048def6e8a2d6c556a3fcc9c66730': 'Progress bar, progress',
  'b1aefb18d4bf96dc283a26d67abc41a8':
    'It is used to display the progress. You can configure this option to display the progresses in different color.',
  '2dde3029c4170a1c8e961a90766e0194': 'Inheritance',
  'ce179eca04fab0d584506b0d19736836': 'Linear',
  '2db0fcd5342b479688fd999a0108ef5a': 'Circular',
  '3fa8b34a2744f62fe93dd599a275af39': 'Dashboard',
  '1ca3fa86d8faa46cc75792bcf90d7fff': 'Progress Value',
  'b0932e5bb7f7d95e3636d82d1a039854': 'No data void prompt',
  '940b12c19fcf7aced0cdd164edc9acbc': 'Placeholder',
  '9cf66e7783e4c9b1d74bcd411edb6950':
    'The value existed when the data field is not defined. The value does not include 0.',
  '2f0faae87508471abce4384b60900e15': 'Line Width',
  'bd9b3f7e564c9eeaedd72f1bcbb7fc9f': 'Notch Angle',
  '31a088147dc0006e4775d066980fa688': 'Notch Position',
  '77a7419dd4fad70c3f3e4b075b2c3fcb': 'Display Animation',
  'a04564aaca658461333b00cbcd071518': 'Pure Color',
  '0a9fc7083e2768107fc178ff36f29ba0': 'Strip',
  'c035fb9e67c0b566fd5d35889035424e':
    'Allocate different value segments. Give a prompt to the user in a different color.',
  '23bf030ca760141f317dde1b7f49b11a': 'Property Table',
  '44f2bc36dacb88424dabf9df71da0e77': 'Machine Configuration',
  '1cb82ab4f259d5b75da0ae86583b31ff': 'Other Description',
  '9a4ca43777061ebc91bc64cb994957bc': 'Columns Per Row',
  '17fa61e1da428936a31b51c955a99d65': 'Display Mode',
  '7fa5c95b26550753b0931fa21cea5d10': 'Property From Variable',
  '6940ea5aa4c18e105cbcd32cbe410839': 'Property List',
  'ae41a992ccceb36f83024f72531186ec': 'Property Name',
  '52dff5b153bb5eaca33a008458ce0209': 'Property Value',
  '42c3762943823c37b537a10c09765822': 'Number Of Crossing Columns ',
  '22b03c024d815ad327e8b95d684ced38': 'QR Code',
  '1857d9050ac0527374f4324c0a5ad910':
    'This option can be used to generate a QR code.',
  'e71377bb59c70af683be127ec49d01c7': 'QR Code Value',
  '57eda9139c0b3bc0605ed4cf303ffbd2':
    'It allows you to use <code>\\${xxx}</code> to get the variable.',
  'f13c3e0717ea842cddc1402e8dabfb6b': 'Complexity',
  'c4bde7dba8c6eed0ca0165b071b259bf': 'Width And Height Values',
  '2f97db95d75280bfedc5afa72d2c717d': 'Background Color',
  'ebf2453eddf55441b711d187f3872ffe': 'Foreground Color',
  'd5e6d5c44426a82e18e31eadf7f1e09b':
    'It is usually used to reset the form data to the initial value.',
  '6a06f12bdf3fc96df6cb45467b9a7c2c': 'Service',
  '9cc50da6bb17ea7ecf44c254c9b37619':
    'The functional container can be used to load the data or the renderer configuration. The loaded data can be used in the container.',
  'c5a33208cf3deab68cd9fe34679edff6':
    'The functional component is used to fetch the data.',
  '54d99a6a50a2373055c0f66ab727a869': 'api initialization data',
  '7a0c222fcaa42473d1c75c113c3641b5': 'api initialization completed',
  'acd4bd22755a537431d74b28c3c2ad67': 'schemaApi initialization data',
  '5a7d03912f38d0b24d04b28da40864a6': 'schemaApi initialization completed',
  'f885d4055567877facf0a3ff376a114e': 'Re-build',
  'c8f0e77a9eb5de26e6ab62695d8494b6':
    'Trigger schemaApi refresh and re-build Schema',
  'e22855f53b7a1ab33e920375c0cd0e3d': 'Variable Assignment',
  '8b10146a8a896b890b3796eefcc3c6d3': 'Update the data in the data domain',
  '47d68cd0f4c3e91a86d23afe8afccfb8': 'Service',
  'f754888421621d122c110d83e710e9d3': 'Add Content',
  '31f8a7a967286a16eb404e3ba237619e': 'Data API',
  '54f876a529283de5668426b2dc8adb15':
    'Set the default prompt information of the service. It is useful when the service does not return the msg information. If the service returns and carries a msg value, the service’s return result value dominates.',
  '7fa237c1b62d04aaec0144d1fc89d620': 'WebSocket Real-time API update',
  '9dfeacc54ab6cd6d2ac08df387777f9e': 'Initial loading of Data API',
  '98223d478e88ccbc2406412a46dda8c2': 'Silent loading',
  '71ae1c76cc4160f8fb76e404e35ca08f':
    'Whether to display the loading animation after you set the automatic timed refresh.',
  'b897babfafd35cc5d6e66470115c93cf': 'Stop timed refresh detection',
  '11bcbff684dfe6edf36e1fd1adc5ba30': 'Schema API',
  '8cc3239eba9fe65b99242adb33634b33': 'Content Schema API',
  '3f423669b0ffeb3993b95085cd8a111e': 'Initial loading of Schema API',
  '8e8aaafe8db0d8eb05e3b11550cbabe7': 'Global Configuration',
  '7481babe858320dd6a4adcf307fd151d': 'Customize Function To Get Data',
  'bfdee34e14602e3113c88a9145843e86':
    'Transfer two parameters, e.g., data and setData',
  'fb559ab354303d1927dcd9f0f2dffa23': 'Succeed to get data',
  '56f0a1c0bc0408556c5810ea4f219dd4': 'Fail to get data',
  '29326bcd28fb39bd41e54242fa532c85': 'Trend Chart',
  '270301455c3de762a7e2b145dac7a8b4':
    'It is used to embed and display simple charts',
  '44e13bdad8c7eb6391e84d940513b927': 'Status Display',
  '82a3047196be368be13dcdd2373520ff':
    'Display the status by using icons to associate fields. For example, display √ for 1 and x for 0. You can customize this block.',
  '774b2bcaca8a64f46c84b510cec89109': 'Icon Configuration',
  'b698a95f1e217e5465835ee0d23b1b1c':
    'Configure different value segments. Give a prompt to the user in a different style.',
  'aacb2b36c47395e4b4b409e351eb4279': 'Steps  Step bar',
  '863a8583132d087e57aebb7d89e18a50': 'Step 1',
  '9757f2c59c17e9aea46e0c8adb69597e': 'Step 2',
  '207e30c0e7318027d521dd7c6fab6a99': 'Step 3',
  'b8a2d347bdb22fde367a851df8335771': 'Step List',
  '59cecbff0cc77511590d2161cc3058e9': 'Current Steps',
  'f3f08da7f65e1d5596a66cedd7caeb9a': 'Start from 0',
  '6bf1f392c0a404d1f7558e6dcdd6c2e6': 'Current Status',
  'fb852fc6cce168301447d1baff276dc5': 'In progress',
  '8797922788916874c39ee1524bbc3638': 'Wait',
  '769d88e425e03120b83ee4ed6b9d588e': 'Completed',
  'ad8e01fe719bf1a5af82ee0d100d246b': 'Error',
  'd1f03ea8d9d3c3a241e8a340b8a384d3': 'API For Getting Steps ',
  '74f0f6730053049f4c9beca2ab00c193':
    'It is used to submit a form. The form verification is required. If it is in the popup, the popup is closed automatically.',
  'ea2b32f5d78d2305b9b7bc21e056a009':
    'It is used to display the table data. You can configure the column information and then associate the data to complete the display. It supports the nested and super table headers, coluln fixing, table header fixing, and cell merging. You need to configure a data source for the current component. This component does not have the data fetch feature. You need to take the priority to use the "CRUD" component.',
  '257f5a3886d87d2255206f86b880d07e': 'Select Table Item',
  '6130b1f75d624b2f73f5d923492e92f7': 'Manual table item selection event',
  'aeddca0456d8fe520dc95545a83458e9': 'Selected Row',
  '4f907cb94921bb62a8399adec922bb60': 'Unselected Row',
  'f3d21138c8ecf5683503c4f814cc7199': 'Column Sorting',
  'd84464cfb2a5828a200fe9c28a323122': 'Column Sorting Click Event',
  '652f155e644e82ebb0a1aed97ab6ab23': 'Column Sorting Name',
  '460e3a697d1680445a47139c0816fbe6': 'Column Sorting Value',
  '3d0b957a99d0c366612c01913e17a0c7': 'Column Screening',
  'b35963687361af98e6acdc004e87fc3c': 'Column Screening Click Event',
  'ad11fba3ac676233f3105e76e7de0501': 'Column Screening Name',
  '8e4b9c88c51aaad1a28a28e8b536697f': 'Column Screening Value',
  '93a5a0253f11e3a2e58f4e87a52fb094': 'Column Search',
  '6d4c4990ab2c32efe8a17c5f22e10cb5': 'Column Search Click Event',
  'a80a4486100baf3f45fab3a59e4a816d': 'Column Search Name',
  'b9a565fe1dc488efae1d63464f277f09': 'Column Search Data',
  '85ddd38957256b6e9026f42ed570bc35': 'Row Sorting',
  'd7a66def82af88cd5d408e38feb8a65a': 'Manual drag-and-drop and sorting event',
  '1987561c006c7192ab619f81103d2a2f': 'Sorted Data',
  'ecfebbc91e2c18a512aeb11b7da15193': 'Column Display Change',
  'c94f45773a42dc386b9c9dcdc6fa542b': 'Column Customization Click Event',
  '70567329ee851a5ba7e7301bd8e9d9a1':
    'Column configuration event displayed currently',
  '76e47871d654c3b0b0e301c0a076e55a': 'Row Click',
  'cc13521eab2c7423b3fb857772405cc3': 'Whole-row Click Event',
  '2fbbf5c38b66ac5496ac42246bbe9e0b': 'Row Click Data',
  '8eb3c8b16106e5487cd1fa3b8a1342ce': 'Set selected item',
  '908cc16fe4f7972450167e26276ac726': 'Set selected table item',
  'a2b39e5a8b5015234dcd8e07a2e00e3d': 'Selected Item',
  '366a3c07289bd6efb7c2a182f7a12772': 'Set to select all',
  'e97c09cd119b64ae0a8dfd42a1d449cb': 'Set to select all table items',
  'c3e8652924c258e121eed16414d3a9e5': 'Clear selected items',
  '7619ec29c0a854dd49e0a7a47bf1a127': 'Clear selected all table items',
  'b94bd878cae4ddc567b00a2dc4f21d74': 'Enable Sorting',
  '5dc3017c21ae2e31ab127dbde8ec80e9':
    'Enable table dragging and sorting feature',
  '25915fb58615ba9a5e145efa252fec30': 'Auto Merge Cells',
  'd7d0936858fcdf4ffdb2899451ec74fc': 'Set Number Of Columns',
  '80174cabf025dfe269aee5390b813708':
    'Set the number of columns from left to right within which you can enable the feature of automatically merging cells. Thus, you can decide whether to merge cells according to whether their field values are the same.',
  '********************************': 'Column Display Switch',
  'cc42dd3170fdf36bdc2b0f58ab23eb84': 'Enable',
  'b15d91274e9fc68608c609999e0413fa': 'Disable',
  '6fe0b71c07a5ce5f7a09f7fdb1d63827':
    'It is enabled automatically when there are more than 5 columns.',
  '2206c0e11aa5f4f154aa9e5dfffcb474': 'Whether to fix the table header',
  '5006fdc5659989e42c3855c17c57f878': 'Enable Bottom Display',
  '9db359f376a1a588ef7dcbef53cc114a':
    'If there are too many columns, some columns can be displayed at the footer of the current row.',
  'df74194830e695efbfce16c0c64223cf': 'Default Footer Expansion',
  '94be543c4fd399f0839211464c8583ce': 'First Entry',
  '9a7b52fc8659f1786907fe93efa85bf7': 'All',
  'b07deca9076bd3354b1b2709d58d725a': 'Fail to expand',
  '76b3250fb1e8593fac075b64029300fa': 'Row Highlight Rule',
  '1fe38acb67e766f7767d9f8e88bfe990':
    "It supports the template syntax, such as <%= data.id % 2 ? 'bg-success' : '' %>.",
  '734ebf8b33422c456e937fc27c9a16ce': 'CSS of External Layer',
  '320f489db3dade075d69f155b346f98b': 'CSS of Table',
  '08ca0d502abc4336855d837f281caef4': 'CSS of Top Layer',
  'b6469055adf2b7bfb187f74ae17dfe54': 'CSS of footer external layer',
  '39ff38577b97cf98fc130f9bd596d4c4': 'CSS of Toolbar ',
  '175e01917d9d4891a40eab43f4487030': 'Table View',
  '9fe8304dade75a37bc04f45515688325': 'Display table type',
  '2560b304e691da78ee2e02f5af9b494d': 'Region',
  'f7d29dfae05b5d049b64b040b14d9a00': 'City',
  '44e7ebb4007104495dcb7afbbb6778fb': 'Sales Volume',
  '3f0cb8b8c238c3b4e08898ce6d449c8d': 'North China',
  '692e92669c0ca340eff4fdcef32896ee': 'Beijing',
  'b8b75a5f9109919ff3f67b336b62afe9': 'Tianjin',
  'd1c2b2d68063b4a57af61e3027861cd8': 'View Width',
  'b32ec25f2bdf7b2eed5e947cf82a4fde': 'Default Cell Spacing',
  '8a42ded5c9d58f3dd9e3a8968ec04b34': 'Display Border',
  '9b4bae5d8251de0b6f00b704936b00d3': 'Border Color',
  'd273f56b3e598e794c3480f1e62f3ed9': 'Cell {{@1}}, {{@2}}',
  '466c65230ac92494c3af79757b4b78aa': 'Row {{@1}}',
  '1ebd0cd417700f3f4a7ee5f64518fcd1': 'Cell',
  '4d775d4cd79e2ed6a2fc66fd1e7139c8': 'Display',
  '7ec907e7059b758ace2f3adb9bb803ff': 'Text Color',
  '104711e38d3cd9335dbd1f4301178edb': 'Text In Bold',
  '65194da33aa3aa1d0fd08b5690af6f26': 'Cell Width',
  'fbd9998b10e690230b0f2fa9b24087ac': 'Cell Padding',
  '0bbc2ea4e1d1f23feb576de5dca1ce3b': 'Center',
  '4745afe0f89f665e41adf819da5df1b6': 'Number Of Horizontally Merged Columns',
  '7c2e1f863e86715e892f61a54e558b20': 'Number Of Vertically Merged Columns',
  '5a431ad16d8f7f23fac3be5650e51caa': 'Row Height',
  'db439b129f3143e14a7024f08ea3732d': 'Row Background Color',
  '30d6ed36667cb98165f07c59702754ea': 'Grid',
  '49d45317662097180e27fa53235d9b13': 'No corresponding td id is found.',
  '013326241579b9b2735756f2204bf8bc': 'No content in the column I',
  'a896691b72032fe21a00b6487381a529': 'Add A Column On The Left',
  '24e4bbbf29a8d31e711c8d9366bf1a6f': 'Add A Row On The Right',
  'aea2dd682bc9dc2974dd971581148459': 'Add A Row Above',
  'bac058b86f8fd4e8f5e2ef3807799aea': 'Add A Column On The Right',
  '488d0742c010851e9c6ce3264df9542b': 'Split Cell',
  '9377e388f7189d6103a3985a321115c8': 'Tab',
  'a8b1273cb2d53ad858906ff9744a9891':
    'The content group can be displayed in the form of tab to reduce the user cost.',
  'f78416dbd6c6a40a3ecd1c1f2b0672c8': 'Tab 1',
  '9769ee568100b0c530a06ec3f0c0044d': 'Content 1',
  '4be268145385303e8ebeb480458a380e': 'Tab 2',
  'c50159e2acff0f4ffdce4c67ec3513a3': 'Content 2',
  'c8794c58d0eb020ca40905d1904d88b2': 'Modify Activation Tab Value',
  'fcd3abb110aab48ebd0ac2a1d7040d6d':
    'Modify the key of currently activated tab item of key',
  '9d000284174ff09642502803887f28ed': 'Activate the item ?',
  '29645b509093191cad34c673c1b3efb7': '?',
  '9578012b7d75a3a47c76acc176caf403': 'Active Item',
  '3ba265c6b63bde0319822afd6b9a649d': 'Title Prompt',
  'b744b72fd649d904e561358fc26c455f':
    'Pop up the prompt when you move the mouse to the tab. It can give a complete prompt when the title is too long.',
  '6e7fe62a865cb9ae90e52f85f4c6b8a0': 'Default Tab',
  'a8986713e2b83c9ecafe5b107c09b68e':
    'Display a tab by default. When you configure hash, use hash. Otherwise, you need to use an index value. It can get variables, such as <code>tab\\${id}</code> and <code>\\${id}</code>.',
  'acb839aac679bb34be9b4b49806b74a8':
    'You can use <code>\\${xxx}</code> value. You can re-render the configured tab dynamically according to the data.',
  '5b9af1bc3012bb3c8e07b983b423ec17': 'Render Content Upon Activation',
  '4bcecc15d16e8c851dc3d155b8f30929':
    'The content rendering can be conducted to improve the rendering performance only when you activate the tab.',
  '12c6a62683d63e404d71a31c9cb5209c': 'Destroy Content After Being Hidden',
  '5b52bcb62a2e1dab99ef841b05395b6d':
    'When you destroy other option cards, destroy the current content. Thus, you can re-render the content when you activate the tab again. This applies to the scenarios for getting the data when the data container should be rendered every time.',
  'ecfcea4b381d761fecd512761bc07954': 'Linear',
  'b30f254eccefa14c9980235bcbec74f9': 'Simple',
  '3d7443aeba7c8eaf1cbb42ad5232fa10': 'Enhanced',
  'ee5e5a588705699a51eb3c5778c3020a': 'Imitate Chrome',
  'c5b8044dacf2e63931d85e5e307a9168': 'Horizontal Fill-up',
  '22d18bf0c476ebe7aa9303108677ff2e': 'Selector',
  '5bff38cb05e3710a0c0cb16ed3ced78f': 'Sidebar',
  '593c1c61592f80831f58b2e44cfe63fa': 'Title Area Position',
  '030c8cb75e9707285b28c4931bfeddc5': 'Title Area',
  '32b4bc87bf7f95cd6094992f0135ff7f': 'Title Icon',
  '996a919888ae86e842c76245daae2360':
    'After you set it, update the Hash of the address bar synchronously.',
  'f3051dd9b3538e170322fd5224b28de0': 'It Only Activates During Use',
  'ee51f2d49fa12c730d2a0efef0d67e44':
    'Its content area cannot be rendered until you select the tab. Thus, it is possible to improve the rendering performance.',
  '024f24defb08c5c9d463a2668cbb9802': 'Destroy Upon Being Hidden',
  '370bb4d6806c88a7df2ac17ca2a7b6a6':
    'Disabling the tab leads to the destruction of its content area. By configuring the "Render only during activation" tab, it is possible to realize the re-load effect every time when you select it.',
  'f6724322c613ca164ea9a9d03e9055c9': 'Card {{@1}}',
  '8a471486c6c7bbe43e14392c6b127aea': 'Asynchronous Task',
  '4f58f808d62c4e31c347e483898396d5':
    'It is used to show or perform asynchronous tasks.',
  '9ee043b0a77a26d22eec0f4ea99afbd3': 'hive task',
  '2c8a99d35cb5704994cabcc61a4c3a4a':
    'View details<a target="_blank" href="https://online.iwhalecloud.com/">Log</a>.',
  '3709f71c9552ed5db76cbe8f3cb5d4be': 'Small Traffic',
  'c60ad696dee4e1eeff6f0f2c2e9b9fc0': 'Total',
  '5c0dc424442c913c6d16a2cf43137da4': 'Initial Task Information',
  '78caf7115c5140f8913c581920239f22': 'Task Name',
  '3a3778f20c0e1a55adafad4861a71216': 'Task ID',
  'bc7e74f7ccf8ed6fa5b7b7649b221daa': 'Task Status',
  '1d35dcbf191e36dcc6c15f71277d72ed': 'Task Description',
  '76ba17faedd82297d09b2edd70c5914e': 'Add Task Information',
  '093bcd735847b8464d683464165adbb8':
    'You do not have to set it if the detection interface returns this information.',
  '7dca021cccc260dbe1d81dfc6b29f513': 'Status Detection Interface',
  '358e55678114f19424efbb42c0a927d9': 'Timed Detection Interval',
  '77bd60ba17a73ede5d81c4eeba0f830d': 'Submission API',
  '1e057692fcf81e07e20b5f7c9073ea35': 'Retry API',
  '24e3562a3262e80c3119f22b8f447f64': 'Title Of Task Name Column',
  'cb8e07cea4df337bb6dcb8362b5f7e02': 'Action Bar Title',
  'f2acd3adcc0a0d73b318c83a29a4d2a9': 'Status Bar Title',
  '8a4cf07caf84c91a87e8ff3c48a944b9': 'Remarks',
  '0cbbb89050458c2bcf0ca98c19dc8864': 'Remarks Bar Title',
  '879eb99c7b1aa3223925b9b2dbad4c63': 'Launch',
  '804b6382fa6d8b53c5a2a409f30f7fe2': 'Re-try Button Name',
  '132c5cdcceb0f1f17c8c088a42959aa4': 'Try Again',
  'dd4e55c39cee201b82dbc9cb2aca173f': 'Do not get started',
  'c0d2181d579cd1e965ed10d5183b1fc0': 'Ready',
  'fad5222ca0acfaee54f06458188d916a': 'Completed',
  '7a4b9e6f14bda48d2c3bf0fa431bd2b3': 'Configuration Of Status Label Text',
  'f198581dbbc357ccc0283cfe02d56edd': 'Initial Status Code',
  'd6bab2368de31490741ed95f732aaa25': 'Ready Status Code',
  '6eafca9359acbb0bedcf86d6b8609e41': 'Status Code In Progress',
  '7e8b2e41a303cb8532b9ad2006da3c25': 'Error Status Code',
  '231b6f799949f9a743d5193006a15af7': 'Completion Status Code',
  '003797f6b66c67cd87ec684cacb4ab70': 'Error And Retry Status Code ',
  '89d19c1fda4906bd7a336895835ce20e': 'CSS Of Button ',
  'c52b46333f6d5d2796ee64cb359cd58a': 'CSS Of Re-try Button ',
  'b091a100499d48dd4ccf0b982aa37d68': 'Configure CSS Of Status Label ',
  '7ac24322bc8eeac88db6823942423ac3': 'Time Display',
  '82315a0cd63e7f81233ad804e0d02deb': 'Time Value',
  'a389ce9c52a94bbdd5c7fa84af85348c': 'Display Time Format',
  'dd438f6fb09d6223fd95df16383f0f44': 'Text Prompt Container',
  '33813749a95477897085e2435acc16b6':
    'It is similar to a container. Multiple renderers can be put together. When the user puts the mouse over or clicks on the container, a floating layer for the text prompt is displayed.',
  '9b14c9051067bef2dd9a15683201dd18': 'Prompt Text',
  'ab97cef55407efa11f79211e17cb2b4b':
    'The default configuration is "Mouseover".',
  'b47707f0e916e3e3f4ba885bc2cf2c11': 'Theme Color',
  'a32b3b848eee6929634dfc9a9d8bcdb1': 'Bright',
  'adb7e5312abdb9a44297e48d63815fa3': 'Dark',
  '0de826c66ae3fe8043e9a39b35616ee6': 'Container Inlink',
  '4583d3453c31cd3ff068c1358d8e7f1c':
    'Click the area outside the container to close the prompt',
  '8e290c31bc0b4f76edbd58c3575b8420': 'Floating Layer Offset',
  '03c87fc8d49f865f0c2895d9ef3fe352':
    'Prompt the offset of the floating layer position from the "Horizontal" and "Vertical" position.',
  '4e3e1e12e701890f4461808cc8f9d407': 'Can Enter The Floating Layer',
  '9d6246f57f6924410b0c68f2172420f9':
    'With this option enabled, the floating layer is closed after your mouse is over the prompt floating layer.',
  '90919000a708f8d66b7591e21b8e33f1': 'Display Floating Layer Arrow',
  'ed81f127f3b2aaff73a4f4dd5968fdcb':
    'With this option enabled, a prompt floating layer does not display the direction arrow. ',
  'b60e5222037939812dabb7da9979c27d': 'Delay In Enabling',
  '82bb338503938f2da52e91f7244a34a2': 'Delay In Disabling',
  'd55c726c99995e106ba5f3bb2b791a86': 'CSS of content area',
  '0ce2605d7eed6782adb6bc62ffae2335': 'CSS of floating layer',
  'edb5acdc9ee5e75fbc238ab4a0300eaf': 'Text Content',
  'b1f824deef0d11e1fe3b73167a902e31': 'Text Style',
  '74cfa7e77be335e8e5489a00ef099cb9': 'Ordinary Text',
  'd482086f653d92fa8f1011d39738dba3': 'Paragraph',
  'ae27115431c46fa374ac28200304f341': 'Level 1 heading',
  'de86106d6632da3fafb946f85ba91324': 'Level 2 heading',
  '27f2c5c60f373a4380ec107ad8895f0e': 'Level 3 heading',
  'c961174771e843ac4046b2b21c49424b': 'Level 4 heading',
  'a791de104833c917f801c1976b9af960': 'Level 5 heading',
  'de2687f9a16fea3654be84c74137b805': 'Level 6 heading',
  '65862c97143c6c5479e0c623093a25d6':
    'It is used to display a text or paragraph. Also, it supports the template syntax which can be used associate the dynamic data.',
  '38accbc34901ee6fd7bd7cd9f92f0a2a':
    'This is the current time of the template content <%- new Date() %>.',
  '590e147e49735ebbfc51ae2175c36eb0': 'Edit Content',
  '47ae015d04dda362d066e4f9ac09d647':
    'In the inlink mode, the <code>span</code> label packaged content is adopted by default. In the non-inlink mode, the <code>div</code> label is used as a container by default.',
  '7fcf42edf5817042904f4a122ff77582': 'Video',
  '1d5bbe0ab0d90a223c162fb375997a98':
    'The video control can be used to play various types of video files, including flv and hls formats.',
  'f50bc38cf567e68250a8d15edfd8eb27': 'Video Address',
  '733dda7842619a437b2486f6f71b0c10':
    'You can write a static value and can also use variables, such as <code>\\${videoSrc}</code>.',
  'fb103fc64a0caeec24c707b9e7b50870': 'Video Cover Image Address',
  '828f83110677bab8ef940f79f77b9049':
    'You can write a static value and can also use variables, such as <code>\\${videoPoster}</code>.',
  '351cb1f8ffbcc9d2d4c1f35505e15864': 'Mute',
  'c7638cec349cb86eaeaeb983909fae0e': 'Live Stream',
  '82f5f7bf3fb529360947cbb3b988037f':
    'If it is a live stream, select it. Otherwise, it may not play normally.',
  'df0134afa26415a560ae1320dee10c19': 'Video Proportion',
  '0d682d277649d8c7952d36f836619a44': 'Display Cover Separately',
  'ee1600dfbd6f9e86ca8761cf3fcf6a17': 'Video Rate',
  '8e7124c3069460d4a8a04c3e3d9ce752': 'Video Frame Information',
  '1fc7d723b3a82ce32bfbbfa0a1761969':
    'For example, enter <code>\\${videoFrames}</code>. You can search for the videoFrames in the current application domain. If it is an object, a video screen-shot list is generated. After you click the list, click the screen-shot to redirect to the corresponding frame.',
  '1fa035e78c5408c9079c20637acdb2bb': 'It is used to render the Web Component.',
  '8df7c8a1f9f579f0ddc35bb4ee50f166': 'Package',
  '5acec91385a3b9093e3c803f6b0d869a': 'Wizard',
  'c8c0339a6f9e105cfef45b76b788b635':
    'The form wizard can split multiple complex form items into multiple steps. Then, it can guide the user to fill the items  step by step.',
  '155149d24d20197bc0836bededf63abf': 'Click "Complete"',
  'd384a3c931bdf315e4760c9fc5980e6d': 'Be triggered during final submission',
  '335d6c56c43204f9efcefe36f097d35a': 'Form Data Submitted',
  '5be9e21eb0797c9faa053eb0237c36f9': 'Step Change-over',
  'd837ddaacb39a13806590da335e89397': 'Be Triggered During Step Change-over',
  '02f706d7510e68c96aa073852d90ec20': 'Step Index',
  '33c627bce8015c50152941a5b6fada32':
    'Be triggered when the final submission is successful',
  '574f27f7223c86545a7724d18da96651':
    'Be triggered when the final submission fails',
  '8a427977d8135a019e4f1056120bfad2': 'Submit steps successfully',
  'd65dcca33a0118a0a5ce343264192ea6': 'Submit a single step successfully',
  '5d7d91d9da162ee8fddd4d331d1434b5': 'Submitting steps fails',
  '1c11d38e7ecf2facbf82f772d9222d45': 'Submitting a single step fails',
  '357954d848a9e2f12208673b3906a972':
    'Error information returned after submitting a single step fails',
  'd6c21651c32c63c8d61c85944c2c91af': 'Submit All',
  '75e3dc4be4ae7aca7e1cebc13f7e486a': 'Submit All Data',
  '0c3005b490ef428660ca2265a714bdbb': 'Step-by-step Submission',
  '832efcc5c30746b84b910cde8630d491':
    'Submit the data of the current step data',
  'eeb6908870e058bc23d52c1e405a054e': 'Previous Step',
  'fc5dbc5789e158384f634eb8ff466b46': 'Return To The Previous Step',
  '38ce27d84639f3a6e07c00b3b4995c0e': 'Next Step',
  '5fa57cab26a9d4e659c2e497018729ef': 'Positioning Step',
  'fce22163929e8191b7de43699316f2a0': 'Switch To The Specified Step',
  'd529f7d5ed8956cd890173b4b5045a67': 'Switch To The Step ???',
  '4a0ff5106d129883b446a29b1dac6f47': '??',
  '78ada959bf5bdd6c70ee411c4cf23601': 'Target Step',
  '098521c027a49fba5eb7f35430a6a9da': 'Setting Steps',
  'c5538d5c74235d2988e51e632c4eed0b': 'Add A Step',
  'dda36edbd4626e7fc868c14f9aa1556a': 'Other Settings',
  '1fd41e410930ac58e748f7704c3a05f3':
    'If the API returns the  <code>step</code> variable and the value is of digit type, e.g., <code>3</code>, redirect to Step 3 after the submission.',
  '938b484df1447d8f01f96e45125eb031': 'Open or Not',
  '20022725ac2c53869f7af6646ca4ba29':
    'An expression is used to decide whether the current step can be open by clicking it. Additional available variable: currentStep represents the current step.',
  'd4b8306441c00f01d4f044b3802c4266': 'Default Start Value',
  '37c0c041a0ad487d23c9f42c29f6d5e1':
    'Get started from Step N? It supports a template. However, you can render the template and set the current number of steps only during the component creation. Then, the current step cannot change according to the startStep when the component is refreshed.',
  '076bd7c0adfc4f5d2abde6b309d9f53b':
    'It is used to initialize the wizard data. When the <code>step</code> field is returned from the interface, it is possible to control the default redirection to the Step N. Note that the value must be of number type. When <code>submiting</code> is returned and an asynchronous save interface exists in the current step, the wizard can initially enter the asynchronous submission status.',
  'dbb19fea1965f7ef88cf1d1e0450c0f4':
    'It is used to save the form data. In the last step, click Finish to trigger the saving. <code>If the save interface is already set in the last step, the setting here is invalid. </code>',
  '0f04a65952b58cbbc5ca6cba868c3bec': 'Previous Button Name',
  'e54827ae56fcb690d879b9cdd29f0ac7': 'Next Button Name',
  'abb7ba84b95c6c90341ac9c883fbc85b': 'Save And Next Button Name',
  'bed196af058f458def957031f88abd09': 'Save And Next Step',
  '81b522590d543401ad15ae8a9155361d': 'Finish Button Name',
  '22c2aa6b59ab30c88fd84e8e5b3c4ad7': 'Previous Step',
  '7b91646d808737e7138ad8f32a3b6cde': 'Next Step',
  '52b36576f88c31ed3971ca4d1fccd46f': 'Step',
  '8164ad50987e0508caf7638c663f8b7b': 'Step {{@1}}',
  'de5e232d10e2fa6218259289d4de4835':
    'Similar to the container, the only difference is that there is a layer of padding by default.',
  '3954d7a9c047b7522ef92ddd5fc35852': 'Padding',
  '********************************': 'Child Node',
  '5db7ca044a5179bf05428b283ac0452c':
    'After you set the style, the size setting is invalid.',
  'c5e1f01e3d98b4db536765ae0d5b91a9': 'Click "Select"',
  '7c57a563ab87bc6eb5edd8f5b953f499': 'Interface Setting',
  '6aa351f5dacd13d3d862d9c93e4a0241': 'Sending Method',
  '8dc91bca9bc83efea73150e3478657fc': 'Sending Conditions',
  'bf9e242338d2c26b182aa6b9c015d84c': 'For example: this.type == "123"',
  '91ee84292a5bf5e59d3b6309f948f2f1':
    'An expression is used to set the sending conditions for the request.',
  '55409342e28d37db86fb23efbd84a025': 'The sending entity format is ',
  'e06a14abe7ef66a8ead143db4ae9786e':
    'When a file exists in the sending content, the form-data format is used automatically.',
  '773a0e8384fd6f784088b829d7cc2f68': 'Cache Setting',
  'c1b110f13431df9662299f26def71df1':
    'Set the valid time of the request cache, which is in ms.',
  'a18ea11244325dd3d20c5988bc7f6e39': 'Download File',
  '68caa6082eda1745aa3f6b6d12efe423':
    'When the interface is for the binary file download, select it. Otherwise, the file shows unrecognizable codes.',
  '91831507074270c0da8a31ad9ff87495': 'Data Replacement',
  '42be3061671b38468cc6ac84f6a0dd77':
    'The default data is the append method. After being enabled, the current data is replaced completely.',
  '4e7c006f535b13b9737ac310bc34193a': 'Initial loading',
  '********************************':
    'When you configure the initialization interface, the component fetches the interface data initially. It can be modified according to the following configuration.',
  '713ec76479b992652ed39364d3d03870':
    'For example, this.id represents the initial loading when an id value exists.',
  'e5e3131aaf96b6dd10574bc9beeaf934': 'Timed Refresh',
  '81fe75a5216d4f612f1809c122f5145a': 'Timed refresh interval which is in ms.',
  '83f16354dd1532422dc8b3581d096e7b': 'Stop Timed Refresh',
  '620f826a77f079c5683a9d3c59461ea1':
    'The timed refresh is always triggered once you set the timed refresh successfully. The refresh proceeds unless and until an expression is given and the conditions are satisfied.',
  '90260d55567cfd97ec2f085963a60bcf': 'HTTP Configuration',
  '9ae7a582479116d4cb41e828fbd59798': 'Send Data Mapping',
  '0fcbf036057c6dd88b7b809daa0c5eb7':
    'When the data mapping is not enabled, it send as much data as possible when the API is sent. If you want to control the data sent by yourself, or need additional data processing, enable this option.',
  '5414824fb8efdb7d59beae4bf95fdefd':
    '<p>When the data mapping is not enabled, the sent data is automatically cut into the whitelist mode. Thus, what is sent is determined by the configuration. Bind the data, e.g., <code>{"a": "\\${a}", "b": 2}</code></p><p>If you want to customize (???) based on the default(?????), add a Key as `&`. The Value is `\\$$` in the first row. </p><div>When the value is <code>__undefined</code>, it indicates that you need to delete the corresponding field. You can combine the <code>{"&": "\\$$"}</code> to achieve the blacklist effect. </div>',
  '7dd590a9d9e783e980d318bd52891905': 'Return The Result Mapping',
  '7e295b6ff39ec7356e06c4534bfc4fb3':
    'If required to process the additional data for the return result, enable this option.',
  '417125a06b1d2bfff025e83a4e067bf0': 'Sending Adapter',
  '62efcb25e5b21da47c09780119da3458':
    'Function signature: (api) => api, The data is in api.data. After being revised, return the api object.',
  '6eb8944029108ad3b6bb3572a648fafa': 'Receiving adapter',
  'e83cbec70e17988749c4a02a3b73f55c':
    'Function signature: (payload, response, api) => payload',
  'be47bd270e7756d4233e59bbe4cd5b96': 'Request Header',
  '9e8c8bc795ad25fc992cee9d81a8c46d':
    'You can configure the headers object and add a custom request header.',
  'be604f8b7ec5e80288b091ee12bbab7f': 'Point',
  '9adcfe38533f68035b2bf7831d310381': 'Ribbon',
  'cbc608353218e1d63e6f47c9db1eae64': 'Text Content',
  '2613e43b46ca52dabc232054c1611c80': 'Corner Mark Theme',
  'd8c7e04c8e2be23dd3b81a31db6e04f1': 'Information',
  'e4f10a8916d4c5375529e21d9b66e5f9': 'Corner Mark Position',
  'ebc556841a9264ebaab728efad7af777': 'Offset',
  '81cdf47e8d8adfc70faac2cbc55e4067':
    'Offset of corner mark relative to the "horizontal" and "vertical" positions',
  '7ba3812c61e01b3049404a46fac8deda': 'Customize Corner Mark Size',
  '29e13ada94b145a1359291d5e1564655': 'Capping Number',
  '42becf09dfd209746b66726e7d21d014':
    'It takes effect when it is a number in the text content.',
  'b599979e9a40823363451aeaadc0723f': 'Animation',
  'b8c467fce096a649583c0bc9d9281a5c': 'Corner Mark',
  'e35abd1e31f244eb08d1f62e6f825df2': 'Bind Variable',
  'f7d2996639d97b4a03fc0e40e2eb853a': 'Shortcut Key',
  '71dc8feb597052ecd0e73c9062eecdeb': 'No Configuration',
  'e8755fb1e985a5d26df0fce1f0b7b0f8': 'Add Option',
  '51d8a85a3c59453eed398eb8f5d35585': 'Select default value',
  'cb73fbd12620c6ff4d7e5d1047c3be4d': 'Enter static default value',
  '303efd5ba79e639001b4328cd266dddc': 'Click to configure expression',
  '7d92f998d24da41b58db140b1864f773':
    'The current expression becomes exceptional. The loop reference exists.',
  'b5cc1cd60cd694f45142dc52a5bf53fc': 'The numeric type do not match.',
  '01820262aa9ad5b130f8f5b86bfd2968': 'Customize Option',
  'c8158b3cad598b0b5939788ca4efb298': 'Get API',
  'b4fdf79b8f54856b072ec3874b830d1f': 'Enter display text',
  'f4ab507e2fa2d2bd66bcdeafd9fef797':
    'Keep consistent with the text by default',
  'aafda9e8f6b7b613680677c513edb7a6': 'Cancel Selection',
  '0560b060c438e9326f92718ccbc3f95b': 'Select this item by default',
  '5d26b8a41e805204c9dcd5ea7e23b150': 'Enter text or value',
  '22de6ef85ed60ec54dbdc1d8583e5104': 'Add In Batch',
  '421252e16c6cb544fe9ce0be94a190e0': 'Add Options In Batch',
  'c130bd5b55edefdaf8923269e9a52439':
    'Each option is listed in a separate row. All items with non-repeated values are added as new options. <br/>You can set the label and value for each row using a space, respectively, e.g., "张三 zhangsan"',
  '1e2f96a69fbef8caa8823a3067ebbdc7': 'Enter option content',
  'a4f1ddbbfc96930d24e4b54cb815b62b': 'No Option',
  '5b4ffa2eadaf629b833b37a3e8742b2c': 'No Chunking',
  '3569877e498155b59ef5299870c43f80': 'By Average',
  '15e1fdd9d5cc4dc595fba0eee0719cba': 'By Step Width',
  '56d37871117270ce5d157a8de90dacd6': 'Keep consistent with the chunk',
  '6903085e7f31286560e4a2e160beac42': 'Chunk',
  '7145575ab9e3b4529eea61fe5fe76d0e': 'Number Of Chunks',
  '1c8737ec7da60e12207c9eb04ccabcd4': 'Add Chunk',
  'dc4c91dfaa3b760147bd92e648560af4': 'Subscript',
  '662d8b49913650f543c024d4d02009a2': 'Add Subscript',
  '69fbb2e5fc9eb3ba06096cbedbf5a622': 'Conditions',
  '8baf21fa26d6d24b4faa872953275d8d': 'Static',
  '8494b2036d3ccfe6102e930d9d8a3397': 'Enter {{@1}} conditions',
  '311f6d1fa5f13b0e280d7b3c9d40c5be': 'Expand More',
  '38aa9dc2a0f14555322061d2ff997349': 'More Configurations',
  '355c54009e364bf4396be424fba10e0f': 'At least retain a node',
  '9046ad86a1a47f16e954f2ec38fb680a':
    'Because the level is too deep, it is recommended to use the [Get API] management option.',
  'f69608e93e9728f4fbef583bfa1326c1': 'Option Name',
  '684a0d1aeca4e9acff89221b57826d4d': 'Option Value',
  'bbcbe681f9225f8adf3663f563a9f294': 'Add Sub-option',
  'c08dbaf90614532aed9f526e58b7fef2': 'Option Management',
  '442781667396d6eff51113f482d89e54': 'Add Check Rule',
  'd7a169e81b60ee08c82b5d9de473e362': 'Error prompt',
  '9d9cf35ff82a6d960538ecd650e09945': 'Default system prompt: {{@1}}',
  '5ef6ce89f52b4331b080a0f3019414f3': 'Use system-customized prompt by default',
  'db1cac8e2f6206e8f179b1ff47a676df': 'The data match fails',
  'e9908cdf79e965f6907ce9f291cdfcf8': 'Action Configuration',
  'be5fbbe34ce9979bfb6576d9eddc5612': 'Save',
  'd1d9049139d870edd490215530d90458': 'Execute Action',
  '9eac7e07ca0a3181766e5ecc70d20727': 'Search for the running action',
  '0174bdde9517fa331bf7d716a553e023': 'Action Description',
  '0aeca07a02601a8e701a46d1a8b5ce43': 'Basic Settings',
  'da1ed600ce65be863766444e60c2da05': 'Execution Conditions',
  '13b2de1073f76444c49d2c6a21e46e26': 'Execute this action by default',
  'd80bc0fcbfb250480320b683e48b1467': 'Select Component',
  '07682f1424e400c467accdb556d59e1c': 'No configuration content',
  '2e3ca80a58643bc28e87cc3b17bc9d80': 'Execute This Action',
  '7030ff64701a938becbc5aa67ddb86e8': 'Error',
  'dec2eb7145e149f281cb7e75fbe8972a': 'Redirection Link',
  'ae10a948eca808b3dd77506b24f3fd0e': 'Redirect to the specified page',
  'c7a34a3465d1beea2f85d53edcff8235': 'Redirect To',
  '0b72392143e4038e98128cb0f6f679b3': 'Page Parameters',
  'c068b579db3bf0a553bd0af4f81cc14f': 'Parameter Name',
  'bfed4943c5f487de1b63a82f7230cce2': 'Parameter Value',
  '56aa76ab3c987377e855ae2c6c612050': 'Open A New Window',
  'fd5fb471ecce1eea63a6a95b6707f815': 'Open A Page',
  '67e21dd387607ae3fb59846504fa2c4c': 'Open the specified page',
  'd7098f5050f017673319c5db1473ada7': 'Open',
  '39e107b7c4aa580f913ccbebc00f7534': 'Refresh The Page',
  '261242fe62b18b620419802c7dd7da7f': 'Trigger the browser to refresh the page',
  'ca180138a862543561d3a2c4f08b2e1b': 'Return Page',
  '5f6b9e7a050ae3f34b38191435e14b24': 'Back From Browser',
  '27e0ca877865238aad6940481b2984d4': 'Return to the previous page',
  'f80d12dcd65429fd28841e768062d3c2': 'Dialog&Message',
  '0561589c26e732981f29709a9b574234': 'Popup',
  '256dbc5161ae393ec8a0e83ae6cf9469':
    'Open popout. The popup supports the complicated interaction design.',
  '6cff4b6d794cc17f5d24dbe0d21e5732': 'Dialog',
  '507c1d40c5d6b990cf8c832b0a91cadb': 'Popout Content',
  'a532be3ad5f3fda70d228b8542e81835': 'Configure',
  'b67cbb1ca7439053f06d59aac5e410dc': 'Drawer Content',
  '3b02248ca3790e356e47b6900c0e3931': 'Close',
  'f33c2c6ff58bcec40d3e74e591bb3df2': 'Close the current popup window',
  'e495f416b83e4c7ff3c66ec3be96a76f': 'Message Prompt',
  '61d7aaa88181c527cfb936d4c686d267': 'Pop Up Message Prompt',
  '4e5242a645864528e10f04dc2326a5c4': 'Message:',
  '6d00710a2528332bfcac14b58e412042': 'Message Type',
  'b87b77561e776367e6756e11ea652217': 'Message Content',
  '43ab9af06e1e0f0b2a8767b46cf8b1cf': 'Title Content',
  'f41a94bb85c5223181c4cdf83ea9021b': 'Duration (ms)',
  'a0a837f2873de80bc9ec353c30e73171': 'Display Position',
  'f3296f64a8a1330d7a07f1d269a1db92': 'Top Left',
  'b97a5adf068bee6c852db9dcea3a9799': 'Middle Upper',
  'eafeba264b6338939f11f1b1adf40d2b': 'Top Right',
  'd429ffb093e9aa3bf80da125f1be318c': 'Bottom Left',
  'c241aa8f427118a719b94cbd8f2bb22d': 'Middle Lower',
  '9cd707caffdfb314d939298f2f2c267c': 'Bottom Right',
  '8c8fbec263e20f087555c9abcb6dd07a': 'Display Close Button',
  '3f3a016027e540ef10a16dbd49fffde9': 'Display Icon',
  '0cd902f953656adb29985b68e6fc9754': 'Configure And Send An API Request',
  '1535fcfa4cb8e4d467127154977e9788': 'Sending',
  'c14a21300b61bb83b4420a1586497951': 'Request:',
  '88bdaf32c27ab169d3d686b86b3fae99': 'Configure Request',
  'c5dec2a8d2308c1c15ec2e5441fd721c': 'Static Mode',
  '4abbdba4b6b06ce00702a255bd89c92c':
    'After being selected, the service request is sent in silent mode. That is, no success or error prompt pops up.',
  'e3b49b5bbbdea05598525e91dbdfa638': 'Storage Result',
  '4dca05af026848011eedee1b53efa61c':
    'Enter variable name of storage request result',
  '4da82260041107e5780bcbb3a14ef791':
    'If you need to perform multiple sending requests, you can modify this variable name to distinguish the results returned by different requests.',
  '0d83078816aa273f2941c9b55ec82bf3': 'Data',
  '3f9e257178738d5d180ddc2996809c10': 'Status Identification',
  '99c74120cc62f4bf31d661e3212b7121': 'Prompt Information',
  '89049706952412d790b801def284629e': 'Trigger Download File',
  'bb79667f37035e9562ec6bcffd6cf8ef': 'Components',
  'c852fb60f1b8ce921c3def1eba000bc5': 'Component Visibility',
  '1bd4cfded5e11a7a8ea4dcfd2fa17e15':
    'Control the display and hide of the selected component',
  'dce5379cb978a8259ecfca8f08f00817': 'Hide',
  'edf25860e3d457eb8ca9cb5dca06dfd7': 'Display/Hide',
  '12c8d50c55eeec7059ddd5c303e34f77': 'Component Availability',
  '5e75800641ec5c1198092bcf9d34f180':
    'Control the enabling and disabling of the selected component',
  '7854b52a889b3ef0590d9f542efeb4c8': 'Enable',
  'd86d5919f595226b7a1e8264635ca23d': 'Enable/Disable',
  'c5a9b6e8c522de8a14ad7fab51c1a1e3': 'Refresh Component',
  'be4b778e7f5aa6aa5a811d7db4e1a8b3':
    'Request and re-load the data of the selected component',
  '694fc5efa9e1d1c2c5eb6525e1c7fb29': 'Refresh',
  'ea4d3723b350b2cb8f4c1a615e1b7df1': 'Set Component Data',
  'cb7add16ba6f0cd65d5ddcad71359813':
    'Set the data of the data container or form item',
  'b91ebe714155c83b6d3bc02b675a31e9': 'Data',
  '08ce6e74bb4a64753f1af2e5c836debb': 'Assignment Method',
  '********************************': 'Designate Serial Number',
  'a7b0b80a7bea1e5e973967c179866ef0': 'Enter Serial Number',
  'e887792fbbd65d21e43e832a5cd63aac': 'Enter the serial number to be updated',
  'e3a6f648390842e0c7aa82d0f2c3f6e3': 'Field Assignment',
  'a25657422b40023f2731619587940bc7': 'Variable Name',
  'a33903526e8fb3d1ac3066da70e7941e': 'Field Value',
  '82986a4cab1d0efdbc23b3ac5f0fd509': 'Clear Form Data',
  'f457845da8c119a8688e333a3554284f': 'Reset Form Data',
  'a84a1311bea7370f1749341ffa6f75e1': 'Check Form Data',
  'b7579706a363e5f23b1040fecfbcb677': 'Check',
  '51325230409d4b7c64aaeb3db9904801': 'Component Feature Action',
  '98d130cb9a360df782f6510abacbc022':
    'Trigger the feature action of the selected component',
  '557d01c07aa7c4450a414932e6c1ed2a': 'Copy the text content to the clipboard',
  '5aa4369ec61715ddef3641992fad0d4d': 'Copy content:',
  'ac04259507be8ba6b891dc9dc208f491': 'Content Template',
  'c7f16d729f3bca8f6936416884a74fb8': 'Customize JS',
  '1b5a6299ef404c1f7b4292c290b80f55':
    'Customize action logic through JavaScript',
  '9a2ee7044ff04234a8892a13583d14b6': 'Variable Value',
  'fe9e25f4e4b3aeefeb9b7a9c368ede7e': 'All Data',
  '186c8d63db1c09c38bcfd048fb15846e': 'Scroll To Previous Page',
  'd9b6b8e29d63ac6bb7a0381e994ebcb5': 'Return To Previous Step',
  '47b9cbf9f3a3f08264b19f4a1228e865': 'Scroll To Next Page',
  '211ae8c8666f8b803282a74f90fb0dc6': 'Open Prompt Dialog',
  '56eafb82d11c72b65efe07b9bedb5c19': 'Open Confirmation Dialog',
  'e777eb796f8a02e97a891fde43d10ce4': '{{@1}} variable',
  'cc6aeb073ebc3cb29734a49164f8964c': 'Page Variables',
  '979a50681e278dcc0be18f68459e8217': 'System Variables',
  '8deee3cdecdf06a05d22fcacc7031492': 'Component Action',
  '9654916723a8d1d82f5ab9d2911edf93':
    'Tip: After you add the following event action, the following event action should be done earlier than the old version action. It is recommended to you migrate it to the event action mechanism, helping you achieve the more flexible interaction design.',
  '88f7a3aef4888dd507482aedc02bb808': 'Configuration action (old version)',
  'c500cfabdec9b2761fe9f1aa543933eb': 'Action',
  'fa476b76ccbd4ac9316f0fd80257b77a': 'Reset And Submit',
  'c0a8088f2bbc993500c5a01b0f0a1887': 'Data Source Variable',
  '71448f8c10c79b32312b7e3abe9104ef': '({{@1}} result)',
  'f4381cd48d8cb4307bc140613ea57d48': '{{@1}} result',
  '0b446df580ad309e7c26e5242eddafac': 'Event Variable',
  '1fab2b4934161e87a1f0133b9d5bc1b5': 'Add Event',
  '4db5110d41293fef57f5a1f364187896':
    'Go to add an event quickly and start you product.',
  '5baa9ac58c00a7fb818d4a113d3f8cff':
    'The image upload address is not configured.',
  '7241f67ee4fa8e0adb5d602b5f9516df': 'Start Color',
  '9f1b854df133912bb46203e84f0594ee': 'Start Color',
  '8ea65e3d4e52c871d1c58b1926380ab0': 'End Color',
  '0c46a7f77a7247a9cc6d6e995c0ea8cb': 'End Color',
  'c7706039e8ad85969df13ce3458d199a': 'Gradually-changed Angle',
  'f4882cbf65b232af449d7289354b9a16':
    '* The angle ranges from 0 to 360 degrees. 0 degree indicates that the angle changes gradually from the bottom to the top.',
  '5b9ea77bb6ce2d6c82e97b120d757201': 'Click or drag image to upload',
  '61a87a021904dc65995e99d4c476cb21': 'Image Position',
  '737391648d1216a1f84ac9ff52da5aa2': 'Image Size',
  '0f1fd39145bad43e18f81337e0144c8c': 'Fill-up',
  'e0d76824dfe5e09c7068b44f605266d2': 'Appropriate',
  'e39d3b7a3600d9327221a637f910fc0b': 'Stretch',
  'e1ff2c83c09f2dc6cc74ae02ab6b8222': 'Tile',
  '7ddd9dbf373f760acfd63778469b5c4b': 'Tile Horizontally',
  '4ab931e0f709f9b493e660156925a113': 'Tile Vertically',
  '21f3929a4484e6e992af64ec1dd3a576': 'Original Size',
  'a80a25b59908402cf7ee31a07d0e7739': 'Left Border',
  'e0dfa25ebceabddb2180720d36d4c3b6': 'Top Border',
  '57463587977a534f7859eb9d7c536629': 'Right Border',
  '154d5216e42c916884431f0eea951999': 'Bottom Border',
  'a8b0c20416853bda54120bf19477ad11': 'All',
  'df68a5dc8f8847179b7afdf943f80796': 'Top Left Corner',
  'e717b4ae480e7c073fd5a44647a7f0da': 'Top Right Corner',
  '2a97dfb2d236c87c41fd588f006111dc': 'Bottom Left Corner',
  '1c9bc9dab33944e953412f8b22cb024a': 'Bottom Right Corner',
  '4dbe7c40ee82a56bb7a8152d4bbc07f9': 'Rounded Corner Size',
  'bb1531cc9643230ba0cbd7465818b52f': 'Border Line Type',
  'cc4c575642609fbf059a5df81eb86bfc': 'Solid Line',
  'b1e0ebac23ed95807ecc566da3ffab48': 'Dotted Line',
  '61f6f4fc0b806ac9d41ad0792e6155f6': 'Dashed Line',
  'bade10099f8447210ce7b97fa106b527': 'Line Width',
  '5babfafd769570de3bba47605753361a': 'Border Width ',
  'af208c560d926f0daf4b2ce2d396505d': 'X-axis Offset',
  'b6b46126bfb0851ca10e74541c5d7be1': 'Y-axis Offset',
  '4a32b29da68a6ee204b3743e0fab8bb3': 'Blur',
  '22d460dcddb7fda718bc62034c459158': 'Spread',
  'b24a723b73f96ab3340fe9502370ee13': 'Inner Shadow',
  '1cbac849ccc41edb12271d9fe9b65b5f': 'Shadow Color',
  'e94d6fc08a97892ff9d8c8d9a8d9e0ab': 'Set Shadow Color',
  'a5d833839a610994dc4752e2d91f4192': 'Display Type',
  '2c86d897c71cc9c2e648222a200d5bbb': 'Block',
  '0b0052c532b457b2d032c9f56af9e4e5': 'Inline Block',
  '70d457cd36de1acefe7d9587f3f862c9': 'Inline Element',
  'b5f55e5c7101d9be5218d63850e1ad8b': 'Flex Layout',
  'a648bd0b9eb3c86e39cabeac484917a5': 'Flex Layout Configuration',
  '452dba7c65211630f8066b070fdf157f': 'Automatic Line Wrapping',
  '098d946b6f0cc85110c54cfac8691cc3': 'Flex Direction',
  'aee1aeb8d65547f8a368bd7364cacf12': 'Default Level',
  '4c38eb0fea71a9506fd434a7ac5cb1c2': 'Default To Vertical',
  'ae07817b441091793c8af39256908373': 'Horizontal Reverse',
  '618515fd68ecfc4a5b58462f0cb9666b': 'Vertical Reverse',
  '58554717be87c1f8a6d6c769f74f0679': 'Main Axis Alignment',
  'dfc71567fb75f5c73da377013a223168':
    'Leave a blank at the starting and end points',
  'eb5ec10ef70689996dd5cd66e17a64aa':
    'Alignment at the starting and end points',
  'ba3a7db7cc1ac5a908358f62fdbb21de': 'Equal spacing between elements',
  'f6cbf29fdb5e94052a22986533c7267f': 'Cross-axis Alignment',
  '6b461e0a4ffef9438fb8f01369091cac': 'Font Type',
  '2539fe0e6d40a023dd95d0bd71d10bc9': 'Select Font',
  '690660d9dbd7312ad2825e554736e2f8': 'Font Color',
  '5f15efdc32badce0902c46a7a0105c51': 'Font Size',
  '4c5fead0489fbc7651c91400dec5d379': 'Text Style',
  'd1b490c01d24a1a70e9c83a29ac38fde': 'Text Position',
  '4ae7f423d9ed9ffa48c3e404f96bb51f': 'Text Type Setting',
  '65786fea9d354015d3a2724086f7f3d6': 'Default Font',
  '1a63ac23010e0573f7c0a8cd3314b8c6': 'Example',
  '226c06861b0605a3276311b1369204f2': 'Interface Return Example',
  '1f318234cab713b51b5172d91770bc11': 'Advanced Configuration',
  'a0472043a9598b1b19f1adcec1e1d75d':
    'An error exists in the interface configuration, so you need to check it carefully.',
  '2816c3584802b2fc75db5bc3c6aa81e8': 'Set valid time of request cache',
  '6e889f0cc8c1d8f705edfd5d0bbdaaa0':
    'Select it and set Content-Disposition when the interface is used to download binary files.',
  '508a38f518821a0f6bb2d15269b31ece':
    'The default data is the append method. After you enable this option, it is replaced completely.',
  '5c131eb3bc61f6b0a26e20449ad7ce56': 'Customize Adapter',
  'f514cabe63553a850063834c9d44a5ee':
    'You can get dynamic options through the interface and fetch all options once.',
  '47d0a7caaa2baee8d38612a1c57421ef': 'Whether to perform initial load',
  '866a60d7fbdfeba6ae42c7e9c7b03059':
    'When you configure the initialization interface, the component fetches the interface data initially. It can be modified according to the following configuration.',
  '3f4f3acd6968f38361dddc6612a0c54f': 'Backend Agency',
  '66670400b1f3e4b6c94cff171d441585': 'Form Item Inline',
  '21a1d138166d5d92276d126cf1d6ecac': 'Control Width',
  'ffaa96ef7232cb6d8ee4d7e673dbf4ac': 'Full Occupation',
  '4e9ce9dfe13d97031d228fc5ae229f0d': 'Hide Title',
  'e2773277c7765d4590f7128423c58cee':
    'After the title is hidden, the title width is 0 in the horizontal layout.',
  'cc572c07586f4ea0c5b9b1060eb777f2': 'Placeholder for empty content prompt',
  '8528426e54902956723f322bdbbcfafc': 'Input Box Prompt',
  '********************************':
    'It appears when the input box gets the focus. It is used to prompt the user’s input content.',
  'ff255db9b108589abd5c649e2a02e3f1': 'Control Prompt',
  '9e783b34207fe24bc488136faba65442':
    'Display the prompt beside the input control. Note that you need to set the control width. Otherwise, the prompt to trigger the icon leads to the automatic wrapping.',
  '7ad83bbe4646a0011ece7fd338d1e189': 'Display Prompt Beside Title',
  'b3e55578af5dd473bab62641bb2f5f8e': 'Input Type',
  '16084784a0f126b501e96994c792d411': 'Date Type',
  '8190915888889ed18be44ea0351d0448': 'Month',
  '8f30e9f8678c24496921bebae6ca2ac6': 'Year',
  'dc46b7f19799047a26fee668d49be0f3':
    'The option rendering template supports JSX and uses the variable \\${xx}.',
  'b18ec08df2e47313bcc93430e7a25fd3':
    'It supports the JS expression, such as `this.xxx == 1`.',
  'b7dee01f2d085d90c47bcb8b490d9055': 'Click to select icon',
  '689fad203a167d542c12bdc46f27e921': 'Name',
  '6c1e18fd4cb57288a9f43603d5167292':
    'It is required when you need to link it. Other components can be linked with the current component through this name.',
  'ae344073ea6ca9ce742899cdf6d3e106': 'Enter a letter or figure',
  '1a1ff1e1149a0cd1b39c0b231a334d04':
    'You can specify to refresh the target component after the operation is complete. Enter the <code>name</code> property of the target component. Multiple components should be separated with <code>,</code>. If the target component is a form item, first enter the form name, and then connect the form item name with <code>.</code>, such as <code>xxForm.xxControl</code>. Additionally, you can refresh the whole page if you set the refresh target object to <code>window</code>. ',
  '705b3b333576dbf6c063d7060a4cc509':
    'After the name is set, the completion of the current component operation will trigger the refresh of the target component (according to the set name).',
  '7080fa6e0ca1d7e24f6f8cac05077a3a':
    'Which secondary CSS class names are available? Go to <a href="https://baidu.github.io/amis/docs/concepts/style" target="_blank">Style instructions</a>. In addition, you can add a custom class name, and then add a custom style in the system configuration.',
  '3bce1a6217990c8dc087d254f1fe754a': 'Automatic Fill',
  'f01553e415ca33cc89d0bca84023f4b5':
    'Automatically fill the value in a field of the currently-selected option into a form item in the form. The data mapping is supportive.',
  '04d815a5d4b803d6bb956d2da5a82688': 'Required',
  'a1b6281dc554ac84a3e6062f812fe50d':
    'Light text description below the form item control',
  '85541bd9f7f39a6e6d9d26cbe09cbdd4': 'Read-Only',
  '4d681c4aa93c8d005ec2ca2370618d6e': 'Visible',
  '5d0595edc3d14aec24efef85534e4314': 'Default Value Setting',
  '495333d64fc1efafd6c40bc9a3929fee':
    'Get the value according to the name when it is not set',
  '32f13c9db34f638c2c5cf2bf19326ebf': 'Keyboard Event',
  '2882b15b04cb47d1497160b5061b9186': 'Disabled By Default',
  '530c4483c7e52dc409509b755eabee11': 'Thousands Separator',
  '090dbd614a66a56a5eadec87f59ea15c': 'Image Path',
  '2a0c4740f156a9536049f4610da25400': 'File',
  '4fa2ae7d726dc395cfea70ff3d7256d2': 'Markdown Content',
  '52f43ce846b2bf73f86195cf196fe578': 'Counter',
  'b9245d69d2d82b0081ced47a18c27f41': 'Full',
  '8f9b1b7e38cd2ed16f22807417ab3573': 'Half',
  '7fc7940b4f7f58b49c71bf9e237b633e': 'No',
  'b4521626a48dcb61001fc563d2433ed3': 'Sortable',
  '6fdccea6068e0698c565acd92052a86e': 'Select End Level',
  'a80c61384a8459ef7bfb5082a2b54b5f':
    'You must select the end level, but cannot select the middle level.',
  '71758057056e7f31d73e3f3ac8860b4f': 'Delete Field When Hidden',
  'ce641d8297471a5d65c46cdfb023097c':
    'Delete the value of the form item in the form submission data when the current form item is hid.',
  '3e719b87b9ee71d4613caefbf2fd1074': 'UTC Conversion',
  'd52e57147787797ae0153d43bf8be298':
    'After this feature is enabled, the submitted data and displayed data are UTC-converted. Recommend you enable the applications that have cross-region users.',
  '339b9ebd91070de050b4bfe483aa4474': 'Embedded Mode',
  '49a79f4047b81186c069ed1c9c151c66': 'Light Color',
  '41e8e8b9935c9ee4e88b06643a2d5b81': 'Dark Color',
  'bde770827b9137ddb3eb676878af9709': 'Secondary',
  'b1ea078db7298ea7872d894283378507': 'Upload Method',
  '74cef1162781310e1503d2dc463a76fc': 'Baidu Object Storage',
  '38fbc7fb70b4399d7e4050d3cbcdf229': 'Storage Repository',
  'e5d59ccec2caa64ca83b7cc740645928': 'Default Repository In The Platform',
  'b6789c926cf3d23d13a6a3220bcb8e23': 'Initial Static Data',
  'a4895ee2e87d1c47b734dbcf1a535aeb': 'Select page',
  '150075376834f1879d6ceb7c2d10ec67': 'Proportion',
  '34598b2c71a8affb13da84ba39e905d5':
    '12 equally-divided parts. The title width occupies n/12.',
  '73ec114993142f627ab6c3e6706fa3c3': 'Title Width',
  'ae6fa9e75d38b9db24f78496b72bcc75': 'Fixed Width',
  '424f04d454332ddaff4daa0a365e9335': 'Sub-form Display Mode',
  'b17754a193ac835bcae0bf960f3a3772': 'Set Sub-form Horizontal Ratio',
  '2e282fb6405a60830112991f88e95753': 'Left Width',
  'c41b3271487a3cc63e79feeb11b11de0': 'Ratio',
  '0fcf69b5d0b44955fcbec9702f90b556': 'Small Width',
  'b591aed69defa2abf0486da6a58dfb5e': 'Fixed Width',
  '0fa590019ace86acee8d7655e5fb11b7': 'Big Width',
  '51c1e80408011ba118cdcc696f28283d':
    'Left and right distribution adjustment (n/12)',
  'aa0ff71cb10c3e54d68874dc2b17acaf':
    'There are 12 equally-divided parts in total. You can set the left width equivalent to n/12.',
  '1f08c91da33fc5f0616e8a85c0456a18':
    'The static data do not support multiple levels temporarily. Thus, you can switch to the code mode or use the source interface to get the data.',
  'cf763c357566be6fdaee886a40ddcca7': 'Option Text',
  '1ca0b9b486be3b766a92474189f11fc8': 'Concat Symbol',
  'ab8e2e8cd076bd115cdd600d17ca5020':
    'Concatenate multiple values to a connector that consists of a string.',
  '6df0630b4f00b6bd05de8af09c2f78ad': 'Extract Value Only',
  'd7d810ec89408c206a220f62edde737f':
    'After this option is enabled, the value of the selected option is encapsulated to an array. After this option is disabled, the data of the whole option is encapsulated to an array.',
  '4cff56e2b9703018efc48218b83844b1': 'Can Create',
  '457c4cf8e1e2e0daef8949085555563f':
    'The configuration event action can be inserted or intercept the default interaction.',
  'd03c96a2da4905c5f13a87c0d8ddbdb4': 'Add API',
  '26bb8418786593149c0bf9f8970ab6de': 'Create',
  'a32b3bf74850faad3a9ae6a0a5dac781': 'Editable',
  'ea56ca3dac0d39e463a8233fd40a9eb6': 'Edit API',
  '3c87af7c432e6b1f59e4f415fd5060cf': 'Deletable',
  '793e260d5b7c67d43b5c6d5e885d2363': 'Delete API',
  '63bbd68594c9a987d0ff41d645fafa16':
    'Whether the first option is selected by default.',
  '0dd2e4469872c176ab1e85b66d99da98': 'Hide Path',
  'b7e26fcff328b28b393ef2e57e96e258':
    'Hide the text information about the ancestor node of the selected node',
  'c49fc970bcd168e1c652a8ced5d95d0d': 'Other Item',
  'a45b5d3fcdb8210d249b2c6d545b6691': 'Cursor Type',
  '2c3888961c01fc9c466d88c88029158f': 'No Pointer',
  '062d0b688adb10f3af5ebc2fd2667f1c': 'Floating',
  '92e3a830ae9a1252278da742c0af74c3': 'Help',
  '87e277f953fd3c867d7fa02897c2c124': 'Cross Pointer',
  'c19639326396d2527268484379a671d8': 'Moveable',
  '183f48b3c1f0f84e538cd4315e5645e9': 'Fetchable',
  '4f9b192ce84b4df0900510257082ef43': 'Enlarged',
  'b21ac25366449b1720bdd75d39f163d2': 'Reduced',
  '93ab99d761c9ca97dc926e6db10469a3': 'Font Size',
  '86cdd659decaa1b959795eff92a0c2d6': 'Font Weight',
  'e8ed49e9fde0bb778e2185876c2d2697': 'Margin',
  '4e7f76261f8c4c6d78998f85fc1f4c6e': 'Margin',
  '841d77223f0ec8cd0b530ed8e0775b20': 'Padding',
  'b5bd9a6703f5433ff34aa0af9049740c': 'Rounded Corner Width',
  '14eb3a698316caf3fbe9b2ab9c3d9527': 'Special Effect',
  'cff1ec632eaf35f64791615e15ce6d76': 'Mailbox Format',
  '********************************': 'Url Format',
  '1111c44adfa40fe9cb22797d2c1e37e8': 'Letter',
  '725bf3485a0456cf7f65a507ce67254b': 'Letters And Numbers',
  '96c4ea83892a3227a2aa5b8f3759bca4': 'Integer',
  '35962d17a3fba5f4802d7845695a3e72': 'Floating-point Number',
  '28e8e048490110c8dd8e2ad6af324980': 'Fixed Length',
  '8c4ee6022f1525097a1141acad094d4e': 'Max Length',
  '17971609e210034c0d6a25b0186e2b7b': 'Min Length',
  '92448a35f41de3a1fa69135acfed5ce9': 'Mobile phone number',
  '193a8c42c1c373f385a4c7b33ffc381e': 'Phone number',
  '6102d474314f27577d89e85b4c6cc4a5': 'Postcode',
  '84e0cb5d57ed995b0cc04b4ab9a7997b': 'ID Card Number',
  '8dbec4f0c05be45a8acf6a5ae9d1f880': 'JSON Format',
  'eb242bc7524c797fb1aee2344dec92da': 'Identical To The Specified Value',
  'c17d9577233793976d3902c117eed82b':
    'It is identical to the specified field value.',
  'b457177c184722b655954a08cf3f71ca': 'Customize Regular Expressions',
  'd3927ffde0fdefc47f910e70226d6a4e': 'Customize Regular Expression 2',
  '0ebee58f4f2a0f807f08a6427dc58497': 'Customize Regular Expression 3',
  '15f52cddb226421e68c70488fff3db5b': 'Customize Regular Expression 4',
  '271b01959e09c0771760f59964baed56': 'Customize Regular Expression 5',
  '4eddee6a20aceddd6bcdf7e0736887ee': 'Verification Rules',
  'ba3c802f3ce1641eb6f8986e8d19e672': 'Add Rule',
  '7be30fe376e9bfd8895ee50e6f4216f3': 'Set Length',
  'd00f99fd76e86ba4dab6f70858010ca0': 'Set Max Value',
  'e993ecfbb3f934481257f1bb57056bfe': 'Set Min Value',
  '99f5d503544334c670cbe1f400aea9e1': 'Set Max Length Value',
  '91bdac623455c91b7400328a5600cec0': 'Set Min Length Value',
  '7002c4a1b7cb5bc32ffd52e1f2d74c70': 'Set Value',
  'aa9cfa5321e589494841ddd90a10c467': 'Set Field Name',
  'd22b6fb1c857777ba21467835efc65d6': 'Set Regular Expression Rules',
  '1b7e06ef04d7167e174eb6929421592f': 'Email format incorrect',
  '2d163645de4d4b4760e9fbdb535a1a88': 'This option is required.',
  '87c91ce706ab845b55ce95372265b92e': 'Url format incorrect',
  '2b702fb5b95d47944246f79ae4032281': 'Enter integer',
  '48e2aca8e6347b008b6fbdb48fc4b597': 'Enter letter',
  'de66a286057d4e3f1ee2d9bccbd48ce5': 'Enter figure',
  '84f46c9b82c3c8fe276dfa65173c59bb': 'Enter floating-point value',
  'fb73d98245f558dbb2d6a0c8d2699780':
    'You can input letters, figures, `-`, and `_` only.',
  '923a91fdc2c777f8443c85278060195a':
    'The format is incorrect. Enter the content that meets the rule of `$1`.',
  'd87b1a3d180a1cc56bbd174d2860ca4c':
    'Enter more content which includes at least $1 character.',
  '9b8d6abb2f03fbcbdb0e4d2b1970a751':
    'Control the content length and do not enter more than $1 character.',
  '2a5d0be4d5cd088f0371ba6f8656fe7a':
    'Check it when the current input value exceeds the maximum value $1 ',
  '8256618e16217325e6a1d880f8eb7adb':
    'Check it when the current input value is less than minimum value $1',
  'f58829312013d929923b0c2a1fbacf19': 'Check Json Format',
  '161278fb2c71e5a8aa8aac50f230233d': 'Enter valid phone number',
  'ba42949accfe87c20e6c2486cd065dd2': 'Enter valid phone number',
  '1d2c5048143328e21cb9c2dd84b696fb': 'Enter valid postal address',
  '038b3ed111e87b56572f3945a1b0e02c': 'Enter valid ID number',
  '335e2618dda48c05f4f833ebb1e299df': 'Enter the content with a length of $1',
  'bce45e909d6d14a126554c8cf6f65a13': 'Do not enter all blank characters',
  '17bb8e76f78bd16a1841e36ab8462e53': 'The input data is inconsistent with $1.',
  'ab534c35774dccc322331a079ae6e7df': 'The input data is inconsistent with $1.',
  'ebd0dc3ebde6182caa3b66e0faf658b6': 'Customize Verification Prompt',
  'd7772d568894afbb1c924bed7f7ddb32':
    'When the prompt conditions are not satisfied, you can customize it.',
  '58c9592f818d706420236c6f9f595517': 'Add Prompt',
  '171f7b825707ddd79175fed3f8def6cd': 'Submit After Modification',
  '6ccb7091c39a5229f7e77eff4dd44a0e':
    'After you enable this option, trigger the submission once you make a modification every time',
  '1a006028adf7167ae28cdf532bb75ef4': 'Trigger Check',
  '8d877748c3bc71b517e2d46344916b3f':
    'Trigger the check action once you make a modification every time after the submission',
  '2c60032f2a57717e4f7c16ee185795d6':
    'Trigger the check once you make a modification',
  '5ed62f810226722d7c910c2d8dc4a0e8': 'Trigger it upon the submission',
  'ed3dd0bfa89500c5feb306cd4d9db56c': 'Regular Expressions',
  '537b39a8b56fdc27a5fdd70aa032d3bc': 'Required',
  'ab90c616dd114af087b31b90d3cb4063': 'Number Of Characters',
  '41e82a5a0e53ba94d1160ee855c72a7a': 'Enter number of characters',
  'a85ce2404b26140080c929ad9c3ec305':
    'Enter more content which includes at least $1 character',
  'e93275245d529c486018e47136bfae2e': 'Check Json Format',
  '2c8c25bb51dfd9ddfc74fd75a8a380a1': 'Value Content',
  '859102d8ced9928cc71bb225961171bf': 'Enter Js regular expression',
  'a4313469fd7361486fe47076629c76ac': 'Add Record',
  '50abd0bf31e07dbee84e9e5b9a407840': 'Add Record',
  '73f28ac83b623fb81d73f16fb3856fa0': 'Data Import',
  '8d9a071ee2ef45e045968e117a205c07': 'Import',
  '09d44261d7883bf5141a462a790d2816': 'Data Export',
  '55405ea6ff6fd823ffab7e6b10ddfa95': 'Export',
  '6ff4bf3d567e977aa4c90c27dff1e6db': 'Fuzzy Query',
  'e5f71fc31e7246dd6ccc5539570471b0': 'Search',
  'c26996a6506adf397f0668d376d0b40b': 'Simple Query',
  '9c4666fd08c2738eb9611a3721cb5f0f': 'Advanced Query',
  'e22b59b6bda1cf9a58f8979fd0a0b43c': 'Edit Record',
  'a790208cafd5c95a18dd9a168319ecf8': 'Delete Record',
  '39ccc34fa3ee9be12d8bae7e6fecbac2': 'Action Column',
  '240145572215920ae06db1eeb85b29c0': 'Optional',
  '099cf136d6a4b6ed4646af4a2ed066b2': 'Retain Selected Items',
  '60011314ed92794f3d4f3e874c359279':
    'After the default switch-over to a page and the search, the items selected by the user are cleared. After you enable this feature, the user’s selection is retained. Also, this feature allows you to achieve the cross-page batch actions.',
  '949a8b7bd2c10070a2fae16f9c66afbb': 'Column Setting',
  '2816cea6c4887a53c417831deb5fbe00': 'Custom Display Column',
  'd3c5079f7e26b1a7374ff76341376de4':
    'It is enabled automatically when the number of columns exceeds 10.',
  '4a3ebd0ef27212de3b0c39e6a9701b1d': 'Search Setting',
  'a9a3a4608d48214efbdfac99888f6e0f': 'Action Setting',
  '84a2f40385427bbf2edc79e3368e4e0f': 'Add Action',
  'cb43abed5ba14bf32fbb1058e12d2303': 'More And Paging',
  '48b42e5c3ea447092eaf0a1a13195540': 'Paging Mode',
  '16b8ff2b147382be4cf8654f829df904': 'Frontend Paging',
  'd2e20bb1e977f9571a9e2d1b39a7ff10':
    'The data is loaded to the browser once, so you do not make a request to load the data on the current page that is requested by the backend user. It is not recommended to enable this option because it has significant influence on the performance.',
  '73721e611daaafe5c34aa9f3f901d016': 'Data Containers',
  '46a0f3086dce242abe54e48bd86e0394': 'List Display',
  '278249b178c958cee0f5ee9ee9d1e0f5': '{{@1}} Create Wizard',
  'd75a7984d3fa5b32f5d8312e899aeea8': 'Data Configuration',
  'c2f1f9254c245976e346377515c2e578': 'Feature Configuration',
  '0943d61befec4c6cf2d21d170c9b066e': 'Condition Query',
  '5246d2c81fa12b1f4f73635c257e232d': 'Data Operation',
  '1b79a4f49b7a21e62b8868f12189b0b0': 'Data loading',
  '5a28d015b7b3518f75bc4cc27d9f6c20': 'List Data Paging',
  'cfd84204d9476936c949d071cc2338cf': 'Data Query',
  'c6bd3393c21379d3f75d179abe36da3d':
    'Use specified conditions to accomplish list data query',
  '3a6ecf25c38317b21b8c6287100f053a': 'Trigger data query',
  'b3a4d6a345372c5def1d5a1bf6077bce': 'Load more data to list container',
  '34e83e1be408c4f198464da1bf56bf9c': 'Load more data',
  '2c77cfaef73ce2e81131861e9c6d670e': 'Selected Data',
  '9c9153c49491c381dc2adb2c36fccb04': 'No Data Selected',
  '9a4fe969f1066e197fd2369a44d879ac': 'Current Page',
  'a7f33a2d99056edcdaced5c8841a9bcb': 'Total Number Of Data Entries',
  'a0c35361a003527d123cb581f5c68f4b':
    'This option is to realize the addition, deletion, change, and view of the data in the card list. It is used for data fetch, paging, single-entry operation, batch operation, sorting, quick edit, and other features. Also, it is integrated with the query conditions.',
  '860827796ce2fa94e9ee39e53f011ec0':
    'This option is to realize the addition, deletion, change, and view of the data in the list. It is used for data fetching, paging, single operation, batch operation, sorting, quick edit, and other features. Also, it is integrated with the query conditions.',
  '629645f147f378869fe9d7ee2bbc2857': 'Sub-title Content',
  '97d03d4621f0024cf045afbd901197a5': 'Select Area',
  '44705bb94d83e7bd6b3b6c1480ebfb38':
    'Click to trigger selected area or cancel selected area',
  'e30a958a6397e53fae9d5316e851d3aa': 'Entire',
  '31ad7a215f3df84c33b8c28223147b8e': 'Select-box',
  '********************************': 'Hide select-box',
  '********************************':
    'This option is used to hide the select-box. It is possible to achieve the selection style by customizing the appearance of the selection status.',
  '1d4103a96a70de2cb69dd597d679fefe': 'Selection Status',
  'e8b90f43fc3cfb0a68e4392054de97e9':
    '\r\n              If the backend does not return the Echart configuration directly, you can compile a paragraph of function to pack it. \r\n              <p>Signature: (config, echarts, data) => config</p>\r\n              <p>Parameter description</p>\r\n              <ul>\r\n              <li><code>config</code> Original data</li>\r\n              <li><code>echarts</code> echarts object</li>\r\n              <li><code>data</code> If you configure a data API, the data returned from the API is imported through this variable</li>\r\n              </ul>\r\n              <p>Example</p>\r\n              <pre>debugger; // You can conduct the breakpoint debugging in the browser\\n\\n// Check the original data\\nconsole.log(config)\\n\\n// Return the new result\\nreturn {}</pre>\r\n              ',
  'a955021cdf0249de1f3818f83cf248b7':
    'It is used to display the custom display column button in the table. You can configure different display styles.',
  '24bdc7e8957abfc5d82f4206e92bb518': 'Button Text',
  '787366b0d678071a5ed98e55d31eba84': 'Button Prompt',
  '6a0508144ae12bfa79001693d713c0d6': 'Whether to unfold the panel by default',
  'ae3816c5b00fcff4111842ac19f0a706': 'Button Icon',
  'fe805d91ae93be775670c61214dd2f28': 'Button Size',
  'fbbbe30d78c335bad27f3dfc5efd2a5d': 'Display Column Style',
  'f8c5e0ac29e905e91146e967cfd39dc9':
    "\r\n      const button = document.createElement('button');\r\n      button.innerText = 'Click to modify the name';\r\n      button.onclick = event => {\r\n        event.preventDefault();\r\n      };\r\n      dom.appendChild(button);",
  'a8065b6d2062bf061762b9200a88251a':
    "\r\n        const button = document.createElement('button');\r\n        button.innerText = 'Click to modify the name ddd';\r\n        button.onclick = event => {\r\n          onChange('new name');\r\n          event.preventDefault();\r\n        };\r\n        dom.appendChild(button);",
  '73ac822ddf4685dbfec661dec41a96b7':
    '<div>\r\n<h2>hello, world!</h2>\r\n<div id="customBox">Custom container area</div>\r\n</div>',
  'f29ab26877ed22ffa59636d747d824b9': 'Enable Esc Key To Close',
  'dcba76890a534e1fe94421be2a17b484': 'Show Error Bottom Left',
  'af5876b89583552eef4c781718886dec': 'Show Loading Animation Bottom Left',
  'f19464cd1d7c55610b84b4972eaf506f':
    '<div> With the data mapping enabled, the data in the popout only contains the set part. Then, you need to bind the data. For example:{"a": "${a}", "b": 2}. </div>',
  '784cf5219012b5f16f2985b7f1fd52f5':
    '<div>When the value is __undefined, it indicates that the corresponding field is deleted. You can combine {"&": "$$"} to achieve the blacklist effect. </div>',
  'af9f27383daeec508815a33753247f42':
    '${data["&"] ? "The parameter customization feature is already enabled. Thus, you can click to disable this feature." : "If you need to customize the parameters based on the default data, enable the parameter customization feature and then define the key and value."}',
  '9567f8bbb315b1217b7b32be37f97792':
    '${data[&] ? Disable immediately:  disable immediately}',
  'bde8a41fc64bfe78d0c4951ac6f93718': 'Display Mask',
  '874cf31274d782914c7833cc39836c4e':
    'You can drag the drawer to change its size.',
  '94397b87ac63fe238c779120fadab024':
    "It takes effect when it is at the 'left' or 'right' position. The default width is the width configured in the 'size' field. The value unit defaults to px. It also supports such units as percentage, e.g., 100%.",
  'a6d91e801974dfa735a4ae0e098c522a':
    "It takes effect when it is at the 'upper' or 'bottom' position. The default width is the height configured in the 'size' field. The value unit defaults to px. It also supports such units as percentage, e.g., 100%.",
  '45ce37c24c6e7252d98c6d450e3ca4ad': 'Title Area',
  '660553eee939d2bd8ea68172fa7216df': 'Footer Area',
  '1495c26f33625552e4845630b6b7b44a':
    '<div>Description of available variables</div><ul>\r\n                      <li><code>value</code>Current value</li>\r\n                      <li><code>level</code>The fetch level starts from <code>1</code>. </li>\r\n                      <li><code>parentId</code><code>value</code> selected at the previous layer</li>\r\n                      <li><code>parent</code>The option selected at the previous layer includes a <code>label</code> and <code>value</code>. </li>\r\n                  </ul>',
  '057a9c2b2027a6b443741d8a0c04e4be':
    '{\r\n    "status": 0,\r\n    "msg": "",\r\n\r\n    // The data cannot be returned. If the data is returned, the data is merged. \r\n    data: {}\r\n  }',
  'e23c9e6279487960ebf85b9985dcba07': 'Fixed Size',
  '4838f2f4ecafd0a3a4fcfe82521acdd9': 'Enable Crop',
  'c7fa7f54ece94ae684aee1ee2088a5ae': 'Rotate During Cropping',
  'bd698e3f47ede4e59aafb28a291b77e7': 'Zooming-in During Cropping',
  'f785a357a820555445acd6f7051b1048': 'Cropping Area',
  'b7623c7e17098d5950694437aa7584ad': 'Aspect Ratio',
  '204770091fd4b7cd0611ddc65bf21d37': 'Automatic selection of child node',
  'be7d848c40dfdd3e20f233c373af00a5':
    'Select child node in a cascaded manner when you select a parent node.',
  '4c3ed2fc7331db687fc0e8ffb8f672a7':
    'The child node can be selected inversely.',
  '0ae8c01434dc2a38a90561fcbf5d79b5':
    'The child node can be selected inversely. The value includes the parent and child nodes. ',
  '22c023bf51970a461cc164e711f3d3ff': 'The value includes parent nodes.',
  '6e19e48a52986659ae5ba1bfe612ba8a':
    'When you select the parent node, its value includes the value of the parent and child nodes. Otherwise, only the value of the parent node is retained.',
  'cccb3595f8ff536e27d94ec91a49bfdf': 'The value contains the sub-nodes only.',
  '49ccf100ac35e1cc3d40b7222cdcd1a7':
    'Through the ui behavior, you can select the child nodes in a cascaded manner. The child node can be selected reversely. The value contains the value of the child node only.',
  '4e373f3ff64ad29a01da87317eb92088': 'Min Number Of Nodes',
  '160cb9a7d57c5b5ca65b5cdf79e8fd28': 'Max Number Of Nodes',
  '1cd9e1bb335cb0643d9e310dd4edd830': 'Icon Field',
  'a0e965072593eb1b19b4568fa26d977c': 'Select Child Nodes Only',
  '8865c6822a31e0da6bc7eece8677d8f0': 'Add',
  'bc560e477282dafc3c37b7a665af4f9c':
    'You can add a child node at the top layer',
  '89664aa96ba7eb788ea273c0d36467cc': 'Top-layer Text',
  'b4eab506cf436d3cdd1cd68fc857ec04': 'Add a level 1 node',
  '2b3073b1ba5f9deab7818d8be02de3a1': 'Highlight Node',
  '14dc30e68a3d8d53e8ddd98876c1d480': 'Include Parent Nodes',
  '68c7da9593da32100cb82ef5106047e1':
    'After being enabled, the corresponding node value contains parent nodes.',
  '9cb417cfac37e6d65dedbc35ee9e916e': 'Path Separator',
  'd6840c02c4d9cd5f4f5fd4d9f3e6b916': 'Display Top-level Node',
  'e2c310a329c2cf62a0764fc912f2f583': 'Node Text',
  '8c9a3a8cd5de83121c03c83f909a7534': 'Display Node Icon',
  'ae7d563d3190ddbafdda8093fc28fc5f': 'Display Node Select-box',
  '********************************':
    'In the single-select condition, it can display the tree node select-box.',
  '********************************': 'Customize Unfolding Levels',
  '346f7e0c7ef27b6a83f3c9f8f406d737':
    'By default, unfold all node levels. With this option enabled, you can customize the number of unfolding levels.',
  'b40163733b8e3a420e38f295bfab369d': 'Set levels',
  '0911a348aaf24601e633e318ccb8aace': 'Left Option Title',
  '84a76ba52297727f6bb47d8a1cc74094': 'Right Result Title',
  'a2888bca5f435690ff9f5a5d615a968a': 'Left Option Panel',
  '1e409f26f9c1d58ce0c47a68104d45f0': 'Display Form',
  'eebda442c4bff2aaaf8274383d0ed12f': 'Tree Form',
  'b3a17ee1af382c467d857d33089fc0c7':
    'The left option rendering template supports JSX and uses the variable \\${xx}.',
  'd5dbf5285b2dbe07b481fbd5d4536c60': 'Right Result Panel',
  'b720b2abd62161c3c99625c8160df987': 'Follows The Left Panel',
  'acef03eee638dc4239ee60f627f33d85':
    'Currently, the query feature allows you to perform fuzzy match query according to the name or value.',
  'e3ed623b79100791f156d3586751c652': 'Supports Sorting',
  'd4865602e26e9c985814380beae99b6d':
    'The result option rendering template supports JSX and uses the variable \\${xx}.',
  '0aa73cfbe5a84cd34a212de5bab2058d':
    'The option text only displays the selected node.',
  '4dabfefd787102b8159b5c4a221aa048':
    'Hide the text information about the ancestor node of the selected node in the select-box.',
  '********************************': 'Show Level Line',
  '846da282ebfa6dfe61deb1d58145158c':
    'Display the unfolding line of the tree level',
  '93199f3864cf5a68b286b927fa68ae6a': 'Tree Container',
  '391555a3772260743f19278f01adf75e': 'Title Bar',
  '8d6b5924f187048cfa28d6c21fa6d2d6': 'Page Title',
  'e7f2f04f7c2b2e9e07b69767ea28d6ab': 'The sidebar width is adjustable',
  '28d602809bd1dc6b47ceb38cb54f32de': 'Sidebar Fixed',
  '36bded76593f98fab62453c7430b2918':
    'Means whether the sidebar content is fixed. The sidebar content does not roll together the content area.',
  '4143d7418de740e8bc26ef4b27c63534':
    'It is used to get the API for initial data. The returned data can be used at the whole page level.',
  'c95e748d5811faae5c52bdc07bee51a0': 'Mobile Terminal',
  '04f767eaa571383ea271432bee6deedf': 'Drop-down Text',
  '0b3eef4f8a8061baa22416dc1e5dad03': 'Release Text',
  '0059d50e3f7b2fe7f1822ccc218fed66': 'Redirection Page',
  '41ed1e238b846005dfb0f087cbec644b':
    'Select this option to indicate that it is enabled. You can drag and drop the features to adjust their sequence.',
  '7b755ba413eac50423859395c68e6280': 'Table V2',
  '642e5368b742c82472e2ef35114459c9': 'Table Data',
  '8369004103635f8e75026217ebf237da': 'Bind current environment variables',
  'bd3e7a1b636e4477a4ea59922ed2cc1e': 'Display Title',
  '11d00f37d934b2464f3258952a398626': 'Table Title',
  '4e3cd1a7b193e2fd3458278d10c530e2': 'Display Table Header',
  '023ff3530e48493e653eb48e958a4eb8': 'Freeze Table Header',
  'dd9b85b2cd13ca724afd1f43567abdbf': 'Display Table Footer',
  'c89b5fd3b706a17feb016d93c80e34b1': 'Table Footer',
  'a5baa4818b14f4680955aa34dd559d02': 'Content Height',
  '1ef8fd21130d17cb7c1613eaed6ca1e4': 'Fix',
  'ee18dc475df8654cb13ad67dd84eec28': 'Height Value',
  '8bb5781dc5f2745e6356cdc5e6d76b16': 'Content Width',
  '88a364068f684dc77aca5b6c006ef576':
    'When there are too much column content and the content exceeds the width limit, you can view the data in the landscape mode.',
  '6e3d35b57c29b1b419569cc55b3a5d33': 'Width Value',
  '8ce1cd75b6e9c0c0e3468589fcea822c': 'Placeholder',
  'ba4f461832cbdb7fbdb170fc9c1db647': 'Adjustable Column Width',
  'aa2bd9f54608c0c85d3ceecb707938c9':
    'The user can drag the column to adjust its width.',
  'ed85be57262e5a0c3116293e88278fef': 'Row Setting',
  '3d2ac2fd2c60931fff1db814662334c3': 'Select Height',
  '85a49c5ed4628647f2ead9206224dba3': 'Follow-up Content',
  '4296d7d293c9ea4a0e52c6415f0b5c96': 'High',
  '1f1ca9df5fa3648c718ad04649888943': 'Selectable Area',
  '596171970b639a35dadde2aa930d666a': 'Whole Row',
  '388855093d17f3df43ff80242d7a1bed': 'Row Disable Conditions',
  '46705a530ba9721527a4202bae7091bd': 'Select Menu Item',
  'aab57a3547a451f756bb8231a1eee8d7': 'Inverse',
  '76159d0d1261c0b6c310901244457e36': 'Cancel Selection',
  'be4751b0c9adf1d8deee45226c6124ee': 'Select odd item',
  '49f4010dade8652e5aff6a2c67aa23a4': 'Select even item',
  '49b4aa407b91ac997e27314e30c03110': 'Expandable',
  '8c1f5c49de09adab9a0e0c39e0106f78': 'Row Expansion Conditions',
  'caca6cb58342bb604483d94f49515234': 'Nestable',
  '5bdff9fd07d2a2430ac50e1559dbee27': 'Draggable',
  '33eaf97ecb3465754855e847f14d129c': 'Quick Saving',
  'ce7d31d64f2315e1d4cede288b9dfc60': 'Single Saving',
  '927b639f244953f237cd12943c8b815c': 'Horizontal Scroll',
  'fdd59ca00eba17d4bfebf744056ce4ab': 'Nested Indent',
  '6bfd4423d8aebbf0cac7ba4d74f245bd': 'Select Column Width',
  '21922c6479665dcba83106f8e9ffdf68': 'Width Of Fixed Selected Column',
  '31b7c58c2d9a170829b90314ff98b66a': 'Expanded Column Width',
  '81ccf26d9622d139a13ba2a61bd9fea4': 'Width Of Fixed Expanded Column',
  '68e9249db7bd12ab17994b1761b049f5': 'Customize Row Style',
  'ef0c5b6fa16497343eedb76171d61d68': 'Expanded Row Style',
  '1a7bd457c08093cf2cf887403dc249d8': 'Expanded Column Content',
  '4ca07911d10b74cc7c357b510e7cc948': 'Column Field',
  'eb2719a7e6cebda7ca234560f21fb448': 'Column Title',
  'f35c90b504521a2da346960b9db23828':
    'When no value is available, this prompt is shown instead of the value.',
  'ac83dbca40c9d2151b5f7d81795535cc':
    'With this option enabled, you can sort it according to the current column sequence. For the interface types, you can increase sorting parameters.',
  'af9cbd3988196fc104af4fed9461e152': 'Searchable',
  '34dceb7c51000849ea3596fbaab6f67c': 'Search Type Of Configuration Column',
  '9a899d9ab83d8ffa6308fb31e93f23a1': 'Quick Edit',
  '4562be5a3f9823a5f61b25f8d14b2b43': 'Modify And Save Immediately',
  'c115f372bcdced1e70824bcbf42b5923':
    'With this feature enabled, the modification is submitted immediately. Instead, the modifications cannot be submitted in batch.',
  'fba91204d335ae6eda35809023a94f7f': 'Configure Edited Table',
  '90ef7c485bd31fab681c6e9d9afd5be8': 'View More',
  '71c0319fce9f416330b18e554e0acc55': 'Floating Window',
  '64035b04a21bc337a351b5a2a5d12acb': 'Floating Window Position',
  'ea71a81cf874780294d517b0314feada': 'Configure Content',
  'ca60c525372028b9f75ee4c708cccae1': 'It defaults to the current field value.',
  'c9f7324519225bc72fce24a09518a8a8': 'Merge Rows',
  '9af45e91a08b54764610ada28272d590': 'Merge Rows',
  'ad96280bb24a38f9a83051b16ebc9688':
    'Default display during the customization of columns',
  'eb58b078f2f8560160ebf87bc7109de9': 'Content exceeding line break',
  'f1f4c88f30744f2365b65f1790c71da8':
    'Wrong component merging object. The panel is too old, it cannot be handled unless you add a panel.',
  'd17020cd3e6564f5154cf2251cd30f52': ' Row',
  '7cef725b75da8afecda59900b780be75': 'Timeline',
  '8f32bcb8d5baf6cbb410ef3f6dbed8d5': 'It is used to display the timeline.',
  '1aa46d7cdc632756dfbf16c55436bcdf': 'Node Data',
  'c360e994dbcffdf31e86d2d8875370e1': 'Sorting',
  '825f53899a11e598fc9f9b43e0814a58': 'Positive',
  '8eb4b7abb66f0922778a39044b42d345': 'Inverted',
  '0da2ab28a6e03922d4a0c78451146b87': 'Direction',
  '00ff9356c34d05ecbfd6559a46f56e25': 'Position Of Text Relative To Timeline',
  '71039986e2386573ab6e5681986c2230': 'Alternation On Both Sides',
  'f80cc88446cc10da4838556666f6b9f1':
    'You can configure the <code>headers</code> object, add the custom request header.',
  '005c50d1af6e833d6991ab882653b7ae':
    'Due to failure to load the available field, please contact the administrator! ',
  '76f47297fe988267a26073a9aaf7911f': 'No bindable field available',
  '020586d0c69f8211840ddf9ee9bbf6ab': 'Bind Field',
  '91be693dd1ccea38f8f514318fd8a944': 'Enter name for search',
  '8517171ce4ad0e9a5b511bd6bb26f839': 'No available field available',
  'b51796f5778fdc31bac73769a85f89c7':
    'The corresponding component is not found.',
  'eee03351367bb1907dcc3140ffa3e3b8': 'Go to edit',
  'f99603414a616bdee85de0e6e3938b65': 'External API',
  'e6ff6a97bf600c02942db3126a7077b8': 'API Center',
  '37087e5bb2d0367872a461f535580d91': 'Enter Time',
  '96641a78cfd9f9f8ba68f0524347b186': 'Enter Title',
  '1bf14fd24efe68f62bbff0538dee238a':
    'Each option is listed in a separate row. All items with non-repeated values are added as new options. <br/>You can set the time and title for each row using spaces, respectively, e.g., "End-of-term makeup examination on June 23, 2022"',
  'd584018521820dac9e92120737b733ba': 'Node Configuration',
  '72a3c1690dead6e24f7ac1abc90d5063': 'Enter Display Time',
  '79d3abe929f67f0644a78bf32adb3a89': 'Copy',
  'f86418b525af4b573aed36b8e3f9aeb8': 'Add Table Column',
  'ec159d98c6c25fadd38bcd9362f6a28e': 'Set Table Column Option',
  'f302b37cf6530d3fb39234a220d95437':
    'You cannot set the table row until you set the table column.',
  '6090f7af1ae15892abe97409b9e557b1': 'Add Table Row',
  'e6a10b831ae920bba1bb89a725e0fbe8': 'Set Table Row Option',
  '9bef5e571702130c5710af4ee2c27455':
    "/* Customize JS instructions: \r\n  * 1. The action execution function doAction can execute all types of actions.\r\n  * 2. You can get the current component instance through the context object. For example,  context.props can get the relevant properties of this component.\r\n  * 3. The event object can execute event.stopPropagation = true after the doAction. It can stop executing the follow-up action*/\r\nconst myMsg = ‘I am the custom JS';\r\ndoAction({\r\n  actionType: 'toast',\r\n  args: {\r\n    msg: myMsg\r\n  }\r\n});\r\n",
  '8df0f3891f8a80a392816f6ca662a33d': 'Refresh interval',
  '21157cbff85802e353409f647f1f1f91': 'Millisecond',
  '4fe2f10c6d5bedac03f40a4362e4f69b': 'Enter Component Name',
  '89bc2a21c778b36d09c8d795aac8260e': 'Data Entry',
  '666352a09304cba42de24312f509c3b4': 'Automatic fill or reference entry',
  '7237d0dfa065bf451120d06330291b7d':
    "({\n          isEmail: 'The Email format is incorrect',\n          isRequired: 'This option is required',\n          isUrl: 'The Url format is incorrect',\n          isInt: 'Enter an integer',\n          isAlpha: 'Enter a letter',\n          isNumeric: 'Enter a number',\n          isAlpha: 'Enter a letter or number',\n          isFloat: 'Enter a floating-point value',\n          isWords: 'Enter a letter',\n          isUrlPath: 'You can enter letters, numbers, \\-\\, and \\_\\. only',\n          matchRegexp: 'The format is incorrect. Enter the content that meets the rule of \\$1\\ .' ,\n          minLength: 'Enter more content which includes at least $1 character.' ,\n          maxLength: 'Control the content length and do not enter more than $1 character',\n          maximum: ''Check it when the current input value exceeds the maximum value $1',\n          minimum: 'Check it when the current input value is less than minimum value $1',\n          isJson: 'Check the Json format.' ,\n          isLength: 'Enter the content with a length of $1',\n          notEmptyString: 'Do not enter all blank characters',\n          equalsField: 'The input data is inconsistent with $1',\n          equals: 'The input data is inconsistent with $1',\n          isPhoneNumber: 'Enter a valid phone number',\n          isTelNumber: 'Enter a valid phone number',\n          isZipcode: 'Enter a valid postal address',\n          isId: 'Enter a valid ID number',\n      })[data.type] || ''",
  'd3e329f73ae4b58d95cc7769eeca8370':
    'It takes effect when it is at the left or right position.',
  'fd179c2844536ce198290441c38c814e':
    '<span class=label label-default><% if (data.type === button-group) { %> Button group <% } else { %><%= data.label %><% if (data.icon) { %><i class=<%= data.icon %>/><% }%><% } %></span>',
  'c5d48d5732c64d5dea4bb0b4aaf13813': 'Enter className',
  'a11cc7a65b27f3993d58438d275f3447': 'Enter Content',
  '34df758502e02c7c1a58f804a6c96c28': 'Text before being folded',
  '1d20d90b7c7301b7739900242d38544e':
    'No configuration. The title appears by default.',
  '8aea4138b4fac2627c9b72da37e0671f': 'Text After Being Folded',
  'e9b91e9101059dc2e234d9847dd7b003': 'Popup Data',
  '544fac400db790f57ea8ee4207cbeb6b': 'Standard',
  'fcb9b16d5d056bfbf6b6cba9dcf61efa':
    'Refer to the format application in <a href="https://momentjs.com/" target="_blank">moment</a>.',
  '17a689143f0c7003123bb3c947d35273':
    'The verification priority is higher than the maximum width.',
  '2aa41edf8cfa79e7e5fcf38c9742b495':
    'The verification priority is higher than the maximum height.',
  '37ad5d98b12853d786b08cb52b91a43a':
    'After enabling it, you need to set its height and width through the CSS class.',
  '324d0ccd6fa9d976d2f17de3bf0b70bd':
    'Control the display size according to this value when the fixed size is enabled.',
  '7a377ac3904628fd73e7d33ce6130ae1':
    'Min number of nodes selected for form validation',
  '674ddb63a7cd9e14a49813d52cf7e25e':
    'Max number of nodes selected for form validation',
  '24c5febd312d27b5e80354cf03e241f0': 'Outer Container',
  '54d621d5bd588bea4e896de52147a229': 'Row data selected',
  'ae76ff4dcb615d3b0232dcd10115e149': 'Initialize Static Data',
  '152b66069787294e53cfc176a189c81f': 'Field corresponding to the icon',
  '10cfe8897db8c032986138ba7b2f48da': 'Data Field Assignment',
  'a6e2cf5b7fa625f571a3e6d0df3a7327': 'Data Field Member Assignment',
  '3371427f1b82095309092ef82418ec1a': 'You have added this event.',
  '0e9525b2bb1493c567c114dd61b69095': 'Notes:',
  'ff0d36eac9b9c029d91c365cd2c7e6cf':
    '${data[&] ? Disable immediately:  disable immediately}',
  'a718dcd3a16bee2a4086244ef1eb0ab4': 'Enter the content with a length of $1',
  'c323e3527a805cfdd264700fdf013daf':
    'Control the content length and do not enter more than $1 character.',
  'c762cefa0ff423010af0a943c04d603b':
    'Enter more content which includes at least $1 character',
  'b95aed5c6f2c8e49e23b382ac3d593ba':
    'Check it when the current input value exceeds the maximum value $1',
  'fd11733fbabaf2ae3cf1fcd3fe385cc5':
    'Check it when the current input value is less than minimum value $1',
  'acf719549561f28f38bf750a64cda508': 'The input data is inconsistent with $1.',
  'e027500d91d46a962036f63c09492c6c': 'The input data is inconsistent with $1.',
  'd01886eeef1de19f2e99617017f4def8':
    "'The format is incorrect. Enter the content that meets the rule of \\$1\\ .'",
  '5e568c42a5ecb74db2dc3d8531079dd6':
    '<a target="_blank" href="https://www.tiny.cloud/docs/general-configuration-guide/basic-setup/">Reference documentation</a>',
  'cadf0e302ddbc6fdbf005aed670b5e3e': 'Code Changes',
  '6006074bdabc639b86f42aa18876e33a': 'Fired When Code Changes',
  '28e5fd494ea37a09fd2ad36d0f98bacc': 'Deleted Item',
  '046de1a6cb21ff83af74958342c0db64': 'Activated Item',
  'f6e5fc909971d2e5f6ffe23edae93584':
    'If there is a lot of data and the comparison is stuck, you can turn on this configuration item',
  '31dae237cbd03ae0d2dcf7ad2fd4fb76':
    'The data returned after the initial interface request is successful',
  '850c62f7b1ebfb4b89182ecd51202a7d':
    'Triggered after the form is submitted successfully. If the event source is a button and the type of the button is "Submit", the submit success event will be triggered even if the current form is not configured with "Save Interface"',
  '88b2930823f9fd6706473805e9b11797':
    'Save the data returned after the interface request is successful',
  '3e07258baf3c4389c1ffd4a98c20b8fe':
    'Save the error information returned after the interface request fails',
  '0d6581b6dd51f6c03a4edf26475d75c6': 'Removed Item',
  '4ea280a2e54969de1d1b9bbd5b708e63': 'Click Option',
  'f05520432bb87ced419a1da818c6cc9d': 'Fired when an option is clicked',
  '91f7b7fdf9b91073ca3519260f7a62d7': 'Option Clicked',
  '6afde638796d237377b0755506d08ded': 'Target Component',
  'fee99f2bcced486e4753a8f58f511d3f': 'Append Data',
  '3f3f4c71c1b736e7f01cf910a553ff43':
    'When "Yes" is selected, the data domain variables of the source component will be synchronized to the data domain of the target component.<br/>If the target component is a CRUD component, and the data pull interface of the CRUD component is a get request, the data domain variable where the source component is located will be appended to the target component\'s initialization request query.',
  '2aecb19ca1655d66fc80fc27a783cc9e': 'Append Method',
  'b67b01a3b9170f1daf78082cfd0df793':
    'When "Merge" is selected, the data is merged into the data field of the target component.<br/>When "Override" is selected, the data will directly overwrite the data field of the target component.',
  'bd81577a6fd4956e676cec499bb70d00': 'Merge',
  'e09fea40f7e4abd4b2a495b315940688': 'Cover',
  '9a5500b6013ec1ebf61bdf0e18452348':
    'The data defaults to the data field where the source component is located, you can select "Custom" to customize the required data',
  'f30bcdccf71b19e858c37d8881d2b206':
    'The data domain where the source component is located',
  'fa644cb20c66f7530d8a376d4fa6a36e': 'Component Data',
  '7d9260bd45b2e85a09398f218c25f220':
    'Update the data field of the target component or the data value of the target form item',
  'e7af71fb102cc86ab3be6a2fb32b5e3f': 'Data Settings',
  '77b1081c177fa3334cc93c99f0ecee75': 'Direct Assignment',
  'f5c5e3d69daee06ea1606378ef466765': 'Member Assignment',
  '697af73997072e0ce9ee65b15a7b3715': 'Reset PageNo',
  'd7bf42dd6e66f2818f9a232603c4a53b':
    'When "Yes" is selected, the crud component will request the first page of data',
  '3e573fd37473d789211ee44335d82fad': 'Static CSS',
  '905407c57ccd033cb6bd64bfad20a8c7': 'CSS In Static Display',
  '0d1f68afa19f3f3dd88e28d17c98ddf9': 'Table 2',
  'e052287273ad39a1d3fa9fa3decb5fd9': 'Component Display Status',
  '506f28f48dbebd5d19e19dfc721e13be':
    'Control the input state/static state of the selected component',
  '0e35b091e18032508758899735664df7': 'Components Switch To Static',
  '34d361256526b04909e064c29d9a9b76':
    'The component switches to the input state',
  'd2e930293da37452638759e17d771adf': 'Component Status',
  'b535bea11c97ec5588b1494799de4d60': 'Form Input',
  'f7784642f42d33f506ba05f3daefc3c4': 'Form Static',
  'a8797a840f3c0bbb5297aada95f9b13a': 'Form Item Static',
  '7bb3e24cc54f4b8ee0a65d14fa4c067c': 'Modify Activation Tab Value',
  'fd951a59a7c635d5330bc1aeec22c813': 'Custom Data',
  'b62ce3df3c0d5772006c525b60d5eeab':
    'The data defaults to the data field where the source component is located, you can select "Custom" to customize the required data',
  '0f9803bd27434940d4017007c105a861':
    'When "Yes" is selected, and the target component is CRUD, the request be send with this data. Other target components send with this data just when the request api is POST method.',
  '81e1ff0bb8917a9df99d737982ee24b7':
    'SubForm, configure a subform as the current form item',
  'a5c2dba5ccf62851b24cfa12d4958ce2':
    'It can be used to display data and array type data, such as multiform of multiple',
  '74104c62ed33836f0bc74297539dd7c9':
    'Configure options to realize selection options',
  'b5ade3d97d5b11bc784786111e011571':
    'Tree structure, options can be configured through options, or options can be pulled through source',
  '2c05e451a6f2b2fe1cf55f7afb8c8423':
    'Single or multiple choices, support the source pull option, which can be configured with pictures or customized HTML configuration',
  'c65c9862813c7a66c0df52e301e0e1d1':
    'Applicable to options that contain sub items. You can pull options from source and support multiple choices',
  '8f650b58c8421edecfb380d6f60ef40e':
    'Configure the data sources available for selection through pickerSchema to select the required data. Multiple selections are supported',
  'b7a4abc1e4e975c9df5bb9d9cf4823ba':
    'Configure options through options, and pull options through source',
  'f2fc416c7d95a93a8da621f760be8417':
    'Support multiple choices, input prompts, and use source to obtain options',
  'b4482a3d9523f48e83a816fa85911185':
    'It is purely used to display data, such as json, date, image, progress, etc',
  'ecfd82eb65102274188011a502913d3a': 'Drawer data',
  '951f802ebd0c0d795fbae6767a5ee9b3': 'Initialize interface request succeeded',
  'da0126992b4937a5fd847ef5366b02e6':
    'Data returned by initialization interface request successfully',
  '70b8342d743374233bfee0f56c7f0fc7': 'Node Sample Data',
  '38f85482d657cd4db1280c5efa1950fd': '{{@1}} Alignment',
  '0a0574baedb8eb2abf7daf25159d8bb1':
    'Set the alignment of sub elements on the spindle',
  '5ccc4c05cd41195f202f550a4c307a64':
    'Set the alignment of child elements on the cross axis',
  'b1b98c19058af70d8bd499e1899e93bc': 'Layout Containers',
  '03097563d201ad3a29c79165226764e5':
    'The layout container is a layout effect based on CSS Flex. It is more controllable than Grid and HBox for the location of child nodes, and easier to use than CSS classes',
  'e151c86d57096bb74dcd390ade29362b': 'New column',
  'e5f9b3a3655b8daddcee8b97b735887f': 'Insert Layout Container Forward',
  '577b33bf128fba16ed8e9bf7c395f455': 'Insert Layout Container Backward',
  '31f84d1bc6175fd0828a81b5bfd98736': 'Add Column Level Element',
  'cbc1d00cc640b67ee34a29a694ef162a':
    'Left (upper) insert column level container',
  'bb3cc092e17ff83e943554bde3d5771b':
    'Insert column level container on the right (lower) side',
  'b19b454fe603e03e98ad9772615c7c32': 'Positioning Mode',
  '8444f01399c0003fbb68eeff1310566c':
    'Specifies the positioning type of the current container element',
  '5ddea41072a27a74a1715549dfb79bc2': 'relative',
  'e9513a013011450c57cfe3ef51b7d4b0': 'Fixed (relative to window)',
  '3059599d8ebfec00a8ab53346d9b4fa3': 'Absolute (relative to parent container)',
  '86a6b5a0a45bba5b6187cc2277e3375e': 'Layout Location',
  '6e72759ed1cbf9f9e8523197dd93888f':
    'Specifies the location of the current container element, which is used to configure top, right, bottom, and left.',
  '6896da744f8ae9633263d55af0fceae1': 'Hierarchy',
  '6f649980c839dffca1506f20d534fe3d':
    'Specifies the stacking order of elements. Elements at higher levels will always be above elements at lower levels.',
  'a8489cf57d7f44e889aff79434776f47':
    'The default is block level, which can be set to elastic layout mode (flex layout container)',
  '4180e30c34190007ffaa654e0959b8a3': 'Intra Row Flexible Layout',
  'ebe7bde5c9094813e2924473488d281a': 'In Row Block Level',
  'dde193342b8c350ae29795117c0c5b9a': 'Horizontal Alignment',
  '5b15af1f73b4f2d5bb152410863602f4': 'Vertical Alignment',
  '78d32d2bd35c0262fe77b517c5a4fb62': 'Arrangement Direction',
  '3fa460b81736c0360f6f7571801935b1':
    'If the direction is set to horizontal arrangement, children are placed from left to right; If it is set to the vertical arrangement direction, the sub items will be set from top to bottom',
  'fa228d6bec96d052de0ad369407f5241':
    'Horizontal (starting point is at the right end)',
  '2df3bc66ab3fcb0de1caf11831eff595': 'Vertical (Starting Point At Lower Edge)',
  '98b2fea2d8f3ceb81e9ce32d66383f05': 'Line Wrapping',
  '9af509c2a9636343199b9072e001826c': 'Default (No Line Breaks)',
  'd4054144c4341872496e3550fdb1b826': 'Word Wrap (Reverse)',
  'ee2df1c1a0d99094f641166535948d4b': 'Elastic Mode',
  '947c03e411c20563c7ac67d0a5ad741b':
    'After the elastic mode is set, the current area is automatically adapted',
  'f92626f9e56b3e2d0c47495a446acf71': 'Elastic Width',
  'cf8852316501c22ea19c4e432c59e7d7': 'Default Width',
  '9cc69c8469b23b77519065d3df381113':
    'Define the main size occupied by the project before allocating extra space',
  '0ad8b3b736ae5b9e23cf16ac13e1e283': 'Proportion Setting',
  'fa6bb048a2f73975a40789b30c5b8a06':
    'Defines the magnification of the project, which is 0 by default, that is, if there is any remaining space, it will not be magnified.',
  'c19b79073b676b9bade80613aba2dbfa': 'Fixed Height',
  'd1b91a1a24f0d4935c2dd13e6a22b6d4':
    'The maximum width is the maximum horizontal display area of the current element',
  'c2ed47a1f0f45cf7e2d22bddffc8a732':
    'Scroll mode for setting horizontal direction',
  'cbc7af1d6422e88f4b87ade748e0f07d': 'Exceeding The Display',
  'b48a90c77b5e792260d830c2d68c527e': 'Beyond Hiding',
  'ddea62517e2bd1007712689746ebfe00': 'Scrolling',
  '55becc96b40692cc9cf898b331d16976': 'Automatic Adaptation',
  'ede82efb4a69c35743185c6c73ab771e':
    'The minimum width is the smallest horizontal display area of the current element',
  '6f420734edfaff00a8210a4c762a9207':
    'The maximum height is the display height with the most current elements',
  '411f9d120093314cd38e6dd5cce398c6':
    'The minimum height is the smallest vertical display area of the current element',
  'b31c6aaa78f8e24df665ce80ab5301e2':
    'Scroll mode for setting the vertical direction',
  '4fc0e68b093db41b45a4ea706fbe56f3': 'Center Display',
  '55efb233147f9539de019d9abc7653f9':
    'Center display by setting margin: 0 auto',
  '2bf5bcbe21f39b254a601664fb8b264d': 'Default (Wrap)',
  'b2d418355cb59a5613ecff7b150c588f': 'nowrap ',
  '7d1313925f158b747c094a7f2480e535': 'Position',
  '41a7494315a528f0f9618646f7e0dddf':
    'It can be set as upper left corner, upper right corner, lower right corner and lower left corner. The default is lower right corner',
  '845c61ac8f51c6702dd22e5657c07e8d': 'Lower Right Corner (Default)',
  '2794fe303cf8ad4395fe93271fae7925':
    'Layout containers are mainly used to design container components with complex layouts. The layout effect implemented based on CSS Flex is more controllable than Grid and HBox for the location of child nodes, and easier to use than using CSS classes',
  'abbd790f85282349e2004df9fd494e31':
    'Main size occupied by default before allocating extra space',
  'dbb93e8f413074ead24b6ed822247d98': 'Insert Layout Container Above',
  '5b5765b3fd7e72e04a5cd3e2ef6218a4': 'Insert Layout Container Below',
  'ee466872b9a43e720e296813dbc5adee': '{{@1}} Insert Column Level Container',
  '14c495b1248756310c75396cd41f4fe9': 'upper',
  'e33ac3a4c1a95a02a18f1555038804da': 'Below',
  'e731c52010266b8ade1e7e78e25cdccc': 'Common Layouts',
  'f80bd0a5546465336f4f9cafdfa8b67f': 'Default Height',
  'ba9ccf1040d7abd0848046330ba3558c': 'Classic Layout',
  '230d65546ea0d299907943403608233c':
    'Common layout: classic layout (layout container based on CSS Flex implementation).',
  '9bbb7cfaeb34a2b5c095ac253355f028': 'Levitation Vessel',
  'a3e91631c1a3a43e09526ea7f6b8595c':
    'Common layout: suspended container (layout container based on CSS Flex implementation).',
  'd423930b823fc45f08c18922b19e4e9e': 'Bottom Suction Vessel',
  'b8b4eb373d8ba6f98271b681fba2511d':
    'Common layout: bottom suction container (a layout container based on CSS Flex implementation).',
  'faaa6444a709917ff33e0d58948504dc': 'Ceiling Container',
  '1facf0bd0f56c66759857345e7434443':
    'Common layout: ceiling container (a layout container based on CSS Flex implementation).',
  'f416a3a2566dda04bc0ef67027e6f460': 'X axis rolling container',
  'e3d9ad8453925764f2918dbfd6ff824e':
    'Common layout: x-axis rolling container (based on the layout container implemented by CSS Flex).',
  '053e0cbf18c8fe59b928d52fcd556b88': 'Y-axis rolling container',
  'c9f089cefc06c217c6dddfe2fc772ea3':
    'Common layout: y-axis rolling container (a layout container based on CSS Flex implementation).',
  '3587540660a01f8a8aff6a2c0409a404':
    'After the elastic mode is turned on, the current area is automatically adapted',
  '2ea6f1f33dec7cb3c23a1bf1f9eab11a': 'Mouse Click',
  'fe9228649853d08eebee72ad5521a3dd': 'Triggered when the mouse clicks',
  'a643d2fe12d205eb8fb5cffe92f62c35': 'Triggered on mouse over',
  '764c134791952dd1acb0f23587e75421': 'Toggle legend selection status',
  '262cd2f688751332c1907a659e686210':
    'Triggered when the legend is switched to the selected state',
  'fd312ae2f1c24b8a14d9412bb3c6bb76': 'Last n Days',
  '68cba5f27ab003cfada5eb4c1f29eb21': 'Within n Days',
  '34b06708894a178c440e6f6539e95e9e': 'Last n Weeks',
  'f0e1b8d8a6e731360d7348bc8301d44a': 'Within n Weeks',
  '5cad2778bb8f01d1a1b1226082eb2117': 'Last n Months',
  'ff76f60b3da86a0d2c6663b170a7955a': 'Within n Months',
  '9ff9b56c9ed633ee09396830e93113ad': 'Last n Quarters',
  '2b80b20008b3ae81136217ae10a1fbaf': 'Within n Quarters',
  '84752114d27119dc50d8a7b9ac0b788b': 'Last n Years',
  '5df2352afebd73d1f568ebaa81e2db28': 'Within n Years',
  '8572d14f815d840bd9e940cd8ee4e380':
    'Add or subtract data values through up and down arrow keys on the keyboard',
  '925d31bb30d63576600299475a910c33':
    'Displayed before entering content, not included in data value',
  '42677544e2cbee28f7e7df216e685543':
    'Displayed after input, not included in data value',
  '046c6233e03af774df7b90b4504fa96c': 'lately',
  '249aba763258bbe488af3e79a381d265': 'day',
  '2f8ab07bea442bc6ce8a9e99ff88e5c1': 'Within Days',
  'a657f46f5bb00961adfae80d12e41b3d': 'week',
  'b1599eaa05dc9b3d90be5aebc5c84338': 'Within Weeks',
  'e42b99d59954ce6437e66f416850425a': 'month',
  '3c690347976de82df1909750cbc82b80': 'Within Months',
  'b62ba98a627851e911bef8fbb005bd4a': 'Within Quarter',
  '465260fe80b0c3338d06194bb7a94446': 'year',
  '137bcb84f52bbd48623c37d8dfdebdff': 'Within A Year',
  '279d93f2d745ed08e9034022941510dc': 'Common Span',
  '7a1e5f93e362d371519bcb2bfdb0fc9a': 'Custom Span',
  '4a12e9b9fc5443e5e9999b5a7c56c19a': 'Default (Full)',
  '2cb472ff9cad0c89a033c53996b52053': 'Init',
  '76ddcc0ad85aa4be6875b73244a64faf':
    'Triggered when a component instance is created and inserted into the DOM',
  '584e4b6108e132be92c9de09d7bbed72': 'Current Data Domain',
  '9328b90ded33d16a873db5c0dbd815b8':
    'Initial data interface request successful',
  'f3b97bd71a77cca1e9288089a537cf3b':
    'Triggered on successful remote initialization data interface request',
  '9787088794f42c7e476cf7580f81447e':
    'Initialize the data returned by a successful data interface request',
  '029e280e119b028bffc51424d909c07d': 'SelectedItems',
  'a6eebb9b4fc7148e2dc41335c74e6393':
    'Initialization of Schema interface request successful',
  '887954cd9bdb290003984fe9a6eb99e2':
    'Triggered when remote initialization Schema interface request is successful',
  '8f0064a9cfd7dcbb3c729f1357f11772': 'Send Data',
  '6d33298a54888a30753373ca5bfe3bc2': 'Return Data',
  '542e06175ff4f7407c467bbde90de56a':
    'Turn on this option if you need to do additional data processing on the data in the returned results',
  '7c583ecdf795ce4f1f40d7960ead9344': 'Default Prompt Text',
  '70941a02776496ec446f21f98ebf754e': 'Request Successful',
  'f50bf418323ee425eca7208c09a4577e': 'Request Failed',
  'f3dc08e3617d1e19cf8135be4412a80b':
    'After clicking, ask the user first, and then execute the action after manual confirmation to avoid accidental touch. Values can be taken from data field variables.',
  'faa29265819714253843e23437b9193e':
    'The prompt content under normal status. If it is not filled, no prompt will pop up. Values can be taken from data field variables.',
  'f855f46ce6146aa17a9ed423da16bfa2':
    'Prompt content in disabled status. If it is not filled in, a normal prompt will pop up. Values can be taken from data field variables.',
  '037becbe8bff2f8838d141cc7b6b2df7':
    'Support such relative value usage as:<code>now,+3 days, - 2weeks,+1hour,+2years</code>, etc. (minute | min | hour | day | week | month | year | weekday | second | millisecond)',
  '8f7ae284d0039fe05b9f57fd5ae3ede9': 'Please select a static value',
  '98229308e2e9484583fde4ae363a979f': 'Expression or relative value',
  '6b3c2a07db1bb3c229bbc5df48068792':
    'Support such relative value usage as:<code>now,+3 days, - 2weeks,+1 hour,+2 years</code>(minute | hour | day | week | month | year | weekday | second | millisecond)',
  'dcc94ea1715bd502c709c5d5092e9c82':
    'Support such relative value usage as:<code>3days, 2weeks, 1hour, 2years</code>(minute | hour | day | week | month | year | weekday | second | millisecond)',
  '13ce82d026daa5a30e50105bd2a204a6': 'Please enter a relative value',
  '31c29c46536a5007522032d2a42db56a': 'Numerical Template',
  'a6a41d1bfb5896210eb527d183a07958':
    'Value rendering template, which supports the use of JSX and data field variables. The default is ${value}%',
  'eadd1d64cd6ceb2c50554281cd2d3be0':
    'Dynamically re render the configured tabs based on this data',
  '0d9d899edb456e8806a99850e9c38212': 'Configured Expression',
  'fbb96f7ea104d34fc4b7bd625d892c45': 'Click Write Expression',
  'e0c7ac5eb397512fdbe71600baa09dab': 'Please enter a static value',
  '48942ef507ea38d8ead03f8bfdffae5a': 'Relative value configured',
  '49041f245018a6d799fee3c6f177c782': 'Exit Full Screen',
  '185926bf986c784d03a9a73102da6542': 'Full Screen',
  '891ec6336d4243714c25eecb2f8f774a': 'Option Text',
  'fb7ea2b05ca7328ee16a562d90c2eb96': 'Option Field',
  '1ca87f0171481e27d94e81b477150b7d': 'Option Template',
  'd6ecb32a380c91887a9346653c2427e9':
    'Customized option rendering template, supporting the use of JSX and data field variables',
  'cb048b2d8426afd464dc01b72c446342': 'Block Level (Default)',
  '3b6e8d54b7b2ae890d5357b7eaaeaaf2': 'No Line Breaks (Default)',
  '0611733b53e0098e6fd880bd44b2806f':
    'The minimum height is the smallest vertical display area of the current element',
  '03bfb834c8a5fef58d885e448a4e13b4': 'List Threshold',
  '50437e080edc71ab624c93d419472919':
    'When the number of options exceeds the threshold, the virtual list is opened to optimize performance',
  '02b9880e1d2df8a07e90e9878080c739': 'Option Height',
  'a3f66655c3d2bcfecc6afba0e4424460':
    'Height of each option when virtual list is turned on',
  '183f00df0922a6be371fea58cd46a60a': 'Elastic Height',
  '9e7c8d1554f6449121a83f951cf21ca1': 'Elastic Scale',
  'ff9e9329fe186be342ef59ee711b9371': 'Y-axis Scrolling Mode',
  'c18457fe4f249f06b48297ccfe6224e8': 'X-axis Scrolling Mode',
  '99b57d8c9244ff9a695fcd519b4e2e57': 'Max Width',
  'fc2bc4193eea63128961d09497e07dc8': 'Max Height',
  'a2b62974f4d7564bb68b570116f25a10': 'Min Width',
  '2bd921d0ea7a73b77ee0fcddb1afcc84': 'Min Height',
  'eb7366583485f478e3d8c2b105ea51ff':
    'Defines the magnification of the item. If it is set to 0, it will not be magnified even if there is space left in the parent container.',
  '5d3d48de1ba22368eacdc1c69fb044ce': 'Component inner layer CSS',
  '72c32b47c5e4dcd58ddabdb8fde761a0':
    'Suspended container: a special layout container based on CSS Flex implementation.',
  'a03384e93b62e3110aa92c9e345111ba':
    'Bottom suction container: a layout container based on CSS Flex.',
  'f564e3e66dd2aca0e080cb6484c95a5e':
    'Ceiling container: a layout container based on CSS Flex.',
  'db805d4e361ac2d3fc6047eaea1a7c69': 'Free Container',
  '9ccbfde404798593fa6fdeac9dbef200':
    'Free container: its direct child elements support dragging to adjust the position.',
  'f20ca09e513399510ce34ba2cb05b7d9':
    'X axis rolling container: a layout container based on CSS Flex implementation.',
  '47ef0cec2c3436377a42390e10de4567':
    'Y-axis rolling container: a layout container based on CSS Flex.',
  '8c2bb89f516205027b9ed6609fb17726': 'Blocking Condition',
  '46a3c6ab94da0b16a707bdd3b74c9e09':
    'When the conditions are met, the execution of subsequent actions of the current event will be blocked',
  '6e6d4269d0dc3324d551062350a2ae9f': 'Silent Request',
  '56e13c39822a814ab39b0d5a0867d7dc':
    'When enabled, the service request will be sent in a silent mode, that is, no success or error message will pop up.',
  '7f019b96ffb7d72ec8d6ce8d76e5362f': 'Expression configured',
  '699829ed5bab67dfb360764c3bbaed4e': '{{@1}} - Event Configuration',
  '5ba999eb762f60324033b735e55d989c': 'Event Weight Prevention',
  '7fe94616be0e8fb5ef5ab40a7397f0aa':
    'After event anti duplication is enabled, only the last event will be executed when triggered multiple times within the anti duplication time',
  '91b72e901f1663637157cda638ac4dcc': 'Anti gravity time',
  '969e9e56b3812abffa3994f35ea31835': 'Adsorption Vessel',
  'bb0e1fea25aafbe731886295fcef9a2e':
    'Adsorption container: It can be set as a ceiling or ceiling display.',
  '7d30297d4e1f310c73b27be88c748026': 'Adsorption Position',
  '2ea76e2ffc1b92911d6f7decfa993360': 'Top',
  '8cfc818a76662085cb64752d6d592fbe': 'Bottom',
  'e2f6535e21570a0703c7c65f41b30eaa': 'Rolling',
  '1c5ea0ffb2b15713cb22c41a02576924':
    'When rolling adsorption is enabled, the adsorption mode will be automatically enabled when rolling to the upper and lower edges of the parent container.',
  '5f9be0002394f0b58952969d5952e24c':
    'Used to set the position of rolling adsorption',
  '28968f372fe88c0ef855c7f79f42bbad': 'Uniform Distribution',
  'd481ed97681365f18cf20ef914473a02': 'Align Start',
  '02f6ac7a3bf6a8b4440a2a1c3e1daeea': 'Align End',
  'cbac406a3f51abad691702015b0784ba': 'Full Height',
  '5a4e41af91746f8a3905aa9f66048955': 'Load Settings',
  '6ade3082696deb00357f5c0359093cd4': 'Merge To Upper Loading',
  'b6ac896eff6a6502e4ae7079b3e507a5': 'Not Displaying Loading',
  '0e9ffe1f1cf3f7a620970ea75dba9f39': 'Use Page Global Loading',
  'b1119174b1beedd2218e0b359ef14aec': 'Form Submission',
  'c6a65a86cd2bd74a6f72df0a7b81d3cb':
    'After the event is configured, the default verification, submission to the api or target and other behaviors will not be triggered when the form is submitted. All behaviors need to be configured by yourself',
  'c207ba29769aca6ffd45db9f80bcb29e': 'Zero As Head',
  'f02f876ee64cc016d97fa4dc498d4857': 'Height Setting',
  '091885db07e43ff7cbe60c3b664b0b50': 'Elastic',
  '363165ccee78341a65f1d42174e8b08f': 'Adaptation',
  'eb44269adb6ba70569cd62ea88cb2750': 'Elastic Settings',
  '64561733c68085af3d09315c1d4d7ed6': 'Mouse Event Object',
  '052f93928af33d4d7035e7c8e8a73f17':
    'The alignment is set by the margin value, where margin: 0 auto is used to set the center alignment',
  'a738a8594bd2b71002d09277b84d86dd': 'Left',
  'fc0f19e9e47e352d36d36cf6eb653210': 'Right',
  'c70638412c6cffd150117ae403dea939': 'Component Static Data',
  '77fdd35933c099cdcb64b71f3fbe7a6c': 'Data List',
  'f01c3cd99ecb0c534cc47081d3433c9f': 'Custom Styles',
  'f7e3d7e3146bb53b5e6f09f7c90dea3a': 'Input Box Style',
  '********************************': 'AddOn Style',
  '9b6425cd2d496c9cb5a6c6b8ff125d1b': 'Input Box',
  '********************************': 'Custom Style Source Code',
  'ec8f3e4a298160dface2fda5c0125df2': 'Please enter the class name',
  '7e8eb474f73b6fd60c9aa5082f75e008': 'Label Style',
  'ef55ff12fb0d3efa3491236fee4dabc2': 'Description Style',
  '60bac4c0a381a42b320a703227be59eb': 'Spacing',
  '00d1a6230e9a31664c895e95fde707d3': 'Add Item',
  'cf3f7c0c1e6c77197bb0b6508a9173aa': 'Add A New Item',
  '3a553b1123f403cf1f81eb28b3e4b814': 'Max Number',
  '93bc4888bc13bb6e6c49b34b54077438': 'Min Number',
  'e61c32382d879b867938086a50ef094e': 'Flat Value',
  '30d4e9f1c60f2d5851f77463476d568b':
    'If you need to expand the user-defined new functions, you can expand them by configuring components-new items',
  '405a48545938c075e62eafb80b732769': 'Button Mode',
  'f4b06bd9e4f5ceaac7fbb0e17fffc518': 'Copywriting',
  '178bf4dd4b8d56370e2fc8275f9dc9e4':
    'Delete the confirmation document. When the configuration deletion interface takes effect',
  'cb8f7758eb03574f9b8402659c0f02b2': 'Are you sure you want to delete?',
  'f7894f17d0eba299011d856ce3efea73': 'Auto Populate Parent Variables',
  '7fc8dab2069004acb8f5c98c27ee0883': 'Sync Fields',
  '8e32c9ffc35059f8ac5254ffaf3c99b0': 'Title Template',
  '21766034147682a2435ecc766de5ea22': 'Multiline Display',
  'd8848daffd80dbb21ace876ade2ea8a8': 'Administrative Code',
  '977cfa6e8c7f036674afedfcc19ec7a2': 'Object Structure',
  '05fcf03ba044a04ce81bdbe6eaf49d17': 'Optional Cities',
  '912beb37a3785e50d6483852be41111f':
    'Tree structure selection, supporting the appearance switch between [Embedded Mode] and [Floating Layer Mode]',
  '0b52d7d00ed28e41f4845fec1622d056':
    'Tree, tree drop-down, tree drop-down box, tree-select',
  '********************************': 'Tree selection box - embedded mode',
  '********************************': 'Edit Mode',
  'ba1a077af717cb3c4788849c2342200b':
    'The configuration interface for immediate saving is given separately. If it is not configured, quickSaveItemApi is used by default.',
  'cf8774ca07f11ecb14b3b59c3891e9cc':
    '<p>The current component has stopped maintenance. It is recommended that you use<a href="https://baidu.gitee.io/amis/zh-CN/components/form/formitem#%E9%85%8D%E7%BD%AE%E9%9D%99%E6%80%81%E5%B1%95%E7%A4%BA" target="_ Blank">Static display</a>The new feature implements the static display of form items</p>',
  '26122d95c72204c83ebdc37cd16a96f9': 'Picture Enlargement Function',
  'a19769d02b8de60a1e3b46c3ef96f122':
    'There is a conflict between the zoom function and the open external link function. If you want to open the external link when clicking, please close this function first',
  'd196eb8f65e84003b7ca64d5dd8fc737': 'Occupy Bitmap',
  'd98b3f3d11e6dce8eac1a041247fbc50': 'Picture List',
  '8e1fee898434093aad55f5888497f970':
    '<p>Currently, it is configured for the field content node. There are more configurations for selecting the upper layer</p>',
  '86aafaa75b388deb4a4cbdab2293c099': 'Head',
  'e6aa1b827415217c524ae9d9b665cca5': 'Configure Header',
  'a2ecfd5a0db9c855f59eea75083678e6': 'Configure Bottom',
  '1d3d7de6b84f4d5d4a4e126e145af132': 'Text Encoding',
  'd55bbcd785be46c3182dcd7663c3c041':
    'Returns the character encoding of the content, such as UTF-8, ISO-8859-2, KOI8-R, GBK, and so on. Default UTF-8',
  '0021bd6b2290ddca1ad6a9c8848377bc': 'Load Prompt',
  'f013ea9dcba3f5ca1278aa850931fec8': 'Loading',
  'd7eb1e98e6cfbe38ab3b242c4dda1b7b': 'Follow Bottom',
  'ab2dd531871defe1ef7d8d06548411f3':
    'Automatically scroll to the bottom to view the latest log content',
  '41058383885336fbe750665044e4e38a':
    'The following action buttons can be added at the top of the log',
  '095e938e2a09eaddc8db146b86879f4a': 'Stop It',
  '0cee3cd1e0b0956fb3447d7188553e4b': 'Hide Line Number',
  'c2f42bd2a149bbeb4627b1e1b5fabedb': 'Performance Optimization',
  '45f14d9548decc8a1df4e470efcf8d08': 'Height Per Row',
  '7a5d14c5f61745f08700ba315609cf9a': 'Show Rows',
  'bddc8f2d9bdcc083bb4f3dd38eaba459': 'Display Area Height',
  '0218cbc88086353118fea07e364334d4': 'happy',
  '8167b3dd560a83cdc757d1022ff111e2': 'anger',
  '8fa28f95c03faa7cc87e487a89a35a72': 'Sad',
  'd246bdddaf59436cb2160837f7bef634': 'apathy',
  '2ab01e418dca1500dcb133d50656deea': 'commonly',
  '3585e4dd456b41fb8aec43f927b6a27c': 'Custom Display Template',
  '25e4c39320150bca74b4c05c7740e365': 'Configure Display Templates',
  '9e25d776a57c610940bcc1c19847b97d': 'Top Outer Layer',
  '2ec1b0bb189b486945e79c167a4a024d': 'Bottom Layer',
  '77ad0879912d0a306724c319eed113e2': 'Label for marking and selection',
  'ee66d7a2d02188816d633d11cf1a8b27': 'General Label',
  '092c4410e162bb3371f2aab804501f24': 'Front Icon',
  '3e7392ab43c6c76f294f41c3058c2ab1': 'Value Matching Field',
  '2135be5a60fc3c6fe864e159e289af51':
    'When the mapping table is an array object and the object has multiple keys, the field used to match the value is value by default',
  '6a1ca1c113a9b5bee58ea009e40f5954': 'Matching field, default is value',
  'dc7becbad712786c9e4766636047e509':
    'When the mapping table is an array object, it is used as the display field. Please use a custom display template to display multiple fields. The default is label',
  '1140acc9ac4b803b733f70445e7d495a': 'Display field, default is label',
  '55497c5de8558f1a164e437205cd510b':
    '<p>When the value hits the left key, display the content on the right<br/>When the value does not hit, display the content with the key<code>*</code>by default</div><br/>(please ensure that the key value is unique)',
  '94124fa72dbfbb81611496cada6ebf4c':
    'Please keep the array member structure the same',
  'a3221d2d224767df4afa7a8653ded8fe': 'Internal Alignment',
  'e8bb313fb86cf474c0e264794bc85896': 'Fix Header',
  '8e903bee4578f72bbecf9eb62d7b875c': 'Display Level',
  'c17fef27ea1d970fc66f4c4c3d442129': 'Default Inner Spacing',
  '236b0cdd2e18418fd17d3cdfcace239e': 'Plug-in Unit',
  'defe851634125bb16e762f26dbb6555f':
    'see https://www.tiny.cloud/docs/general-configuration-guide/basic-setup/ file',
  'df025e01cbbae804f7d720e6b932e8e8': 'Show Menu Bar',
  'b74c3bbb8ec4f18896cd3b5a20ee9e2c': 'Toolbar - Large Screen',
  'e08c3505f9779bf919628166a77d0d77':
    'Screen width ≥ 1200px, reference document: https://froala.com/wysiwyg-editor/docs/options/',
  '04f91b84d48285162d3e29205a194143': 'Toolbar - middle screen',
  '9ac0e46a361565f0b8be2228bef4b679':
    'Screen width ≥ 992px, if not configured, it will be consistent with the toolbar set for the large screen. Refer to the document: https://froala.com/wysiwyg-editor/docs/options/',
  '70d3894aaed305d2fc67f91122d77759': 'Toolbar - small screen',
  'a3133ac3b34da77e612bcb1763adae1e':
    'Screen width ≥ 768px, if not configured, it is consistent with the toolbar set for large screen. Refer to the document: https://froala.com/wysiwyg-editor/docs/options/',
  '194ab42ad1ca7fca9a6ef84fadf99490': 'Quick Insert',
  '94621f44f98c996e49d5b214aebefffc': 'word count ',
  'b720ed498f054a2539d496d0da68f85b': 'Picture Receiving Interface',
  '376496a8918c57220159951e24d3b72d': 'Video Receiving Interface',
  '555a9859e2c34b015438bdfb59a57c5b':
    '${vendor==="tinymce"? "Editor": "edit area"}',
  'afccc17d5d672b10a1292dcd671ef534': 'Quickly Build A Table Edit Box',
  '********************************': 'Display Type',
  '63bb911d6392cb2740140d406ab83d37': 'Edit Type',
  '6530334ebf5ca810e576858eba168685': 'Selection Box',
  '********************************': 'Color Selection Box',
  '********************************': 'Checkbox ',
  '********************************': 'City Selector',
  'e2c59e63f751088ad8b43a1439e4322a': 'Add Single Line Data',
  '41c1e36d5d202588511710ff0ccb9e8d': 'Delete Single Line Data',
  'f38b7eb6f78af9ae86c449153de2fe1f': 'Clear Component Data',
  '5e2eb93cf353f5bfa0920553fc8a0087': 'Trigger Table Data Update',
  'efc47e9829b5831e4d0424191049b880': 'Delete Data Of A Row',
  '383c125a11b0dd070b04194b9e2e78fb':
    'It takes effect when a single line of data is added or edited. When opening, after entering the content, you need to click the "Save" button on the right side of the form before submitting to the data field. If it is not enabled, the data field can be submitted after the content is entered.',
  '1d96dc9b36793e242322dd1e092a010c': 'Confirm Button Name',
  'c507f40ae40ad10fd0b99ac025a6e5d4': 'Confirm Icon',
  'e0698c2a2d5c568edfc5a0b1a1d298eb': 'Cancel Button Name',
  '08f06b06a0db0e978e3acdf7741ccd86': 'Cancel Icon',
  'ffa655818f7dd46fb2a767c51618741b': 'Empty Data Prompt',
  'd173fb23320acba326a4424133969256': 'Show Sequence Number',
  '26eb498526ba909386befc43466db79d': 'Number Of Displays Per Page',
  '078f9734eeef4ff8c06e0b639ce6bd8f':
    'If it is empty, no paging will be performed',
  '52029187eaa09f55193b6a15387e45ca': 'Min Number Of Rows',
  '********************************': 'Max Number Of Rows',
  'aa8b2a821e8e32196a720eaaa41b64d3':
    'For performance, the default change of other form item values will not update the current table. Sometimes, in order to obtain other form item fields synchronously, you need to enable this.',
  '2aa56a9b94ee3fde76a15711c94fdabc':
    'Whether the parent data, that is, the peer data in the form, can be accessed, usually needs to be used with the "Strict Mode" attribute.',
  '53a9db06d0b7e3482dc21e53f150e257': 'Row Style',
  'd679aea3aae1201e38c4baaaeef86efe': 'In Operation',
  'e5ac1d2029adff17ec123b86ea07ce26': 'Queuing',
  'f406ef0ea3f09572835a9b1ec61f576a': 'Scheduling',
  'acd5cb847a4aff235c9a01ddeb6f9770': 'fail',
  '********************************':
    'The following states are supported by default and can be used without configuration. The custom state is merged with the default state.',
  '33563f1d3d203bc682069a8c85506b86': 'Default Icon Value',
  '2cc90d2f2cd9ba213f9aace88c386f3f': 'Default Label',
  'aad245582dc9f55cf71e3934bb3b1709': 'Default Value Value',
  '2634cea5a95ece79e05b5a68c38cbd4d': 'Required ',
  '2464e9d13bfc84169eb8333b6996203c':
    'Update the data value of the target component or variable',
  'c85c8d61a67014c4b5d44f25e49e87fc': 'Set Variable',
  'df24d894cd3331f53964bc75f5c192e2': '"',
  '9e1bafbb00018beacc8f579c8ddfaa36': 'Set Component',
  '6c6e12c54723170f214527bedaf81f7d': 'Action Type',
  '1b7e6b2dbf3b7f4b1baf2c42e49a995d': 'Component Variables',
  '2eb4c7ac45befad0f1f9c750bda57166': 'Apply temporary variables',
  '844a7a7aacc5be82d0fd6225edc6bf63': 'Please select a variable',
  '85451d2eb59327a23e8f745161066d4a': 'Please enter variable value',
  '3d4d83f05a12364e2522fcfb265d8ce8':
    'When it is enabled, you need to click the "Save" button on the right side of the table to add or edit component data. When not enabled, add, edit and delete operations directly change component data.',
  'c18169dd6fceab2f023216fa6f7d22c1': 'Confirm Button Icon',
  '5720057e62e80f7a04489dc4c035b4f1': 'Cancel Button Icon',
  '8985ea173dce8f9bee667b3cdf0b7bdf':
    'This configuration item only applies to the "Add" button in the table operation bar',
  '3f64a567662a24714768237a3a6d0de7': 'New button below the table',
  '9dd651411c1cb25e19249bb4ea8878c3': 'Animation Interval (ms)',
  '46bc66b19c2b589ebd24d1c583325080': 'Animation Duration (ms)',
  '9cb33a16b57ef10b79ae76a66379d66f': 'Arrows Are Always Displayed',
  '0bf60b32f9db93b87e08763b1c815469': 'Quantity',
  '98e04bf7cb91497e4225d272e3a331c8': 'Custom Arrows',
  '7076ef56f5c4f13d3c9bf87d3536352f': 'Left Arrow',
  'fce3880b7a24a47f02a16331a294b255': 'Right Arrow',
  'f4f965513462fcc9fe6fe896a9c249d8': 'Multi-picture Display',
  '522cddc343d72db3db80cf3d71f99210':
    'The API return format is incorrect. Please click the example on the right side of the interface address to view the CRUD data interface structure requirements',
  '5323ab3e5c12066101244f0577c30e22': 'Custom Container Area',
  'b34422e637c90181d3fca4485a97c712': 'fraction',
  '95e0d70d1809d5267c2419eda58e78ca': 'Grade',
  'c13998e4c837dc40b8e90828d99561df':
    'If it is blank, editing is not supported',
  '738b58219dda4a849e293c0f75d06438': 'New Button In Operation Bar',
  '306abb77f96a1048cf6e61bfe6e7bae4':
    'If it is empty, no paging will be performed',
  '136ecd1380f3fa10c1fd99b93c63fc9b': 'Open A New Page',
  '9778ba8e0cbbae9471485dfc28df8948': 'Expand Initially',
  '18eae9f567a9c425bf59147a2601ee6a': 'Menu Click',
  '12b91237057ca7d81d50cca9873c8267': 'Triggered When The Menu Is Clicked',
  '0a974408aab362095e939e5364315971': 'Menu Selected',
  '4b5f4893cf06a9d9ea5b8486bef87c26': 'Triggered When The Menu Is Selected',
  '9b317a5d43f72026b0c0699b1d596436': 'Menu Expansion',
  '9ef3c85c39e1e686f6b8c58292352ce1': 'Triggered When The Menu Is Expanded',
  'afce1df2e30a4674f507292d642fe270': 'Menu Collapse',
  '4015091668f286adf085d60a040f89a1': 'Triggered When The Menu Is Collapsed',
  '2631c12c40aa18fff4c6a2793f03f95b': 'Data Loading Completed',
  '26a92da0738fed6a6178be565cfb3a59': 'Triggered After Data Loading',
  '67d8dc76a8c8a3f0f52232969d72e8bb': 'Update Menu Items',
  '12fbc0729d69e4243c4818093e96de8c': 'Trigger Component Update Menu Item',
  'ee621e1f1429f1150a69bd02eecf6964':
    'Trigger the folding and expansion of components',
  'e0e2b0cf2a3c8379b1d9144b10b3824d': 'Horizontal Placement',
  '8dd27bbe64ec245ce449caab5cf7f12b':
    'The default is inline mode. After opening, the submenu will not expand below the parent level, but will be displayed on the side of the menu',
  '154a7ec36c43427f73705ae834967703': 'Submenu Floating Display',
  '9dcb6b749d6682f1ac51b2dcdefd3208':
    'Click the menu to expand only the current parent menu and collapse other expanded menus',
  '49a84da9b39148af54903ba4c6ed257f': 'Default Expand Level',
  'd4b70ea3a756ec4d06c8ecb2f27330ab':
    'Expand the corresponding level of all menus by default',
  '96198d14846a941a60824bd6ebda4d9e': 'Max Display Level',
  'cac013d011fe1ac71c3b9f0bd0594d65':
    'After configuration, menu items beyond this level will be hidden. For example, if the maximum display level is two, three levels and below of menu items will be hidden',
  '7419d6bab78d959d5c3b7a38f9888258': 'Menu Item',
  '1a9aaf05889002e65708c4950012a652': 'Yes, you just clicked',
  '243e91d5fbc5a03a5c54da6d8e662e4f':
    'Page renderer, the top entry of the page. It contains multiple regions. You can choose to place different renderers in different regions.',
  'a7999d1390b7dc775e4287511c395d6c': 'Corner Type',
  '31175366279c15232e27b6736ccb2fd4':
    'Only valid if the text content is numeric',
  '58f966670529f4753fb3c5a0647606be': 'size',
  'b3c1b71ed42c7f2fe55f3c64346f1ae6':
    'The corner marker style is configured here. The corner marker takes effect after the corner marker content is configured in the menu item at the same time',
  '515e8b11c3c24c6c3b03c6c76b1da496': 'Default Selected Menu',
  '4f80ea1e761598ba9fe393e7c745468d':
    'Priority is given to matching according to the URL of the current browser address bar, and the menu item you configured will be selected in case of miss',
  'e3cd6fc0612b22f7c7e209b8184378c8': 'Menu name:',
  '4cd5629f32fc9710fbb0291b51adc34b': 'Please enter a menu name',
  '9e0b4b20e7177b83916ab7b017c4162b': 'Jump address:',
  '2e507449ccaac4522f503f0ec3dfffa9': 'Please enter jump address',
  'ec3cc7a898eb5eb6f87a1d70a22a05c7': 'Whether to open a new page:',
  'a93a3970825296478d40658ad07af43a': 'Whether to fold initially:',
  'd5004744a86589d72041b0a3fa8efa7b': 'Include submenus:',
  '14794add5446201274dd148086bc0361': 'Add Menu',
  'aae5ccb98564e19c48f19c740c3c10b7': 'Default (Static)',
  '5d721446605f21bddb3b8e2ab2a3841c': 'Relative',
  'b55b525d56fd0d4d3dcb9291e59e3433': 'Fixed',
  '25ece43050dda20ad4d8dd058dd590d1': 'Absolute',
  '22b47452f52254ce07507287d137d167':
    '<div>When data mapping is enabled, the data in the pop-up box will only contain the set part. Please bind the data. For example: {"a": " ${a}", "b": 2}</ div>',
  '68419387f5bb8487a848b818d78424ae':
    '<div>When the value is__ When undefined, it means to delete the corresponding field. You can combine {"&": " $$"} to achieve the blacklist effect</ div>',
  'cb65841ea7dec5ae0af20b3f5e52abfc': 'Raw data leveling',
  '6922790f45faf064e063069816e4d2ec':
    'After opening, all the original data will be flattened and set in the data, and customized on this basis',
  '9791b05a4df9d72f1a01b81fa695fbc6':
    'When the grouping names of multiple columns are consistent, the table will display the super header on the upper layer of the display header.<a href=" https://baidu.github.io/amis/zh-CN/components/table#%E8%B6%85%E7%BA%A7%E8%A1%A8%E5%A4%B4 " target="_ Blank ">Example</a>',
  '7441768e25f67a80f0f2173c2a618c35': 'Date value',
  '8036cf5e8dbf62ee4f4e896f7354ce5c': 'Date Time Value',
  '598f69a9b640508d8e124fd7d33131f0': 'Select icon',
  'ef79da787ad206e5d5f8cf62e195c836': 'Icon Size',
  'ff58428ef8221c4c1bbf532dd3c77113': 'Icon Bottom Margin',
  '88f109195ad926bcd436f0c56198240d': 'Image Upload Button',
  'd825ba2b8ea0c1b0737b0dd5ca9bc128': 'Upload Icon',
  'bade9c4e0b8a75a251c1a2760571d3c3': 'Number Input Box Style',
  '********************************': 'Number Input Box',
  '********************************': 'Table Form Data',
  'fb7b49ff7f85f6e3f995b5eaae42d084': 'Label Selection',
  '0aefac04b467ce313ca6b05e33c6145a': 'Max Words',
  '188676cb26835b8e4d8ea568467c55cd': 'Limit the max number of text input',
  'f1ee660e12ef0b76c10cccc3d923e605':
    'AK cannot be empty, please visit http://lbsyun.baidu.com/ Obtain Key (AK)',
  '4d01bb9f50eb1480e0e9995a2a003181':
    'Please select<a href=" http://lbsyun.baidu.com/ " target="_ Blank "class=" text sm ">Baidu Maps open platform</a>Get',
  'f1f7a5f821ca0ee4caaa9324eda48d91': 'Static Form Item',
  '4602761ee85e2e6e5360cd13fe642a08':
    '<p>The current component has stopped maintenance. It is recommended that you use the new feature of<a="/amis/zh CN/components/form/formitem #% E9% 85% 8D% E7% BD% AE% E9% 9D% 99% E6% 80% 81% E5% B1% 95% E7% A4% BA" target="_blank">static display</a>to achieve static display of form items</ p>',
  '6ca92e3386f9e392584367df5054c27c':
    'Used to display an icon, you can configure different icon styles.',
  '47fd366b711a0567646854f541449f8b':
    'For example:  ${listVar}, used to associate existing data in the scope',
  '98204720c30a843a9234bdf22dc05d51': 'Elastic Layout Settings',
  '3212f1de74130815d12d982437fc6919': 'Document Preview',
  'a7199769ae58f8a5c35ac0e5f8804abf': 'Office Document Preview',
  'e414473c886072e393710563f201d7f3': 'Printing',
  '6a62d33c838524f0609a624aa59ee9e7': 'Print A Document',
  'f26ef914245883c80f181c4aade2ed04': 'download',
  'fc856cd721f5b5955f8c4be2767a1cee': 'Download Documents',
  '270c7dfc38ec1f8eb7b211481d26659a': 'Address',
  '2bd4fa4fe6637a09add46354f52ea9dc': 'Whether To Render',
  '010787d733c97c5f7a8f9bda438af9e2': 'Word Rendering Configuration',
  '9a50cbc2f0c7115043a19c3b1db5776b': 'Ignore Width',
  '927ed823f52a6d3bbceed4436636a7dd': 'Page Inner Margin',
  '755ea661684e7bffe9f97fb07b8d4921': 'List Using Font',
  '9b87dc5a019f749722a1d3a9c854a2b9': 'Variable Replacement',
  'c6adeba660df8e19ac6cd3b8c57416ad': 'Forced Row Height',
  'b93ea0046d63e2df7cf24a7a63bf5c99': 'Font Mapping',
  'f6e9b56f97af64235bf81f4ddc2288ab': 'Whether To Enable Pagination Rendering',
  '4c71a09da7ce050d45514b68bb15b4ab': 'Page Top And Bottom Margins',
  '3651c159e19c05a3bdaa7036dac91e4e': 'Page Background Color',
  '91bc444339545a7785f3aa3055d9ba05': 'Show Page Shadows',
  'c856051e8c80913ff6607dc880341a20': 'Whether to display page packages',
  '6a6772a0eae27591ed8763b6e616e988': 'Page Wrap Width',
  '22bca073daae505d7fc9e7d7c8ee047c': 'Page Wrap Background Color',
  '2839785a190e062058635add192f961d': 'Zoom Ratio',
  '9723e66141840db4dc6bd1db9b165302': 'Adaptive Width',
  'b7dd79307fb7bcc921aa1b94ef904fe9': 'Behavior Button',
  'c3e817974de836e80c95cc6ce5718eff': 'Search Box',
  '********************************':
    'Used to display a simple search box, usually used in conjunction with other components. For example, after configuring initApi on the page, it can be used to achieve simple data filtering and lookup, and name keywords will be passed as parameters to initApi on the page.',
  'bb50b53491c2c43845c58b16e48c27f3': 'Search Box Content',
  '********************************': 'Click Search',
  'a7be3c702997f49cf9429240fbbc5e36':
    'Triggered when clicking on the search icon',
  '1c113a8c88ba15fc1ff04ea410e63f33': 'Search Content',
  '0796ba76b4b553687e5ffaeb78512ccb': 'Basics',
  '218bcea849478df7335ac31072860e8e': 'Search After Clear',
  'a48b511d5da79faf6f7f58c5470738f0': 'Search Now',
  '71c198baa12405e56705a3c68f66e3ef': 'Mini Version',
  'b814fb5782f733a22ee561397ad376fc': 'Enhanced Style',
  '87d88a457161f2a09f95f6aa29b38051': 'Step Bar',
  '2a9a1329b191c2787b1a70c289e3bbe0': 'vertical',
  'd517acb68fbed2331b57d1a11ca21dcc': 'simple',
  '341fe804cc8b65dc17a31c7a25a90444': 'Label Name',
  'd04f139ee0fb6fac19ccaec0f7b323df': 'Click To Close',
  'fe7967a547915be9ae4083ed50c3b94a': 'Triggered When Clicked To Close',
  'b624985146c759cfeb1be80325eccd65': 'Text Prompt',
  '6d8e3115be41a8a5690d6fefa637dac7':
    'The data returned by the interface needs to conform to the following format, with status, msg, and data as necessary fields',
  '09212e946a4d9b0f775700c46ef0dcd5':
    'Returning 0 indicates that the current interface is returning correctly, otherwise it will be processed as an error request',
  '7391774e57425e5d8e83de64100b5f2e':
    'Returns interface processing information, mainly used for displaying the toast when a form submission or request fails',
  'fa385c23820ee9999c82035086baa772':
    'Must return an object with a key value structure',
  '6494bc042d99f2f5de34a858b8a699c6':
    'Verification interface return format field description:',
  'e6246c03148f553e5d6a66adbdabb9f8':
    'Returning 0 indicates successful verification, 422 indicates failed verification',
  'b8079b9d1e6d3e8a457787910a75cce4':
    'When the return status is 422, the displayed verification failure message',
  'a59c65bea7d5065f19eb9c918a716c33':
    'The modified API object must be returned.',
  '05fb5edb84e41c19b0f5429fff20b834': 'Return Adapter',
  '98498eb59e87ec9a0eaf98ac55628da9':
    "The current request's response payload, which is response.data",
  'ed33c46d1d69336bb011813e8352fa01': 'Horizontal Direction',
  '963e9ff4252cdef288063c41eb6d4998': 'Vertical Direction',
  '4117e80d2c2e52f795ec64160f399364': 'Vertically Centered',
  'd365d58d281508b9982f6a0370649ae2': 'Horizontally',
  '21af94c1abc5891b2703c9321417a1a9': 'Interval Distribution',
  '849b9b944a65eb0685f3e6af60a0c523': 'Horizontal Spreading',
  '50334fc77fc5a2c2636f14f158d3c417': 'Context Variables',
  '7f05bea37729325a6cc84eb26bb9f8c8': 'Please enter the subscript text',
  'bc3f5a690d8c3a47d27ef8a1b127bafc':
    'Hidden options cannot be set as default values',
  '240a19929878c26f5e4c41c08f63cd1c': 'Interface Verification',
  'c6f30c2f084ddeacb7944235348bdaa4': 'Memory Variables',
  'fa6b01f51cc2b8e16bfbb914b6c08ace': 'Confirmation Dialog Box',
  '********************************': 'Drawer Title',
  'fd2bed5e9a84273b22f79950a0a1807f': 'Suspended Style',
  '1daee167a43c72dbe9e31e955b670b4f': 'Click On Style',
  'd117954f3769008ef22b864060913c65': 'Disabled Style',
  'd37c4140bd531fc117e91e727d7e576c': 'Normal State Style',
  '0371c0e05806c01e5ce2f26e9e2e39c8': 'Edit Style Source Code',
  'bb7654f7c2768614e95a0da7e94f4045': 'Expected Colon',
  'af26fdb215bad5b2296529802b129c12': 'Expected Attribute',
  '3955b2e2e06144010a1142d3624b17b1': 'Missing semicolon',
  '052f7eec7ca35a6b4d72d169ee1de494': 'Please enter an expression',
  '10710f1c01d960a3ffde384115296026': 'Block Level',
  '26b10072a4e0c8c9a3a1142db3d7b3b4': 'Inline Block',
  '2c7fe494c99c94ba5965f963fd7d3a4c': 'Inline Element',
  '39353c2b258e4bc73d8dd6a46f0a7955': 'Flexible Layout (Flex)',
  '553333a72dec41b54e8ed18d49453a76': 'Select The First Item By Default',
  '86bf16f0cb7fd8b1fef2e1439e06b632': 'Style Source Code',
  '7893f221dae53be8e3bfe72d2eb8a240': 'Icon Size',
  'f36616e35765ac3d5c9c7646e50a305d':
    'Initialize Data Interface Request Completed',
  '48c43999cf3908d9f5100a6d5f0b4404':
    'Triggered when the remote initialization data interface request is completed',
  'aa6070a7f454f554fc1c7d8b1d2d935f': 'Response Data',
  '23d861e4b6635508eb877a50a9333a9b': 'Response status (0 indicates success)',
  'a303669d47440ed05558efbf3d5d0592': 'Response Message',
  '31769d6063c5ab0cfee842b395916fd4': 'Total Number Of Rows',
  '092798b39c21e4f73dea5107fef3a61c': 'Response Status',
  'a556b5535b6b0e5925d59bbc54b58946':
    'Initialize Schema Interface Request Completed',
  '2cfbb19c3e801c5f4a11dafa1ec7884d':
    'Triggered when the remote initialization of the Schema interface request is completed',
  '7bb4a8abc423086dbd1edeed4cd3f718': 'Current row data',
  'f74fd69ce55e3f96fe9a032c0da42330': 'Current row index',
  '5db079b140feffa84c84d1b51d824dea': 'Mouse in row event',
  '90a5608bcd85a3cfcfbb5263a229915c':
    'Triggered when moving into the entire row',
  'e8bf039de5dc6751c4045f55930e0c83': 'Mouse movement travel event',
  'f1ca0d305f3251a81f361a2dc8e11fff':
    'Triggered when moving out the entire row',
  '41c5c859a80b635c23b3b4d1d8b44efb': 'Selected Row',
  'f87912f19be48f36e4e261e585764d6e': 'Unselected Row',
  'f97f8b90f09fb7df39c5c9ae3554b4c7': 'Update List Records',
  '0449dab523020003eee89a0751e1c003': 'Content Area Adaptive Height',
  'e406488d1b9545168eb1896e35139bf2': 'Confirm button copy',
  'd919d62d7e5ec5cb63ad47e5dc347a7e': 'Cancel button copy',
  'b12df8decf62700970e08b9b2ebce1f8': 'Confirm Button Style',
  '7f29931020c2d8ce97b8043a933db151': 'Cancel Button Style',
  '1b43b43e692a5b6b2f6ec1417791a550': 'Remote request polling ended',
  '0de773dedbe5875ca4529e7d0ce9d2bf':
    'Triggered after asyncApi remote request polling ends',
  '9bf078fb5589f2c09031f1f2b5a8d2b1':
    'After setting this property, after the form is submitted and sent to the save interface, it will continue to poll for requests to that interface until the finished property is returned as true',
  'f63ba7e3c9d8de0b8b4f769055a36d2e':
    'After setting this property, after the form requests initApi, it will continue to poll for requests to the interface until the finished property is returned as true',
  '58477471b3bb8ad53fe9ab18a244868b': 'add rows',
  '6070fdf451b08e4302a7dab560771222':
    'Triggered when clicking the add button in the bottom left corner or the add button in the right action bar of a certain line',
  'c9f63503ee66082fc00c3e6eac57a95f': 'List Record',
  '7b25d5ff93da66fe95b5409731bb9838': 'Add Row Record Index',
  '5e3640e4bc8efbe00a7b7c6ba169560c': 'Confirm Add',
  '008ae858112dcc7739985045e4326f3a':
    "Activate the 'confirm mode', click the add button, fill in the data, and click the 'save' button to trigger",
  '1fb1cbe49c3e72c2b5f4e0151a4cb5d9': 'Add Line Record',
  '3fdaeadf0e8a3154ae62784f04138c28': 'Successfully added',
  'bd4ef227fe8aae88ae6aa97236c0e9bb':
    'Trigger when "Confirm Mode" is enabled and "Add Interface" is configured, and "Save" is clicked to successfully add it',
  '6452a05591d7402a8bcd9fadc950c449': 'Add failed',
  'bd7c39d10dbf6985d2be2d9a4fdcccf7':
    'Turn on "Confirm Mode" and configure "New Interface". Trigger when calling interface fails after clicking "Save"',
  '37122b9e4499da38258b3d51b09d68a1':
    'Error message returned by the interface after request failure',
  '88ebffeee507da0f0d039eb2d05f7e2c': 'Edit Line',
  'daf24d1f83e1b4fce2f57a6b041ddba6':
    'Triggered when clicking the "Edit" button on the right side of a row',
  'a9e009a9936bf5bcc9d4eabcb3e952e0': 'Record in row',
  'e756ec22a0f897c4dd886c5f4a95b7a6': 'Record index of the row',
  '4271f29faca65d7840ad6bb2c4a7b8c6': 'Confirm Editing',
  '562f54a2fec7a68f85ff192a68cf69f2':
    "Activate the 'Confirm Mode', click the 'Edit' button, fill in the data, and click the 'Save' button to trigger",
  '3bb47b67994cb374e601fab35f63bc8e': 'Successfully edited',
  'b394745a8a77179da0000293638e8a56':
    'Triggered when "confirm mode" is enabled and "editing interface" is configured, and "save" is clicked to successfully edit',
  '9304e8f4c324b5882b550caa971b64b8': 'Editing failed',
  'b9d277c3ffab7d4b955ad10308c7ae0a':
    'Turn on "confirmation mode" and configure "editing interface". Trigger when calling interface fails after clicking "Save"',
  '650bccdd6f99fe5fc4ca6cb8788e7cb4':
    'Error message returned after requesting an error',
  '947fa341a6d676d7f25bae6bef8342cd': 'Delete Row',
  '7508f6d66d920323d87a9f9d58487a40':
    'Triggered when the "Delete" button is clicked on the right side of a row',
  '0007d170de017dafc266aa03926d7f00': 'Successfully deleted',
  '13640e78822f62b7b71bfabb4604025e':
    "Configured 'Delete Interface', triggered when the interface is successfully called",
  'acf0664a54dc58d9d0377bb56e162092': 'Delete failed',
  '14d36ca583bcbfc5516a000eb06ccedd':
    "Configured 'delete interface', triggered when interface call fails",
  '692dc28abc42931daca8fa12d69d5c99': 'Triggered when table data changes',
  'f8692d409bb7f122a374872e01efd03a': 'Add Row Data',
  '459fd144ee129b501545d19c169269e9': 'Insert Position',
  '196e71fa869b8410088b4ceb54aa7988':
    'Please enter the line number. If it is blank, insert it in the header',
  'a26858cccbc451fd53515416a5968550': 'Add A New Line',
  'c520ed9911d349c7974116d3d1e1423e': 'Add Fields',
  '********************************': 'Delete A Row Of Data',
  '8910acd418e45a30f01e7e531b90b61b': 'Delete Method',
  'ebf62450c3fb11c0b7e06da2d7535d6c': 'Specify Line Number',
  '4bf7636a84714fac140e42b4cee242d4': 'Conditional Expression',
  '7708fb1394ce722ee73326437a66c77a': 'Delete Scope',
  '015f108b96c70bba6511091e159db0ac':
    'Please enter the line number, and separate multiple entries with English commas',
  '63aeb2082d7cc0a316fc1e44913d7749': 'Delete Condition',
  '4622a4ce221f9b79aa3396cc461adc75': 'Clear Component Data',
  '800dfdd90200bd47bb4bb83def4fea56': 'Today',
  'a6a93b404bc039cded728683af5d625d': 'Shortcut Key Name',
  '快去添加事件，让你的产品动起来吧':
    'Quickly add events and get your product moving',
  '50f198f07fc820a4911d1c97a0ceb8c2': 'Context',
  '6142a89066ca7dd6a1ce9493462c5aca': 'Selected row records',
  '21bd0846bd8aa2296c597a1c1ff8e1a7': 'No row records selected',
  '8f98291c9fa89c0bfce463c0a2eaf97c': 'Column Name',
  'c35c1a13309c6f9da9837857517e65fc': 'Sort Values',
  'e125986c2ba6783c4297ffe5405cc8bc': 'Filter Values',
  'caafbcb52c70ad0bbbbf127ee7b08b89': 'Search Values',
  '64ef585f778c9d1b010e86b032398ab6': 'Sorted Records',
  'db9df54392e408520ca12c6a56113b5a': 'Current displayed column configuration',
  'bf2a4fd8ecd654982e2d466f10f54d3f': 'Current Row Record',
  '85f1708454f409855d552f702ac27b19':
    'The current data field can be used to read the corresponding value through the field name',
  'e48d65cda774019d9a6677354bc781f2': 'Selected value',
  '10d23d1f68ee1facb03b1f86678aa2ba': 'Status Value',
  'ed85c3f659acc9e89bcf6f0dbaa02a62': 'Current Code Content',
  'e26f6832d586f9e73d2361573bf5273f': 'The value of the combination item',
  '91190195405845950230616929d852cf': 'Deleted index',
  '1b5cf3e354142cc1cdd6f56b6afaba49':
    'The current form data can be read from the corresponding value through the field name',
  'b06216eac0df52f6072a8adb095f72b7': 'Current City',
  'a610ef4a4fbe8f0c8b756162acfb6186': 'current date',
  '05606badb4b14ffd3c38c278fb0f3c9f': 'Current time range',
  'de3ad0cd57153f799f7538dd1e4fd441': 'Excel parsed data',
  '6d829f061ed82a688f2669c54dd83301': 'current value',
  'bc0689a4c353e9c95c5b7fc5aa49b59f': 'Current slider value',
  '9b0c6dee9b5f48734c37901d4a430b71': 'Current score',
  'd84017fa76584f7475e26f79767df28d': 'Add Index',
  '9d776ddd9dd2d8d85ea225df9c27e929': 'Current label value',
  '4ed30a5be1b6680e6cc9fec0965d0f4f': 'Selected labels',
  'e01315f74dee36831d93a117cbc47c8f': 'Label List',
  'd5c135b5a4aed5dc39ef846a6f502d4f': 'Current text content',
  '2eda8e3f67e2c6e02e63d27978530ec2': 'Changing node values',
  'b4e54cb84d448952a4aa1a17ceaa6ad3': 'Selected row records',
  'b2a18e08b0b0e0fd7e80554b89244aa0': 'Option List',
  'c46f27dcf45a345993f1cbb63380fa98': 'Activated Index',
  'f7daf85b4501d9d2aa048f85618b3f1f': 'Current text content',
  'e5369f1a5e8d2b3e64eeb627e69c4e9b': 'Selected node values',
  '28387ec7d7fd160541e7901d9f0a900d': 'The currently selected value',
  '94935dfa6c9b908515a593956ee7d07c': 'Selected row records',
  'f10b94a4ac77878be53fad599a761928': 'No row records selected',
  '6c200daeb748ecce2c730d01837d3508': 'Initial Tab',
  'bd749c7a75af1236325d8d669e9bc5fc':
    'The tab activated during component initialization has higher priority than the activated tab and cannot respond to contextual data. When configuring the hash for the tab, use hash, otherwise use index values, and support obtaining variables, such as<code>tab  ${id}</code>,<code> ${id}</code>',
  '8b4de52c23ad472b9ece9e30d8750c48': 'Initial default activated tab',
  '7806807651c37e4467f9d2fc1c18eb2a': 'Active Tabs',
  '8a59e0a5705fea1751d77a97b7bf5d8d':
    'By default, a certain tab is displayed, which can respond to contextual data. When configuring a hash for a tab, use hash. Otherwise, use index values, and support obtaining variables, such as<code>tab  ${id}</code>,<code> ${id}</code>',
  '27e0d57c4412bcb89e6aaeeb1e5935fe': 'Default activated tab',
  '8d1903162d2a50d6321819c3fcc1f2f6': '{{@1}} ({{@2}} action input)',
  'd5fb02425d3b8586d8d7b98971d63e68': 'Event Action',
  '5e3406cb54f255dc1be5edbaa6f87389': 'Arrange By',
  '60e237a1b5e9a4cc3633898d527d5a38': 'Label Width',
  '813a5158d9f7171d20e7df340c5b48f9': 'Component Context',
  '8b3fd9147a07d27ad95c0ba2594fb67a': 'Current data field {{@1}}',
  '0383d6f467ed0dd89860a7b8cc793ce9': 'Upper {{@1}} layer {{@2}}',
  '45f3a444853a11667f97e941b53266b0': 'API Interface',
  '419b0a74c438b81124992616dd0a36f3':
    'API for batch saving after quick editing',
  '152e8c553fe6fbc51df7c72a45917107': 'API used for instant saving',
  '91aa2166ee4811414381c8d94e6567e6':
    'The API return format is incorrect. Please check the interface response format requirements',
  'c00a96da01a3cf0445df3b0d05918317': 'Viewing Data',
  '7695a3b5bfbcfdb3e7b085f8cd6455be': 'Edit Data',
  '6f2f7e3794aa8502d02cb20aba881df1': 'Batch Edit Data',
  '40f8f022c51542d282edda5b9ed4b512': 'Delete data',
  'c1c29e445748fa076c5fee45274bdd36': 'Batch Delete Data',
  'ebf98d77b1a002935ad5c41446f257b3': 'Confirm to delete data',
  'd00d3377afe33a0f1b63293f3a3e3a79': 'Confirm to batch delete data',
  'a094e5b7699ea4b61094cc4120170423': 'Data Sources ',
  '6f99b6eed37795cb97d5f6370c32113b': 'Basic Styles',
  'c8b9dbab4a3e3ad283d3d95a1663cd68': 'Current row record: {{@1}}',
  '83c9828692e1bb250a069bbf37807190': 'Table 2.0',
  '63ddcc28ac20f6cbd4197671ae7e628c': 'Start automatic refresh',
  'd6ba60b5bbf5df4cc2959dc897c2f792': 'Stop automatic refresh',
  '16ea2200bfba281fdf5e6870498790cc':
    'The value of [amis editor] [CRUD2Plugin] dynamicControls must be an object',
  'd45a439fa6e82798bc1e98d738cedea3': 'Enable selection of table row data',
  'e821ce185e41eac2ab846ef5cfde2363': 'Paging Settings',
  '4d7080ff1405a1f08c5415a0f942c336': 'Paging Mode ',
  '290026b0b40b637e774c6af435b897b5': 'Refresh On Filter',
  '67584675004a48be903e9f61c733cb35':
    'When starting front-end pagination, do you want to request API initialization again after filtering the header',
  '8cc3589c442c478dde8ceb60aeb29e03': 'Return To Top After Page Flip',
  '9cb14f1355b3a312ebd62ebff5e1e06b':
    'When infinite loading, set the number of loads per page based on this option, leaving blank means there is no limit',
  'f300691f823aacea572e63bb7fb7ce8a': 'Click To Edit The Pagination Component',
  'd81ad681a447abae7cba38779ffc0c9e': 'Paging component not found',
  '86b17bba54b65c6a62895357095f63a3': 'Interface Polling',
  '983a8ab6a97da0a0dec7b5c751cf02b1':
    'Enable initialization interface polling, which will poll and call the interface according to the set time interval',
  '4cbec5cb54ba399ea81381a75f5f1f93': 'Polling Interval',
  '3a5d9512f474ff7c2a017a13e7f8a9af': 'Stop Condition',
  'fd649ca959662306b734f03438869bf0':
    'The timed refresh stops the expression. If the conditions are met, the timed refresh will be stopped. Otherwise, the initialization interface will be continuously polled and called.',
  '0516133b87f03f859e23bf014d71ab57': 'Stop during modal window',
  '52dce697d35795d7835e483d69f72419':
    'Stop interface polling when there are pop-up windows on the page to avoid interrupting operations',
  '6d5b1e5a235fa839c759d2362654d638': 'Silent Pull',
  '8b34b22a4da1b9b855a5efd33434f5e7': 'Hide loading animation when refreshing',
  '32aaf2f04e983290deceb0674fb0d159': 'Table Area',
  'e3e100dab1d8d13a2e3f9c391b0b108d': 'Top Toolbar ',
  '2aee96dd788b815f83b3d118188e7fd9': 'Bottom Toolbars ',
  'cc6a173a3601403f2d96cae2180539b3':
    'Used to add, delete, modify, and query data, display table data, configure column information, and then associate the data to complete the display. Supports nesting, super headers, fixed columns, fixed headers, merged cells, and more.',
  'b58da2d7e87937280042e1deca1153fa': 'Pipeline task instance',
  '93b824b57d4c22085c1035f7254619db': 'unit testing ',
  '2a8249ce9e52240855dfabc0efc83a3a': 'Pass Rate',
  '09eb6d258fd00deaf350c2d1a0fd6f46': 'Task Instance',
  'a5cd4ea1820d5c17c35e86885b1ef10d': 'report',
  '3791ba5c2962a42a251489872342b7d6': 'Component Name',
  'fcd70206ed8109b7ed361c7bce0922c4': 'Number Of Rows',
  '3ccfcb4c0daac7bb3ef1399b4389e3e5': 'Left-Right Spacing',
  'ab57255c391c3732b4b38ae78f55e058': 'Top-Bottom Distance',
  '390d9ad686ba5622d49443d1e3659d51': 'Current List Item',
  'b271e427962758c71e342a2c06df493a': 'index',
  'd63b707be8a6feb914a3f6899c38770f': 'Left Switch Icon',
  '911f1640e858c362bfb3ba9a55e8269a': 'Right Switch Icon',
  '8a0b967b90e89dd8e7311065c1de720a': 'Switch Icon Size',
  'eabda74dd2ac5b4e6eab9229e0e63f79':
    'The interface can return the complete configuration of the Echart chart or chart data. It is recommended to map the returned chart data to the Echarts configuration',
  '59688d1a484179aef8edc0ccbabb9fc1': 'Trace Expression',
  '3fd81a573ea309b203ab019c1aa95a2c':
    'If the value of this expression changes, the chart will be updated, which is useful when data mapping is used in config',
  'd0d852432ce09f627e6ae471b3f86b0a': 'Data Filter',
  '06e89ed3cb501da6d57e35bc28a37089':
    'If the backend does not directly return Echart configuration, you can write a function to wrap it yourself',
  'f25c9fd2852b85502157decbedd19082': 'raw data',
  '2695f318db820c2bfe4c886fed3697f0': 'Echarts Object',
  'b76e25de5689ea51af0688d0ea6d7b39':
    'If a data interface is configured, the data returned by the interface is passed in through this variable',
  '45bc998df29cf75725df6141098d5cec':
    'Debugger// Can debug browser breakpoints  n  n//View raw data  nconsole. log (config)  n  n//Return new results  nreturn {}',
  'e8f96ea47cb322f4f1a4c938c7b155ac':
    'The default is append mode, and the new configuration will be merged with the old configuration. If checked, it will be directly overwritten completely',
  'e732f4c0d8075670b6c75cc52bd1f7d5': 'Chart Drill Down',
  '00958a92c5804d56047c18fc206382e1': 'Width And Height Settings',
  'b3e991d11b9f0ee04f55627c62d4fcba':
    'The default height is 300px, and the value unit is px. It also supports units such as percentage, such as 100%',
  '8e5ff4f20463c03f7935e0627888c03c': 'Folding State Change',
  'bcfcd9d31a7a469fa1f3a2ea9e3e3f89':
    'Triggered when the folding state of the folding device changes',
  '0e8638286319f6efb0afe2616714e8c3': 'Folder Status',
  'dce3879aaf11920ab97c94781ddaaed5': 'Folder Unfolding',
  '0f680e944e33feb4719bd0dfe618aa50':
    'Triggered when the folding device status changes to unfolded',
  '0469f19533c2fa1d63418b86ec203ab7': 'Folder Retracted',
  '3ffa0c7285daa4c39f7b7699a845860c':
    'Triggered when the folding device status changes to folded',
  '578125c5d7586aefb797caca9111ed1f': 'Component Deployment',
  'ae772db91ee1bd385ffafed3e9b8f4cc':
    'Component folding state changed to expanded',
  '5882cb6b5133d35488c386965321c60b': 'Component Retraction',
  '04e36bd57e0e1623da86ee2c19c2a885':
    'Component folding status changed to folded',
  'd2fa917958506736ea39edbef5e1cea5':
    'Triggered when the folding status of the folding panel changes',
  '58164864ad00f5d134f304d309055eda': 'Current Expanded Index List',
  '44732aa2566399b71483e63252d3dbc7': 'Folder Index',
  'abd55237a0df8da6ffda1ef377982707': 'Column Default Display',
  '67a2d9746956b631dd3ae9d13b6ae9ff': 'Click On The Mask To Close',
  '36f5b682310bd52f19c63b077ec054d1': 'Hide Button Area',
  '6bdc97671296112658e3a1cd369c0686': 'Mask Color',
  'b3b287010ea79586507a77e0580d9cad': 'Bottom Zone',
  '38cf16f2204ffab8a6e0187070558721': 'Confirm',
  '40d39c3bc7bebced7d63eace0191a0a8': 'angle',
  '5d606821df2528b127c07333f5f403cd': 'Top 1 number of follow-up visits',
  '796011a5b11f78292d93a73ff284de50': 'Beijing Branch',
  '7232042d4e1b0c8c7172d68048358619': 'Max Number Of Displays',
  '64095ae27232995731f776f12bf66d8d': 'Current Loop Item',
  '23b3169341314eda02d860be90ebf487': 'Select All Text',
  'e2b67a7f51c977887d2b3c2de2727509': 'Operation Area',
  '4f2ef285e13e20f551f7111f535cde11': 'Form Creation Wizard',
  '7efcb0ce09e8842951c5cfd298b4e7ee': 'Usage Scenario',
  'b4f035fa6f9faaa78707b72c352c6a8b':
    'The value of [amis editor] [FormPlugin] dynamicControls must be an object',
  'cd8b367599d0c748e74029da8c10d295': 'Local Cache',
  '09a94b2400f7f05fbf2fc37a20b097d0': 'Clear cache after successful submission',
  '69445b3c5ddb606c7d51fd941e4aae10': 'Auto-fill Matching Fields',
  '86f056f1cd4e25d5bd2bebddc971032b':
    'The default form can obtain data from the complete data chain. If you want the data field of the form to be independent, please turn off this configuration',
  '5cd08fe4cd86fffe7cd23d934d4d3b32': 'Submit Settings',
  'e59e1cb3f366d6e7012fe64748355b0a':
    'If the bottom button is not a custom button, you can quickly modify the button name through this configuration. If it is set to blank, you can remove the default button.',
  'd3458f4ee2c2203bb51f9d138dfd0c05': 'Reset Form After Submission',
  'e89bb79f0a0cb4fa4693c4a5ace130f4':
    'After submitting the form, restore the values of all form items to their original values',
  '59c1b37b7ff983385a2521541cbcccf8': 'Block Carriage Return Submission',
  '794020840d754ea7fb58ea3bf4394e1a':
    'By default, pressing the Enter key triggers form submission, which will prevent this behavior when enabled',
  '87a6f142d80987db2e31569e403619f7': 'Close The Dialog Box After Submission',
  '********************************': 'Combination Verification',
  '585293753528cd2f74501f41fc2e2c30':
    'Display the data of the current form at the top of the form',
  '86ed196f63524be7775e5a01b0152793': 'Label Alignment',
  '6bde56d911167d0f47aae6bab762cd70': 'Panel Package',
  '019078f52f120a70fc23d94ccd364200': 'Adsorption Operation Column',
  'f9e5a5377c1f56f1d09ae2bc58bd2d5b':
    'After opening, make the bottom operation area suspended and adsorbed when scrolling the form content area',
  'cc6c35a3e0f97fb9747905dc13e9b625': 'Coding',
  '936b62c222fcf6ba10fde069212fcf97': 'Province Code',
  'd7009d07f04d208f84e6c343fbc80ab7': 'province',
  'e982852e24060d47f29be600837b675a': 'City Code',
  '0dad46b34f0ddc802bce0351dc4745eb': 'Region Code',
  'd3ce40d862f1c7e4748e6c28ffb0a007': 'region',
  '716c3dc1bd990ccebc4fee34eb9171cf': 'street',
  'fd3913adcb81427f1895ab5b79ebe0b2':
    'Response data returned after successful remote upload request',
  'cfe5917c83c8c31d5e57ddeb1f3460a5':
    'Please enter the line number. If it is blank, insert it at the end',
  '9a3af29bac4ff8dff3305e283acb2516': 'Root node copy',
  'f273cc663180d6f62497ff3a28f4fdd7': 'New copywriting prompt',
  '897deae9c4c3dc0baa4a6989bb8727b6': 'Add Child Nodes',
  'aefacb210d5fa5eff897a302269ed920': 'Edit copy prompt',
  'f9886b3adb1ec253d24432615b62151c': 'Edit this node',
  'b8079d1411b21dbc48749154a827a680': 'Delete copy prompt',
  '176a53a7fc4759482d71312551d868ec': 'Remove this node',
  '7650487a8758fd50c87d6c9cff0aa5ac': 'address',
  '3d18ca01ddd1b95e982ec44ffcda8165': 'longitude',
  '6acaee71fe6a23c17f18625df01bab23': 'latitude',
  '40d58bb6ac9888b0c672f5bcff74da25': 'Map manufacturer',
  '542241c52bd2efb24dc37b32cab2329c': 'Select All Columns',
  'd94ec7663ac9ad2d09fca5c86928b434': 'Column level all selection function',
  '7020fa7949a5ae24cc8eb696772d97fc': 'Select All Rows',
  '227c24282ff52f3f6f52dfdb853cc1ad': 'Row level all selection function',
  '83a60f8b752a1ef3ce6a240388d635aa':
    'By default, when selecting all is enabled, align to the left',
  '316a639631f712780829a202258ec3cc': 'be at the left side',
  'e2aaec83377244c9d15f78f51cccfe6f': 'be at the right',
  'b7fc7efe26efb867838223936b7fc467': 'Show Left Option',
  '505e204cdd98afd08c174d6dcec0dc09': 'Selected',
  '9b1ddba55066e0f329ca3cca2e58909c':
    'The retrieval results can be obtained through the interface, and the retrieval value can be obtained through the variable  ${term}, such as:" https://xxx/search?name= \\{term}"',
  '14079611c014884bbdffce4d0e5f4d73':
    'Dynamic options can be obtained through the interface, pulling all at once',
  '1fd65acd90f99791d70ca70e046c9f05': 'Fake Data Image',
  '5789be67da4a1dc0fd9600bd626776a1':
    'Simulated images displayed only in the editing area will display the actual content of the images at runtime',
  'eded9ed93a453c3f790126e30b776a22': 'Title Text',
  '11d938eaa50cff1b7e59c64b891de73d': 'Title Margins',
  '36285b8c01571203859d6b8ce7af0cba': 'Descriptive Text',
  'a7dcee68c68f8f19f39c9788e08fac31': 'Description Margins',
  '80dc1f6307e4acf29ece7ac2f6e04334': 'Enlarge Icon',
  'faaadc447b1c2b1adc920d9c9aedcc25': 'Serial Number',
  'c4767bc3ad5c6812a8ea302e6126d988': 'March',
  '499d02b4034d1234b407042ccce614a8': 'List Title',
  'a302da1d8160ed0c72719c13346042a9':
    'This is the content introduction, and you can set the number of displayed lines',
  '4e74ff55b36a0f35d511a761c264fce3': 'Content Area Styles',
  'a67496c9ef1b1951fb9f104d1ef17997': 'Title Block Styles',
  'af608093f1d3c25e85bdb4ed17f8b947': 'Toolbar Styles',
  'fa898e5ae4c0315061129c6cef47c326': 'Sidebar Styles',
  '087e631da111edc6db508289a7b5a95b':
    'Assign different value ranges and prompt users with different colors. If only one color is configured without a value, the default value is 100',
  '88f8919dd239f2018d267f79d71a672b': 'Search box, searchbox',
  '********************************': 'Clear Input Box',
  '********************************': 'Service',
  '6f6f1e6feb9fa966acaddae627b73948': 'Interface Configuration',
  '270ac7e329c21eb9a5e4c342f202bbb4': 'Schema Data Source',
  'bfa178f875f63957378fc2d0bd8d0f59':
    'After configuring schemaApi, dynamic rendering of page content can be achieved',
  'dd22d42f40442605fbe0c5da03203ffb': 'Is the schema initially loaded',
  'eb1d029d107422d00f55c8b76e66ec75': 'WebSocket Interface',
  '470e0b39b2486883a10c1048f2df9f40':
    'Service supports obtaining data through WebSocket (ws) for real-time updates.',
  '3bfc17446f0456f9692f25fc90383ec1':
    'For complex data acquisition situations, external functions can be used to obtain data',
  '642d94fa418e15a3997bcf7488315e4d': 'Atomic Table',
  'dad6519c7c75a23bee8ccb576e4609a2': 'Data Source Key',
  'cba09997ca646e67544f880c80ab97cb': 'Draggable Conditions',
  'cbe89f133fda6e0d1bec31eaf6aa7853': 'Save Sort',
  '413c6b52c23a1bf5cbc9fba1485f88ff':
    'The value of [amis editor] [Table2Plugin] dynamicControls must be an object',
  '5b11953888d7c376458b397f222d4533': 'Bind the current context variable',
  '5e1872b4afc82fc5114b32aaf4477500': 'Select Type',
  '443f46c76ebe6ec4eb502f95c451e4b0': 'Multiple Choice',
  '9fd1b7cb41cfa3b83bdfd2a44381417f': 'Single Choice',
  '9c5c1f96ba29b9c0a8915be950e91cb3': 'Fixed Selection Column',
  '5fd9a061aa8d25137801caf78d8d0f42': 'Select column width',
  '107ce5ae5c46f0e63565c593eb09a312': 'Select All',
  'f3993a00b12133950b96199dbf08fc43': 'Invert the current page',
  '0cf1882623b2b50f0416030c980c3179': 'Clear All',
  'b5a34b813ffd5d7d776eb8ca56a1b45d': 'Select odd rows',
  '0c2a525c7e183c05b46caa6b52a21b9f': 'Select Even Rows',
  'df5a3392ee995cfaa13787b11a1e2652': 'Line Checkable Conditions',
  '76417db4eeb031b0bc15f4cf8178ae46': 'Max Number Of Selections',
  '4d250d2754fbdc9cbd131bf48d445894': 'Expand Button Position',
  'ea7026a83bf0b63ecc31fd8e215766de': 'Expandable Condition',
  '9e9b4716c08d4f15d9d52e00281f4265': 'Configure Deployment Area',
  'f09e007fa1c2e7eb9ec01f8481104d94': 'Nested Fields',
  '1b4b8d809a7d253bb650d0f104d24ea2':
    'Declare the field name as a child node in the data structure, which defaults to<code>children</code>',
  'f53160d07e516a3b0d38d61822944b03': 'Row Angle Markers',
  '8952f4e24070a79741a505dc20bad8f0': 'Highly Adaptive',
  'dd7befc8e7c124f6f4ba9bbf3de9dc53': 'Indent Size ',
  'eedd7279409d000114e27194f3c8a5ea':
    'When displaying nested structures, set the indentation value of child nodes in px',
  '4bf8b7a0b3385fcd34a8e0cd355d7964': 'Row CSS',
  'a193a0f0b38ea06f46b1d3051c433cf5': 'Popup ',
  '3c87936b95f844aa21605c75d217c8f9': 'Popup Box Content',
  '********************************': 'Configure Popup Box',
  '********************************':
    'The value of [amis editor] [TableCell2Plugin] dynamicControls must be an object',
  'f4a3780b66d65a315a762976ab5e781f': 'Operation Button',
  'a0dbb2b0a000cdb3a265d096d4e5ed8d': 'Fix Current Column',
  '00ed921de3ababcafcb0594ff0e9a997': 'Left Fixed',
  'a5b48f7807e3c1ddd80fa160f46f9cc9': 'Right Fixed',
  '5cbd7ad8a3ecf059b62219c17fa58cae': 'Label Content',
  '3b4bcc788ee92004cae962801b471b8d': 'Custom Title Display Template',
  '2328f91f1e160de6a06c2004d49ec53b': 'Configure Title Display Template',
  '11e6dfd7d3fcb0892c2cecb6d7549102': 'Please edit the title content',
  '3d95415ee75837db2660255ea1ca1b9b': 'Time Zone',
  '2fe3c5280ea644639bc6378bcecc8b27': 'Details Area',
  'e1445cd3c4710c8abf7c1b3b1ce060d8': 'Floating Layer Styles',
  '1f9ba0bdeb0cf0d3193cac269dd2f708': 'Max Rows',
  '2043742930f9833b4405aa0314a496b1': 'Fill In False Data',
  'a903e51cb1915b56f94bf54a81816cf5':
    'False data text displayed only in the editing area will display the actual content of the text at runtime',
  'ddca9c0f0e3d07c3341701b80f139cc0':
    'Whether to silently send requests and block error prompts',
  '6de215632addbb664d254e00532d92aa': 'Last n hours',
  '5bc676c0d274d9a4674f832ae07c6757':
    "The field name for sequence number '{{@1}}' cannot be empty",
  'aa70a469e4ceb0dede9d73cb8d8d953a':
    "The field name '{{@2}}' for sequence number '{{@1}}' is not unique",
  'acb3aec4d12f6ca06a1e45302030cdb1': 'Data processing',
  'cdfd25057876424324682b5bdde38a3d':
    'Please fill in the initialization interface first',
  'a9fea442707e26dee478b34a2f2ce263': 'Please fill in the interface first',
  '4cc6a76c146c0360a41ceaf5e212c891':
    'Automatically generate fields based on interfaces',
  '4484fa04e7b71db4c8293e5bcb53eca4': 'Add Field',
  'eea3ebc33e69694e0c12d4ab2e07a553': 'Field Title',
  '404f38ae7ac36860c0b3af6f2f4a13f9': 'Menu name is required',
  '822be91778b5ac22d31681f7256b849b': 'Cannot drag a menu inside itself',
  '088b54ee8f10a43977afa9d16ea5350f': 'Customize Menu',
  'cd1e63aed43df0827cc09fb26521936c': 'Edit Menu Item',
  '648c5e847b923bdd51bf5c72436169ba': 'Add Menu Item',
  '8ee9f276a6356aab65f8178c4f30fabd': 'Menu Name',
  'e3cc5bd7fc92d7287a14bf5398c4ecc3': 'Parent Menu',
  '24ceb2a06b1962b396b75286fc0960d1':
    'Please select. If not selected, it will default to the first level menu',
  '9483042d09dbad731addc1791b5d207d': 'Menu Icon',
  '6107b3c4fd8587589210cb9fe2fcdad9': 'Please enter the address',
  '76b5162d1b7a16b4b6adf1b79231c96a': 'Jump Method',
  'b2d1bffc689e4478519d8a010450192c': 'Expand Current Page',
  '80fb2db8d3f212b3dd130d24da1c970e': 'New Tab Open',
  '70b4d0676f9a9640c5a7b1d5f66faa64': 'Content Of Corner Markers',
  '9f3cdf6aa12759fab68a5a88179462c4': 'If empty, do not display corner markers',
  '07c60b53a84fd7751095864a5310cc7d': 'Self-adaption',
  '81522afdfef2e4121c9240d00583f531': 'Percentage',
  '9a8aed590bb7fcdd9198daca349b055a': 'Fixed Column Width',
  'e0dbf4a939e6c84610d28b8a9b1803c1': 'Percentage Column Width',
  '41be5ce31e28742d0b259fe734e49c28': 'Retain at least one root node',
  '49198f84cea6c212cd5d463037827cd5': 'Add Column',
  'fb4c95bae088e3f216ea9312bcdb26ca': 'Column Type',
  '8eebb8ae809cf1ceaa23cd2fa2c73898': 'Field Column',
  'b302476563937e24190496ab668ebd26': "Corresponding column '{{@1}}' not found",
  'f9e759ac21d528e7d1e84b5feae4560a': 'Empty Column',
  'bcba3b2e1f7b4fb528213e999333d331':
    "Are you sure you want to delete column '{{@1}}'?",
  '7202bba23492811d2964234485a909e5': 'Container Column',
  '4c2dc6491d29ef77f421daa19541c8ab': 'Field Loading',
  'e11e888f47bc6cd5816a1147be05e792': 'Target component not found',
  'e6413abdc2fd3ccbbf15327e4004b1b2': 'To edit the target component',
  'fcebd5cda006253365b4453596290f48': 'There are currently no fields available',
  '161f53cdfabe18451e4067f2426cca0f':
    "The corresponding action '{{@1}}' in the toolbar was not found",
  '4357955c64f836dd7f36af048d857f9b':
    "Are you sure you want to delete '{{@1}}' from the toolbar?",
  'dc858fed3c66a1194d5f9aec81faee3d':
    'There are currently no operations to add',
  'fb3304d27d85c79d89ce6cac22a174b8': 'Custom Button',
  'e40c411876b4a57e24fbc6b4b4fd5027': 'Operation generation in progress',
  '0e7bb2c7879f0a34c02f547820b0b0b3': 'Error Ignored',
  '5d9fe78268b03e8aa41ac316ef610d9a':
    'When an action error occurs, do you ignore the error and continue executing',
  'c0d5d68f5f1cc399311e92905ed2fa80': 'Ignore',
  'a165be161ac250720f6f25820dd2a5b3': 'Do Not Ignore',
  '9cdfce42ef0fa346511538131e51328f': 'Presets',
  '17817a4d2da41f4261f4155ada59e395':
    '<%=data. ignoreError==false? Cannot find component and interrupt action execution failure ": typeof data. ignoreError==" undefined "? Component tolerance not found, action execution failed before interruption ":" "%>',
  '0db9e779f5cd9ad8bd3d16d7e8a16b64': 'Pop Up Message',
  '4a502e748d1335385c2c05bf30e582e7':
    'Open the pop-up window, which supports complex interactive designs',
  'c1b3a483bf057f5afa118f96644dc8a3': 'Popup Source',
  '0c5cbc9d345936876230a0b09aece2ff': 'Select an existing pop-up on the page',
  'a781b5903a4013c147783e1047f42e08': 'New Popup',
  '53bed22bc03c0fd61fe4fb81101f712a': 'Popup Title',
  '953131d14e66fae5e3611f8b419b7ed5': 'Please enter the pop-up title',
  '7cc53692d650e049802d808b81efe7f5': 'Select Popup',
  '939402f3ff754d8d815eb7f6cc991bea': 'Popup Type',
  '139c619a4dbfc26fb61d76dc388e3e7d': 'Request Results',
  '0eb4e63db88e158600dab0e723e8a213': 'Requesting Data Again',
  'f16654604d6cb2f62469e5aa9db19871':
    'If sending data is enabled, the configuration data will be sent to the target component first, and then the data will be requested again.',
  'a0c117d927c6290bab55ae0e848a4d4b':
    "After turning on 'Send Data', the configured data will be sent to the target component, which will be merged or overwritten with the target component data field",
  'eb6cd21b9ed45ded3ecdb12f62b590e1': 'Data Processing Method',
  'ac24ffeb131a7a9d2465b3ba7b14e10c': 'Submit Results',
  '0e3517fb21e2c4066bd0ab75c51bc6fb':
    'Please enter the name of the variable that stores the submission results',
  '0e2ba6becfa8760853cfa31c9e15a94b':
    'If you need to perform multiple form submissions, you can modify this variable name to distinguish different submission results',
  '4604d5023479171cb6e901dbeccf62c0': 'error message',
  '71f6236494bfc8b023804abb0cca1639': 'Error details',
  '4e6bfc5ad98f719f9bc6b2ad3b6440ee': 'Response data for submitting requests',
  'bf0f829689370b36d01ce871324e0bb6': 'Verification Results',
  'd23157205c994bde20bb2605c193fd27':
    'Please enter the name of the variable that stores the verification results',
  'a422eb12ebbfdd3347c4deb5ec6b4b54':
    'If multiple form validations need to be performed, this variable name can be modified to distinguish different validation results',
  'aaf9656ba493b58fbab398d52efa9f7c': 'Edit Style',
  '********************************': 'End Field Name',
  '9da561f34a93e0275c56b84717ac9cf0':
    'Configured the end field name, which saves the start and end as two fields',
  '475cdfcaf614f2b69d88e1e34ba76079': 'Primary Key',
  'c28f86f11fc814ea5696af5aa9464cbe':
    'A unique identifier for each row of records, usually used in scenarios such as row selection and batch operations.',
  'fa66d1acaef7cd181f21f5fc2895becc':
    'Enabling strict mode will adopt strict equality comparison of values',
  '9e3790244299ed296601d79e0bf43a5c': 'New Button Name',
  'bbbeba31bfc391bd0741ac62ade78c5a': 'Edit Initialization Interface',
  'ba62070c3a350918d542e990d3278b07': 'Add {{@1}}',
  '06e2f88f428b7e26f7da3cd4d40ec2ed': 'Please enter a name',
  'c630aa278a251b72bebf09284095112a': 'Configure New Forms',
  '52830af276fb186ff93f16a562acb2f6': 'Card Deck',
  'f27f10e4660cba730189cc73bcbec0dc': 'Line Length',
  '35633d4bcf019258d4a5b927e8644bed': 'Title Styles',
  '3ec2bba02a859a90b023ee793a2381d5': 'Distance',
  '12ae2cd5a178cdaed37967ec4226c4d3':
    'The distance between the title and the nearest left and right borders, with a default value of 5%',
  '7eac9d196c43693b0820c7bc0ab504c7': 'Loop Renderer',
  '21157967162b6206ec88d66789bb1eb4': 'Comparison Editor',
  'e2e0f5e01ebe60cbdd5d088fe3aecda4': 'Horizontal Left',
  '7c010e13bc2da0b7654f575ce180f8f0': 'Horizontal Right',
  '6d36d88078b28530bd4a236b1033c1db': 'Form Item Collection',
  'b400e476d7cc8a1b42744661f57525f2': 'Hide Fields',
  'a0e6ecd1eba5673e16218e559f549112': 'Color Selector',
  'bb712c16299683ab1af22258740a537a':
    'Date box, input date time, date time box, input time, time box, input month, month box, input quarter, quarter box, input year, year box, year box, year box, year selection',
  '********************************':
    'Date range box, input date time range, date time range, input time range, time range, input month range, month range, input quarter range, quarter range, input year range, year range, year range',
  '912e3cf5ce8720c39e7a778e916898b5': 'Input Box Combination',
  '********************************': 'Repetition Frequency Selector',
  '224c9f857217e09f7c244093d4f08cd0': 'Changes In Input Content',
  'ce369362c3ae69c27d717d3210303e27': 'Value of Rich Text',
  'de0302f6f891653ac2d8417a46249ebf': 'Column Toggle',
  '142184926b0ae6169a9b7bdefb9b42ff':
    'Whether to display explicit and implicit controls for table columns, "automatic" means to automatically turn on when the number of columns is greater than 5',
  'a0d2d27fe320721bd5b0157fd5466b9d': 'Tag Chooser ',
  '997a27d508152023fd04b7227b531681':
    'Text box, email box, input email, URL box, input url, password box, input password, password input box',
  '********************************':
    'Tree, tree dropdown, tree dropdown box, tree select, tree selection box, tree selector',
  '********************************': 'Current Options',
  'd8bab42dd5fad3f25760e190b552fac0': 'Configure default state templates',
  '70864b156cd3a9c8d982074feb01f587': 'Configure Active Template',
  '369f7b53301a54c2240a618827ee2f83': 'Option Name',
  '70aefc6ad8dffc85d083aaa38aad9d28': 'manufacturer',
  'ab7c4bf8e1f3d2779d62d0e5e038e2de': 'List Selector',
  '828476b63e65ac62976920fc753a3071': 'Multiline text input box',
  '********************************': 'UUID Field',
  '993844afdf1cd0bd368cda41fe2d39d7': 'Horizontal Column',
  'a94180c2142df3122cffcf1900c507f9': 'Real time logs',
  '319c7a1a88910df53d6cd1b408f5894d': 'Current page number value',
  '52f3b7dd57c2cc44b645afe6d944d07c': 'Functional Container',
  '7c39d9e0d516e260fc3513eb8e5243a5': 'Status Container',
  '39282fd22802ac1d9f99cdd0ffff253e':
    'Container for component conditional rendering based on state, facilitating the design of multi-state components',
  'f449bcac0f09a349882e74800f20d724': 'Status One',
  '3b5a5cee1bd8775463be15d96eac23a1': 'Status 1 Content',
  'f7361c5ad849477766b12e970bbd71f0': 'State Two',
  '0880f36708cd0408167f318936cc6df1': 'Status 2 Content',
  'c887c83f280bd63012d32179ff5273ed': 'Status Name',
  'a30fd0a2a17e3dcf701b3334341aa39c': 'State Sonditions',
  '7829a5f84414f02562a5b17b1c301046': 'Status {{@1}}',
  'ca035ede21ad993ba8f29a2e615b61e0': 'Next State',
  'b7349a87c4d838d1a6047c2844b1744a': 'Previous Status',
  'df7f223af52c3261a3b23a152bffe26c': 'Status List',
  '8256d18231287d07406fbf019e81bb01': 'New Component Status',
  'bd863f35feb02c40cf14493b1dc1b198': 'Display Conditions',
  '93553f4588b0f0c782f58182db0490d7': 'Status Content',
  '7cb7a4480adf1c3e13378201684dbd5f': 'Table Presentation',
  '22efa960b935516b3016b4a5bd7f1160': 'Task Action Collection',
  'bab9f8592e1b8e286ee17ddf5dbea4b3': 'Display Text',
  '97d43b5b163c9299f70b76d12541828c': 'New Status',
  '01ceb3edde98440157f199c292011dfc': 'No data',
  '8191e975f70edd63fe65476dd8bc0ddc': 'Component ID',
  '7c80a8d7f7af4e8eeef653c0af82010d': 'Input Component ID',
  'c619838a5e3c9641a80fc5f1a9001a9e': 'Persistent Key',
  '78979b40f53cb6510c6bc1e1a2473fc1':
    'Use static data or variables:<code>" ${id}"</code>to specify a unique Key for the Form',
  '72f4712f23226b5e01442ab9b6ae90bd': 'Preserve Field Collection',
  'bee8b35158dc5ddd44ebbbd9f29de422':
    'If you only need to save some of the field values in the Form, please configure the set of field names that need to be saved. If left blank, all fields will be retained',
  '5755fa70717929d1cabc4f4d9778d08e': 'Please enter a field name',
  '8ea6d154a7c25f8802406719c2ec3c02':
    'Embedded: displayed in a tiled manner on the page, while the other two are displayed in pop-up or drawer form',
  '361f434f3b82973d6526efefbb538118': 'Label Storage',
  'deab2747ddf5310781cad655c4f7a50f':
    'When the number of values exceeds a certain amount, it can be stored and displayed',
  '8c019e522d7bfbf02d6600f91f3b4edf': 'Max Labels',
  '5404c803438f1027131cb6fe35037075': 'Selector Storage Unit',
  '5d16dd78f1b9194d063322d117c75162': 'CRUD Storage Unit',
  '00e44cfe38bdcdefa40ad63dec4e9bd4': 'Label Template',
  'b2f3c4387bcabafaf39a7be9c624b584': 'Label display content for selected data',
  '988d7b545c09108379a54a1e6060f563': 'Icon Color',
  '7278e97a3360f7b31768b86b8d22173d': 'Icon Hover Color',
  '9df3213a5f8211cf2626f4c5c646d71d': 'Add Data Item',
  'a6354302d04750ab5e28fac72be054b1': 'data item',
  '1a873cce402693b4c3b4bf16a4a04523': 'The Key of the attribute in Option',
  '75208f0b7fa9dd1633fa2dded76a6e8d': 'The value of the parent data item',
  'dcc957ddbe7fd921ea03d008f7ddae4c':
    'Please enter the value of the parent data item valueField',
  'cd407112daa4d933aead47835bedafb2': 'Edit Data Item',
  'a3b6fcf7cfabfc83543bb00375235915': 'Value of data editing item',
  '0f2dd97ecc370524fd8ff0a9ffb880a5':
    'Please enter the value of the value Field before editing the data item',
  'e70edec390d0e602208f6951e1b5f01f': 'Delete Data Item',
  'd16814fa14c14dbeca8d7e8600852255': 'Value of data deletion item',
  '6a2e7e69d5bcf2e2ab0c3e288d7fd2ab':
    'Please enter the value for the deletion item valueField',
  '6e065eee612393b1d6a16e7696e4c911': 'Tree Component',
  '555fbc1714ad0d899939b12b95dabe09': 'Tree Component - Embedded Mode',
  '5c52d3e283d197588e29ca31bc613ffe': 'Option Search Interface',
  'dd52923c45686a6f507854e438d4880e': 'Lazy Loading Interface',
  'a386f65cf2c47acabfab13781dbdd89f': 'Tree Component - Floating Layer Mode',
  'b5d9da39175bc5bdcd2cc7dddeaa232a': 'Lazy Load Field',
  '31ff316f7b6aa169b2575a50e0342be0':
    'Is it the field name of the lazy loading node? The default is defer, and this configuration item can be used to customize the field name',
  '37a3bfd0cf20cc17c53396a1077c0852': 'Customize fields to enable lazy loading',
  '6c1f091d6ee0780a6f836d212b8afa82': 'New Form Configured',
  '9de4041a80c32ada7fb8b3f212da62d2': 'Edited Form Configured',
  '7eca7159d5a09db3a0008280faae395c': 'Search Matching Function',
  'e95903f4c15aab31ea66ecd51b76e1a3':
    'Custom search matching function, when<code>loadDataOnce</code>is enabled, will filter based on the matching results calculated by this function. It is mainly used to handle scenarios where column field types are complex or field value formats and backend returns are inconsistent<code>matchSorter</code>function is used to handle complex filtering scenarios, such as fuzzy matching. For more detailed content, it is recommended to check<a href="https://github.com/kentcdodds/match-sorter" target="_blank">match-sorter</a>.',
  '4f54012c824bfcd8f9ca9ded82051d94': 'Mock Configuration',
  '3566441483da64f6bb1dd44cfd6b0184': 'Data Mock',
  'fb677689a875ed3078e386ad88273e73':
    'After activation, when the data source is empty, Mock data will be used',
  'af192f42759b02f6fd256776b12a238f':
    'After setting, the data will be displayed according to the set quantity, which can improve the rendering speed of the design state, reduce the height of the table, and facilitate layout settings. If set to<code>-1</code>, there is no restriction',
  'a15e0eac4fe49346a744fa7943c46890': 'Calendar Schedule',
  'b13bf2bdf8a7abbd8cdf85683214be45': 'Display the calendar and schedule.',
  '0c6482892e9df6505c8de1eabca68266': 'time stamp',
  '398e51caf9e8fabbeb075833f0c1e701': 'Date Format',
  '2ca9949e2a05e63f735b713260307c45': 'Time Format',
  'e63e4cf37bf0af962b6ad66243dbe0f4': 'Date Time Format',
  '69a79a0e5083305255d191f708a41860': 'Enable Drag And Drop',
  'bc48a7871960f7a395fa3ff20774f81c':
    '{\\ n "status": 0, \\ n "msg": "", \\ n//can not be returned. If data is returned, it will be merged in. \\ n data: {} \\ n}',
  '1385df54facf9564658f05d0e9e2c3c7': 'Double Click On Row',
  '1c00d9cceec92084b116a382f2714ebb': 'Double Click On The Entire Row Event',
  '4f0a3e65cf67ca5a117a3f28745ae930': 'Display The New Button At The Bottom',
  '2424c96966b9c38c7d094a2d40f23a5f': 'Display Operation Column Add Button',
  '2e2c57b664358061f9f20aeda43b53af': 'Automatic Selection',
  '3fd78aa49302ff2634badc2ef49828f7':
    "After activation, automatically select the user's current geographic location",
  '1acee853829433de3ece301ee365bc99': 'Restriction Mode',
  'ce8b1e6f3819ea068921a4fa9ead6968':
    'After activation, only the current geographical location can be used and no other geographical locations can be selected',
  'fb58b1b692e6cc562764f6969269f802': 'Box Details Configured',
  '********************************': 'Preview',
  '9aa70276e6d364abdc0543deccea2dac': 'Preview Pictures',
  '893a9d45e4df161d0b0c57340631bd2f': 'Adjusting Image Scale',
  'e8aaab64292d8b17db25a1ab44bcd8e0':
    'Enlarge or shrink the image proportionally',
  '28a7e3af290b229c490da0ad4e9b700e': 'Adjusting the proportion',
  '7f2081cebf9bd4b10b953b4008c4b568':
    'Define the percentage size of the image to be enlarged or reduced each time, with positive values indicating enlargement and negative values indicating reduction. The default value is 50',
  'cb90ac33f6b3bf684635d4f57cb290a0': 'Amplification Limit',
  '7ab252f7aab256f1785c793049d01e13':
    'Define the maximum percentage of action to adjust image size, default to 200',
  '7d645933c48d5061c2a13dd5f8b9b4fa': 'Reduce Limits',
  '164f3e6ced78578c534477da66e0b008':
    'Define the minimum percentage of action to adjust image size, default to 50',
  '8e9f5f321ac9b39dbcf0f603190815ce':
    'Suspended container: a special layout container based on CSS Fixed implementation.',
  'ace080d5d9684a05aa5ec0ca38ec2831': 'Number of records displayed per page',
  'e52664b945c685d43d6ea81953a33a4a': 'Trigger QR Code Download',
  'a605077feb2a94f1f72627703ff48e39':
    'There is no data in the current table. Use Mock data for effect preview. Relevant configurations can be modified in the Mock configuration of the component panel',
  '014b4de0dd3108fc912547e4a1ed19fd':
    'The current table only displays 1 piece of data for effect preview. Click "Preview" at the top to view the real scene data. The relevant configuration can be modified in the Mock configuration of the component panel',
  '8bb1910d5bc3b1b32c1e037978ba9d3b':
    'Modify and submit after activation, instead of batch submission. It is necessary to configure a quick save interface for submitting data',
  'df23e8db7baa38e0d9db41384788a40c':
    'By default, the "Quick Save Single Entry" interface of the table is used. If a separate interface for immediate save configuration is provided, local configuration will be prioritized.',
  'bf4bc760ad07eccb701c8be8579005fc':
    'The number of list items cannot be less than {{@1}}',
  '0e69a56857c65181f96cd272d20455d6': 'Click to add an expression',
  'd846706b91f99cd12545a78212fa5984': 'Common actions:',
  '1889907dcbe32f9e8ac2c9df6603f12e': 'Verify Individual Form Items',
  '1a04578671f6c3bfa2d35a8cbd9d1a9e': 'Verify Form Items',
  'e4092e07c4613278048c91064f4bfdf7': 'Verify individual form item data',
  'dd39bd1bf78f1b2aec6ed8a05ced6884': 'The value of the verified form item',
  '0a2680def3df6912d7c96d95a047b680': 'Verification Successful',
  '136b339ae7f40edc05251da87dd8a233': 'Verification Failed',
  '14a265b60d169e9d9c11b9a286de199d': 'Event Burial Point',
  '5c1024a792dfc345b4d8a3b34255691b':
    'After enabling event burying, every time an event is triggered, burying data will be sent to the background',
  'b420567f7b473ae1a68d1754a70ff32b': 'Pop up to enter parameters',
  '0b6bad3bc224e893a3972a32b2a5daed': 'Add to the parameter',
  '87e2f37db14a6d201805063d0257fb84': 'Pop up parameters',
  '466cbd9ddbfbb53ad6753c74ec5f6775': 'Avatar Address',
  '5402f86a1c7ec56ed792225a5aad9fa1': 'Default Avatar',
  '5f7b62a975fcb8b8a493b66821aa18b7': 'Button Basic Style',
  '69ce077907d7e7cf54adc92bcb8d1ab0': 'Button Content Style',
  '6dd8962fbf03abd87c75e55505c38992':
    'Driving rope leads the way with strength',
  'd6c6e029578569c4d0c3828ed0b6ae52': 'trigger',
  '4793ea431592921524fc31117e9a662e': 'load',
  '365bce0cce7816191593eb7680abc0e3': 'More data',
  '9ff126c0c5cf17d836a2f475e2f10297':
    'When loading infinitely, set the loading quantity per page based on this option, and leave it blank to default to 10 items',
  'fd68baae75f0dc2efcc94e52764eb2c1': 'Reset the value to its initial value',
  '8f40db3b25528063f1b6a59602a05cd5': 'assignment',
  '0e3b706136f235f9368e41858f91ee86': 'Title area style',
  'c409adcc9f7ef3789c2f03c4da4295b5': 'Display style',
  'e7e07e58639b91dba2531381c9882f3b': 'concise',
  '74ef6bf722dc30b7a927f0c2737e9277': 'Pull down menu alignment',
  '395e591c46c65bb082408c63aeb55d87': ' Insert One ',
  '2d5aef4f2443b4694bd1dcc434636c6e': 'line',
  '9969614487f9bf68da5b5f46117356e7': 'Pop-up Method',
  '86dbfaf290d218967e17a8e526da5dd4': 'Trigger confirmation operation',
  '7513dd4382015cf5d874f1f05635f32e': 'Select Box Style',
  '********************************': 'Drop Down Box Style',
  '********************************': 'Selected',
  '9116f77b722c5e5066f7294a5eab3258': 'Option Style',
  '7d9550e4650922a116d0524199fd4ba0': 'Selected State Suspension',
  '056be0efc6da4c479842d8bd0e48ce44': 'Check Disable',
  'a1ff16babff54c3a5130d924fab66077': 'Checkbox Style',
  '********************************': 'Hide The Checkbox',
  '********************************': 'End Of Drag And Drop',
  '4a6052ca42be59c3ba9d4643c1219820':
    'Triggered when the combination item drag ends and the position changes',
  'db3b15e5f4a62aabbbeae5d97831865a': 'Index after drag and drop',
  '7d1578f87dcc598b587bbef905df1a60': 'Index before drag and drop',
  '39f0e1740b5a1ee35861ed3d95f47160': 'Dragged Items',
  'f92c83fc847f44f5bbec68093823b642':
    'The value of the combination item before dragging',
  '29075779cbee0247af40dd1faef9d8c4':
    'The value of the combined item after dragging',
  'a464ee5fc18625f4a3705371a62980fa': 'Configure unique items',
  '16bf955c22835b6acbc202bd2738b50c': 'Grid',
  '98cf14f371887d369548aa7323eee85c': 'Effective only for PC pages',
  '5f35e6afa1712206c8ca6358bb0d8974': 'form sheet ',
  '6a01ded8bc34fc12c2fd3db9d53fd31b': 'Panel style',
  'd4e08f2d68b6a5e424f0c588e74e2d79': 'Form Item Style',
  '2798cd2f320e1f47a19c98409feddc39': 'Up And Down Layout',
  'e6b03b4b253a211cae522ba2ddd25ef0': 'Form Item Margins',
  '0457e75acf23d1f08df052ce76392f10': 'Static display text',
  '2698a98498f2cbfd2981abe114d98d88': 'Operating area style',
  '82dc7569ff9b0d6fc5cff3c16ebd1b2f':
    'Support<code>hex, hex, hls, rgb, rgba</code>formats, default to<code>hex</code>format',
  '07e8860ca70f31dfdeefc423fbbe71cf': 'Show Up And Down Buttons',
  'bd13c6190081411bfc68f1d2dedfe66b': 'Basic Style of Form Items',
  '86c705e304b94409e504c0816cf705b6': 'Basic style of numeric box',
  '********************************': 'Signature',
  '0937b57f4520b147cf1ca55212073961': 'Handwritten signature panel',
  'be2525ebade48dee835e25c04f130725': 'Autograph',
  'ca15cf7f2255339b2c76472661ee1cad': 'Signature Panel',
  'febcf824278892810c15874ef8091c54': 'Signature Mode',
  '3c37b8f85d2956314157313230a65a23': 'Function Button Configuration',
  '1dfce787bf34fc9ebeaf2c45a01f1e74': 'Witnessing',
  'e6690b4a5eeb5fe0e80a9a686528b68e': 'Cancel Signature',
  '6d5a04648898247773df72222485377b': 'Cancel Signature',
  'cc3a5a4cdcf5c351dc2cf6a30fbc6c25': 'Clear Signature',
  '5bb67fb82af68a0fc68051e010cd34b3': 'Signature Button',
  '6fa55bbbd57143589f07456b6490ca37': 'Index Path',
  'c61fd58caebbe493bfae4dc8970a870d': 'Cancel sorting',
  '5d247589c2361f7eecc34ab2f05bf170':
    'Cancel the drag and drop sorting function of the table',
  '8424ef1246fde86185c86f29e76b7705': 'Max Number Of Tags',
  '990056ee43b48c3757c1a42fcfecc32b': 'Maximum number of selected tags',
  '8b76adb277cb335ecc55f4742f3ae3f8': 'To the end',
  '5c419515e88f2528de8858879fe8825b': 'Collapse hierarchy',
  '8214a59b9ded0f512ee313b1ba09f42f':
    'Search for options within the current data source',
  '16cca0782ad018a5aa116125bc74d76e': 'The options',
  '9699a50e47f6c25c62851c06c5f05191': 'key word',
  '5f8e53e94d4c96150845f8f8f04c64ee':
    'Reverse selection of child nodes and cancellation of parent nodes',
  '0f11ffe65cea25ee815350349a4af981':
    'Cancel the selection status of any child node while canceling the selection status of the parent node',
  'f95191a22264512fe771a97126358f8f': 'Node Behavior',
  '645aa00b95df2c0f133cae9b8eaeba9e': 'Operation Field Location',
  'e787d249c3861cff191cf89f7ff1271a': 'Custom Operation',
  '50e7872c5ae24a71d7eb7a525ed79606': 'Configure custom operation templates',
  '7c893acb8cd7e7ed6ba47cac83a9d1a7': 'Tree basic style',
  '3c7a5740603788647438ed460548ab1a': 'Tree toolbar style',
  'f9bad394c0cc6ce790b7da387d25da7a': 'Current node',
  '5cc731040b5d6c7d92f826f9381c9cd2': 'Node index',
  'b1785ef01e9b97956eec7a7266a3fb52': 'Node Name',
  '94ec9e18c7faa520231cccecf4f4a77a': 'Node value',
  'b86224e030e5948f96b70a4c3600b33f': 'Node status',
  'df19687fd0e738ab705bece977dd7efa': 'Radio Style',
  'ee9a0585c8cd603e6dd49cf266fc98c5': 'Select tab',
  'f66aff3d8be711b30d15560791684444': 'Multiline Text Style',
  '39c853dcd16e8f93bbc26b2c79046fdf': 'Only values containing child nodes',
  'a6fadd83bd384afc9c2435350de2602a':
    'Only effective when autoCheckChildren=true',
  '0f3c63b3680c5aff632494685ad0615c':
    'Select parent node and automatically select child nodes',
  '7cc032aca90bf4d2d5032580053fc3b5': 'Pop up window size',
  '0d7234df4bc9931b75f5cacbdae88a91': 'Shuttle Selector',
  'c47fc67905aecc2c0fe2a7b32a5a1f95': 'Shuttle Selector Component',
  'b6417c8c8429de8091117a34b4cd3ec8': 'Node Options',
  '1827f06ea993ef2a305bf10ff5e6cca6': 'adjustment',
  '507417dce637412e4f67cc9c651418d1': 'Image scale',
  'dc9c1c19fe2da23caf8c839cb305032c': 'Quick layout settings',
  '7ded21833b89a2da13b6cff3b3414ada': 'Internal alignment settings',
  'dcf6ca3fe486fd851d344fbe2b2a0327': 'Interval between rows',
  'e0913d8f0238e2dbc497b97c256e08c2':
    'When arranged vertically, the spacing between internal containers',
  '8ecd2ee16695bf2bc997286b7e5d0391': 'Column spacing',
  '3f5abfd6b6b56dcc490e328bf8d2966e':
    'When arranged horizontally, the spacing between internal containers',
  '32ac152be1911e9360047dcb0898cf70': 'to update',
  'cbfd78693bac7c74c0f0065e40984233': 'Table Header Alignment',
  '582a5fd6bee616a51533a0e2f966eb11': 'Reuse Alignment',
  'a305a0dac61dd6d2776f158e3309f6ce': 'Text Overrun',
  '91b314d626f639f47de564bb2c2deee3': 'Overflow Hiding',
  '5bf278e051a29ab3333e6026f555fb13': 'Unwrap',
  '117265698bf794951a0d5b07533ddd09': 'Allow Break Lines',
  '50993ca7f4e7717bd8d9e51bd6176c85':
    'If this option is enabled, line breaks at any letter, and long English words or long English characters, such as url links, are cut off.',
  'a9d23531ba618e11b467f5530c59dfb4': 'Sidebar Position',
  '6ffde2843ddeaf536a3451c863ac67ca': 'Basic Page Style',
  'bae2bbd65546c06101495548c58f1ef2': 'Page Content Area Style',
  'b6ceddd4553ad4f0b7af0e46cfe82441': 'Page Title Bar Style',
  '67df95d649c47f6aa3957bc1df2026b3': 'Page Toolbar Style',
  '530269d9698d01a147cc1a12c404c0ad': 'Page Sidebar Style',
  '0de9cf51289089dbcc3aa250fff0d533': 'Multiple Page Jumps',
  '5cab3e445ecabdd21be267f4b0654ee2': 'Miniature',
  '7fc0c1364e6078cdb4b2a6d545b6a411': 'Bottom area style',
  '3bb48fbf9a96b72f434d87ac767e843c': 'PDF Preview',
  '682c47a594e7ed21aac36de207e19abc': 'PDF file preview',
  '9d577fe51b38070678cad01e994931a6': 'Source',
  '5a878b8b32da24a3467d49a437d16936': 'Link',
  '7cfa34aea62c19462ee2074f58a74aa4': 'Path',
  'bcf03e212f6351c863bb8ada2014aaa5': 'Path',
  'feea9211d32cb4ed1cccbad9cc773083': 'Download QR code',
  'd09c4dd6414ba34c26fee8ce6ec1716a': 'Vertical display of icon text',
  'c252de4695f13a88d7092629eef62e8b':
    "Support template syntax, such as<%=this. id% 2? ' bg-success' : '' %>",
  '703cb4e22041256b9060d77a662ae22c': 'Header',
  '17169fd8c2ffa82c4e1d08e7267725f4': 'Row Height',
  'c4f3fbc6379d062e230e5590e913bf51': 'Tab deletion',
  '8739b49403bc65abd95f85fcdcff248e': 'Modify the current active tab item',
  '83a991d77ce351f5edbbf8c0aa878d11': 'activation',
  'ac007746080a48f195815c62f8c1570c': 'The',
  '1d56565b64ef592403ab7de9c83e49fc': 'Delete specified tab',
  '9c4b40ce474b1ff0897e392636bff81d':
    'Delete the tab item of the specified hash',
  '4798e120ee7568c397cd17b8ec4e8c25': 'Hash is',
  '7cca05cbe9c6298a42957abf29040812': 'The tab item',
  '4149ee3efdffed299e4b517a508b0469': 'Delete item',
  '29a7f9c52775d3668cecb3dcb01b1633': 'Please enter the hash value',
  'a069859a7b209ed546008c3906548592': 'Basic tab style',
  '7f434fad0e2eaf5ef1937063012864a2': 'Tab toolbar style',
  '41d064f868823c433dac65ac62755b77': 'Tab Title Style',
  '4df40591212dcc3b7a0a4b114d77ddad': 'Tab Content Area Style',
  'f8f23b5d6a0724f940650b93d2f6df91': 'Customize Title Template',
  'f4c4d3e31442c8c605be328d16b1c057': 'Configure display template',
  'ce811fde00a65108c19dad52fbfc2d78': 'Configure title display template',
  '50f34990b0f1147f0e5c7f35f620926a': 'Current step data',
  'c76c602025a0d7bacb0096bc1cec7396': 'roll',
  '5f411223ca5a01a5adf20fe735a433d0': 'return',
  '28c35aafea77fa3d92e70a0ad0260d86': 'Previous picture',
  '7875754736f0fc077a49d2a4937c22ba': 'Previous step',
  '2b9239697fc5e1d8a2b7b5858fdfadb2': 'To the next one',
  'bec7e4d621a66bff059edb31816fbf52': 'switch',
  '717b9f738e2da460071b1b5ae7cc0e06':
    "{\n url: string; // current interface address\n method: 'get' | 'post' | 'put' | 'delete';\n data? Object; // request body\n headers? Object; // request headers\n ... \n}",
  'fd8f406e39d5c4b1f1f2251156eb34ef':
    '{\n // context data\n [key: string]: any;\n}',
  'd1bfe86cb1776358c5ed50bc137a2b7a':
    '{\n data: Object; // Data returned by the interface,\n request: XMLHttpRequest;// headers? Object; // Request headers\n status: number; // Status codes 200, 404, 500... \n statusText: string; // Status message \n... \n}',
  'd3b13672e0e24d1490b2564ae7f6da4e':
    "// API response or customised processing needs to conform to the following format \nreturn {\n status: 0, // 0 means the request was successful, otherwise it will be handled as an error \n msg: 'Request was successful',\n data: {\n text: 'world',\n items: [\n {label: 'Zhang San', value: 1}\n ]\n n }\n}",
  '36d7adab7769ff6741b2860f041e56d2':
    "// Verification successful\nreturn {\n status: 0\n};\n\n// Verification failed\nreturn {\n status: 422,\n errors: 'Current user already exists'\n}",
  '0228c8f19830732b523a58a2ee0bbcfd':
    '{\n "status": 0,\n "msg": "",\n "data": {\n // ... Other Fields \n }\n}',
  'e83caa4c6ef82543603609c916cd804d':
    'You can directly input the function body of the sending adapter based on JavaScript language. Within this function body, you can process or return new content to<span style="color: # 108CEE">API</span>. Finally, you need<span style="color: # 108CEE">return</span><span style="color: # 108CEE">API</span>< The variables that can be accessed within the function body are as follows:; 1.<span style="color: # 108CEE">API</span>: The schema configuration object of the interface<br/> n  2.<span style="color: # 108CEE">API. data</span>: Request data<br/> n  3.<span style="color: # 108CEE">API. query</span>: Request query parameters<br/> n  4.<span style="color: # 108CEE">API. headers</span>: Request header<br/> n  5.<span style="color: # 108CEE">API. URL</span>: Request Address<br/>',
  '44af993b124817085dc2579a9f842d55':
    'You can directly input the function body of the return adapter based on JavaScript language. Within the function body, you can process or return new content to the<span style="color: # 108CEE">payload</span>interface. Finally, you need the final return result from the<span style="color: # 108CEE">return</span>interface< The variables that can be accessed within the function body are as follows:; 1.<span style="color: # 108CEE">payload</span>: The return result of the interface<br/> n  2.<span style="color: # 108CEE">response</span>: The response object of the interface<br/> n  3.<span style="color: # 108CEE">API</span>: The schema configuration object of the interface<br/>',
  '4cdf4ce158a0adb3468608aaa7ce5d61': 'Settlement Of Condition',
  '73148d5908d22de26cf65b00dea2ffea': 'Click To Edit Condition',
  '1943442b4ebb62ba60ec9f364a7232ec': 'Quick Variables',
  'e3c45feea8c90fce5e05f4dd4c91b5ca': 'Function calculation',
  'f9968e1893e7a2fd940a00776b6c56ca':
    'Each option is listed on a separate line, and all items with unique values are added as new options;',
  '9d4ce7f085df6c8e70ec8efc49894e4b':
    'Each line can be set with a label and value separately using spaces, for example: "Zhang San zhangsan"',
  '20fe75797aeb27230d7a46d797d3bc49': 'Custom Condition',
  'dd46fd79e0e9b5917bc9b65553189483':
    'Each option is listed on a separate line, and all items with unique values are added as new options< Each data is on a separate line, separated by a space between the time and title, for example: "Submit Application on January 1, 2024"',
  '85f3189774581e5dcd697cfced4251d3': 'Add an item',
  'a5d07592917279b17941531a3d96e8a5': 'Setting Up Table Columns',
  'adf692b7cdf447c6afa8deccd238f54b': 'Setting Table Rows',
  'ae7ca6f3dec57a73ddc145a7094adc97':
    'Configure a verification interface to remotely verify form items, using the same configuration method as a regular interface<br/> n 1 The interface returns<span class="ae Validation Control label code">{status: 0}</span>indicating that the validation has passed<br/> n 2 The interface returns<span class="ae Validation Control label code">{status: 422}</span>indicating that the validation did not pass<br/> n 3 If the verification fails, an error message needs to be displayed and the errors field needs to be returned. For example,<br/> n<span class="ae Validation Control label code">{status: 422, errors: \'Error message \'}</span> n',
  '270e6864d08bdb3537b6a275bb0bb006': 'context data error',
  '0cb74306e3873be06f679d0324db3b0d': 'programme exception',
  'e9aa3a35103faf8ef66d1c2994cefa95': 'Please select a pop-up window',
  '404371742f29489a92a49ad6990093ac': 'Parameters cannot be null',
  'afdfbc5f36063757e50d1d71636ed7ca':
    'There are mandatory parameters in the parameter that are not assigned a value',
  'bf86dc21effb6c836c6d2d64ee4d772f': 'unnamed pop-up window',
  'fb35e6699c4e260c27f9bb569353c5e7': '<current action inline popup>',
  '48f1f6b60f4b63df17e055a9084e9429': '<embedded pop-up window>',
  'c96df65b93a6a6cf30537b836958ada7': 'confirmation box',
  '********************************': 'pull-out window',
  '0f136747da0492424ae0f3a5f8536cd7': 'pop-up window content',
  '391a66bca64718f749d70ce9ecf0b6ec': 'Edit pop-up window',
  'a7a42528f0fa1819259397e64cd9cdc1':
    'After switching pop ups, the original embedded pop ups will be deleted',
  '111e2cc37bd69e6118662049de03b68f': 'Edit the selected pop-up window',
  '839353031488319956d1a8d4626e7f6c': 'parameter assignment ',
  '3f21ce6f1561647add9c21d91d44e875':
    'The parameter assignment in the pop-up window will prioritize this configuration. If the configuration is turned off or there is no configuration value, context data will be transmitted transparently.',
  '52288dd0fd7a1a96109e28742df9eb1f': 'Add parameters',
  'ba1470102e66d11d7133942faf589065': 'Waiting for pop-up window',
  'a39785220eae7064a5afce72af6b23ee':
    'The next action will only be executed after the current pop-up window opening action is completed',
  '15d5fffa6a4cacd866ce3a1134afed49': 'Response results',
  '297d92523f813a6d28a33b5826a66e17':
    'Configuration of output variable names after pop-up action ends',
  '55dcd671e5416b769d6d973e000999e6':
    'Please enter the variable name that stores the response result',
  'bb47cf4a90893b1d3255c1027d8e91e7': 'Action Description',
  '2bbe5a93eeb01ef5970fddf84c101b0e':
    '<%= this.ignoreError === false ?  Unable to find component and action execution failed both interrupted: typeof this.ignoreError === "undefined" ?  Unable to find component tolerance, action execution failed before interrupting ""%>',
  '7b7312051cd7e29209225f561ad37541': 'ActionType cannot be empty',
  'b250b2f9c038cc2dbceeb3c9edeb1293': 'Label or tag cannot be empty',
  '0fe0032cfb95f8b822bfa6a0ee9b15a8':
    'If there is an action panel with the same actionType: {{@1}}, it will overwrite the original action panel',
  '23f01311516d41e5fc87fbd7d241fce2': 'set variable',
  '637db2ac48c5b47f42dacdefa4e59533': 'Set up components',
  'c6df636df2f58d6a1840461bd6fc14f7': 'Switch to static display',
  '2c00fd772a5165fcde0f8b57160991c3': 'Open&nbsp;',
  '2a2eee02daa6fc08c038105ab35d55a7':
    '{{@1}}. Click to view pop-up configuration',
  '82708671c66054145588b0ccf33b589f': 'Global Broadcast Event',
  '8aa6c7f0ea9b90540d42439a62bdcd05': 'Trigger global broadcast event',
  '5895a6b62e90730d7069f8f492980c0f': 'Please select global event',
  '3d502eb9a597bf467517d5653e3770b2': 'Parameter Mapping',
  'e73d88fd48b62fefeddb2a0ac4bddfc5': 'Please enter parameter values',
  '8fb2a370be5ae9fce3ec64924ef38b47': 'Configure parameter values',
  '50d41f10522686a3d297ffbe076d87cd': 'Please enter the actionType',
  '32039746caad9fc07f464af73315b7bb':
    '{{@1}}. Click to anchor to this component',
  '6b1fafce7b035e7d6ef582c56fdd6560': 'Reminder: The component was not found',
  '0e0cf27195aa443a6c77ff289b3da2e6':
    'This component is in the pop-up window and cannot be directly anchored to it at the moment',
  '8e90b820067c81922e0585e130ff830f': 'All options',
  '8dc09ebe97e0de817d74acab93088c0d': 'obtain',
  'e0096ff3f908374978ad2b166b0eeac3': 'focus',
  '6eccf8477beb3a509bbfe833c3207846': '{{@1}} (Global Event Parameters)',
  'eca9acda905705b2e161257e0c4aa0de':
    'You can see which components call which actions of the current component',
  '878f48025dcd724ce8480da94c12c798': 'Viewing Call Relationships',
  'ad6d665d1a6ac68d64d630504d344646': 'You have added this global event',
  '8b30d6b1df387b743772d949b66cf7fb': 'Global Event',
  'c3ae33aa8d7ae462c6376301e0f83acf': 'Arrange {{@1}}',
  '28f7f1bb2bf5c0cc8f5319db7e063ff0': 'Custom Separation Ratio',
  'b9241e371f7fae571b1887e893eea9cb': 'For example, 1:3:2',
  'b9a3db6a1a805c8a5324de48df3b7439': 'Can close tabs',
  'f41ebe896c38e85342201dc3ec3ca3c5': 'Higher priority within the tab',
  '8230519f7c697911f9c1e92ba3b9ea8b': 'Inhibit Input',
  'fea2358bd4486d4420487174a32e58f2':
    'Relative positioning to the original position',
  '09a2682f7e9084eef44c64effadbf798': 'Fixed in the window',
  'dd02cc5a07221639a51096b005d9ce77': 'Absolute positioning',
  '8b0cabbb1334cf35f326a4830269e1a4': 'Level content exceeds',
  'a1b66f536630c4456a552a593febf1e4': 'horizontal scroll',
  '8c176b5df33ea7b8c0c68c1fc494a17e': 'Vertical content exceeds',
  '1215dd0689aa1fdf89adbb9b7530a90e': 'Vertical scrolling',
  'a6ba771dc7e0c778952b2c2c6b075fa1': 'Fade in',
  '340480f5796b020c4060efff34929807': 'Fade in from above',
  'f8519f45ebc32c93b93707c97ea80c57': 'Fade in from top (strengthening effect)',
  'd1329a02546ca79e10dde3497537c454': 'Fade in from the left',
  '3de47f181cf84d96e16ad17ebf74cb29':
    'Fade in from left (strengthening effect)',
  '2438838b29ab59e5c192f18785f85ffa': 'Fade in from the right',
  'df5b72f4a6ba70e3afb3816689501425':
    'Fade in from the right (strengthening effect)',
  '39377ae457c7b617ed7c5a9d8709aad6': 'Fade in from below',
  'c92e19fe08d566eaf0a36dd93732147c':
    'Fade in from below (strengthening effect)',
  '285b0d56e364277b24b72df4a1679427': 'springback',
  '85025eac2602d5792bdfd7abc5a149e4': 'Rebound from above and enter',
  'c9403db4e39beab29f38f99b58ce6f49': 'Rebound from the left and enter',
  '7cdd1e7fd3bea2a89be768eb51c7a344': 'Rebound from the right and enter',
  'd223c08efde7802938d8283645c557d1': 'Rebound from below and enter',
  '79b5a65645855d1e066e17648d320002': 'rotate',
  '18e5859e5f0bbc60afd14da0dd788d9e': 'Rotating in',
  'de9e534c129089c34f0f3d2a3abc6302': 'Rotate into the top left corner',
  'd496ec3e33e1c55c7830153bf03c5006':
    'Rotate in the upper right corner to enter',
  'e1363364b3e6a75f4b171ec033b31dc6': 'Rotate into the lower left corner',
  '3b1dbd7b02841bc23c3eb535fb774598':
    'Rotate in the lower right corner to enter',
  '367f6ba4391cb049015b931c8da81037': 'slide',
  'ad240914679d522d125ac1a203762feb': 'From sliding in',
  '217a27c317d559b749396e1e9b07a5ec': 'Slide in from above',
  'f5a74e26709b963f843713260d8afba0': 'Slide in from the left',
  '40747a59ff4121f8264c372dd5c0cf18': 'Slide in from the right',
  '369662b059423885b010c09ccf4885d2': 'Page turning',
  '7eb53077f889447c8e91730ec0fb6ddb': 'Horizontal page flipping',
  '5cacc7f3c3688dc600ff4350e28323da': 'Vertical page flipping',
  '198ee35b44d88a9d68259b7d8deba2da': 'bounce',
  '0679561b8354b4ae830876a7de5b78a8': 'Bounce in',
  '194046fb163c32bac4636cbc1d5f0e15': 'Bounce up and enter',
  '1cd7936be0f7fe6f3b95ce06530c47a7': 'Bounce from the left and enter',
  '8b7430f252e8017ee8ff796fb9e86838': 'Bounce from the right and enter',
  '8dd64aaa73906e5580e09d04475e3710': 'Bounce in from below',
  '05853d9ceb108fc5434cdb1a34ab3fc3': 'zoom',
  '22ff588ab903f554b26ddc3ac6e5a2f3': 'Zoom in',
  '60105a62f5bb45db82a2af1cbfa49de7': 'Zoom in from above',
  '529568bf3f02080f77b4acdd98b225c8': 'Zoom in from the left',
  '4be6fb1f3b77b092c0b97a128d400328': 'Zoom in from the right',
  '64ea0ec38ac25c5aaadae965d6e964b8': 'Zoom in from below',
  '9b916bf3adff39bbeb6d5b68ed3118d7': 'Entering at the speed of left light',
  '891b421d61cbc5526ca2ed579c3726e6': 'Entering at the speed of right light',
  '70e344c162554d439887d4186a348837': 'Scroll in',
  '1548bf2b4a1f3c3937bc62a8b2fad739': 'twinkle',
  '91bd8bdd6599511fad9f7c38cdffc71c': 'Shake your head',
  'f3d7a0cd14258418967f731370993b52': 'heartbeat',
  '61c275a8e526d2a3fa0bc52fda4e1894': 'jelly',
  '0308bd6d07412dc4628b0fe2cbf11eb8': 'beat',
  'eb8186075ac943b645b5646c9ac893bb': 'swing',
  'e660a1fb4de45e3bf6e6525ccd041821': 'shock',
  '9f6012f3eaff11a8721072cd5bfd7dbc': 'Shaking',
  'f1b8e0c6053ea917fbc702380bff9b73': 'shake',
  'c1754c2d14d1eb4f0d18eb4f3e6df78c': 'Horizontal shaking',
  'df83af49facc0e41864bec77c3aeb052': 'vertical jitter ',
  '78a83c170c9ebceeea30dda253b8a9f9': 'elastic',
  '8c90bea6b1f0f200e47737df611df0e7': 'Fade out',
  'c31bb885b514fc3670af717575f45e07': 'Downward fade out',
  '494226348e54f0757e9f5acae3e0f69d':
    'Downward fade out (strengthening effect)',
  '115a2f5e84c129fa14a46477991ef707': 'Fade out to the left',
  '7b8b99d894fca4adaa4cb7db7de88b44': 'Fade to the left (strengthening effect)',
  'fba93f5b3a172d74f947fdb03b614b76': 'Fade out to the right',
  'ac5fb0d7a285ff37046d4a49389221ab':
    'Fade to the right (strengthening effect)',
  '3a9ccb41592a1d9af3c3a0a31b0bb940': 'Step up and fade out',
  '5615ffa3af206322fbe4c7ccb91a3fac': 'Fading upwards (strengthening effect)',
  '548c9e55448ef954b3a39b1731a8196c': 'Rebound downwards and exit',
  'ddd0c226af7b15b72605c5737b937f2c': 'Rebound to the left and exit',
  '127d7a2c25a46ed6c1502d652ff705cb': 'Rebound to the right and exit',
  '9e67d753c7ee78ef752bdc2759d71799': 'Rebound upwards and exit',
  'a8a73aff502d78c90a5c20eaf257e411': 'Rotate exit',
  'd6b661719a464b61cca75e161ea44d23': 'Rotate the top left corner to exit',
  '167cd96d6da5f49e82f3caedf4da507c':
    'Rotate in the upper right corner to exit',
  '9a38bfebab98787ffbf54e7b9e16f16f': 'Rotate the bottom left corner to exit',
  'df3a9e899eb6cf4531a2c01742d53964': 'Rotate the bottom right corner to exit',
  '370f59195bc9427f4cb05ebf4f1f056d': 'Slide up and in',
  '8c6f065535f9a34e7bac0fc86f7dbf57': 'Slide down',
  'aed469d08234bc2522696064f53e220f': 'Slide in to the left',
  '6d891408fa37c301c88732d991dbba23': 'Slide in to the right',
  '25ce3b051c233c20e60e09e68a27aae8': 'Bounce exit',
  'c7d4cc3f68e40032e82a9060a624075d': 'Bounce down and exit',
  '3a5543863562663a0b74a8f518b4a7f8': 'Bounce left and exit',
  '4864869dd574cb2757271c984a69e221': 'Bounce to the right and exit',
  '81a1635facb5e25802e63c0ec38d736f': 'Bounce up and exit',
  '74e98545bdd4f179a7b618438e972d73': 'Zoom out',
  '30217532652eed9cd1c0683c8930d5c9': 'Zoom up to exit',
  'cf9da34220fc2d63f801ddbb32553759': 'Zoom left to exit',
  '792782766ea1402a33787b57df79da4f': 'Zoom right to exit',
  'a76d8ecbbeeb08ca8fc9ea37098f9ca4': 'Zoom down to exit',
  '4183baf0de66292097ac18c4b12e08f4': 'Exit at the speed of light to the left',
  '7d90a511ea18060bace3357ec3c68493': 'Exit at the speed of light to the right',
  'cd35750918e16b38f8d2d6c817363bd7': 'Scroll out',
  '9d10187a72e379ac7c6e66a9bdf7a320': 'Upper spacing',
  '37ae4f181d4f139be90fb80cae3fc493': 'Icon Style',
  'a68e1efc4cfb1c098588ba307a83e5b7': 'Icon margin',
  '9f553e501dd2d1cfffa42ae21773da00': 'continued',
  '0c1fec657f7865ded377b43250a015fc': 'second',
  'db732ecb48b1a9cfb3ebea9fcfbf0980': 'delay',
  '82683e3be20c6b7163fef820c660b809': 'Enter Animation',
  '4c1b6f4b9ab9a5e2d9ae472d02d1f002': 'Emphasis Animation',
  '33e3c0d279016d61870b21123bef105b': 'repeat',
  '9c7d7a67aaa0026807c22a5a5c59c456': 'infinite',
  'b77a11aead7c0b84307ee2bd33a60c51': 'Exit Animation',
  'dd1c01bd4b36b5f4793a41bda20b334d': 'ID card number (18/15 digits)',
  '5c20c316de54221879cbef3c01bdb1fc': 'ID card number (18 digits)',
  'cd393d01a120aae11de4e3015f1e4334': 'Confirm the addition',
  '76d898a22c0a44a13482a9d9a32b088e': 'Triggered when adding a submission',
  '69f9962f200bf6bbcf3be61e1ea5adf2': 'Triggered when editing and submitting',
  '631cd22018a47a2171c01d1c1c703261': 'confirm deletion',
  'a89e51f8d0e84d2c96d2950c07693c1a': 'Triggered when deleting submission',
  'a4a667ccd3f4c87ce1a02f6f0278ba94': 'Confirm addition (not recommended)',
  'c1decf80d615094aeabccb94965c7d6b': 'Confirm editing (not recommended)',
  '0ea333785c950e92761898a19ba166dd': 'Confirm deletion (not recommended)',
  'b4e23d11bddd3135f3ecc614e5e183ca': 'Selected item collection',
  'c5393293444e7b6e2535fd40f88ee2a6': 'Node click',
  '9d5df6103865f50a2974ff7dd56acae6': 'Click on the node to trigger',
  '7a9fab192455e65d6265b1978a32c5f2':
    'DeferApi lazily loads the result returned after a successful remote request',
  'c1cecab6116bfb14ce9f9d84d6c4150e':
    'Lazy loading completed (not recommended)',
  '67d5946c409872ab453390a49a405838':
    'After the directory management page is completed, add the return function',
  '6e3541884d20e95ad039b9b3c894cac0': 'User Information',
  '30acd20d6e6115b769020b8bc395e4fb': 'User ID',
  '819767ada1805b0f0117f10967b94d4d': 'user name',
  '23eb0e6024b9c5e694c18344887c4fe7': 'nickname',
  '8098e2b4e82c9fff83ec2d91bda6f1a6': 'cell-phone number',
  'f3c144991c6d4631fd31067199658918': 'Application Information',
  '3751f65ff0252d27275b472a5c7888e6': 'Application ID',
  '27c3862a86325ca60b6306825379e6fa': 'apply name',
  '2eb6f32efa500ca53578b48a99f5a22e': 'Application Logo',
  '0359cf618fd6b710375353cfb2b60f32': 'Current operating environment',
  'acfc983e79f7bc74837d353878b52a0a': 'Organizational Information',
  '1eb4bf1b121d558c4b13a8021e8eb8ce': 'Organizational ID',
  '4c12d831e36386981052eebce255278a': 'Organization Name',
  'e42b01933b2d2f9960941764d4906487': 'Organizational Logo',
  '9d24d229baae271f3cdf1b3ace2c9681': 'Organizational identification',
  '10c645d2523e160068638a81a8a2806f': 'Browser variables',
  '8863d6bd178bcd28503b5716a7974174': 'Page entry parameters',
  'ff6855279eeccc6d6f1ff32f40a8e4d4': 'Product Name',
  'c9452aeeb55874f01c0bc221f06e42e3': 'Account Balance',
  '82330ce88fe46e7fd37a4ea971f0a8bf': 'Product Quantity',
  'ebe7816542f5838cb9bedf9d7c3bcae6': 'Is it an online environment',
  'f836f49bd38a4ec3c37831d34c4a906a': 'Product List',
  'eab1293c98ed5068dfa21a2507603f2b': 'personal information',
  'a01a9963238df560a9b974f45c7e253a': 'Age',
  '63385eb63e8a246739dc47987f9094fe': 'Street name',
  '4d8e094ac4913ab43009b365cae194bb': 'Postal Code',
  'd688a3a4d07d16ac647e799e472bff2e': 'Simplified Chinese',
  '68f3880860c3de1a7b0fd237b9a500d8': 'Global Event A',
  'd8ac01bb2d794d5f422d22faae2dff79': 'Global Event Action A',
  '6b128d2d7e40de95d6253fc8284fdd2a': 'Global Event B',
  'b2014cfc0ff281d075946e3020894dfb': 'Custom renderer',
  '606c8c7399d5c3b86c88a25b6748bd1f': 'This is just an example',
  '6fce1c9aa3654ae2f0eedcc068a7e189': 'Tailored',
  'f8a2de5e15f7524480a904de65b8ef7a': 'PC mode',
  'bb1be951fecf7bfb45ba1891c941eec9': 'Mobile mode',
  'df5b97b65e12438c5f86826767c13974':
    'Click on the Test Internationalization button',
  'c0c4a2e6c8e71769c21fb44849872fe5': 'Switch corpus content',
  '53cb1a5b565d2e94c61a26e089571472': 'Evenly divided left and right',
  '0eca59595d401d76fe464fdb310f4617':
    'Common layout: evenly distributed on both sides (layout container based on CSS Flex implementation).',
  '0b6d1a0fc4bcca3a316e86c8041920ba': 'Three column equal distribution',
  '5bf0bab26f838aa4bccfb3da33066f73':
    'Common layout: Three columns evenly distributed (layout container based on CSS Flex implementation).',
  '4e66d1a5829f4e16723adbf93d61852e': 'Up, down, middle',
  '4a32e12cf434a580d16e0d82284ac9a0':
    'Common layout: Top, middle, and bottom layout (based on CSS Flex implemented layout container).',
  '17bc41719cc7a4aa88787402d3fef3d8':
    'Common Layout: Top and Bottom Layout (a layout container implemented based on CSS Flex).',
  '2de76c22e8c6952000cd9c0104e9c4ac': '1: 2 Layout',
  '5962c11edae5093617b77277e38ace1d':
    'Common layout: 1:2 layout (based on CSS Flex implemented layout container).',
  '1712b9adb39c5f03cd9fc9e18ee31316': '1: 2:3 three columns',
  'cc864704f2535633f2c76462b9026631':
    'Common layout: 1:2:3 three column layout (based on CSS Flex implemented layout container).',
  'de3d5434b3ec146e56fc618e22e7ac49': 'One drag two',
  'f4632577e5c07f9c54ff3aa741395e1a':
    'Common layout: One to Two layout (based on CSS Flex implemented layout container).',
  'd929350236d58607bd0f3aea90302ab9': 'Left one, right two',
  'cbd4886dd4935c67984677441afdf09a':
    'Common Layout: Left One Right Two Layout (based on CSS Flex implemented layout container).',
  'dad2ae63d3f4f415f17193b00f804aef': '1: 3 Layout',
  '38cde83c212e38fd807848be1bfa1eda':
    'Common layout: 1:3 layout (based on CSS Flex implemented layout container).',
  'ec9f8096ab5b23ed9a93389b954c9951': 'Two dragging one',
  '76fa2d38561a834fb2eb464dc02d8570':
    'Common layout: Two drag one layout (a layout container implemented based on CSS Flex).',
  '95b2440c1264b8a52707714c0bb3a4f2': 'Left two, right one',
  '9e7ec97f41b35e0d9ae38b1762c9f4cd':
    'Common layout: Left two, right one layout (based on CSS Flex implemented layout container).',
  '056f41e5ecf54f5fe8fb18707b441cc5': 'Multilingual',
  '766959d419bcbf6463dc55081c6ac640': 'compression quality',
  '2f67a22a0696dfd6bdc6520e06a6cb4b':
    'After cutting, it will be regenerated and the volume may increase. It is necessary to set the compression quality to reduce the volume. The smaller the value, the higher the compression rate',
  '5f0eec58f36853e40a718b9f250881ab': 'visualization',
  'db06c78d1e24cf708a14ce81c9b617ec': 'test',
  '99929330245c293d6ee7ba29838bdbf3': 'Customer Information',
  'c38aea521be9eda79369b59e962b0270': 'Next Page',
  '240f6f9f1dc4812dd332d73b68ac21c8': 'Page Flow',
  '6bbd0a3bc0d4e680561f35ac85aa23a3': 'Go to next page node',
  '23d0eed8b1d828aea8040adbd622d949': 'Next',
  'e84aba2a7ced97a4eca8531fe64bd306': 'Event Name',
  '0ecb267c0be471118549f19a84e0fc17': 'Event Code',
  'c52ea850ea91ef3999b78329101aae98': 'Parameters',
  '5d2a0557aaa340ca2936093018b0c630':
    'Basic buttons that comply with Pad Pos project UI specifications',
  'eb7a017dfec58837ba26c90a0b7dfc55': 'Unified Bottom',
  '56988fadece52d99e83693392edf2460': 'Secondary Business Page Template',
  'b13830d1f27d66093042e59bd46e224e': 'Customer Search',
  '931339437c6b324c7940f00483821d68': 'Home Menu List',
  'bf388d9e0f50fb9bed029908f963d532': 'Menu item group',
  'e416671d35a4612e17002a90be28c3fb': 'Home Pruduct',
  '32ce6fc48a9211c41caa3112905f4573': 'Home Search',
  '4ce8061939d1831915d8e8f7ac8b2cd5': 'Customer List Data Source',
  '51bf01601bc7790434555be3657aba12': 'Business Signature',
  '5ee791c074edd8d49d78c16c20f36862': 'Title 3',
  'c4fd4f9595ba755eb8adb0db15112d76':
    'Icon that complies with Pad Pos project UI specifications',
  'cabafa50d58210f437a63c46005f0bae': 'Background image of Customer Case',
  '87dfcbdd3ee44fb3950fa37f5caaf8e3': 'Order Entry Customer Search',
  '32de092b611bcaeece4a39bbfb1b6643': 'POS Head',
  'a718237d51da29ab951632bdc07c033d': 'Hot Offer List',
  '02323e73b4f9b872d54c595b8a96e84e': 'Package filtering',
  '46f3e97cb774978266c3b75c0432bdbb': 'Package type',
  '0790d1cc29bef1edf327ec8aea7108c1': 'Iconfont icon',
  'e489325f46666d8864c8d3b1dc53ccc9': 'Icon type',
  '02e0fbc65369f1d716a665dce24d24e7': 'Antd Input component',
  'd34952ff329297d2d4b07eb5dfd4f85b': 'POS left menu',
  '83e774125172bb9e5fac5b54de948c0f': 'Login',
  'c89cbf5fed462ec0f9966a793919ca34': 'Login Form',
  '7ae7269a52d44c96d2cd0f6f76cd09df': 'Order entry homepage interface',
  'd8ad116c24483fe748a3bcb011ed6b0d': 'Menu settings',
  'af0cea2d439f88ef17f2371d296420a0': 'User identification',
  '588fc8225ce6b7940cebdbf810206015': 'User name path',
  '6b06edfe33e766f900abd2346b79d909': 'User ID path',
  'afeb8e0f887043d444e4f89a66361014': 'Form submitted successfully',
  '89c34d6bb87ee79c4044a7c00e516c7e': 'User login information',
  'c29e4097806f93f9c0722e7d3da7002a': 'synthesis',
  '83b0d23429991afa9781d554eb77c633': 'Customer Name',
  '506139cc209fdd8f47e20fbf6d21c9d9': 'Customer Cert Number',
  '2eb47fa7e3ee097a673065e2b8ca75fb': 'Click to add',
  '2e9519bfe71b77c43c7e11d726e2ddde': 'Click to add Icon',
  '0ebd349602d17729afe915730996284c': 'Order Entry',
  '2487f137e37b09d705d46ae12bc43c88': 'Update Global Data',
  'd409158feafd4634d31af1c723b1b4cf': 'Customer ID',
  '06af6f6db617710af7a5a39702f7522c': 'Customer Default Language',
  '603c6eed9b5d2ba5c1985294b6145752': 'Left Menu',
  '8d8784b51a62b2af1628cdc3a1c36869': 'Shop Title Information',
  'c8042d8dc70c31c87181948de6225f8c': 'Shop Offer Show',
  '34169690466d261c88ae2edf2d2ff427': 'Shop Offer Type',
  'ef496d670b725a10e96bf4f3aede3f5f': 'Shop Search Condition',
  '5bf004397ae096a80aca649b2c94b35d': 'Display sorting criteria',
  'b8d1bb96cba395c09f79ce848e8660c8': 'Display filter button',
  'aa492b01a15daecad547fa72d8afefd8': 'Global Mock Data',
  '2fdfffe4f998fcb5564f5b2dd2adcd06': 'Account List',
  'ae579b1087d0bff38f7947232ee06781': 'Account List Data Source',
  '7e12f4823d6a975a19baae7eaa2e31b2': 'Optional Package Information',
  'c673e84da92a66bd7a8c29949882e052': 'SIM Select Page',
  '5034b67f7df65d2413a6535b246c9d98': 'Data source after successful login',
  '43a4d021eb7c5b1ab066682a48673575': 'Interface called after successful login',
  'c5473851ac3eb19a28a366062c3a6b43':
    'Local data source after successful login',
  '92c4f987349e7fdc8f042f3cb6c0423c': 'Job data source after successful login',
  '1d28ccd1b65f60c0df14a42dfcb8957d': 'Offer Confirm Page',
  '135381a31b44b2597136f1d49fbfdc97': 'Order Success Page',
  'abc93ee157ead39e06fe0d81704bd7e2': 'Payment Page Tabs',
  '99f57a75ba95f5d06a4686ce55ca69ca': 'Click on the Order button',
  '0755c5a08541b4e47f4776dc0cee5cb5':
    'Click the order button on the card to trigger a function callback',
  'b6804f5d59b6478ac5fb39edddbe81e6': 'Offer information',
  'e75e90135e558d219bd31bfc9fb47db3': 'Query the data source of the offer list',
  '956aafa01c7920efa0ed699fab248846': 'Add Favorite Data Source',
  '1e47e5a6c4982fc0907a9328bca90142': 'Delete Favorite Data Source',
  'a62fbc492ada923716aa57c62ef18c70': 'Page Steps',
  '0127f318eb22077b75b54698089ea3b7': 'Subscriber List',
  'd748497cb56bcc9f6d37e7091451d75f': 'Background image',
  'c7bb11ade8e0da823ec76ad9f406c9c2': 'Customer Universal List Members',
  '3362142c607404de7d4f984e811121d4': 'Is the prefix Icon',
  '1428a7d67039ad05ebe1089719ca05a6': 'Do you want to display sub blocks',
  '314dcc7a0d942e96254fe4d6f0985be0': 'Customer Universal Tag',
  'bb34b4a5d24b3f5415ca40c0eb2571d0': 'Customer Universal Panel',
  '8c7e479dd35d2813dfbb1c4078fe36f0': 'Do you want to switch to tag mode',
  '47591f9cce55f65ae69021f752aa3971': 'Customer card information',
  '9e5fea52973647473280af3c97d78e3d': 'Quantity binding key',
  'aa65df685f5650234a87e9936ccd310e': 'Icon icon',
  'c767c4b2ba3322c3cbbef99f829dd7d4': 'Bind key',
  'fd8ca24641927752e2ebce8fb810c4a6': 'Customer Card Information List',
  'fa4d6cb0f733eb44621902e10151e4f8': 'Customer text information',
  '648e4585ea26a7632eeb4f0fc395a13c': 'Customer text information area',
  '5dc794d2ddba9a5b938e749b286bf9a9': 'Create a Case Sheet',
  'b6e28374cf6f2c98297e784f36498012': 'Define interface',
  '46cb212c547e923cc922d88fa8c7d186': 'Page input parameters',
  '98ba97f09a6201fbb4be4484481bddca': 'Page output parameters',
  '283b02659ba1fcdc95760bb05f951c4a': 'Customer type',
  'f022ff766e1585a8eea2d96fd6df3587': "Customer's native language",
  'ae55769bbc87764404ea77be3ef6b7a3': 'Document information',
  'f2474f9b83b97b7491589c22c70d7144': 'Identification Number ',
  'fc0fd628a2e618a39b07573da9681f14': 'Prepaid fees',
  'cf0377d9954dc612eafb36cafbfa4df4': 'postpaid ',
  'ae17525c1d5dfe018fba921b4bcbbd53': 'Display prefix or not',
  '5ac6749398d6250ba0c87436c6edf890': 'Do you want to display the suffix',
  '7bfc7a4eac22144cfff03e2570a56a5a': 'Customer Universal Tag List',
  '9634adba95636da563ccfddd4f586e8c': 'Customer Tab',
  'c3c12b4639a46579f7ed4e94d38135c4': 'Customer Tab Panel',
  'ad35accdc61ed40bc04bd702e623fb0d': 'Customer Group',
  'e6f6a08827e402b58cfdfbb94c46d72e': 'Customer Special List',
  '16f473d67315f4787a3bbdd9b1a1069b': 'Subscriber Tag Data Card Information',
  '4874ad17bf1a42df8549214f6359bb8a': 'Customer Tabs',
  'a9d809aa105d881e1b89ea4f08bacbd6': 'The key of the current tab',
  '49bf25707aee855f7b13cc6b838e3643': 'Customer Timeline',
  'c5eab2fb506e1d16366710557aaa7bc2': 'Customer Timeline Dun',
  '1974aaf2bfc14fe1660b45ec790e53d0': 'Customer Timeline Members',
  'c7b755e0ae89e0c8cbc61be5af7c4618': 'Is it a horizontal display',
  'b197fb5664a036ab97c204973126c0a6': 'Is the style separated',
  '380f87759d91afb9202705432e5b7e43': 'Product Reservation',
  '3c108fa74d3a872cadf5de323c4a4c80': 'Package Recommendation',
  '4835c676f9e86a54346544928b512ff0': 'Processing orders',
  '99c64459072a4b6d7da59ca8fdbc81fb': 'Order Members',
  '843d0322295eff8f31f01bd20b6715c6': 'Order Member Button',
  '4c7d77362f61a84c374f0ba34461eb76': 'Copy Text Content',
  '06f7b155ea570fd2001d91925ab66162': 'Add shopping cart data source',
  '6234d8dfefbd1c5208acec9b893c26eb':
    'The page schema has changed, please save it again',
  '5f3a1f11daedbbda850c0876bbf9f580': 'jump to the next page',
  'bc6ed944c3a25f1ac48ff611ec39c666': 'jump to the previous page',
  '6cb1692a13a117802b7cb53c72b04ad7': 'New Version',
  '83611abd5f806cb2db7cb992e91186dd': 'Release',
  '4805dd77c6f7af3ae511025a666646b5': 'Offline',
  'a05e4eecb8dcf594dc92f894b66966fc': 'Version Name',
  '84fd8ef5ab92b704c6af98ea76f7f16b': 'Copy Version',
  'f4abce256a9e6615f7a2cd1e0c1db625': 'Version Description',
  '15b3c6aaf23d140ab8c294b46ab9a812': 'Select account',
  '3ad511e40f864f84a5e810d6970a3dad': 'Account information',
  'e3840ea344d19e0735d3fbfdf63cc479': 'Successfully added account',
  'a99b083f04e17ba589d7feaa69742a30': 'Number information changed',
  'cb4ba4e06e297ccb79065f73b5ce1737': 'Number information',
  '86547f4141237fb14fded53fd26f12f8': 'Whether to display the dividing line',
  '072b4e2bda1bcd39190718a6cfaaf2e6': 'Subscriber Member',
  '6ad26b925b49a57e26f8ee42dbb455d7': 'Click event',
  '2253650dbefb51d9e60c136da54b4dfd': 'Trigger when the container is selected',
  'c5ebb7875503d433f459ca2c083cfe23': 'Subscriber click information',
  '065362c2990f223cca9e36d53f937ee7': 'Customer subscriber list',
  'd34e4114809396952aaea92455ea16d1': 'Physical Reservation',
  '13aacb98609ada554a5569bb13ef0e9f': 'Package details',
  '66757e7ed00cc9a8c6d4594b54aa9f0a': 'Placeholder component',
  '3b9e4205f65ef167779de4d2f0d572dc':
    'After opening, the body of this component will be replaced with the component that sets the layout',
  '4a0df9c4798e6f5d99f1a00911214396': 'Fish component',
  '14c54c6b41cb5f54959e2d5047f448df': 'Open layout',
  'e605deb06ebe66d616243c62eaa4e4d1': 'Update Step Data',
  '2c2b212031ad6b5e331b9164030eb592': 'Optional Package',
  '601c150040e245ed7a328648e09f0295': 'Optional Package new',
  '97c29eb2cae077f80df111bd7a0d36f8': 'Optional Package List New',
  '9e5ffa068ed435ced73dc9bf5dd8e09c': 'essential information',
  'bac8eef8df435320c0fda9b3df446b15': 'Subscriber Information',
  '0536318253320bd95497256fd0af59d7': 'Query package details',
  '2feffaae305089ccb80287692271ae18': 'Query package details data source',
  '033b7d67e28dd1c594d534cc3b756efd': 'Query subscriber details',
  'dbe638d827cf83b936597c642a52ccec':
    'Query the details of optional package attributes',
  '48227160dff2811c5bc1c74414f9ef0e':
    'Query optional package attribute details data source',
  'b9ed6640c73619fb7b305bb2cad30588': 'Query the name of the leasing unit type',
  'f728eb30d7abd76ba25960a8bdbbd085': 'Query rental unit type name data source',
  '58ade282be83b008e35660b215164885': 'Required data',
  'a2fd136a035abe5f6d360a430d09a12e': 'Optional Package Preview',
  '3c721873e4d88aeec1dbe86738fba804': 'Add on information',
  '130dea7f0ee340c846405dcae2dd9699': 'Order Entry Search Interface',
  'ec3fb565b83597729ab29694808195db': 'Select customer',
  'c62d5c820e44e79a6bc95b82cc3629ee': 'Triggered when selecting a customer',
  'a5739f04a794c6c782d0dd03a0a5ee2f': 'Account identification',
  'ded73665f2d336be598335b0d9116734': 'Post paid identification',
  '1047d22a649472e74a15c44b7295e954':
    'Data source for querying account balance',
  'bbc4b07317bf60af11ee165322c5f902': 'Query the data source of overdue fees',
  '909b67fa65d1a64424a56a08c05ebddf': 'Balance information members',
  'fa5edb835228eedba72c3138e8546713': 'Balance information member list',
  '959d18e353664212b8a5750463e8d9f7':
    'One of the technological and market leaders. In China, Xike China (Guangdong Xike Intelligent Technology Co., Ltd.) is responsible for factory automation',
  'fa21edc00bd961863f41dfeb2203b1ed': 'Text prefix',
  '5a90f57702f5ec67d1c1727a3ae69406': 'Selected subscribers',
  '40e1f94ac881583fc30477ca6b3f3c5e':
    'Data source for querying the number of subscribers',
  '634a2d37f70e47fed88dff65d524a954':
    'Query the data source of the subscriber list',
  '8a08f7bb793fe22294328eef985138ab':
    'Query the data source for the total order quantity',
  '3804a4729c9ba7a4941300e489211e65': 'Query the data source of the order list',
  'f04f189563a29bb4f72b40d955de1cb1': 'Customer Order Information',
  '6488c71bc2507681c5c28aaeeaa716c4': 'Number of displays per line',
  '130255f2df524a0241090d56e83fcbe1': 'Customer order settings',
  '70eebf090da2a1f1f3da9169a4c090db': 'Customer order content',
  'af3931e10fc9f17b64f21d32a86b3e0d': 'Member Name',
  '7f126577ce54346a8e09fc8852aceafb': 'Member identification',
  '772d69760de57537303295d4565e7357': 'Horizontal spacing of elements',
  '4d81ff8464680a9f7692304236124d27': 'Vertical spacing of elements',
  '2325a5fab66d8dba69e94c585e789e98': 'Customer order information member',
  '4eb88f22d7c81d3b887320eb3ceb2a1a': 'Footer',
  'd42348d8b7655733f67da4a29fe0423e': 'Cost item display data',
  'e8f0ea944ee4d735187a2638c696018a': 'Optional Package List',
  '7cba654ebedb420aa641dae4942ffa1d': 'Query optional package properties',
  '3d879d0dcefeeef8ccb7015a4ad61e6a':
    'Query optional package attribute data source',
  '9eabbc78e77824a814ce1dd3167f6658': 'Offer Information',
  'b685d3dcded195102fc96ed161b92449': 'Amount accuracy information',
  'fb5a7ee3f384adc26a0ed5925dd94cbe': 'Gsm Layout',
  '1f6b3deb84439dea442a56cf88c1529e': 'Click on the icon',
  '69be3672ecd4b979bcf6d6ba989c4324': 'Icon information',
  'e657132e682189e1a386a865c6dba435': 'Activate menu item',
  '91e661879a78463c3c34f86fafb7b0c6': 'Left menu item',
  'ab60642e4f15e700f67cee39c5dd5a3d': 'Click on the menu item',
  '3d391b2bba14fba14998cbd6097b6356': 'Menu information',
  '8d1330f9d1cfc83f8043a7853e8bce70': 'Menu Item Identification',
  'd7aa7fe6b27e06f4f468d276ff7f27a8': 'Order Confirmation Interface',
  'b492aa617017a788890e8b4984a92edf': 'Optional package information',
  '587a4ab6a23f67b4426ee7aece934e30': 'Cost information',
  '60fd6a52523568a487735eccaf867d21': 'Side Menu',
  '1c38344f373a726ec01fc717aa3d05e6': 'Signature callback',
  '99d692b945bb6af9456ffd01bef87e9c': 'Callback when signature changes',
  'ab09ad555696a0b7f85216eeefdc1786': 'Signature information',
  'eb4b7b6d399dd49834827645274752d2': 'Subscriber Information Card',
  '0f0c77117328fa615c4264fb0549eddd': 'Icon icon modification',
  '76f88923edda515a12812cf69dcc819e': 'Secondary business click',
  '152c6c39efe12e4a1ea821c001ddfb60': 'Subscriber details',
  '59c5febebe8277495aab2e8ed5cc5adb': 'Subscriber event ID',
  '0d992caf57f6926884ad1168d30736cb': 'User Details Data Source',
  '0e69e26e445149bc9a6374319a2cc0e4': 'Query subscriber details data source',
  '46da65b134cf4c0e725a53153085dcd4': 'Data positioning',
  '08364b78ff3bce209d3ba67514f8d0c2': 'Keep page container',
  '7afc91d8a47319919941c75b1708905c':
    'After opening, the page loads the layout component, otherwise the page body loads the layout component',
  '4f996391a1a21d540410794d2ad1703c': 'Retain page data',
  '9709167f654e8e8976b05bde55a97743':
    'Page data will be merged with global data, and page data will have higher priority',
  'e0eff20edc25f711171c57b7372d3312': 'Global loading animation',
  'b1f76db9c53927d8136752ee043fc9b1':
    'Open global loading animation when sending requests through the interface',
  '27e9ea84565c0b8e6fc7586863f404f7': 'URL jump',
  '46f3912df125c9d88c9b6d6faef6842e': 'Page type',
  'e455bbdfd3abfc6f97ed7b6b8d65e9cc': 'URL type',
  '29c8bb662316911a05f49223857b4603': 'URL address',
  'c283c644d3be852a4a8ac8748fcfb5a4': 'login information',
  'ba66b9ffec6f72b1d0fbc331ecfa6fbb': 'Job information',
  '98efb6af528fa2788eeff78ce3bf4ebf': 'Local information',
  '10c6755b6f804a1bd7bde0137094c2f1': 'accuracy',
  'f53e291fc5dd473eabc4cbb41a8e126c': 'User code',
  'ccc70a664dc3e9dc7672e50e2ab0201e': 'Do you want to display additional areas',
  '67e1f34294865a8e4caf5dd5691fd2f0': 'Next Page',
  'c2e45f82d34fa53148cf222fe14fbbb6': 'Route Redirection',
  '84fd3fd1865f1056b8b890f7c876899b': 'Update Step Data',
  'c795e2cb45259f9ca4c7190e208ecf24': 'Update Global Data',
  '599025cac31d971550f89c26596757ba': 'Next Step',
  '5325b0e36967ad4b012342a90b17114a': 'Jump to the next step',
  '1b34930e05cce48990ec473906845469': 'Previous Step',
  'b0b161b6d8aff2b8661fb48e9e7e555d': 'Jump to the previous step',
  'ae9cf16870cf3b14387e9effafd6b024': 'Content Area 23',
  '89cde8fd3e955888342389bf3e6e4c8a': 'Process startup callback',
  'b3a8a1451ee00f26e8081dd08ec4ba42': 'PortIn Checkbox',
  '********************************': 'Check selection',
  '7e078c52d1f1c4c813133475dddd5949': 'Triggered when checking selection',
  '867d37ad38682fd7c8f46d6d7502376e': 'Check status',
  'b4f6bff3f0150515944dddf26557f1e5': 'Check identification',
  '8483a8ef48c0b0d391a6edb524d249c6': 'BO Data Identification',
  '9967969d9b50948ea437cca0aa1486d7': 'SIM Card Type',
  '207816e6c17b908039efa554fa95954d': 'SIM type selection',
  'ad5e93e83758f121a1651aed33877447': 'SIM type',
  'dfb1500bb9448ae06113d2d00f9aadab': 'Data binding',
  '4c3d18a04943b3bc25114e8e548f6067': 'Option settings (priority effect)',
  '11c4cf314d9af56ccf6a89d45db821bb':
    'Specify the field name for the option label value',
  '12938481657dcb7dd6d33cea8b0b023e':
    'Specify the field name for the option key value',
  '2c530775e3aaa98cde7777985e774440': 'Custom option settings',
  'd0c8ef9fc518ac94bcca2138713d67dc': 'SIM card type',
  '72af9529003667b3cc7446a956ec157f': 'Query callback',
  '844393f752c5f980a1684408beb1e490': 'Query results',
  '07166e9a9e36893d5736abb4df3e5982': 'order list',
  'a4049c474a598de77a1f98b8669baf22': 'Number selection',
  '34c0dc1ec33528d2368aea70622a8055': 'Selected numbers',
  'eeabf0db5bfe9d3d3bbefac3ea7f8e16': 'Disable before action completion',
  'e0be7cfdce198406596f331d739ee705': 'Triggered upon reloading or query reset',
  'aff69cc0f8cf108586caf28831bd5abb': 'Selected row index',
  '2bc80b42059f1e21092e9fff01276251': 'Enable selection',
  'a69516665bbd5dd4bfdc4b61a3bd0210':
    'Even without a batch operation button after activation, it still displays clickable options',
  '7a99893930bd8e69b536344e8127af6d': 'Enable multiple selection',
  'bdb5ae35d79c8b6d972d16f60e9c96af':
    'Control whether to choose single or multiple options',
  '047d7d986d09f9a1c6f33063644a1c2b': 'Clear verification status',
  '82f734a76a21cd2ea9484634a6155d9d':
    'Clear the error status generated by form validation',
  '29cb280f3bc6d139ef99b5e83ec21795': 'File upload path concatenation',
  'cf349e79f5765bd7956321f283e2bf73': 'View password',
  '9d262cc24e9f899d853d1c900a754a70': 'When clicking on the password view icon',
  'dd909adbe395bdf8439d6afcde19d8f3': 'Hide password',
  '388ae7e2b90cab255311e1f5300b257b':
    'When clicking on the hidden password icon',
  '234dead0f8b58b6ff1772baf16025df8':
    'Trigger to view real password when password type is used',
  '4894c9cfc59cfaaa117a352c3199c32d':
    'Trigger hidden real password when password type is used',
  '********************************': 'Basic style of text box',
  '********************************': 'Outer style of input box',
  '********************************': 'Frame size',
  'd61c5ae1b2bf84c4043534a1e5428617': 'The selected item can be deleted',
  '36b4942ce8a1770d6835c58d26cfffef': 'List selection basic style',
  '96abd5a4a11da305223654eeb26bafad': 'Configure and edit templates',
  'fcfffbf5d7dd5c491645e2e0947eddb8': 'global variable',
  'f2196f7e3a68af97302d2e0df46df38f':
    'Do you want to display the serial number',
  '61f1ea1bba6d7fa642821d52df2a4077': 'Node Title',
  '9cd700df625adcd5450a4dde0de1af38': 'Client persistence',
  '9fea607976a818e5ccbe7c93455bc023':
    'Is persistent storage on the client side still valid after refreshing the page',
  '0cea6b668196bab5c0fb9297c75dec28': 'Data Scope',
  '9f589edf9c1772c1d5f79c69855f8473': 'Page Sharing',
  '32a7213c48ebd1a2a82bdd737caacd2d': 'Global Sharing',
  'a5ead186e12a531148ff637a55fc404b': 'Basic variables',
  '2b8bdc1349eb750d33f67e2293b02ba4': 'Global variables built into the system',
  '11eb1a8e9254458f195c6833773bd2c8':
    'No corresponding variable type configuration panel found',
  'd78f93a5ad4a40e8a72e15f454199288':
    'Are you sure to delete this global variable?',
  '9c3d1f05074276628a9949cf1028035b': 'Variable name already exists',
  '52cbb59656bb7e6d38011eaad8ef14d1': 'Variable type',
  'f61f4cf6d0d544fad4507868b74696a1': 'None',
  'e2e4c374e59fa5acc981c0a0ed6ce96f': 'Edit global variables',
  '6c95367d14682115f83b647c482b4a2f': 'Add Global Variables',
  '28eb5b4a25ecf8c0c2da703de3b9fa02': 'amplification effect',
  '121e212b7e9b6972aa98d7f982013c55': 'Shrinking effect',
  '1563ef9dc3edbe3f2e887b1b846bf4b4': 'shadow enhancement',
  '42969502d9d5a359774481cdc32dcea8': 'Luminous border',
  '6b978922ce4748240bc00ae30e3a0d70': 'Content increase',
  'dd9476eba02d98354bbafbda8f604c01': 'Content decline',
  '680f410e0e95bfe2406816243c774aae': 'Content flipping',
  'c54405c1a79eea023f3032068edc1621': 'Triggered when visible',
  '980c1b4180bf5096a35ea7505fe59ff6':
    'The animation is triggered only when the component enters the visible area',
  'c18063462332b341f4cc674761577d70':
    'Repeat the animation when the component enters the visible area again',
  '933ec2e002276dda7d8940d426de8df7': 'Floating animation',
  '4f91513e8383967d5e4797bd81445fd7': 'Triggered when invisible',
  'cdbe611aea802f0fc55099161af20256':
    'Component exits visible area triggers entry into animation',
  'b1271049f8a2d3235bf7873093c24215': '193 environment login exception',
  '7116e7ec169a0780408f1ccea427b9ac': 'account',
  'f0adb293772218222754d86f8bcd54f7': 'CRM Order List',
  'd19ba67ea375011c1305dbbef53bc6db': 'Customer Basic Information',
  '469400d03177246bd62d77a9568850aa': 'Display index bar',
  '917c4d67e1d3a4df20594f2e0ccd7ad2': 'Top offset',
  '48da289c738e3964d66553b1875b51ce':
    'The distance maintained from the top when clicking on the index bar to jump',
  '4c4bcbc24bf6782ee6db46fc33b8e19c': 'index field',
  '9ff6f96b5ec659bdbb5fd18041df1ce8': 'Please enter the index field name',
  '86efd2ff39d92845c9b1d684e38b0b67':
    'Index based on the first letter of the specified field, using the title field by default',
  'eccb240e994c6ff148eda9df71b260a0':
    'Support fontawesome v4 icon and iconfont icon. To use fontawesome v5/v6 versions, you need to set the vendor to an empty string. Default is loading outline',
  '6c375a6511bdfb9705be7ce8df499140': 'Minimum loading time',
  '4f3ccece83adbc3d6bdef56a5ebedaa3':
    'Specify the minimum time (in milliseconds) to display loading during loading',
  '********************************': 'Data addition method',
  '51af6b853b376aa6fd5523aedead90b1':
    'Specify the location to append newly loaded data to existing data',
  'dfa0274d6b1b7e255644db357ec85a47': 'Add to the bottom',
  'd5af2ba9df741bfb42a483be55968df2': 'Add to top',
  '63f3359c10f5638708442a6c45dcc7ea': 'Text configuration',
  'd3d862e14c7983a91544159052d943bb': 'Before loading',
  'fe8fc61a2186e6373adc87f98fd5623c': 'Click to load more',
  '26b5bd4947df8be257c59dde927b400c': 'Loading',
  '977b11fe9834e54432d0c6724df89e04': 'Loading completed',
  '********************************': 'There is no more data available',
  'dacb05cb2e948e47c19df9b1cf63ab24': 'Total number segment name',
  '5b3fda18f1c058decd9b46122558eccb': 'Disable loading more',
  'd316f65c78ec9f4c18d5f72aec83d2cd': 'Default is loading outline',
  '35d0c48adc62e13848d9292f110d5c93': 'Text and icon colors',
  '4f48aaa0bf4350ff76fe1d98a8acb6cf': 'Default is # 777777',
  '40e2afdab75788307ef3f957ce1909b9': 'New data addition location',
  '8c9a33c47ba8e3942e5c60c061d38f9b': 'Minimum loading time (ms)',
  '89581548c692d58a4050c869a000b0db': 'Gesture direction',
  '00d5a294ef43ae80ebd85a5f3e40f68d': 'upward',
  'c4557f2bf8fcaa91c5041ed1b30c1540': 'downward',
  'c822d4676b97a4cbbf2c3c8d14e61b62': 'default text',
  'a9cd27b182577c9c3a0dbb74a0c02df7': 'Pull down process text',
  '5db6ea087a57ee14cf642e89e0c26a23': 'Release refresh text',
  '5cf222112a486ae6d161ff444f6199f0': 'Load Chinese characters',
  '10bb1f20fe35208d4a3b082f25b9603c': 'Successfully loaded text',
  '99976bdbddcbe3c1c1054b990566a1ac': 'Loading completed text',
  '33b3f095fe1b51cf19b6f03be4270fdc': 'Drag and drop components here',
  'b85cad54c102b745d5f5121a5e9ed6a8':
    'The value of [amis editor] [EasyFormPlugin] dynamicControls must be an object',
  'ae0f39d985efa925da4875780c958555': 'Form Item Settings',
  'fad42585b0d8d40878f053c91df60040': 'Operation bar settings',
  '5e8424a6d968e376854b203d76ae3a6b': 'Picture collection mode',
  '82f003bc378f64a46ad41d3cb093a3a7': 'Thumbnail mode',
  '326d6be0ee560f2587cdb0be7affc850': 'Large image mode',
  'b07edf2b5be42a9e7e82a967e5563943': 'Waterfall layout',
  '44620090d2e45c0d7c7e1ed503dadd59':
    'After opening, the card will be displayed in the form of a waterfall',
  '4aa775b3d9625e71b250b26327e198c5': 'Column number setting method',
  'd757d94682043ab0589ac55f68417ece': 'Fixed number of columns',
  '91815d7713fda9690b8b9c725a1f4bd6': 'Responsive layout',
  '8c8dfb78cb78556d33e89a986b25679d': 'Number of rows and columns',
  '8b1a324a876f3df5962667272c01a1db': 'Number of responsive columns',
  '5c6395fccdcdf4bac189f0535e7222a8': 'Ultra small screen (XS)',
  '7baf78ae7bd99ecef5e58472978d2391': '1 column',
  'fa10ffc6dce7ee8002a987c84a41daaa': '2 columns',
  'b767f61bd7fcc251bd97f017082a73d2': '3 columns',
  '75a5a7039854862c57ecd42c9cf4d6c7': '4 columns',
  '6b16e5cff6dc092c38638fdb8445a6ba': 'Small screen (sm)',
  '47bfb85ddc7968a5a1ba6109f44f0d6a': 'Medium Screen (MD)',
  '0e5ed5199217d3c614b58d5790b4eb6a': 'Large screen (LG)',
  '98bc1be32af1632ddfef5ad27ebd78c2': 'Sliding bar',
  'bff6bfeea70f890f1ff9dfc1b3a1f734':
    'Mainly used for mobile devices to support sliding left and right to display more content, and for desktop devices to display more content on the right side',
  '0c49df7faeea0255f92adc7401ad5407': 'Main content area',
  '162b302043b4c92848aa6f806bdcf984': 'Left content area',
  'e667354d2eead09934e594c200954f51': 'Right content area',
  '84a16bf350e47bcd5ad797a04cf19be2': 'Content area width',
  '66224f9dea39d73429ee8a51c2291830':
    'The width ratio of the main content area is 60% by default',
  'd34ecf817b317b4d4b37c8467eea6b05': 'Display the content on the left side',
  '98900b460da433d5231f4931d59873d6': 'Display the content on the right side',
  'd96a7b078404d54eac580f831e155966': 'Step Status',
  'a7591bb6ef77e85980fb440f7557a2ef': 'Hide icon',
  'd099044ace39b79ace126ee5080db444': 'Subtitle Style',
  'ec2d2128688b123b379b42b4e9b319ef': 'Hide Subtitle',
  '8bc54ad0651d37b92e79adadd3a8673d': 'Hide description',
  '1c53ee677c52f2ddb523d58f02e87818': 'Column Setting 123',
  'fe8189070b8c6c7dd52ebd0c9e7c26b4': 'Start playing',
  'd4c2e981c7f6c5b47ab36c261e6d2b0c': 'Triggered during video playback',
  '18d9f2dc2a0b0c2ca8dfa9d34a241028': 'Current playback time',
  'b063ef03f52de4db3a66bb26f65f6d7d':
    'The current playback time point of the video (seconds)',
  '296ed483e27ee65aa2d02b64a2d1902d': 'Total video duration',
  '8dc6348f87ad56ddf71aba58846c83c3': 'Total duration of the video (seconds)',
  '431b991d358babd875697eb0c623ae87':
    'The current video resource address being played',
  '30019156e9340207c4cccd34245a6250': 'Pause playback',
  'da2e1c73167313ceea55293b42031c66': 'Triggered when the video pauses',
  'cffe326796a53ecd533a7c28d7c228a0':
    'Time point when the video pauses (seconds)',
  'a7d200f4422e1ff49f87fa254951fe04': 'ended',
  'ad68db856822a4509b563ee97c1a9522': 'Triggered at the end of video playback',
  '39b75e75d433cba4c57c4af1768f499b': 'Video Cover',
  '5094d4404b88877bacd12d94911e6f95': 'Playback Rate',
  '9ee2f29eebe6e88f5f8e55b1367f49cd': 'Video frames',
  'd4f32ef4f29f0b00c68838bc5ae0bfe8': 'Placeholder',
  'de205e9a976b0f4a85657c2aceb26dde': 'Modify form items',
  '00023a2bbbee434389efce546514e9ac': 'Edit form items',
  'c9f871580f6691e0b67b2d57a2eb1849': 'Add form items',
  'e40b505be9beab346df0435af11fcf73': 'Add form items',
  '1388cfb1dddacc6e92d80b6bf2b9638c': '${label} cannot be empty',
  '129afa9b6da9340d94f789bc0858edff': 'Column Count',
  '75e3286810ea80c00a5d26b0fb475775': 'Modify button',
  'dd21259e1f4ce7d6d617d84a5cc76d77': 'Add button',
  'c5896f48d30f9546751422973fd942bf': 'Add operation button',
  '3628db5934417569d5be86cf89d44564': 'Is the operation bar on the right',
  'aa05fd09a619ddbf07d8d903e6d32c5e': 'Common',
  '0048314e09131553fd8add1d4bbb47be':
    'Common layouts are several commonly used layout styles based on Flex layout presets, which can be quickly set up',
  '4f38e93f6a62690cae448fb7531f6ab7': 'Quickly build page layout',
  '68e4171aba5105f0479c6889f207cd95': 'please choose a common layout',
  'e26de184a681bbfaa1885d8b0e5b790f': 'Professional mode',
  '64b67d07ce0121ad66df27ccee06d765': 'Simplified mode',
  '866f7aee6d1655c06fe9eaef6d49042d': 'Order Catalog',
  '8962e82adabf4b10dc92f545128211a3': 'Order Options Information',
  '4a01b938175101e3e080528813789d33': 'Add New Account',
  '41f2b97e9c41dde90a2c7981a96fa688': 'Top 1 bottom 2',
  'bf986aee29b821bb50e7981733c3ee93':
    'Common layout: Top 1 bottom 2 layout (based on CSS Flex implemented layout container).',
  'c52630a44bb1ecbb4f32c9e2ffe36d07': 'Top 2 bottom 1',
  '4bde33b908b1b3e0d17db7cbc253563e':
    'Common layout: Top 2 bottom 1 layout (a layout container implemented based on CSS Flex).',
  '171e87ae25fc4c47e8c0a599f371ca2f': 'Top and bottom structure',
  '828fa1360014cd8d1dae1b3dc005f600': 'left and right structure',
  '3eddd43fe13e0b5cedf623da98fcef65':
    'Top(fixed), Middle(adaptive), Bottom(fixed)',
  '08bed6a642f812ad59e1568c9a98188f':
    'Common Layout: Top(Fixed), middle(Adaptive), bottom(Fixed) (CSS Flex based layout container), with fixed top height, adaptive middle height, and fixed bottom height.',
  'ee583527e8fb68abf6687e159f9b1deb': 'Left(fixed) right(adaptive) layout',
  '1484a923503256702cddd5adbe15cd08':
    'Common Layout: Left(Fixed) Right(Adaptive) Layout (CSS Flex based Layout Container), with fixed width on the left and adaptive width on the right.',
  'aba9ee8a18c73e1c6d183b71d7f5e5b8': 'Left(adaptive) right(fixed) layout',
  '556c8025b943ead4ab75ee574fd1a340':
    'Common layouts: left(adaptive) right(fixed) layout (based on CSS Flex implemented layout container), left width adaptive, right width fixed.',
  '66344def1e4a8d7a13aa9ae76d5cdf7b': 'Top(adaptive) Bottom(fixed) layout',
  '43488082d2476fdf63275d7edd344029':
    'Common Layout: Top (Adaptive) and Bottom (Fixed) Layout (CSS Flex based Layout Container), with the top height adaptive and the bottom height fixed.',
  '6005a351b653b2ef4415edd6ff168578': 'Top(fixed) bottom(adaptive) layout',
  '5571c79bad2234c8cf3f8609ab874acb':
    'Common Layout: Top (Fixed) and Bottom (Adaptive) Layout (CSS Flex based Layout Container), with a fixed top height and an adaptive bottom height.',
  '0248b98d5f4ee9941dce4b6a0d46547a': 'Order Options Data',
  '03e630d8de0fe9e9225cdbdd0d9f506b': 'Installation Address',
  '7ffd3c32e4e3d3b9c081e22cb83c664c': 'Set address',
  'a620db4dad94d95befbbdbf8fe4e5527': 'Data object for additional information',
  'fe5ce6e90794f94101c8bf1658252a83': 'Maintenance Contact Person',
  '3ff96fb1ea36834f70125ea6a9d1b19b': 'Selected maintenance contact person',
  'ab592135d04deacb5e60d888bdffdd06': 'Options data',
  '54b39f0144713cf79c0b57f6a723a52b':
    'Add a description that supports custom input of px rem%',
  '76e0113750bc18feb1a013039894d359': 'Field column settings',
  '1dcaaa566049ecd72795bc09911ce9e2': 'Operation column settings',
  '153fa67a7fb6ada66a1fcccabbbfab72': 'select',
  'c1b990c22e9f681c8d0ce8fb0bc33c78': 'Number of columns occupied within a row',
  'f463b49764443d609d81db9d3c499b8a': 'The operation bar is on the right',
  '4ba5d34b4005419a71fe177b2bf0b825': 'Number of columns occupied',
  'd32e02d19cb3673f5fbf2271e5c66abe': 'Table column settings',
  '2a6090089f4a26064e1ee1e37c3251c3': 'Add table columns',
  '4e0b8fe26d34aa9c3c3238be5294f715': 'Layout1_1_fixed_left',
  'e45a4d3fdb8c7a4982e135061f7ff872': 'Layout1_1_fixed_right',
  'f51a32dc5a204cf27af71e0d0f767f4b': 'Layout1_2',
  'd94e7d096b87807167a021e511991aa6': 'Layout1_3',
  '4445ecd5efeec89369efa52bf854ac04': 'Layout1_2_v3',
  '9f0ca5bac3fdf54abbeafe65f5dd8806': 'Layout2_1_v3',
  'ef05c86efbab5537d7724efe3bc22771': 'Layout1_1_1',
  '7feb319b49c0974e386de86c1f8f4169': 'Layout1_2_3',
  '6c59c892ac763cd22323f1af8ba5b4da': 'Layout_scroll_x',
  '73f5a078133cfd17d3031532b27985a2': 'Layout1_1_v2_fixed_top',
  'dacbadf972e129adb2ae25f8bc44d5f0': 'Layout1_1_v2_fixed_bottom',
  '700011877c7f7b121a56b42ff26e6389': 'Layout1_1_1_v2',
  'ba974ea97e557ad35cce2ae62f9218f1': 'Layout1_1_1_v2_fixed_top_bottom',
  'cee0e7611ae31e1a5a7a4fcc9c32503c': 'Layout1_2_v2',
  '705c7190693f198d2d14523ef2764b5d': 'Layout2_1_v2',
  '878520066650b91dbd3dde124dca3ff9': 'Layout1_2_v4',
  '82e688864e5b6aba9b33809cc3a4f0bb': 'Layout_scroll_y',
  'c44c6c179c44e794b103053886847dc4': 'No preview image available',
  'ac1695a2bdf7fe467f7f8e2e7513f9b2': 'Account Selector',
  '589897d84e0176e36620ac2ddb5cb8c3': 'Exceeding Omission',
  '2f36359e06018b483b13c5aa7e6d57cc': 'Coupon',
  'a6876608f3f85ec7582cc3db9f5d92fb': 'Hide selection button',
  'bbfe8621f7a31329324d52aaa72fa448': 'Resource loading failed:',
  'c315c74af2c6fc0d37324dd0a107d5d5': 'Check if window.portal has timed out',
  '0c92a7ef8ad3be57c5e02adc13846314': 'Check window.rquire has timed out',
  '95d4523d73e4f2922dd1998780a4dfbe':
    '193 environment login exception, trying again',
  '1edc52cd8c142f1bbc3875a5fe70012b': 'Quick installation address information',
  '41b0a41ed47aaf927cf54bfee90eeb18':
    'How many columns do they occupy in a row',
  '015db4d97e7e4b76eb36b2a4952908ce':
    'Check that window.portal and window.rquire have timed out',
  'fcef5f8bbaf824d160a97ee29a4c01b1':
    'Window.portal and window.rquire already exist',
  '33345ae42f5248db461a95e526ba797d': 'Permission component',
  '943403d3d978758600a82ef660c727c3': 'Select account event',
  '813ba7609071f8fbac674b685ae5991c':
    'The API used to obtain the initial Account, and the returned data can be used at the entire page level',
  'a94470f534c5833de1f8332fce6c3517': 'Account Information Component',
  '12c4d53ad9a8e4c21946506cf8681fd8':
    'Interface for querying unpaid account amounts',
  '7dba90f6f7e3138d46058e21d676eedf': 'API for querying unpaid account amounts',
  '2afb3b8b9f2871d0a193b08e18beed13': 'Search for valid account level ledgers',
  '1e2702907ad8ff536f85efa5e20b0a3d':
    'API used to obtain valid account level ledgers',
  '1d020b5b1daafb7c3ab88c2ac8d9bf50': 'Query account credit control interface',
  '62b3f5fddfa7199571abc8f84db56f93': 'API for querying account credit control',
  '25dc962894011a00a9d1a8e922f3357b':
    'Query the consumption interface of the previous accounting period in the account',
  '7120ab0048f525183dab2776d6134123':
    'API used to query the consumption of the previous account period',
  'e4b40e3d2bf1f917fb6b8c27531566a1':
    'Customer Statistics Information Component',
  'e0950f8a8253616c0b9a7d2bbb708971': 'Display Item Settings',
  '5df67db667a1939a9ad52390a284d6bf': 'Display items',
  'bfd6b39ad2ef2d783f83f1e49368997f': 'Query customer order quantity interface',
  '187c08c1f7f61bec8e2430990de5527c':
    'API used to obtain the number of customer orders in transit',
  'e9021f4fd419042c6e8993940de48de2':
    'Interface for querying the number of customer accounts',
  'dfcefbd3988e282291c0fdb2857e458c':
    'API used to query the number of customer accounts',
  'b40217dfaaed71ef4a22900ee4594331':
    'Interface for querying the number of customer subscribers',
  '3c0300be02fa674f3251cb981d943e1e':
    'API used to query the number of customer subscribers',
  '0e43f4d72bb6153638f291ab3c8dbf67':
    'Query customer TT order quantity interface',
  '5951d1e9dec381650b65df183556229f':
    'API used to query the quantity of customer TT orders',
  '0c3c738e824cb8d4c11494f86afdd337': 'Customer Information Component',
  'bfd1addf6789727c887514af6403125b': 'Mock data',
  '67682c9b02007d79c852d734190fd244': 'Query customer information interface',
  '7d9148d804815b8f7448ae61b0029157':
    'The API used to obtain initial customer information, and the returned data can be used at the entire component level',
  'a3f79989b509598c240c112a8eef31f8': 'Set up enterprise logo interface',
  '4da89dc6c79229f98ae2c50b24561302': 'API for setting enterprise logo',
  'c79f86e183f352daf38dd7cc89336f46': 'Set up the logo upload interface',
  '5bb663f9833709bd43ec9168f259d9c1': 'API for uploading company logo',
  'e26c3d19bf33b23ea286b1eacf95d667': 'Component identification (field name)',
  'e479e2df5827207178a70e21ee279844': 'Tag Name (Non FormItem)',
  'dadac2456735b0ef132d265da6e42894': 'Label width (non FormItem)',
  '02d00e47f9650bf891a17a0b09ea4636': 'Add Bundle Member',
  '68aefd8f6db6b83e74e3488bbc914e01': 'Regional Content Title',
  'ffef4df1f16586e3f3cf803f5a29610f': 'Fish Title Component',
  'e1e315d1a93fa6860e358259b42f2036': 'Dynamic Form',
  'fedf405884ca8b19a539316f71901884': 'Dynamic form identification',
  '13c3e5e134f0962f224597a6277baeb9': 'Property Array',
  '3f86e0d288ac9a6ae65df711a550d7a6': 'Select Subscriber',
  'cf076dcdee94e726dc0316e76c888940': 'GSM Customer Order Information',
  '1fb0ca80d7afa3b784f9db306cb67d92': 'Options Object Data',
  '90ad07b0a901fb8d4f8080a7741745b6': 'Order entry plan information',
  'a5201f9f480f2f3a90460636e9452f12': 'Do you want to hide components',
  '9e4848adbfe20121682b1f1abdf4c76b': 'Do you want to hide the 360 view',
  '82819b726c3611b7357c4a133da9b701': 'Card reading button status',
  'cbb05213cae5baba7ba42d5eaf094b34': 'Customer search callback',
  'db26b496ebe6fb40babc988f0fde79c8': 'Click on the advanced query button',
  '957b98b08fd59031d9dcafd19302da19': 'Select Number',
  '5c705be129a1e9a11e828ccdcd50d8f4':
    'Independent product specification identification',
  '59b0173583aed5c86a3a1966e6c70328': 'Subscription Plan Identification',
  '924f67de61fc9e07fff979306900dc6a': 'Service Type',
  'd4eaaaf051a5027fdb7231895fac6651': 'GSM Number Selection',
  '58ba1baa4d39e50debed2a7eff9d069f': 'Social Media',
  'fb20cdd33c419fa440a9fac36ad17473': 'Order Item',
  '7e611050e4e9772a35609e7e1513ba88': 'Please choose a copy',
  '8ace6b57ea7764104dcc5b41dac3e34b': 'Unbound copy',
  '7f87e02f5fb9577a7492c0bbd6a2ba47': 'Already bound:',
  'f2992715424a4eeadbce85560195a6ff': 'Click to copy',
  'f7d7896fa29613e78674e5014025bc2c': 'unbind',
  '02afc31e325152cc47cc8e7012dfaa56': 'Amount display item settings',
  '828d5216aaca380fff8c7e83abd11da6': 'Bind Key',
  '7d86892e15fc98bdd412835a57d47f69': 'Number of Customers',
  '4b4ab48b032c9ad8d08d2c2288092120':
    'Customer Information Display Item Settings',
  '818279bcca08d700b3c5705f3b16e4a8': 'Back to the previous',
  '286f047c9187daa51a038a39aa69ac09': 'Back to Page Designer',
  'a44afb46f963e10047f918c240cb53cc': 'Journey Designer',
  '98d0b1b0ac58199821be7d3eb42b2536': 'Flow Designer',
  '151ec3dc757d5beb308788d18c80146b': 'Description:',
  '22b4334f915541755261249f38d0df9b': 'Draft',
  '0044f62f5599155c3578c6110f7a52f0': 'Retired',
  '8428a3096a2c8bd8c81c369896820364': 'No Copy',
  'a496e15c79cdecae1c001c228baddcce': 'Flow API',
  '11b1a77a90b1ca5534f0c16cb6254308': 'Native API',
  '5e4863b5bf13c09673738a8e362d8a54': 'Flow service',
  'e13030ac144576fed24ae2a67716b982': 'Variable Name:',
  'eaaa50407ec2b696143350bd33729b4f': 'title:',
  '3925f08a9614c06aa6401ffd34f3b2be': 'Value format:',
  '8b393188c8be2173483c2fc5780f6957': 'Default value:',
  'f8d38a48716370be187d524911da4f9b': 'Client persistence:',
  '9d65105f27b89f0a2fb4184382e02211': 'Data Scope:',
  'f120a0cd2a1fc8231020ca0a83b3afbc': 'Save and update',
  '1008677b6a86f453b384a68223df5283': 'Update Page',
  '9b601b8efa642f1b3c8159cd20d061b3': 'Current Version',
  '64922b0a2e653fe745c50f0cbe3756d6': 'Switch succeeded, please save!',
  '08e7e3bcde6e3b97b4777c868b39fbb5': 'Data has been rolled back successfully!',
  '13667f31c58766d157ef45c4580b87f6': 'Reversion History',
  '14cb7d323bb5d4cbf1205505ae91c1ab': 'Rollback History',
  '696b48ca6b2ec7e61084f127ea0f1d24': 'Are you sure you want to roll back to this version?',
  'd00b485b26c28f3df105a5a48bdde2b0': 'Rollback'
});
