import {
  BaseEventContext,
  BasePlugin,
  getSchemaTpl,
  registerEditorPlugin,
  RendererPluginEvent,
  tipedLabel,
} from 'amis-editor-core';
import { getEventControlConfig } from '../../renderer/event-control/helper';

export class PosOrderListPlugin extends BasePlugin {
  static id = 'OrderPlugin';
  rendererName = 'pad-pos-order-list'; // 这里需要和注册的渲染器名称保持一致
  $schema = '/schemas/OrderListSchema.json'; // 目前还不清楚这个字段的作用

  tags = ['Order Entry']; // 组名称
  name = '订单列表';
  isBaseComponent = false; // 控制渲染器在编辑器组件面板中的位置
  isBusinessComponent = true;
  description = '订单列表';

  // 当组件拖入编辑器时，默认插入的schema，参数会作为props传递给组件
  scaffold = {
    type: 'pad-pos-order-list',
    // sourceQrySubsDetail: {
    //   // 默认查询订户详情的数据源
    //   url: '../easycode/api/execute/3277/qrySubsDetail',
    //   datasource: 'logic-flow',
    //   method: 'post',
    //   requestAdaptor: '',
    //   adaptor: '',
    //   messages: {},
    //   logicFlowService: 3277
    // }
  };

  previewSchema = {
    ...this.scaffold,
  };

  panelTitle = '订单列表';

  // 事件定义
  events: RendererPluginEvent[] = [];
  // [
  //   {
  //     eventName: 'onLowCodeBusiness',
  //     eventLabel: '二次业务点击',
  //     description: '二次业务点击',
  //     defaultShow: false,
  //     dataSchema: (manager: EditorManager) => {
  //       return [
  //         {
  //           type: 'object',
  //           properties: {
  //             subsInfo: {
  //               type: 'object',
  //               title: '订户详情',
  //             },
  //             subsEventId: {
  //               type: 'number',
  //               title: '订户事件Id',
  //             },
  //           },
  //         },
  //       ];
  //     },
  //   },
  // ];

  panelBodyCreator = (context: BaseEventContext) => {
    return [
      getSchemaTpl('tabs', [
        {
          title: '属性',
          body: getSchemaTpl('collapseGroup', [
            {
              title: '基本',
              body: [
                {
                  type: 'input-text',
                  name: 'custId',
                  label: '客户标识',
                },
                getSchemaTpl('apiControl', {
                  name: 'source',
                  label: tipedLabel('数据源', '订单列表数据源'),
                }),
                // getSchemaTpl('apiControl', {
                //   name: 'sourceQrySubsDetail',
                //   label: tipedLabel('订户详情数据源', '查询订户详情数据源')
                // })
              ],
            },
          ]),
        },
        {
          title: '外观',
          className: 'p-none',
          body: getSchemaTpl('collapseGroup', [
            {
              title: '基本',
              body: [
                getSchemaTpl('layout:max-height', {
                  name: 'style.width',
                  label: '宽度',
                }),
                getSchemaTpl('theme:paddingAndMargin', {
                  name: 'themeCss.baseControlClassName.padding-and-margin:default',
                }),
              ],
            },
          ]),
        },
        {
          title: '事件',
          className: 'p-none',
          body: getSchemaTpl('eventControl', {
            name: 'onEvent',
            ...getEventControlConfig(this.manager, context),
          }),
        },
      ]),
    ];
  };
}
registerEditorPlugin(PosOrderListPlugin);
