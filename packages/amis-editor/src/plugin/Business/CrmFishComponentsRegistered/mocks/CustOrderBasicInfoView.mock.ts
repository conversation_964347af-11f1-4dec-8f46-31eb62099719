export const custOrderBasicInfoViewMockData = {
  custOrder: {
    SERV_TYPE: '79',
    ACCEPT_DATE: '2025-08-04 11:10:32',
    FIX_INSTALL_ORDER: [],
    CUST_ORDER_ID: ****************,
    OFFER_ID: '200277',
    CONTACT_CHANNEL_ID: 1,
    ORDER_ITEM: [
      {
        SERV_TYPE: 79,
        DP_OFFER_ORDER: [
          {
            SP_ID: 0,
            SUBSCRIBE_METHOD: 'OFFER_GROUP_DEFAULT',
            OFFER_TYPE: '4',
            EFF_TYPE: 'B',
            OFFER_GROUP_ID: 100301,
            ROUTING_ID: 0,
            OFFER_ID: 200320,
            OFFER_SEQ: '290ac161-f99e-4b65-ab50-2efe07064d1a',
            TRIGGER_OBJ_ID: 100301,
            DP_ORDER_ID: ****************,
            DEFAULT_FLAG: 'Y',
            OFFER_CODE: 'Broadband',
            OFFER_NAME: 'Broadband',
            OPERATION_TYPE: 'A',
            PART_ID: 308,
            EXITS_AGREEMENT_FLAG: 'N',
            OFFER_INST_ID: *********,
            ORDER_ITEM_ID: ****************,
            HIDE_FLAG: 'Y',
          },
        ],
        ACCT: {
          PAYMENT_METHOD_ID: 1,
          CUST_ID: *********,
          ACCT_ID: *********,
          STATE: 'A',
          BILLING_CYCLE_TYPE_ID: 2,
          ACCT_NAME: 'ztt0605',
          ACCT_NBR: '**********',
          POSTPAID: 'Y',
        },
        SUBS_PLAN_NAME: 'Broadband',
        CUST_ORDER_ID: ****************,
        CUST_INFO: {
          CUST_ID: *********,
          CUST_TYPE: 'A',
        },
        OFFER_ID: 200277,
        INDEP_PROD_ORDER_ATTR: [
          {
            ATTR_VALUE: '0',
            SP_ID: 0,
            ATTR_ID: 101075,
            ORDER_ITEM_ID: ****************,
            OPERATION_TYPE: 'A',
          },
        ],
        SUBS_EVENT_ID: 1,
        PARTY_TYPE: 'A',
        ORDER_NBR: '2025080416410222',
        ACCT_ID: *********,
        IS_RESERVE: false,
        ORDER_TYPE: 'B',
        OPERATION_TYPE: 'A',
        PART_ID: 308,
        RES_ORDER: [
          {
            EXT_RES_INFO: '{"areaId":1065,"extMap":{},"hlrId":1,"valid":true}',
            RES_TYPE: 'A',
            OPERATION_TYPE: 'A',
            RES_INST_NBR: '<EMAIL>',
            RES_INST_ID: 4100257,
            ORDER_ITEM_ID: ****************,
          },
        ],
        IS_BOOKING: true,
        SUBS_PLAN_ID: 200319,
        CUST_ID: *********,
        STAFF_ID: 1,
        SP_ID: 0,
        ROUTING_ID: 0,
        STATE_DATE: '2025-08-04 11:10:32',
        ACC_NBR_ID: 4100257,
        ORDER_STATE: 'I',
        PREFIX: '60',
        RES_RESERVE_INST_ID: '****************',
        POSTPAID: 'Y',
        SUBS_BASE_ORDER: {
          SERV_TYPE: 79,
          CUST_ID: *********,
          ORG_ID: 1046,
          USER_ID: *********,
          SUBS_PLAN_NAME: 'Broadband',
          AREA_NAME: 'Malaysia',
          ROUTING_ID: 0,
          DEF_LANG_ID: 30,
          POSTPAID: 'Y',
          AREA_ID: 1058,
          DEF_LANG_NAME: 'Bahasa',
          INDEP_PROD_SPEC_ID: 200277,
          ACCT_ID: *********,
          USER_NAME: 'ztt0605',
          OPERATION_TYPE: 'A',
          PART_ID: 308,
          SUBS_PLAN_ID: 200319,
          ORDER_ITEM_ID: ****************,
          ACCT_NBR: '**********',
          CUST_NAME: 'ztt0605',
        },
        BUNDLE_MEMBER_FLAG: 'N',
        SUBS_ID: -********,
        OFFER_NAME: 'Broadband',
        ACC_NBR: '<EMAIL>',
        IS_JOB_OPERATION: false,
        ORDER_ITEM_ID: ****************,
        CUST_NAME: 'ztt0605',
        PARTY_CODE: '1259',
      },
    ],
    CUST_ORDER_VER: {
      UPDATE_DATE: '2025-08-04 11:10:32',
      CREATED_DATE: '2025-08-04 11:10:32',
      STATE: 'A',
      CUST_ORDER_ID: ****************,
      CUST_ORDER_VER_ID: ****************,
    },
    CUST: {
      CUST_ID: *********,
      ZIPCODE: '59200',
      CUST_TYPE: 'A',
      STD_ADDR_ID: 592000,
      ADDRESS: '963',
      CUST_CODE: '1*********',
      CERT_ID: *********,
      CUST_NAME: 'ztt0605',
    },
    SUBS_EVENT_ID: 1,
    CUST_ORDER_STATE: 'I',
    PARTY_TYPE: 'A',
    READ_CARD_FLAG: 'N',
    PART_ID: 308,
    CUST_ID: *********,
    CREATED_DATE: '2025-08-04 11:10:32',
    SP_ID: 0,
    HANDLED_BY_ACCT_MANAGER: 'N',
    CUST_ORDER_ATTR: [
      {
        ROUTING_ID: 0,
        SP_ID: 0,
        ATTR_ID: 100777,
        CUST_ORDER_ID: ****************,
        VALUE: 'N',
        PART_ID: 308,
      },
    ],
    ROUTING_ID: 0,
    A_PARTY_CODE: '*********',
    RES_RESERVE_INST_ID: '****************',
    IS_V9_SERVICE_MODE: 'Y',
    SUBS_EVENT_NAME: 'New Connection',
    CUST_ORDER_NBR: '****************',
    CREDIT_LIMIT_MODE: 'O',
    A_PARTY_TYPE: 'D',
    STAFF_INFO: {
      AREA_ID: 1058,
      ORG_ID: 1046,
      ORG_TYPE: 'B',
      STAFF_ID: 1,
      STAFF_JOB_ID: 1259,
    },
    PARTY_CODE: '1259',
  },
  cust: {
    CUST_ID: *********,
    ZIPCODE: '59200',
    CUST_TYPE: 'A',
    STD_ADDR_ID: 592000,
    ADDRESS: '963',
    CUST_CODE: '1*********',
    CERT_ID: *********,
    CUST_NAME: 'ztt0605',
  },
  genderArray: [
    {
      dataValueArray: ['F', 'Female'],
    },
    {
      dataValueArray: ['M', 'Male'],
    },
    {
      dataValueArray: ['O', 'Undisclosed'],
    },
  ],
  commonDataId: '0swzWgr8xojKe223SgMT9XMNra7t3jAi',
};
