import {
  BaseEventContext,
  BasePlugin,
  getSchemaTpl,
  registerEditorPlugin,
  RendererPluginEvent,
} from 'amis-editor-core';
import { getEventControlConfig } from '../../../renderer/event-control/helper';
import { stringifyJson } from '../../../utils/stringify-json.help';
import { custOrderBasicInfoViewMockData } from './mocks/CustOrderBasicInfoView.mock.ts';

export class CustOrderBasicInfoViewPlugin extends BasePlugin {
  static id = 'CustOrderBasicInfoViewPlugin';
  rendererName = 'crm-cust-order-basic-info-view'; // 这里需要和注册的渲染器名称保持一致
  $schema = '/schemas/CustOrderBasicInfoViewSchema.json'; // 目前还不清楚这个字段的作用

  tags = ['COC(Fish)']; // 组名称
  name = '客户订单基本信息';
  isBaseComponent = false; // 控制渲染器在编辑器组件面板中的位置
  isBusinessComponent = true;
  description = '客户订单基本信息';

  // 当组件拖入编辑器时，默认插入的schema，参数会作为props传递给组件
  scaffold = {
    type: 'crm-cust-order-basic-info-view',
    name: 'custOrderBasicInfoView',
    custOrderBasicInfoViewOptionsData: stringifyJson(custOrderBasicInfoViewMockData),
    editorSetting: {
      mock: {
        custOrderBasicInfoViewMockData
      },
    },
  };

  previewSchema = {
    ...this.scaffold,
  };

  panelTitle = '客户订单基本信息';

  // 事件定义
  events: RendererPluginEvent[] = [];

  panelBodyCreator = (context: BaseEventContext) => {
    return [
      getSchemaTpl('tabs', [
        {
          title: '属性',
          body: [
            getSchemaTpl('formulaControl', {
              label: '客户订单基本信息',
              name: 'custOrderBasicInfoViewOptionsData',
            }),
          ],
        },
        {
          title: '外观',
          className: 'p-none',
          body: getSchemaTpl('collapseGroup', [
            {
              title: '基本',
              body: [
                {
                  type: 'input-number',
                  label: '长度',
                  min: 0,
                  name: 'style.width',
                },
                {
                  type: 'input-number',
                  label: '高度',
                  min: 1,
                  name: 'style.height',
                },
              ],
            },
            {
              header: '内外边距',
              key: 'box-model',
              body: [
                {
                  type: 'style-box-model',
                  label: false,
                  name: 'style',
                }
              ],
            },
            {
              header: '边框',
              key: 'border',
              body: [
                {
                  type: 'style-border',
                  label: false,
                  name: 'style',
                  disableRadius: true,
                }
              ],
            },
            {
              title: '背景',
              body: [
                {
                  type: 'style-background',
                  label: false,
                  name: 'style',
                  noImage: true,
                }
              ],
            }
          ])
        },
        {
          title: '事件',
          className: 'p-none',
          body: getSchemaTpl('eventControl', {
            name: 'onEvent',
            ...getEventControlConfig(this.manager, context),
          }),
        }
      ])
    ];
  };
}
registerEditorPlugin(CustOrderBasicInfoViewPlugin);
  