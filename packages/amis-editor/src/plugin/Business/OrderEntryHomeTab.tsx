import {
  registerEditorPlugin,
  BaseEventContext,
  BasePlugin,
  getSchemaTpl,
  RendererPluginEvent,
  PluginEvent,
  PreventClickEventContext
} from 'amis-editor-core';

export class PosOrderEntryHomeTabPlugin extends BasePlugin {
  static id = 'OrderEntryHomeTabPlugin';
  rendererName = 'pad-pos-order-entry-home-tab'; // 这里需要和注册的渲染器名称保持一致
  $schema = '/schemas/OrderEntryHomeTabSchema.json'; // 目前还不清楚这个字段的作用

  tags = ['Order Entry']; // 组名称
  name = '订单入口';
  isBaseComponent = false; // 控制渲染器在编辑器组件面板中的位置
  isBusinessComponent = true;
  description = '订单入口';

  // 当组件拖入编辑器时，默认插入的schema，参数会作为props传递给组件
  scaffold = {
    type: 'pad-pos-order-entry-home-tab',
    subscriberList: {
      type: 'container',
      wrapperBody: false,
      body: {
        type: 'pad-pos-subscriber-list'
      }
    },
    accountList: {
      type: 'container',
      wrapperBody: false,
      body: {
        type: 'pad-pos-account-list'
      }
    },
    orderList: {
      type: 'container',
      wrapperBody: false,
      body: {
        type: 'pad-pos-order-list',
      },
    }
  };

  previewSchema = {
    ...this.scaffold
  };

  panelTitle = '订单入口';

  // 事件定义
  events: RendererPluginEvent[] = [];

  panelBodyCreator = (context: BaseEventContext) => {
    return [
      getSchemaTpl('tabs', [
        {
          title: '属性',
          body: getSchemaTpl('collapseGroup', [
            {
              title: '基本',
              body: [
                {
                  type: 'input-text',
                  label: '客户标识',
                  name: 'custId'
                },
                getSchemaTpl('apiControl', {
                  label: 'Subscriber Count',
                  name: 'subscriberCount'
                }),
                getSchemaTpl('apiControl', {
                  label: 'Account Count',
                  name: 'accountCount'
                }),
                getSchemaTpl('apiControl', {
                  label: 'Order Count',
                  name: 'orderCount'
                }),
                getSchemaTpl('apiControl', {
                  label: 'Query Subscriber List',
                  name: 'subscriberList'
                }),
                getSchemaTpl('apiControl', {
                  label: 'Query Account List',
                  name: 'acctListApi'
                }),
                getSchemaTpl('apiControl', {
                  label: 'Query Address',
                  name: 'addressApi'
                }),
                getSchemaTpl('apiControl', {
                  label: 'Query Billing Cycle Type',
                  name: 'billingCycleTypeApi'
                }),
                getSchemaTpl('apiControl', {
                  label: 'Area Code List',
                  name: 'areaCodeListApi'
                }),
                getSchemaTpl('apiControl', {
                  label: 'Delivery Method',
                  name: 'deliveryMethodApi'
                }),
                getSchemaTpl('apiControl', {
                  label: 'Add Account',
                  name: 'addAcctApi'
                }),
                getSchemaTpl('apiControl', {
                  label: 'Query Customer Order Page',
                  name: 'custOrderPageApi'
                }),
                getSchemaTpl('apiControl', {
                  label: 'Query Customer Order Detail',
                  name: 'custOrderDetailApi'
                }),
                getSchemaTpl('apiControl', {
                  label: 'Query Channel List',
                  name: 'channelListApi'
                }),
                getSchemaTpl('apiControl', {
                  label: 'Query Subscriber Event List',
                  name: 'subsEventApi'
                }),
                getSchemaTpl('apiControl', {
                  label: 'Query Order State',
                  name: 'orderStateApi'
                })
              ]
            }
          ])
        },
        {
          title: '外观',
          className: 'p-none',
          body: getSchemaTpl('collapseGroup', [
            {
              title: '基本',
              body: [
                {
                  type: 'input-number',
                  label: '长度',
                  min: 0,
                  name: 'style.width'
                },
                {
                  type: 'input-number',
                  label: '高度',
                  min: 1,
                  name: 'style.height'
                }
              ]
            },
            // 兼容旧的外观面板
            {
              header: '文字',
              key: 'font',
              body: [
                {
                  type: 'style-font',
                  label: false,
                  name: 'style'
                }
              ]
            },
            {
              header: '内外边距',
              key: 'box-model',
              body: [
                {
                  type: 'style-box-model',
                  label: false,
                  name: 'style'
                }
              ]
            },
            {
              header: '边框',
              key: 'border',
              body: [
                {
                  type: 'style-border',
                  label: false,
                  name: 'style',
                  disableRadius: true
                }
              ]
            },
            {
              title: '背景',
              body: [
                {
                  type: 'style-background',
                  label: false,
                  name: 'style',
                  noImage: true
                }
              ]
            },
            getSchemaTpl('style:classNames', {isFormItem: false})
          ])
        }
      ])
    ];
  };

  onPreventClick(e: PluginEvent<PreventClickEventContext>) {
    const mouseEvent = e.context.data;

    // 点击tab响应对应的事件
    if ((mouseEvent.target as HTMLElement).closest('[role=tablist]')) {
      return false;
    }
  }
}

registerEditorPlugin(PosOrderEntryHomeTabPlugin);
