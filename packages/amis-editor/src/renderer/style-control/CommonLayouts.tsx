/*
 * @Description: 常用布局
 * @Author: wang.minglei[0027012155]
 * @Date: 2025-04-15 15:40:41
 * @LastEditors: wang.minglei[0027012155]
 * @LastEditTime: 2025-05-08 14:06:07
 */
import { Html, Icon, TooltipWrapper } from 'amis';
import { FormControlProps, FormItem, noop, render } from 'amis-core';
import cx from 'classnames';
import React, { Fragment, useState } from 'react';

import { IconImg } from 'amis-editor-core';
import Layout1_1 from '../../../examples/layout/plugin/Layout1_1';
import Layout1_1_1 from '../../../examples/layout/plugin/Layout1_1_1';
import Layout1_1_1_v2 from '../../../examples/layout/plugin/Layout1_1_1_v2';
import Layout1_1_1_v2_fixed_top_bottom from '../../../examples/layout/plugin/Layout1_1_1_v2_fixed_top_bottom';
import Layout1_1_fixed_left from '../../../examples/layout/plugin/Layout1_1_fixed_left';
import Layout1_1_fixed_right from '../../../examples/layout/plugin/Layout1_1_fixed_right';
import Layout1_1_v2 from '../../../examples/layout/plugin/Layout1_1_v2';
import Layout1_1_v2_fixed_bottom from '../../../examples/layout/plugin/Layout1_1_v2_fixed_bottom';
import Layout1_1_v2_fixed_top from '../../../examples/layout/plugin/Layout1_1_v2_fixed_top';
import Layout1_2 from '../../../examples/layout/plugin/Layout1_2';
import Layout1_2_3 from '../../../examples/layout/plugin/Layout1_2_3';
import Layout1_2_v2 from '../../../examples/layout/plugin/Layout1_2_v2';
import Layout1_2_v3 from '../../../examples/layout/plugin/Layout1_2_v3';
import Layout1_2_v4 from '../../../examples/layout/plugin/Layout1_2_v4';
import Layout1_3 from '../../../examples/layout/plugin/Layout1_3';
import Layout2_1_v2 from '../../../examples/layout/plugin/Layout2_1_v2';
import Layout2_1_v3 from '../../../examples/layout/plugin/Layout2_1_v3';
import Layout_scroll_x from '../../../examples/layout/plugin/Layout_scroll_x';
import Layout_scroll_y from '../../../examples/layout/plugin/Layout_scroll_y';

const CommonLayouts: React.FC<FormControlProps> = props => {
  const { onChange } = props;
  const [activeName, setActiveName] = useState<string>('');
  const lrLayout = [
    Layout1_1, // 左右均分,
    Layout1_1_fixed_left, // 左(固定)右(自适应)布局
    Layout1_1_fixed_right, // 左(自适应)右(固定)布局
    Layout1_2, // 1:2 布局
    Layout1_3, // 1:3 布局
    Layout1_2_v3, // 左一右二
    Layout2_1_v3, // 左二右一
    Layout1_1_1, // 三栏均分
    Layout1_2_3, // 1:2:3 三栏
    Layout_scroll_x, // x轴滚动容器
  ];
  const tbLayout = [
    Layout1_1_v2, // 上下布局
    Layout1_1_v2_fixed_top, // 上(固定)下(自适应)布局
    Layout1_1_v2_fixed_bottom, // 上(自适应)下(固定)布局
    Layout1_1_1_v2, // 上中下
    Layout1_1_1_v2_fixed_top_bottom, // 上(固定)中(自适应)下(固定)
    Layout1_2_v2, // 上一下二
    Layout2_1_v2, // 上二下一
    Layout1_2_v4, // 经典布局
    Layout_scroll_y, // y轴滚动容器
  ];
  const store = props.manager.store;
  const customRenderersByOrder = store.customRenderersByOrder || [];
  const handleClick = (renderComp: any) => {
    setActiveName(renderComp.plugin.constructor.name);
    const { scaffold } = renderComp;
    onChange?.({ scaffold });
  };

  const renderThumb = (schema: any) => {
    const manager = props.manager;
    return schema ? (
      render(
        schema,
        {
          onAction: noop,
        },
        manager.env,
      )
    ) : (
      <p>没有预览图</p>
    );
  };

  return (
    <div className="ae-CommonLayouts">
      {[
        { structure: '上下结构', list: tbLayout },
        { structure: '左右结构', list: lrLayout },
      ].map((item: { structure: string; list: any }, index) => {
        return (
          <Fragment key={index}>
            <div className="ae-CommonLayouts-title">{item.structure}</div>
            <div className="ae-CommonLayouts-wrap">
              {item.list.map((item: any) => {
                const name = item.name;
                const renderComp = customRenderersByOrder.find(
                  r => r.plugin.constructor.name === name,
                );
                if (!renderComp) {
                  return null;
                }
                return (
                  <div className="ae-CommonLayouts-item" key={name}>
                    <div
                      className={cx('ae-CommonLayouts-imgBox', {
                        active: activeName === name,
                      })}
                      data-dnd-id={renderComp.id}
                      onClick={() => handleClick(renderComp)}
                    >
                      <IconImg
                        className="img"
                        iconName={renderComp.pluginIcon}
                      />
                      <div onClick={e => e.stopPropagation()}>
                        <TooltipWrapper
                          tooltipClassName="ae-CommonLayouts-RendererThumb"
                          trigger="hover"
                          rootClose={true}
                          placement="right"
                          tooltip={{
                            offset: [10, -10], // x轴偏移，避免遮挡边框
                            children: () => (
                              <div>
                                <div className="ae-CommonLayouts-Renderer-title">
                                  {renderComp.name}
                                </div>
                                {renderComp.description ||
                                renderComp.docLink ? (
                                  <div className="ae-CommonLayouts-Renderer-info">
                                    <Html
                                      html={
                                        renderComp.description
                                          ? renderComp.description
                                          : ''
                                      }
                                    />
                                  </div>
                                ) : null}
                                <div className="ae-CommonLayouts-Renderer-preview">
                                  {renderThumb(renderComp.previewSchema)}
                                </div>
                              </div>
                            ),
                          }}
                        >
                          <div className="ae-CommonLayouts-RendererIcon">
                            <Icon icon="editor-help" className="icon" />
                          </div>
                        </TooltipWrapper>
                      </div>
                    </div>
                    <div className="ae-CommonLayouts-nameBox">
                      <div
                        className="ae-CommonLayouts-Renderer-info"
                        title={renderComp.name}
                      >
                        {renderComp.name}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </Fragment>
        );
      })}
    </div>
  );
};

@FormItem({ type: 'common-layouts' })
export class CommonLayoutsRenderer extends React.Component<FormControlProps> {
  render() {
    return <CommonLayouts {...this.props} />;
  }
}
