/* eslint-disable no-shadow */
/* eslint-disable no-param-reassign */
/* eslint-disable no-redeclare */
/* eslint-disable no-nested-ternary */
// 忽视的目的：保持原有的业务逻辑，不能随便修改
/**
 * 联系人选择组件
 * 入参：
 *      $el：占位元素
 *      option：orderItem：Bo
 *
 */

/**
 * 联系人选择组件
 * @param {jQuery} $el - 组件挂载的DOM元素
 * @param {Object} option - 配置选项
 * @param {Object} option.orderItem - 订单项BO对象
 * @param {Object} option.custOrder - 客户订单对象
 * @param {string} option.commonDataId - 公共数据ID
 * @param {Array} option.subsContactManOrderList - 订户联系人订单列表
 * @param {string} option.contactManType - 联系人类型
 * @param {string} option.contactManTitle - 联系人标题
 * @param {string} option.installOrderId - 安装订单ID
 * @param {string} [option.contactObjLevel=ContactObjLevelDef.SUBSCRIBER] - 联系人对象级别
 * @param {string} [option.boAccessName='subsContactManOrder'] - BO访问名称
 * @param {Object} [option.externalCustomObj] - 外部自定义对象
 * @param {string} [option.externalCustomObj.customClassName] - 自定义类名
 * @param {Function} [option.externalCustomObj.onCallbackValues] - 回调函数，返回选中的联系人
 * @param {Function} [option.externalCustomObj.onClearValues] - 回调函数，数据清空时触发
 * @param {Object} context - 上下文对象
 */
define([
    'hbs!crm/modules/fix/InstallInfo/templates/AddContactManComponentTpl.hbs',
    'i18n!crm/modules/pos/orderentry/i18n/OrderEntry',
    'crm/modules/common/util/ObjectUtils',
    'crm/modules/fbf/bfm/models/DataInputs',
    'crm/modules/contact/actions/ContactManAction',
    'crm/modules/common/constant/OperationTypeDef',
    'crm/modules/fbf/bfm/models/DataInput',
    'crm/modules/fbf/bfm/utils/Controller',
    'crm/modules/common/constant/ContactObjLevelDef',
    'crm/modules/common/constant/ManagerContactManTypeDef',
    'crm/modules/pos/common/utils/BoUtils',
    'crm/modules/pos/flowpage/bundle/utils/BundleUtils',
    'crm/modules/fix/common/util/ForbidOpUtil',
    'crm/modules/pos/common/constant/SubsEventDef',
    'crm/modules/common/util/StringUtils'
], function (
    AddContactManComponentTpl,
    I18N,
    ObjectUtils,
    DataInputs,
    ContactManAction,
    OperationTypeDef,
    DataInput,
    Controller,
    ContactObjLevelDef,
    ManagerContactManTypeDef,
    BoUtils,
    BundleUtils,
    ForbidOpUtil,
    SubsEventDef,
    StringUtils
) {
    return function ($el, option, context) {
        $el.append(AddContactManComponentTpl(I18N));
        option = option || {};

        // MODIFIED: 获取外部自定义对象
        this.externalCustomObj = option.externalCustomObj || {};

        this.orderItem = option.orderItem;
        this.custOrder = option.custOrder;
        this.commonDataId = option.commonDataId;
        this.subsContactManOrderList = option.subsContactManOrderList;
        this.contactManType = option.contactManType;
        this.contactManTitle = option.contactManTitle;
        this.contactManInfo = null;
        this.installOrderId = option.installOrderId;
        this.contactObjLevel = option.contactObjLevel || ContactObjLevelDef.SUBSCRIBER;
        this.boAccessName = option.boAccessName || 'subsContactManOrder';
        this.installationContactManList = [];
        this.subsContactManList = [];
        this.newCustId = null;
        this.currentMenu = portal.appGlobal.get('currentMenu');
        this.$menuDiv = $('#PORTAL_DIV_MENU_' + this.currentMenu.menuId);
        this.postModalFlag = portal.appGlobal.get('customBackdrop') == 'Y';
        if (this.contactObjLevel == ContactObjLevelDef.CUSTOMER) {
            // 查询当前客户下的所有客户级的安装联系人
            var param = {
                custId: this.orderItem.CUST_ID,
                state: 'A',
                custContactManFlag: 'Y', // 只查询客户级别的联系人
                contactManType: this.contactManType
            };
            ContactManAction.qryContactManInfo(
                param,
                {},
                function (dataList) {
                    this.installationContactManList = dataList.contactManList || [];
                }.bind(this),
                false
            );
        } else if (this.orderItem.SUBS_ID && this.orderItem.SUBS_ID > 0) {
            // 查询当前订户下的HQ联系人
            var param = {
                custId: this.orderItem.CUST_ID,
                subsId: this.orderItem.SUBS_ID,
                state: 'A',
                contactManType: this.contactManType
            };
            ContactManAction.qryContactManInfo(
                param,
                {},
                function (dataList) {
                    this.subsContactManList = dataList.contactManList || [];
                }.bind(this),
                false
            );
        }

        this.dealContactManData = function (contactManData, contactMan) {
            delete contactManData.custId;
            delete contactManData.acctId;
            delete contactManData.subsId;
            delete contactManData.acctName;
            delete contactManData.acctNbr;
            delete contactManData.accNbr;
            delete contactManData.contactManType;
            delete contactManData.contactManTypeName;

            contactManData.custId = this.orderItem.CUST_ID;
            if (!fish.isEmpty(contactMan)) {
                contactManData.primaryFlag = contactMan.primaryFlag || 'N';
            }
            contactManData.contactManType = this.contactManType;
            contactManData.managerContactManType = ManagerContactManTypeDef.CONTACT_MAN;
            if (this.contactObjLevel === ContactObjLevelDef.SUBSCRIBER) {
                contactManData.subsId = this.orderItem.SUBS_ID;
                contactManData.accNbr = this.orderItem.ACC_NBR;
            }
        };

        /**
         * 适配installInfo组件样式
         */
        this.dealCompStyle = function (orderItem) {
            if (orderItem && orderItem.VIRTUAL_FLAG === 'Y') {
                let $contact = $('.js-contact-info');
                if ($contact.children() && $contact.children().length > 0) {
                    fish.forEach($contact.children(), function (child) {
                        // 将最外层的div样式改为col-md-6 匹配组件样式
                        if (child.tagName === 'DIV') {
                            $(child).removeClass(function (index, className) {
                                return (className.match(/(^|\s)(col-sm|col-md|col-lg)-\S+/g) || []).join(' ');
                            });
                            $(child).addClass('col-sm-6 col-md-6 col-lg-6');
                        }
                    });
                }
            }
        };

        this.create = function () {
            if (this.subsContactManOrderList && this.subsContactManOrderList.length > 0) {
                this.contactManOrder = fish.find(
                    this.subsContactManOrderList,
                    function (item) {
                        return this.contactManType == item.CONTACT_MAN_TYPE && item.OPERATION_TYPE != OperationTypeDef.CANCEL;
                    }.bind(this)
                );
            }

            if (this.contactManOrder && this.contactManOrder.SUBS_CONTACT_MAN_ORDER_SEQ) {
                this.subsContactManOrderSeq = this.contactManOrder.SUBS_CONTACT_MAN_ORDER_SEQ;
                this.contactManOrderId = this.contactManOrder.CONTACT_MAN_ORDER_ID;
            } else {
                this.subsContactManOrderSeq = fish.getUUID();
            }
            // 初始化HQ Contact组件
            $el.find('#contactManTitle').text(this.contactManTitle);
            $el.find('#contactManTitle').attr('title', this.contactManTitle);

            this.$contactMan = $el.find('.js-contact-man');
            if (this.contactManTitle) {
                this.$contactMan.prop('name', StringUtils.toCamelTypeName(this.contactManTitle.split(' ').join('_')));
            }
            var contactManData = {
                contactManType: this.contactManType
            };
            var initParam = {
                contactObjLevel: this.contactObjLevel,
                custId: this.orderItem.CUST_ID,
                isIntegrated: true,
                isEditContactType: 'N',
                isCustFilter: 'Y',
                isContactTypeFilter: 'N',
                isEditExistContact: this.contactObjLevel == ContactObjLevelDef.SUBSCRIBER ? 'Y' : 'N',
                isComponent: 'Y', // 组件调用
                contactMan: contactManData,
                operType: 'new',
                isFixSupportModContact: 'Y',
                isFixFlow: 'Y',
                commonDataId: this.commonDataId
            };
            if (this.contactObjLevel === ContactObjLevelDef.SUBSCRIBER) {
                initParam.subsId = this.orderItem.SUBS_ID;
                initParam.accNbr = this.orderItem.ACC_NBR;
                contactManData.serviceNbr = this.orderItem.ACC_NBR;
            }
            if (!fish.isEmpty(this.orderItem.CUST_INFO)) {
                initParam.custId = this.orderItem.CUST_INFO.CUST_ID;
                initParam.custType = this.orderItem.CUST_INFO.CUST_TYPE;
            }
            if (!this.orderItem.ORDER_ITEM_ID || this.orderItem.ORDER_ITEM_ID < 0) {
                initParam.hideCurrentOrderSelect = 'Y';
            }

            // MODIFIED：传入外部自定义类名
            if (this.externalCustomObj && this.externalCustomObj.customClassName) {
                initParam.customClassName = this.externalCustomObj.customClassName;
            }

            this.$contactMan.popedit({
                open: function () {
                    // 重新初始化一次所有安装联系人列表，因为维护联系人那边新增之后这边需要重新查询
                    if (this.contactObjLevel == ContactObjLevelDef.CUSTOMER) {
                        // 查询当前客户下的所有客户级的安装联系人
                        var param = {
                            custId: this.orderItem.CUST_ID,
                            state: 'A',
                            custContactManFlag: 'Y', // 只查询客户级别的联系人
                            contactManType: this.contactManType
                        };
                        ContactManAction.qryContactManInfo(
                            param,
                            {},
                            function (dataList) {
                                this.installationContactManList = dataList.contactManList || [];
                            }.bind(this),
                            false
                        );
                    }
                    if (!fish.isEmpty(this.contactManInfo)) {
                        initParam.contactMan = this.contactManInfo;
                    }
                    if (!fish.isEmpty(this.contactManInfo) && this.contactManInfo.contactManId) {
                        initParam.isFixSupportModContact == 'Y' ? (initParam.operType = 'view') : (initParam.operType = 'edit');
                        initParam.isFixSupportModContact == 'Y' ? (initParam.isSupportEdit = 'Y') : (initParam.isSupportEdit = 'N');
                    } else {
                        initParam.contactMan = { contactManType: this.contactManType };
                        initParam.operType = 'new';
                        delete initParam.isSupportEdit;
                    }
                    if (initParam.operType === 'new') {
                        initParam.isDefaultSelect = 'Y';
                        initParam.isSupportCopy = 'Y';
                    } else {
                        delete initParam.isDefaultSelect;
                        delete initParam.isSupportCopy;
                    }
                    this.getNewCustId(initParam).then(
                        function () {
                            if (
                                this.contactObjLevel == ContactObjLevelDef.SUBSCRIBER &&
                                SubsEventDef.TRANSFER == this.orderItem.SUBS_EVENT_ID &&
                                (!this.newCustId || this.newCustId == this.orderItem.CUST_ID)
                            ) {
                                fish.warn(I18N.ORDER_ENTRY_TRANSFER_SELECT_NEW_CUST);
                                return;
                            }
                            // 显示遮罩层
                            // if (this.postModalFlag) {}

                            fish.popupView({
                                url: 'cvbs/modules/contact/fixcontact/views/popwin/AddContactManPopWin',
                                width: '80%',
                                height: '80%',
                                viewOption: initParam,
                                modal: !this.postModalFlag,
                                dismiss: function () {},
                                close: function (contactMan) {
                                    // MODIFIED：获取被选中的联系人信息
                                    if (this.externalCustomObj && typeof this.externalCustomObj.onCallbackValues === 'function') {
                                        this.externalCustomObj.onCallbackValues(contactMan);
                                    }

                                    contactMan.serviceNbr = this.orderItem.ACC_NBR;
                                    if (contactMan.isSelectExistContact) {
                                        this.chooseFlag = 'B';
                                        var item = null;
                                        if (this.contactObjLevel == ContactObjLevelDef.CUSTOMER) {
                                            // 匹配当前客户是否已经存在这个安装联系人
                                            item = fish.find(
                                                this.installationContactManList,
                                                function (installationContactMan) {
                                                    return installationContactMan.contactManId == contactMan.contactManId;
                                                }
                                            );
                                        }
                                        var param = {
                                            contactManId: contactMan.contactManId,
                                            flag: 'C'
                                        };
                                        if (!fish.isEmpty(item)) {
                                            param.managerContactManId = item.managerContactManId;
                                        } else {
                                            param.contactManName = contactMan.contactManName;
                                            param.contactManSingleFlag = 'Y';
                                        }
                                        if (!fish.isEmpty(item)) {
                                            // 存在安装联系人
                                            this.qryContactManDataAndToBo(param, contactMan);
                                        } else if (this.contactObjLevel == ContactObjLevelDef.SUBSCRIBER) {
                                            // 订户联系人
                                            this.editContactManHandler(contactMan);
                                        } else {
                                            // 新增安装联系人
                                            this.addContactManHandler(contactMan);
                                        }
                                    } else {
                                        this.chooseFlag = 'A';
                                        if (contactMan.contactManId) {
                                            this.editContactManHandler(contactMan);
                                        } else {
                                            this.addContactManHandler(contactMan);
                                        }
                                    }
                                }.bind(this)
                            });
                        }.bind(this)
                    );
                }.bind(this),
                change: function (_, data) {
                    // MODIFIED: 数据清空时回调
                    if (!data && this.externalCustomObj && typeof this.externalCustomObj.onClearValues === 'function') {
                        this.externalCustomObj.onClearValues();
                    }

                    if (data) {
                        // 批量固网开户那里没有实际的订单数据
                        if (!this.orderItem.ORDER_ITEM_ID || this.orderItem.ORDER_ITEM_ID < 0) {
                            return;
                        }
                        var contactMan = data.data;
                        var dataInput = new DataInput();
                        dataInput.set('boAccessName', 'subsContactManOrder');
                        dataInput.set('bo', true);
                        dataInput.set('orderItemId', this.orderItem.ORDER_ITEM_ID);
                        Controller.getInstance().fetchData(
                            this.commonDataId,
                            dataInput,
                            this,
                            function (result) {
                                if (!fish.isEmpty(result.dirtyList)) {
                                    var subsContactManList = result.dirtyList[0].value;
                                }
                                var existSubsContactManList = fish.filter(subsContactManList, function (item) {
                                    return (
                                        item.CONTACT_MAN_ID == contactMan.contactManId &&
                                        item.CONTACT_MAN_TYPE == contactMan.contactManType &&
                                        item.OPERATION_TYPE != OperationTypeDef.CANCEL
                                    );
                                });
                                if (fish.isEmpty(existSubsContactManList)) {
                                    this.contactManToBo(contactMan, OperationTypeDef.NEW);
                                } else {
                                    this.contactManToBo(contactMan, OperationTypeDef.CHANGE);
                                }
                            }.bind(this)
                        );
                    } else {
                        this.contactManInfo = null;
                    }
                }.bind(this)
            });

            if (ForbidOpUtil.isForbid(this.custOrder, ForbidOpUtil.FORBID_CONTRACT_NBR)) {
                this.$contactMan.popedit('disable');
            }
            // 适配其他组件样式
            this.dealCompStyle(this.orderItem);

            // 回显联系人数据
            if (this.contactManOrder && this.subsContactManList) {
                var contactManOrder = ObjectUtils.objFirstLetterLowerToSlash(this.contactManOrder);
                // 重新到后台加载
                var item = null;
                if (this.contactObjLevel == ContactObjLevelDef.CUSTOMER) {
                    // 匹配当前客户是否已经存在这个安装联系人
                    item = fish.find(
                        this.installationContactManList,
                        function (installationContactMan) {
                            return installationContactMan.contactManId == contactManOrder.contactManId;
                        }
                    );
                } else {
                    // 匹配当前订户是否已经存在这个联系人
                    item = fish.find(
                        this.subsContactManList,
                        function (subsContactMan) {
                            return subsContactMan.contactManId == contactManOrder.contactManId;
                        }
                    );
                }
                var param = {
                    contactManId: contactManOrder.contactManId,
                    flag: 'C'
                };
                if (!fish.isEmpty(item)) {
                    param.managerContactManId = item.managerContactManId;
                } else if (contactManOrder.contactManName) {
                    param.contactManName = contactManOrder.contactManName;
                    param.contactManSingleFlag = 'Y';
                    param.qryContactManFlag = 'Y';
                }
                ContactManAction.qryContactManInfo(
                    param,
                    {},
                    function (result) {
                        var contactManList = result.contactManList;
                        if (!fish.isEmpty(contactManList)) {
                            if (!param.managerContactManId) {
                                this.dealContactManData(contactManList[0], contactManOrder);
                            }
                            const result = [];
                            for (let i = 0; i < contactManList.length; i++) {
                                if (contactManList[i].contactManName) {
                                    result.push(contactManList[i]);
                                }
                            }
                            this.contactManInfo = result[0];
                            this.$contactMan.popedit('setValue', {
                                name: result[0].contactManName,
                                data: result[0]
                            });
                            initParam.contactMan = contactManList[0];
                        }
                    }.bind(this),
                    false
                );
            }
        }.bind(this)();

        this.getNewCustId = function (initParam) {
            var def = $.Deferred();

            var orderItemId = BundleUtils.getBundleOrderItemId(this.orderItem);
            // 不是Bundle，取当前订单
            if (!orderItemId) {
                orderItemId = this.orderItem.ORDER_ITEM_ID;
            }
            if (!ObjectUtils.isNullOrUndefined(orderItemId)) {
                BoUtils.fetchOrderItem(
                    orderItemId,
                    context,
                    function (orderItem) {
                        if (orderItem && orderItem.SUBS_BASE_ORDER && orderItem.SUBS_BASE_ORDER.CUST_ID) {
                            this.newCustId = orderItem.SUBS_BASE_ORDER.CUST_ID;
                            initParam.custId = orderItem.SUBS_BASE_ORDER.CUST_ID;
                        }
                        def.resolve();
                    }.bind(this),
                    this.commonDataId
                );
            } else {
                def.resolve();
            }

            return def.promise();
        };

        /**
         * 添加联系人
         *
         * @param contactMan
         */
        this.addContactManHandler = function (contactMan) {
            if (!contactMan) {
                return;
            }
            contactMan.seq = fish.getUUID();

            var contactManReq = this.getContactManReq(contactMan);
            // 只创建ContactMan
            if (this.contactObjLevel == ContactObjLevelDef.SUBSCRIBER) {
                contactManReq.onlyContactManFlag = 'Y';
                if (contactManReq.managerContactMan) {
                    var contactMan = fish.extend({}, contactManReq.managerContactMan.contactMan, contactManReq.managerContactMan);
                    delete contactMan.contactMan;
                    contactManReq.contactMan = contactMan;
                }
            }
            // 直接调用后台添加联系人
            ContactManAction.addContactMan(
                contactManReq,
                function (data) {
                    this.addContactCallback(data);
                }.bind(this),
                null,
                false
            );
        };

        /**
         * 修改联系人
         *
         * @param contactMan
         */
        this.editContactManHandler = function (contactMan) {
            if (!contactMan) {
                return;
            }
            var contactManReq = this.getContactManReq(contactMan);
            // 只修改ContactMan
            if (
                this.contactObjLevel == ContactObjLevelDef.SUBSCRIBER &&
                !(contactManReq.managerContactMan && contactManReq.managerContactMan.managerContactManId)
            ) {
                contactManReq.onlyContactManFlag = 'Y';
                if (contactManReq.managerContactMan) {
                    var contactMan = fish.extend({}, contactManReq.managerContactMan.contactMan, contactManReq.managerContactMan);
                    delete contactMan.contactMan;
                    contactManReq.contactMan = contactMan;
                }
            }
            // 直接修改联系人
            ContactManAction.modContactMan(
                contactManReq,
                function () {
                    this.editContactManCallback(contactMan);
                }.bind(this),
                false
            );
        };

        // 修改联系人成功后的回调函数
        this.editContactManCallback = function (contactMan) {
            var contactMan = fish.extend({}, contactMan);
            var managerContactManId = !fish.isEmpty(contactMan.managerContactMan)
                ? contactMan.managerContactMan.managerContactManId
                : contactMan.managerContactManId;
            // 重新到后台加载
            var param = {
                managerContactManId: managerContactManId,
                contactManId: contactMan.contactManId,
                flag: 'C'
            };
            if (!param.managerContactManId && contactMan.contactManName) {
                param.contactManName = contactMan.contactManName;
                param.contactManSingleFlag = 'Y';
            }
            this.qryContactManDataAndToBo(param);
        };

        // 新增联系人成功后的回调函数
        this.addContactCallback = function (result) {
            var param = {};
            if (result && result.contactMan && result.contactMan.contactMan) {
                var contactManId = result.contactMan.contactMan.contactManId;
                var managerContactManId = result.contactMan.managerContactManId;
                param = {
                    managerContactManId: managerContactManId,
                    contactManId: contactManId,
                    flag: 'C'
                };
            }
            if (!param.managerContactManId && result.contactMan.contactManName) {
                param.contactManName = result.contactMan.contactManName;
                param.contactManId = result.contactMan.contactManId;
                param.contactManSingleFlag = 'Y';
            }
            this.qryContactManDataAndToBo(param, result.managerContactMan);
        };

        /**
         * 联系人发送boChange事件
         * @param contactMan
         * @param oper
         */
        this.contactManToBo = function (contactMan, oper, operationType) {
            if (contactMan && !contactMan.managerContactManType && contactMan.managerContactMan) {
                contactMan.managerContactManType = contactMan.managerContactMan.managerContactManType;
            }

            var subsContactManOrderClass = new DataInput();
            if (this.boAccessName == 'subsContactManOrder') {
                // 匹配当前订户下是否已经存在这个联系人
                var item = fish.find(
                    this.subsContactManList,
                    function (subsContactMan) {
                        return subsContactMan.contactManId == contactMan.contactManId;
                    }
                );
                if (OperationTypeDef.NEW == oper) {
                    this.subsContactManOrderSeq = fish.getUUID();
                }
                subsContactManOrderClass = new DataInput({
                    boAccessName: 'subsContactManOrder',
                    bo: true,
                    operationType: oper,
                    value: {
                        SUBS_CONTACT_MAN_ORDER_SEQ: this.subsContactManOrderSeq,
                        CONTACT_MAN_ID: contactMan ? contactMan.contactManId : null,
                        RELA_ID: contactMan ? contactMan.contactManId : null,
                        ORDER_ITEM_ID: this.orderItem.ORDER_ITEM_ID,
                        SP_ID: portal.appGlobal.get('spId'),
                        OPERATION_TYPE: !fish.isEmpty(operationType)
                            ? operationType
                            : fish.isEmpty(item)
                                ? OperationTypeDef.NEW
                                : OperationTypeDef.CHANGE,
                        // CONTACT_MAN_ORDER_ID: this.contactManOrderId,
                        CONTACT_MAN_NAME: contactMan ? contactMan.contactManName : null,
                        MANAGER_CONTACT_MAN_TYPE: contactMan ? contactMan.managerContactManType : null,
                        CONTACT_MAN_TYPE: this.contactManType,
                        PRIMARY_FLAG: contactMan ? contactMan.primaryFlag : null
                    }
                });
            } else if (this.boAccessName == 'fixContactManOrder') {
                subsContactManOrderClass = new DataInput({
                    boAccessName: 'fixContactManOrder',
                    bo: true,
                    operationType: oper,
                    parentKeyValue: this.installOrderId,
                    value: {
                        SUBS_CONTACT_MAN_ORDER_SEQ: this.subsContactManOrderSeq,
                        CONTACT_MAN_ID: contactMan ? contactMan.contactManId : null,
                        ORDER_ITEM_ID: this.orderItem.ORDER_ITEM_ID,
                        SP_ID: portal.appGlobal.get('spId'),
                        OPERATION_TYPE: oper,
                        // CONTACT_MAN_ORDER_ID: this.contactManOrderId,
                        CONTACT_MAN_NAME: contactMan ? contactMan.contactManName : null,
                        MANAGER_CONTACT_MAN_TYPE: contactMan ? contactMan.managerContactManType : null,
                        CONTACT_MAN_TYPE: this.contactManType,
                        PRIMARY_FLAG: contactMan ? contactMan.primaryFlag : null,
                        INSTALL_ORDER_ID: this.installOrderId,
                        CHOOSE_FLAG: this.chooseFlag
                    }
                });
            }

            Controller.getInstance().sendSingleBoChange(
                this.commonDataId,
                subsContactManOrderClass,
                this,
                null,
                function () {
                    if (this.contactManInfo && this.contactManInfo.isContactManNameChange) {
                        delete this.contactManInfo.isContactManNameChange;
                        this.dealContactManNameChange({
                            contactManId: contactMan.contactManId,
                            contactManName: contactMan.contactManName
                        });
                    }
                }.bind(this)
            );
        };

        // 如果名字发生变化，需要更新其他引用了相同联系人的订单
        this.dealContactManNameChange = function (param) {
            BoUtils.fetchSubsContactManOrderList(
                null,
                this.commonDataId,
                this.orderItem.ORDER_ITEM_ID,
                function (subsContactManOrderList) {
                    if (!fish.isEmpty(subsContactManOrderList)) {
                        var sameContactManOrderList = fish.filter(subsContactManOrderList, function (item) {
                            return item.CONTACT_MAN_ID == param.contactManId && item.CONTACT_MAN_NAME !== param.contactManName;
                        });
                        if (!fish.isEmpty(sameContactManOrderList)) {
                            var dataInputs = new DataInputs();
                            fish.forEach(sameContactManOrderList, function (sameContactManOrder) {
                                var dataInput = new DataInput({
                                    bo: true,
                                    boAccessName: 'subsContactManOrder',
                                    operationType: OperationTypeDef.CHANGE,
                                    value: fish.extend(sameContactManOrder, { CONTACT_MAN_NAME: param.contactManName })
                                });
                                dataInputs.push(dataInput);
                            });
                            Controller.getInstance().sendBoChanges(this.commonDataId, dataInputs, this);
                        }
                    }
                }.bind(this)
            );
        };

        this.qryContactManDataAndToBo = function (param, contactMan) {
            // 重新到后台加载
            ContactManAction.qryContactManInfo(
                param,
                {},
                function (result) {
                    var contactManList = result.contactManList;
                    if (!fish.isEmpty(contactManList)) {
                        contactManList = fish.filter(contactManList, function (item) {
                            return !!item.contactManName;
                        });
                        if (fish.isEmpty(contactManList)) {
                            contactManList = result.contactManList;
                            contactManList[0].contactManName = contactMan.contactManName;
                        }
                        if (!param.managerContactManId) {
                            this.dealContactManData(contactManList[0], contactMan);
                        }
                        contactManList[0].isContactManNameChange = Boolean(
                            !this.contactManInfo || this.contactManInfo.contactManName !== contactManList[0].contactManName
                        );
                        this.contactManInfo = contactManList[0];
                        this.$contactMan.popedit('setValue', {
                            name: contactManList[0].contactManName,
                            data: contactManList[0]
                        });
                    }
                }.bind(this),
                false
            );
        };

        /**
         * 前后台交互差异,修改时慎重
         *
         * @param contactMan
         */
        this.getContactManReq = function (contactMan) {
            contactMan = ObjectUtils.deepClone(contactMan);
            // 构建联系人请求
            var contactManReq = {};
            contactManReq.seq = contactMan.seq;
            contactManReq.contactManAttrValueList = contactMan.contactManAttrValueList;
            // 新增或者修改的情况
            if (contactMan.managerContactMan) {
                contactManReq.managerContactMan = contactMan.managerContactMan;
                contactManReq.managerContactMan.contactManName = contactMan.contactManName;
                contactManReq.managerContactMan.contactMan = contactMan.contactMan || contactMan;
                contactManReq.fixStdAddr = contactMan.fixStdAddr;
                contactManReq.fixSubStdAddr = contactMan.fixSubStdAddr;
                contactManReq.cert = contactMan.cert;
            } else if (contactMan.managerContactManId) {
                // 删除的情况
                contactManReq.managerContactMan = {
                    contactMan: contactMan,
                    managerContactManId: contactMan.managerContactManId,
                    partyType: 'A',
                    partyCode: portal.appGlobal.get('staffJobId')
                };
            }
            delete contactMan.contactManAttrValueList;
            delete contactMan.managerContactMan;
            return contactManReq;
        };
    };
});
