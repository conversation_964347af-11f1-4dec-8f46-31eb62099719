/* eslint-disable */
/* 忽视的目的：保持原有的业务逻辑，不能随便修改 */

/**
 * 业务框架前台总控程序
 */
define(["crm/modules/fbf/bfm/models/DataInput",
    "crm/modules/fbf/bfm/models/DataInputs",
    "crm/modules/fbf/bfm/constant/OperationTypeDef",
    "crm/modules/fbf/bfm/models/Request",
    "crm/modules/fbf/bfm/actions/BfmCommonAction",
    "crm/modules/order/constants/InputTypeDef",
    "crm/modules/common/constant/AttrDataType",
    "crm/modules/common/constant/PrompDef",
    'crm/modules/common/util/CurrencyUtils',
], function (DataInput, DataInputs, OperationTypeDef, Request, BfmCommonAction, InputTypeDef, AttrDataType, PrompDef, CurrencyUtils) {

    var _instance = (function () {


        /**
         * 请求数
         * @type {number}
         */
        var numOfUnreturnedCalls = 0;

        /**
         * 请求队列
         * @type {Array}
         */
        var requestQueue = [];

        /**
         * CommonDataId<->View映射
         * @type {{}}
         */
        var commonDataReverseDict = {};

        /**
         * CommonDataDict
         *
         * @type {{}}
         */
        var commonDataDict = {};


        /**
         * OrderItem <-> View 映射
         * @type {{}}
         */
        var orderItemDict = {};

        /**
         * OrderItem <-> View 映射
         * @type {{}}
         */
        var orderItemReverseDict = {};

        /**
         * 启动流程
         */
        var startOrderFlow = function (dataInputs, success, fail) {
            var request = new Request();
            request.set("action", "START_ORDER_FLOW");
            request.set("bfmKey", fish.getUUID());
            request.set("dataChangeList", dataInputs.toJSON());
            BfmCommonAction.commonDataChange(request.toJSON(),
                function (result) {
                    if (result) {
                        // 存在异常
                        if (!fish.isEmpty(result.errors)) {
                            if (null != fail) {
                                fail(result);
                            }
                            PrompDef.prompErrors(result);
                        } else {
                            success(result);
                        }
                    }
                }, fail);
        };

        /**
         * 流程下一步
         */
        var nextFlowStep = function (commonDataId, success, fail) {
            var request = new Request();
            request.set("commonDataId", commonDataId);
            request.set("action", "NEXT_FLOW_STEP");
            request.set("bfmKey", commonDataId);
            sendRequest(request, success, fail);
        };

        /**
         * 流程上一步
         */
        var previousFlowStep = function (commonDataId, success, fail) {
            var request = new Request();
            request.set("commonDataId", commonDataId);
            request.set("bfmKey", commonDataId);
            request.set("action", "PREVIOUS_FLOW_STEP");
            sendRequest(request, success, fail);
        };

        /**
         * 销毁流程
         */
        var destroyOrder = function (commonDataId, success, fail) {
            var request = new Request();
            request.set("commonDataId", commonDataId);
            request.set("bfmKey", commonDataId);
            request.set("action", "DESTROY");
            sendRequest(request, success, fail);
        };


        /**
         * 发送请求
         *
         * @param action
         * @param request
         * @param success
         * @param fail
         */
        var sendRequest = function (request, success, fail, callbackArgs) {
            if (numOfUnreturnedCalls > 0) {
                var requestObj = {
                    request: request,
                    success: success,
                    fail: fail,
                    callbackArgs: callbackArgs
                }
                requestQueue.push(requestObj);
            } else {
                sendRequestImmediately(request, success, fail, callbackArgs)
            }
        }

        /**
         * 校验页面是否加载完成
         */
        var checkPageIsReady = function (request) {

        };

        /**
         * 发送请求
         *
         * @param action
         * @param request
         * @param success
         * @param fail
         */
        var sendRequestImmediately = function (request, success, fail, callbackArgs) {
            numOfUnreturnedCalls++;
            BfmCommonAction.commonDataChange(request.toJSON(), function (result) {
                try {
                    if (result) {
                        // 存在异常
                        if (!fish.isEmpty(result.errors)) {
                            if (null != fail && fish.isFunction(fail)) {
                                fail(result, callbackArgs);
                            }
                            // 请删掉对应回调fail的error弹框
                            PrompDef.prompErrors(result);
                        } else if (!fish.isEmpty(result.hints)) {
                            if (null != fail && fish.isFunction(fail)) {
                                fail(result, callbackArgs);
                            }
                            PrompDef.prompHints(result);
                        } else {
                            if (null != success && fish.isFunction(success)) {
                                success(result, callbackArgs);
                            }
                        }
                    }
                } catch (err) {
                    console.log(err);
                }
                numOfUnreturnedCalls--;
                // 继续处理请求
                processQueuedRequests();
            }.bind(this), function (err) {
                fish.error(fish.escape(err.Msg));

                // 继续处理请求
                numOfUnreturnedCalls--;
                processQueuedRequests();
            });
        };

        /**
         * 继续处理队列中请求
         */
        var processQueuedRequests = function () {
            if (numOfUnreturnedCalls == 0 && requestQueue.length > 0) {
                var requestObj = requestQueue.shift();
                sendRequestImmediately(requestObj.request, requestObj.success, requestObj.fail, requestObj.callbackArgs);
            }
        }


        /**
         * 发送业务对象修改，如order_item增加和删除
         *
         * @param commonDataId
         * @param dataInput
         * @param $source
         * @param callbackArgs
         * @param success
         * @param fail
         * @param requestDsi
         * @param refreshDirty
         */
        var sendSingleBoChange = function (commonDataId, dataInput, $source, callbackArgs, success, fail, requestDsi, refreshDirty) {
            var request = new Request();
            request.set("action", "BO_CHANGE");
            request.set("commonDataId", commonDataId);

            // 设置业务数据
            setBfmData(request, dataInput, $source);

            var dataInputList = new DataInputs();
            dataInputList.push(dataInput);
            request.set("dataChangeList", dataInputList.toJSON());
            request.set("bfmKey", request.get("commonDataId"));
            request.set("requestDsi", requestDsi == null ? false : requestDsi);
            request.set("refreshDirty", refreshDirty == null ? true : refreshDirty);
            sendRequest(request, success, fail, callbackArgs);
        };

        /**
         * 发送业务对象批量变更
         *
         * @param commonDataId
         * @param dataInputs
         * @param $source
         * @param callbackArgs
         * @param success
         * @param fail
         * @param requestDsi
         * @param refreshDirty
         */
        var sendBoChanges = function (commonDataId, dataInputs, $source, callbackArgs, success, fail, requestDsi, refreshDirty) {
            var request = new Request();
            request.set("action", "BO_CHANGE");
            request.set("commonDataId", commonDataId);
            request.set("requestDsi", requestDsi == null ? false : requestDsi);
            request.set("refreshDirty", refreshDirty == null ? true : refreshDirty);

            // 设置业务数据
            dataInputs.each(function (dataInput) {
                setBfmData(request, dataInput, $source);
            });
            request.set("bfmKey", request.get("commonDataId"));
            request.set("dataChangeList", dataInputs.toJSON());
            sendRequest(request, success, fail, callbackArgs);
        };

        /**
         * 发送业务属性变更
         *
         * @param commonDataId
         * @param dataInput
         * @param $source
         * @param callbackArgs
         * @param success
         * @param fail
         */
        var sendSingleChange = function (commonDataId, dataInput, $source, callbackArgs, success, fail) {
            var request = new Request();
            request.set("commonDataId", commonDataId);
            // 设置业务数据
            setBfmData(request, dataInput, $source);

            var dataInputs = new DataInputs();
            dataInputs.push(dataInput);
            request.set("action", "DATA_CHANGE");
            request.set("bfmKey", request.get("commonDataId"));
            request.set("dataChangeList", dataInputs.toJSON());
            sendRequest(request, success, fail, callbackArgs);
        };

        /**
         *  发送业务属性批量变更
         *
         * @param commonDataId
         * @param dataInputList
         * @param $source
         * @param callbackArgs
         * @param success
         * @param fail
         */
        var sendChanges = function (commonDataId, dataInputs, $source, callbackArgs, success, fail) {
            var request = new Request();
            request.set("action", "DATA_CHANGE");
            request.set("commonDataId", commonDataId);
            // 设置业务数据
            dataInputs.each(function (dataInputItem) {
                setBfmData(request, dataInputItem, $source);
            });
            request.set("bfmKey", request.get("commonDataId"));
            request.set("dataChangeList", dataInputs.toJSON());
            sendRequest(request, success, fail, callbackArgs);
        };

        /**
         * 发送属性变更
         *
         * @param commonDataId
         * @param dataInputs
         * @param $source
         * @param callbackArgs
         * @param success
         * @param fail
         */
        var sendSingleAttrChange = function (commonDataId, dataInput, $source, callbackArgs, success, fail) {
            var dataInputs = new DataInputs();
            dataInputs.push(dataInput);
            dataInput.set("bo", true);
            sendAttrChanges(commonDataId, dataInputs, $source, callbackArgs, success, fail)
        };

        /**
         * 批量发送属性变更
         *
         * @param commonDataId
         * @param dataInputs
         * @param $source
         * @param callbackArgs
         * @param success
         * @param fail
         */
        var sendAttrChanges = function (commonDataId, dataInputs, $source, callbackArgs, success, fail) {
            var request = new Request();
            request.set("action", "ATTR_CHANGE");
            request.set("commonDataId", commonDataId);
            // 设置业务数据
            dataInputs.each(function (dataInputItem) {
                dataInputItem.set("bo", true);
                setBfmData(request, dataInputItem, $source);
            });
            request.set("bfmKey", request.get("commonDataId"));
            request.set("dataChangeList", dataInputs.toJSON());
            sendRequest(request, success, fail, callbackArgs);
        };

        /**
         * 主动获取远程数据
         *
         * @param commonDataId
         * @param dataInput
         * @param $source
         * @param success
         * @param fail
         */
        var fetchData = function (commonDataId, dataInput, $source, success, fail) {
            var request = new Request();
            request.set("action", "FETCH");
            request.set("commonDataId", commonDataId);
            // 设置业务数据
            setBfmData(request, dataInput, $source);
            var dataInputs = new DataInputs();
            dataInputs.push(dataInput);
            request.set("bfmKey", request.get("commonDataId"));
            request.set("dataChangeList", dataInputs.toJSON());
            sendRequest(request, success, fail)
        };

        /**
         * commonDataId与View映射
         *
         * @param commonDataId
         * @param $view
         */
        var registerCommonDataId = function (commonDataId, $view) {
            commonDataDict[commonDataId] = $view.cid;
            commonDataReverseDict[$view.cid] = commonDataId;
        };


        /**
         * 解除commonDataId与View映射
         * @param commonDataId
         */
        var unRegisterCommonDataId = function (commonDataId) {
            var vid = commonDataDict[commonDataId];
            if (vid) {
                delete commonDataDict[commonDataId];
                delete commonDataReverseDict[vid];

                var orderItemId = orderItemReverseDict[vid];
                if (orderItemId) {
                    delete orderItemDict[orderItemId];
                    delete orderItemReverseDict[vid]
                }
            }
        }

        /**
         * OrderItem<->View映射
         * @param orderItemId
         * @param $view
         */
        var registerOrderItem = function (orderItemId, $view) {
            if (null == orderItemDict[orderItemId]) {
                orderItemDict[orderItemId] = [];
            }
            if (orderItemReverseDict[$view.cid] == null) {
                orderItemDict[orderItemId].push($view.cid)
                orderItemReverseDict[$view.cid] = orderItemId;
            }
        };

        /**
         * 触发业务规则
         *
         * @param systemMessage
         * @param dataInputs
         * @param success
         * @param fail
         */
        var ruleCall = function (systemMessage, dataInputs, success, fail) {
            var request = new Request();
            request.set("action", "RULE_CALL");
            request.set("sysEvent", systemMessage);
            request.set("dataChangeList", dataInputs.toJSON());
            sendRequest(request, success, fail);
        };

        /**
         * 触发业务规则
         *
         * @param systemMessage
         * @param dataInputs
         * @param success
         * @param fail
         */
        var ruleCall4Flow = function (commonDataId, sysCode, $source, success, fail) {
            var request = new Request();
            request.set("action", "RULE_CALL_4_FLOW");
            request.set("sysEvent", sysCode);
            request.set("commonDataId", commonDataId);
            setBfmData(request, null, $source);

            request.set("bfmKey", request.get("commonDataId"));
            sendRequest(request, success, fail);
        };

        /**
         * 设置业务数据
         */
        var setBfmData = function (request, dataInput, $source) {
            var commonDataId = request && request.get("commonDataId");
            var orderItemId = dataInput && dataInput.get("orderItemId");
            if (null != $source) {
                if (orderItemId == null && dataInput && orderItemReverseDict[$source.cid] != null) {
                    orderItemId = orderItemReverseDict[$source.cid];
                    dataInput.set("orderItemId", orderItemId)
                }
                if (commonDataId == null && request && commonDataReverseDict[$source.cid] != null) {
                    commonDataId = commonDataReverseDict[$source.cid];
                    request.set("commonDataId", commonDataId)
                }
            }
            if ((!commonDataId || !orderItemId) && $source && $source.parentView) {
                setBfmData(request, dataInput, $source.parentView)
            }
        };

        /**
         * 获取页面组件的commonDataId
         * @param $view
         * @returns {*}
         */
        var getCommonDataId = function ($view) {
            var commonDataId = commonDataReverseDict[$view.cid];
            if (!commonDataId) {
                if ($view.parentView) {
                    return getCommonDataId($view.parentView)
                }
            }
            return commonDataId;
        };

        /**
         * 获取页面组件的orderItemId
         * @param $view
         * @returns {*}
         */
        var getOrderItemId = function ($view) {
            var orderItemId = orderItemReverseDict[$view.cid];
            if (!orderItemId) {
                if ($view.parentView) {
                    return getOrderItemId($view.parentView)
                }
            }
            return orderItemId;
        };

        /**
         * 绑定事件，触发BO/Data请求
         *
         * @param $view
         * @param options
         */
        // MODIFIED: 新增外部传入的 commonDataId
        var registerChange = function ($view, options, isShowBfmKey) {
            var _options = [];
            if (!fish.isArray(options)) {
                _options.push(options);
            } else {
                _options = options;
            }
            fish.each(_options, function (item) {
                var $selector = $view.$(item.selector);
                if (!$selector) {
                    fish.error("can not find" + item.selector);
                    return;
                }
                var _sendChange = function (value, isAttr, isBo) {
                    var dataInput = new DataInput();
                    dataInput.set("boAccessName", item.boAccessName);
                    dataInput.set("boItemAccessName", item.boItemAccessName)
                    dataInput.set("operationType", OperationTypeDef.CHANGE);

                    if (item.boPath) {
                        dataInput.set('path', item.boPath);
                    }


                    if (item.businessData) {
                        if (!fish.isObject(value)) {
                            var tempValue = value;
                            value = {};
                            value[item.boItemAccessName] = tempValue;
                        }
                        fish.each(item.businessData, function (businessDataValue, key) {
                            value[key] = businessDataValue;
                        })
                        if (item.businessData["parentKeyValue"]) {
                            dataInput.set("parentKeyValue", item.businessData["parentKeyValue"]);
                        }
                    }
                    dataInput.set("value", value);
                    // MODIFIED: 新增外部传入的 commonDataId
                    if (isAttr) {
                        sendSingleAttrChange(isShowBfmKey ? $view.commonDataId : null, dataInput, $view, value, item.success, item.fail);
                    } else if (isBo) {
                        sendSingleBoChange(isShowBfmKey ? $view.commonDataId : null, dataInput, $view, item.callbackArgs, item.success, item.fail);
                    } else {
                        sendSingleChange(isShowBfmKey ? $view.commonDataId : null, dataInput, $view, item.callbackArgs, item.success, item.fail);
                    }
                };


                var inputType = item.inputType;
                switch (inputType) {
                    case InputTypeDef.COMBOBOX:
                        $selector.on("combobox:change", function (e) {
                            var txtValue = $selector.combobox("value");
                            var valueMark = null;
                            if (item.isAttr) {
                                var selectedItem = $selector.combobox("getSelectedItem");
                                if (selectedItem) {
                                    valueMark = selectedItem.valueMark
                                }
                            }
                            _sendChange(parseAttrValueNew(item.isAttr, item.attrId, txtValue, valueMark, item.attrName), item.isAttr, item.isBo)
                        });
                        break;
                    case InputTypeDef.TEXT:
                    case InputTypeDef.UNDERLINETEXT:
                    case InputTypeDef.MEMO:
                        if (item.dataType == AttrDataType.MONEY) {
                            $selector.on("currencybox:change", function (e) {
                                let value = $selector.currencybox("value");
                                if (!value &&  value!= '0') {
                                    value = "";
                                }
                                else {
                                    let defCurrency = CurrencyUtils.getCurrency();
                                    value = value * Math.pow(10, defCurrency.rateDisplayScale);
                                }
                                var txtValue = value + "";
                                _sendChange(parseAttrValueNew(item.isAttr, item.attrId, txtValue, txtValue, item.attrName), item.isAttr, item.isBo)
                            });
                        } else {
                            $selector.on('change', function (e) {
                                if (item.preCheckFlag) {
                                    if (!item.preCheckFunc(e)) {
                                        return;
                                    }
                                }
                                var txtValue = $selector.val();
                                _sendChange(parseAttrValueNew(item.isAttr, item.attrId, txtValue, txtValue, item.attrName), item.isAttr, item.isBo)
                            });
                        }
                        break;
                    case InputTypeDef.RADIO:
                        $selector.on("change", function (e) {
                            var txtValue = $selector.filter(':checked').val();
                            _sendChange(parseAttrValueNew(item.isAttr, item.attrId, txtValue, txtValue, item.attrName), item.isAttr, item.isBo)
                        });
                        break;
                    case InputTypeDef.MULTI_CHOICE:
                        $selector.on("multiselect:change", function (e) {
                            var valueArr = $selector.multiselect("value");
                            var txtValue = null;
                            if (!fish.isEmpty(valueArr)) {
                                txtValue = valueArr.join("|");
                            }
                            _sendChange(parseAttrValueNew(item.isAttr, item.attrId, txtValue, txtValue, item.attrName), item.isAttr, item.isBo)
                        });
                        break;
                    case InputTypeDef.DATE_PICKER:
                    case 'B':
                        $selector.datetimepicker("option", "changeDate", function () {
                            var txtValue = $selector.datetimepicker("value");
                            _sendChange(parseAttrValue(item.isAttr, item.attrId, txtValue, txtValue), item.isAttr, item.isBo)
                        })
                        $selector.datetimepicker("option", "clearDate", function () {
                            _sendChange(parseAttrValue(item.isAttr, item.attrId, null, null), item.isAttr, item.isBo)
                        })
                        break;
                    case InputTypeDef.POP_EDIT:
                        $selector.on("popedit:change", function (e) {
                            var valueObj = $selector.popedit("getValue");

                            if (!item.attrId && item.businessData)
                            {
                                item.attrId = item.businessData.attrId;
                            }
                            _sendChange(parseAttrValue4PopEdit(item.isAttr, item.attrId, valueObj), item.isAttr, item.isBo)
                        });
                        break;
                    case InputTypeDef.CUSTOM:
                        $selector.off("dataChange").on("dataChange", function (e, data) {
                            _sendChange(data, item.isAttr, item.isBo);
                        });
                        break;
                    default:
                        break;
                }
            })
        };

        /**
         * 解析属性值
         *
         * @param isAttr
         * @param attrId
         * @param txtValue
         * @param valueMark
         * @returns {*}
         */
        var parseAttrValue = function (isAttr, attrId, txtValue, valueMark) {
            if (isAttr) {
                return {
                    attrId: attrId,
                    operationType: OperationTypeDef.NEW,
                    attrValue: txtValue,
                    value: txtValue,
                    valueMark: valueMark,
                };
            } else {
                return txtValue;
            }
        };

        /**
         * popedit组件返回的属性值
         * @param isAttr
         * @param attrId
         * @param txtValue
         * @param valueMark
         * @param data
         * @returns {{attrId: *, data: *, valueMark: *, operationType: *, attrValue: *, value: *}|*}
         */
        var parseAttrValue4PopEdit = function (isAttr, attrId, data) {
            if (isAttr) {
                if (!data)
                {
                    data = {};
                }
                data.attrId = attrId;
                data.operationType = OperationTypeDef.NEW;
                data.attrValue = data.value || data.data;
                data.value = data.attrValue;
                data.valueMark = data.name;

                return data;
            } else {
                return data.value || data.data;
            }
        };

        /**
         * 解析属性值
         *
         * @param isAttr
         * @param attrId
         * @param txtValue
         * @param valueMark
         * @returns {*}
         */
        var parseAttrValueNew = function (isAttr, attrId, txtValue, valueMark, attrName) {
            if (isAttr) {
                return {
                    attrId: attrId,
                    operationType: OperationTypeDef.NEW,
                    attrValue: txtValue,
                    value: txtValue,
                    valueMark: valueMark,
                    attrName: attrName
                };
            } else {
                return txtValue;
            }
        };

        /**
         * 注册属性
         * TODO:属性注册失败了如何处理？
         *
         * @param $view
         * @param attrList
         */
        var registerAttr = function ($view, attrList, option) {
            var attrBoAccessList = [];
            fish.each(attrList, function (attr) {
                attrBoAccessList.push({
                    selector: ":input[name='name_" + attr.attrId + "']",
                    boAccessName: option.boAccessName,
                    boItemAccessName: attr.attrId,
                    $currentView: $view,
                    inputType: attr.inputType,
                    dataType: attr.dataType,
                    businessData: option.businessData,
                    isAttr: true,
                    attrId: attr.attrId,
                    attrName: attr.attrName,
                    success: option.success || null,
                    fail: option.fail || null
                })
            });
            registerChange($view, attrBoAccessList);
        };

        /**
         * 返回
         */
        return {
            "startOrderFlow": startOrderFlow,
            "nextFlowStep": nextFlowStep,
            "previousFlowStep": previousFlowStep,
            "destroyOrder": destroyOrder,
            "sendSingleBoChange": sendSingleBoChange,
            "sendBoChanges": sendBoChanges,
            "sendSingleChange": sendSingleChange,
            "sendChanges": sendChanges,
            "registerCommonDataId": registerCommonDataId,
            "registerOrderItem": registerOrderItem,
            "getCommonDataId": getCommonDataId,
            "getOrderItemId": getOrderItemId,
            "registerChange": registerChange,
            "registerAttr": registerAttr,
            "fetchData": fetchData,
            "ruleCall4Flow": ruleCall4Flow,
            "ruleCall": ruleCall,
            "sendSingleAttrChange": sendSingleAttrChange,
            "sendAttrChanges": sendAttrChanges,
            "unRegisterCommonDataId": unRegisterCommonDataId
        }
    })();

    return {
        "getInstance": function () {
            return _instance;
        }
    };
});
