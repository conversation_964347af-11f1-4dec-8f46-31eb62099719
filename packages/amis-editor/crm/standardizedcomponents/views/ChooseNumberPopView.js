/**
 * options 参数对象
 *
 * @param {string} orgId - 组织ID，从全局应用状态中获取。
 * @param {string} staffJobId - 员工职位ID，从全局应用状态中获取。
 * @param {string} staffId - 员工ID，从全局应用状态中获取。
 * @param {string} servType - 服务类型，来自当前订单项。
 * @param {string} indepProdSpecId - 独立产品规格ID，来自当前订单项。
 * @param {string} custId - 客户ID，来自当前订单项。
 * @param {string|null} certTypeId - 证件类型ID，如果证件信息存在则获取，否则为null。
 * @param {string|null} certNbr - 证件号码，如果证件信息存在则获取，否则为null。
 * @param {string} subsPlanId - 订阅计划ID，来自当前订单项。
 * @param {string} hlrId - HLR ID，使用旧的HLR ID。
 * @param {string} areaId - 区域ID，使用旧的区域ID。
 * @param {string} bindFlag - 绑定标志，设为'N'表示不绑定。
 * @param {string} offerId - 提供的优惠ID，来自当前订单项。
 * @param {boolean} showPreAccNbr - 是否显示预选账号的标志。
 * @param {Object} $subsPlanView - 当前订阅计划视图的上下文。
 * @param {string} bingSimCardDisabled - SIM卡绑定状态，设为'Y'表示禁用绑定。
 * @param {string} subsEventId - 订阅事件ID，当前上下文中的订阅事件ID。
 * @param {Object} boAccess - 业务访问对象，包含选择器和相关配置信息。
 * @param {string} boAccess.selector - 选择器，用于查找输入框。
 * @param {string} boAccess.boAccessName - 业务访问名称，表示订单项。
 * @param {string} boAccess.boItemAccessName - 业务项访问名称，表示账号ID。
 * @param {Object} boAccess.$currentView - 当前视图的上下文。
 * @param {string} boAccess.inputType - 输入类型，定义为自定义类型。
 * @param {function|null} boAccess.success - 成功回调函数，初始为null。
 * @param {function|null} boAccess.fail - 失败回调函数，初始为null。
 */

define([
    'hbs!crm/modules/pos/orderentry/templates/popwin/SelectNumberPopTpl.hbs',
    'hbs!crm/modules/pos/orderentry/templates/popwin/NbrGradeTpl.hbs',
    'hbs!crm/modules/pos/orderentry/templates/popwin/NbrRuleTpl.hbs',
    'hbs!crm/modules/pos/orderentry/templates/popwin/NbrSelectCardsBoxTpl.hbs',
    'i18n!crm/modules/order/orderentry/i18n/OrderEntry',
    'crm/modules/order/orderentry/actions/OrderEntryAction',
    'crm/modules/order/subsprofile/actions/SubsAdvancedManAction',
    'crm/modules/customer/actions/CustAction',
    'crm/modules/common/actions/CommonAction',
    'crm/modules/fbf/bfm/utils/Controller',
    'crm/modules/common/constant/ContactChannelDef',
    'crm/modules/common/constant/ServTypeDef',
    'crm/modules/common/constant/AccNbrStateDef',
    'crm/modules/pos/common/constant/IntroDef',
    'crm/modules/common/models/TrackPointLogModel',
    'crm/modules/common/actions/ConfigItemAction',
    'css!crm/modules/pos/orderentry/css/select-number.css',
    'css!crm/modules/pos/css/OrderEntry.css'
], function (
    SelectNumberPopTpl,
    NbrGradeTpl,
    NbrRuleTpl,
    NbrSelectCardsBoxTpl,
    I18N,
    OrderEntryAction,
    SubsAdvancedManAction,
    CustAction,
    CommonAction,
    Controller,
    ContactChannelDef,
    ServTypeDef,
    AccNbrStateDef,
    IntroDef,
    TrackPointLogModel,
    ConfigItemAction
) {
    return portal.BaseView.extend({
        el: false,
        template: SelectNumberPopTpl,
        serialize: I18N,
        events: {
            'click .js-ok': 'onOkClick',
            'click .js-search-number': 'onQueryNbrClick',
            'click #changeNumbers': 'onChangeNumbersClick'
        },
        initialize: function (initOptions) {
            this.options = initOptions || {};

            this.subsEventId = this.options.subsEventId;

            var height = $(window).height();

            var pageSize = 8;
            if (height > 900) {
                pageSize = 16;
            } else if (height > 600) {
                pageSize = 12;
            }

            if (initOptions && initOptions.bingSimCardDisabled) {
                this.bingSimCardDisabled = initOptions.bingSimCardDisabled;
            }
            this.showPreAccNbr = initOptions.showPreAccNbr;
            this.qryParamMap = {
                reserveType: null,
                accNbrClassId: null,
                accNbrRuleId: null,
                accNbrStart: '',
                accNbrEnd: '',
                bindFlag: 'N',
                accNbrLike: '',
                accNbr: '',
                pwd: '',
                certTypeId: '',
                certNbr: '',
                dataSetFilter: {
                    pageIndex: 1,
                    pageSize: pageSize
                }
            };
            this.selectedAccNbr = {};

            // MODIFIED：
            this.pageDesignerObj = this.options.pageDesignerObj || null;
        },
        afterRender: function () {
            // MODIFIED: page-designer 的 amis 样式兼容
            if (this.pageDesignerObj && this.pageDesignerObj.customClassNameFishInPageDesigner) {
                this.$('.ui-dialog').prevObject.addClass(this.pageDesignerObj.customClassNameFishInPageDesigner);
            }
            if (this.subsEventId && this.subsEventId === 32) {
                this.$('#port-in-Number-flag').show();
            }
            var ifHideBinding = ConfigItemAction.qryParamValue('POS', 'SELECT_NUMBER', 'BIND_CARD_DISPLAY_FLAG');
            var numberGradeCodes = ConfigItemAction.qryParamValue('POS', 'SELECT_NUMBER', 'NUMBER_GRADE_CODES');
            if (numberGradeCodes) {
                this.numberGradeCodeArr = numberGradeCodes.split(',');
            }
            this.commonInit();
            this.initReserve();
            this.initNbrGrade();
            this.initNbrRule();
            this.qryAccNbrs();
            var that = this;
            this.$(":input[name='bindingFlag']").on('change', function () {
                var logModel = new TrackPointLogModel();
                logModel.set('operCode', 'OE001000');
                logModel.set('stepCode', 'OE001090');
                CommonAction.recordPageTrackPointLog(logModel);
                that.queryNbrByParam();
            });

            this.$(":input[name='portInNumberFlag']").on('change', function () {
                var logModel = new TrackPointLogModel();
                logModel.set('operCode', 'OE001000');
                logModel.set('stepCode', 'OE001090');
                CommonAction.recordPageTrackPointLog(logModel);
                that.queryNbrByParam();
            });

            this.checkCompPriv();

            if (
                portal.appGlobal.get('isBolLogin') &&
                (fish.store.get('accountOpeningMethod') == 'mobileOffer' ||
                    fish.store.get('accountOpeningMethod') == 'boundleOffer' ||
                    fish.store.get('accountOpeningMethod') == 'contractPhone')
            ) {
                this.$(":input[name='bindingFlag']").attr('checked', true);
            }
            if (portal.appGlobal.get('isBolLogin') && fish.store.get('accountOpeningMethod') == 'contractPhone') {
                this.qryParamMap.accNbrClassId = '2';
            }

            // 在Change MSISDN,手动选号bing sim card 应处于不可用状态。
            if (this.bingSimCardDisabled && this.bingSimCardDisabled == 'Y') {
                this.$(":input[name='bindingFlag']").attr('disabled', 'disabled');
            }
            // 查询配置项POS.SELECT_NUMBER.BIND_CARD_DISPLAY_FLAG，取值为N，则选号页面，隐藏Binding SIM Card控件
            if (ifHideBinding == 'N') {
                this.$('#binding-sim-card-intro').hide();
            }
        },
        commonInit: function () {
            this.$('.js-reserve-pwd-info').hide();
            this.$('.js-reserve-customer-id-info').hide();
        },
        reserveChangeInit: function () {
            this.commonInit();
            this.$('.js-nbr-grade-select').find('.active').removeClass('active');
            if (portal.appGlobal.get('isBolLogin') && fish.store.get('accountOpeningMethod') == 'contractPhone') {
                // this.$(".js-nbr-grade-select").find("#nbrGrade_2").addClass("active");
            } else if (this.options.accNbrClassId) {
                this.$('.js-nbr-grade-select')
                    .find('#nbrGrade_' + this.options.accNbrClassId)
                    .addClass('active');
            } else {
                this.$('.js-nbr-grade-select').find('#nbrGrade_-1').addClass('active');
            }
            // this.$(".js-nbr-grade-select").find("#nbrGrade_-1").addClass("active");

            this.$('.js-nbr-rule-select').find('.active').removeClass('active');
            if (this.options.accNbrRuleId) {
                this.$('.js-nbr-rule-select')
                    .find('#nbrRule_' + this.options.accNbrRuleId)
                    .addClass('active');
            } else {
                this.$('.js-nbr-rule-select').find('#nbrRule_-1').addClass('active');
            }

            // this.$(":input[name='bindingFlag']").val("");
            // this.$(":input[name='accNbr']").val("");
            $('.js-acct-nbr-qry-form').form('clear');
        },
        initReserve: function () {
            this.$('.js-nbr-reserve-select')
                .find('#Reserve_-1')
                .on(
                    'click',
                    function (e) {
                        var $currentItem = this.$('.js-nbr-reserve-select').find('.active');
                        this.reserveChangeInit();
                        $currentItem.removeClass('active');
                        this.$(e.target).addClass('active');
                        this.qryParamMap.reserveType = null;
                        this.queryNbrByParam();
                    }.bind(this)
                );
            this.$('.js-nbr-reserve-select')
                .find('#Reserve_CC')
                .on(
                    'click',
                    function (e) {
                        var $currentItem = this.$('.js-nbr-reserve-select').find('.active');
                        this.reserveChangeInit();
                        $currentItem.removeClass('active');
                        this.$(e.target).addClass('active');
                        this.qryParamMap.reserveType = 'CC';
                        this.queryNbrByParam();
                    }.bind(this)
                );
            this.$('.js-nbr-reserve-select')
                .find('#Reserve_P')
                .on(
                    'click',
                    function (e) {
                        var $currentItem = this.$('.js-nbr-reserve-select').find('.active');
                        this.reserveChangeInit();
                        this.$('.js-reserve-pwd-info').show();
                        $currentItem.removeClass('active');
                        this.$(e.target).addClass('active');
                        this.qryParamMap.reserveType = 'P';
                        this.queryNbrByParam();
                    }.bind(this)
                );

            /*      this.$(".js-nbr-reserve-select").find("#Reserve_T").on('click', function (e) {
                      var $currentItem = this.$(".js-nbr-reserve-select").find(".active");
                      this.reserveChangeInit();
                      $currentItem.removeClass("active");
                      this.$(e.target).addClass("active");
                      // this.qryParamMap.reserveType = "T";
                      this.queryNbrByParam();
                  }.bind(this));*/
            this.$('.js-nbr-reserve-select')
                .find('#Reserve_S')
                .on(
                    'click',
                    function (e) {
                        var $currentItem = this.$('.js-nbr-reserve-select').find('.active');
                        this.reserveChangeInit();
                        $currentItem.removeClass('active');
                        this.$(e.target).addClass('active');
                        this.qryParamMap.reserveType = 'S';
                        this.queryNbrByParam();
                    }.bind(this)
                );
            this.$('.js-nbr-reserve-select')
                .find('#Reserve_I')
                .on(
                    'click',
                    function (e) {
                        var $currentItem = this.$('.js-nbr-reserve-select').find('.active');
                        this.reserveChangeInit();
                        this.$('.js-reserve-customer-id-info').hide();
                        $currentItem.removeClass('active');
                        this.$(e.target).addClass('active');
                        // 默认当前客户的ID type 和ID 查询
                        this.qryForDumpCertTypeResource();
                    }.bind(this)
                );

            // 自定义校验规则
            // MODIFIED
            this.$('.js-acct-nbr-detail-form').validator({
                rules: {
                    reservePwdRequiredRule: function () {
                        return !this.$('.js-reserve-pwd-info').is(':hidden');
                    }.bind(this)
                },
                fields: {
                    accNbr: 'required(reservePwdRequiredRule);',
                    pwd: 'required(reservePwdRequiredRule);'
                }
            });
        },
        // 初始化NbrGrade
        initNbrGrade: function () {
            var paramData = {};
            paramData.requestParam = {
                offerId: this.options.subsPlanId,
                productId: this.options.indepProdSpecId
            };
            OrderEntryAction.qryAccNbrGradeAsync(
                paramData,
                function (result) {
                    var nbrGradeList = result.z_d_r || [];
                    var nbrGradeAll = {
                        accNbrClassId: '-1',
                        accNbrClassName: 'All'
                    };
                    nbrGradeList.unshift(nbrGradeAll);

                    // 传入等级，且不可修改
                    if (this.options.accNbrClassId && this.options.numberGradeDisabled == 'Y') {
                        var filterGradeList = fish.filter(
                            nbrGradeList,
                            function (nbrGrade) {
                                return nbrGrade.accNbrClassId == this.options.accNbrClassId;
                            }.bind(this)
                        );
                        // 如果查到有，则限制号码等级
                        if (filterGradeList) {
                            nbrGradeList = filterGradeList;
                        }
                    }
                    this.$('.js-nbr-grade-select').append(
                        NbrGradeTpl(
                            fish.extend(I18N, {
                                nbrGradeList: nbrGradeList
                            })
                        )
                    );
                    if (portal.appGlobal.get('isBolLogin') && fish.store.get('accountOpeningMethod') == 'contractPhone') {
                        // MODIFIED
                        if (this.$('.js-select-goods').parent()) {
                            this.$('.js-select-goods').parent().attr('id', 'intro-select-goods');
                        }
                        fish.trigger(IntroDef.ADD_INTRO, {
                            element: '#nbrGrade_2',
                            tipsContents: IntroDef.CONTRACT_PHONE_STEP_GOLD,
                            position: 'bottom'
                        });
                        $('.ui-intro-helper-layer-inner ').css({ background: '#fff' });
                    }

                    // 默认 all 选项选中,根据入参判断，
                    var disableNbrGrade = false;
                    this.$('.js-nbr-grade-select').find('#nbrGrade_-1').addClass('active');
                    fish.each(
                        nbrGradeList,
                        function (nbrGrade) {
                            // 有预置条件
                            if (this.options.accNbrClassId && this.options.accNbrClassId == nbrGrade.accNbrClassId) {
                                this.$('.js-nbr-grade-select')
                                    .find('#nbrGrade_' + nbrGrade.accNbrClassId)
                                    .addClass('active');
                                this.$('.js-nbr-grade-select').find('#nbrGrade_-1').removeClass('active');

                                // 设置了不可选，且能找到这个等级；如果找不到等级，就不用管
                                if (this.options.numberGradeDisabled && this.options.numberGradeDisabled == 'Y') {
                                    this.qryParamMap.accNbrClassId = this.options.accNbrClassId;
                                    disableNbrGrade = true;
                                }
                            }
                        }.bind(this)
                    );

                    // 默认选中配置的数据
                    if (!fish.isEmpty(this.numberGradeCodeArr)) {
                        var configGradeList = fish.filter(
                            nbrGradeList,
                            function (nbrGrade) {
                                return fish.contains(this.numberGradeCodeArr, nbrGrade.accNbrClassCode);
                            }.bind(this)
                        );
                        if (!fish.isEmpty(configGradeList)) {
                            this.$('.js-nbr-grade-select').find('#nbrGrade_-1').removeClass('active');
                            let accNbrClassIds = [];
                            fish.each(
                                configGradeList,
                                function (nbrGrade) {
                                    accNbrClassIds.push(nbrGrade.accNbrClassId);
                                    // 如果没有选中，设置选中数据
                                    if (
                                        !this.$('.js-nbr-grade-select')
                                            .find('#nbrGrade_' + nbrGrade.accNbrClassId)
                                            .hasClass('active')
                                    ) {
                                        this.$('.js-nbr-grade-select')
                                            .find('#nbrGrade_' + nbrGrade.accNbrClassId)
                                            .addClass('active');
                                    }
                                }.bind(this)
                            );
                            this.qryParamMap.accNbrClassId = accNbrClassIds.join(',');
                        }
                    }

                    fish.each(
                        nbrGradeList,
                        function (nbrGrade) {
                            if (disableNbrGrade) {
                                this.$('.js-nbr-grade-select')
                                    .find('#nbrGrade_' + nbrGrade.accNbrClassId)
                                    .removeAttr('href');
                                return true;
                            }
                            this.$('.js-nbr-grade-select')
                                .find('#nbrGrade_' + nbrGrade.accNbrClassId)
                                .on(
                                    'click',
                                    function (e) {
                                        // 多选
                                        // MODIFIED：Unexpected if as the only statement in an else block.
                                        var accNbrClassId = this.$(e.target).attr('accNbrClassId');
                                        var isActive = this.$(e.target).hasClass('active');

                                        if (isActive && accNbrClassId != '-1') {
                                            this.$(e.target).removeClass('active');
                                        } else if (!isActive && accNbrClassId == '-1') {
                                            this.$('.js-nbr-grade-select').find('.active').removeClass('active');
                                        } else if (!isActive) {
                                            this.$('.js-nbr-grade-select').find('#nbrGrade_-1').removeClass('active');
                                            this.$(e.target).addClass('active');
                                        }

                                        var items = this.$('.js-nbr-grade-select').find('.active');
                                        var accNbrClassIds = [];
                                        fish.each(items, function (item) {
                                            accNbrClassIds.push(item.getAttribute('accNbrClassId'));
                                        });

                                        if (accNbrClassIds.length === 0) {
                                            this.$('.js-nbr-grade-select').find('#nbrGrade_-1').addClass('active');
                                            this.qryParamMap.accNbrClassId = null;
                                        } else {
                                            this.qryParamMap.accNbrClassId = accNbrClassIds.join(',');
                                        }
                                        this.resetNbrRule();
                                        this.queryNbrByParam();
                                        if (portal.appGlobal.get('isBolLogin') && fish.store.get('accountOpeningMethod') == 'contractPhone') {
                                            fish.trigger(IntroDef.ADD_INTRO, {
                                                element: '#acc-nbr-cards-intro',
                                                tipsContents: IntroDef.CONTRACT_PHONE_STEP4,
                                                position: 'top'
                                            });
                                        }
                                    }.bind(this)
                                );
                            // MODIFIED：Expected to return a value at the end of function 'bind'.
                            return false;
                        }.bind(this)
                    );
                }.bind(this)
            );
        },
        initNbrRule: function () {
            var paramData = {};

            paramData.requestParam = {};
            if (this.qryParamMap.accNbrClassId) {
                Object.assign(paramData.requestParam, { accNbrClassIds: this.qryParamMap.accNbrClassId });
            }

            OrderEntryAction.qryAccNbrRule(
                paramData,
                function (result) {
                    var nbrRuleList = result.z_d_r || [];
                    var nbrRuleAll = {
                        accNbrRuleId: '-1',
                        accNbrRuleCode: 'All'
                    };
                    nbrRuleList.unshift(nbrRuleAll);

                    // 传入等级，且不可修改
                    if (this.options.accNbrRuleId && this.options.numberRuleDisabled == 'Y') {
                        var filterRuleList = fish.filter(
                            nbrRuleList,
                            function (nbrRule) {
                                return nbrRule.accNbrRuleId == this.options.accNbrRuleId;
                            }.bind(this)
                        );
                        // 如果查到有，则限制号码等级
                        if (filterRuleList) {
                            nbrRuleList = filterRuleList;
                        }
                    }

                    this.$('.js-nbr-rule-select').children().remove();
                    this.$('.js-nbr-rule-select').append(
                        NbrRuleTpl(
                            fish.extend(I18N, {
                                nbrRuleList: nbrRuleList
                            })
                        )
                    );

                    // 默认 all 选项选中,根据参数判断
                    var disableNbrRule = false;
                    this.$('.js-nbr-rule-select').find('#nbrRule_-1').addClass('active');
                    fish.each(
                        nbrRuleList,
                        function (nbrRule) {
                            // 有预置条件
                            if (this.options.accNbrRuleId && this.options.accNbrRuleId == nbrRule.accNbrRuleId) {
                                this.$('.js-nbr-rule-select')
                                    .find('#nbrRule_' + nbrRule.accNbrRuleId)
                                    .addClass('active');
                                this.$('.js-nbr-rule-select').find('#nbrRule_-1').removeClass('active');

                                // 设置了不可选，且能找到这个等级；如果找不到等级，就不用管
                                if (this.options.numberRuleDisabled && this.options.numberRuleDisabled == 'Y') {
                                    this.qryParamMap.accNbrRuleId = this.options.accNbrRuleId;
                                    disableNbrRule = true;
                                }
                            }
                        }.bind(this)
                    );

                    if (disableNbrRule) {
                        return;
                    }

                    fish.each(
                        nbrRuleList,
                        function (nbrRule) {
                            this.$('.js-nbr-rule-select')
                                .find('#nbrRule_' + nbrRule.accNbrRuleId)
                                .on(
                                    'click',
                                    function (e) {
                                        // 多选
                                        // MODIFIED：Unexpected if as the only statement in an else block.
                                        var accNbrRuleId = this.$(e.target).attr('accNbrRuleId');
                                        var isActive = this.$(e.target).hasClass('active');

                                        if (isActive && accNbrRuleId != '-1') {
                                            this.$(e.target).removeClass('active');
                                        } else if (!isActive && accNbrRuleId == '-1') {
                                            this.$('.js-nbr-rule-select').find('.active').removeClass('active');
                                        } else if (!isActive) {
                                            this.$('.js-nbr-rule-select').find('#nbrRule_-1').removeClass('active');
                                            this.$(e.target).addClass('active');
                                        }

                                        var items = this.$('.js-nbr-rule-select').find('.active');
                                        var accNbrRules = [];
                                        fish.each(items, function (item) {
                                            accNbrRules.push(item.getAttribute('accNbrRuleId'));
                                        });

                                        if (accNbrRules.length === 0) {
                                            this.$('.js-nbr-rule-select').find('#nbrRule_-1').addClass('active');
                                            this.qryParamMap.accNbrRuleId = null;
                                        } else {
                                            this.qryParamMap.accNbrRuleId = accNbrRules.join(',');
                                        }
                                        this.queryNbrByParam();
                                    }.bind(this)
                                );
                        }.bind(this)
                    );
                    this.autoSlideMenu();
                }.bind(this)
            );
        },
        resetNbrRule: function () {
            this.qryParamMap.accNbrRuleId = null;
            this.initNbrRule();
        },
        onQueryNbrClick: function () {
            var logModel = new TrackPointLogModel();
            logModel.set('operCode', 'OE001000');
            logModel.set('stepCode', 'OE001070');
            CommonAction.recordPageTrackPointLog(logModel);
            this.queryNbrByParam();
        },
        onChangeNumbersClick: function () {
            var logModel = new TrackPointLogModel();
            logModel.set('operCode', 'OE001000');
            logModel.set('stepCode', 'OE001100');
            CommonAction.recordPageTrackPointLog(logModel);
            this.qryAccNbrs();
        },
        queryNbrByParam: function () {
            if (!this.$('.js-acct-nbr-detail-form').isValid()) {
                return;
            }

            this.qryParamMap.dataSetFilter.pageIndex = 1;
            // this.$('.js-acc-nbr-pagination').pagination('destroy');
            var formValue = this.$('.js-acct-nbr-qry-form').form('value');

            if (formValue.accNbrStart) {
                this.qryParamMap.accNbrStart = formValue.accNbrStart;
            } else {
                this.qryParamMap.accNbrStart = '';
            }
            if (formValue.accNbrEnd) {
                this.qryParamMap.accNbrEnd = formValue.accNbrEnd;
            } else {
                this.qryParamMap.accNbrEnd = '';
            }
            if (formValue.accNbrLike) {
                this.qryParamMap.accNbrLike = formValue.accNbrLike;
            } else {
                this.qryParamMap.accNbrLike = '';
            }
            if (formValue.bindingFlag) {
                this.qryParamMap.bindFlag = 'Y';
            } else {
                this.qryParamMap.bindFlag = 'N';
            }

            this.qryParamMap.portInNumber = 'N';
            if (formValue.portInNumberFlag) {
                this.qryParamMap.portInNumber = 'Y';
            }

            if (formValue.pwd) {
                this.qryParamMap.pwd = formValue.pwd;
            } else {
                this.qryParamMap.pwd = '';
            }
            if (formValue.accNbr) {
                this.qryParamMap.accNbr = formValue.accNbr;
            } else {
                this.qryParamMap.accNbr = '';
            }
            if (formValue.certTypeId) {
                this.qryParamMap.certTypeId = formValue.certTypeId;
            } else {
                this.qryParamMap.certTypeId = '';
            }
            if (formValue.certNbr) {
                this.qryParamMap.certNbr = formValue.certNbr;
            } else {
                this.qryParamMap.certNbr = '';
            }
            this.qryAccNbrs();
        },

        qryAccNbrs: function () {
            var param = this.getChooseAccNbrParam();
            param.selectFlag = 'Q';
            // 调用手动选号接口
            var paramData = {};
            paramData.requestParam = fish.extend(this.qryParamMap, param);
            if (
                portal.appGlobal.get('isBolLogin') &&
                (fish.store.get('accountOpeningMethod') == 'mobileOffer' ||
                    fish.store.get('accountOpeningMethod') == 'boundleOffer' ||
                    fish.store.get('accountOpeningMethod') == 'contractPhone')
            ) {
                paramData.requestParam.bindFlag = 'Y';
            }
            $.blockUI();
            OrderEntryAction.chooseNbr(
                paramData,
                function (result) {
                    if (result && result.isSuccess === false) {
                        fish.warn(result.MsgCode + ' : ' + result.Msg);
                    } else {
                        this.$('.js-acc-nbr-cards').children().remove();

                        var accNbrList = result.accNbrList;
                        if (accNbrList && accNbrList.length > 0) {
                            this.$('.js-acc-nbr-cards').append(
                                NbrSelectCardsBoxTpl(
                                    fish.extend(I18N, {
                                        accNbrList: accNbrList,
                                        showPreAccNbr: this.showPreAccNbr
                                    })
                                )
                            );

                            setTimeout(function () {
                                if (portal.appGlobal.get('isBolLogin')) {
                                    var tips = '';
                                    if (fish.store.get('accountOpeningMethod') == 'mobileOffer') {
                                        tips = IntroDef.MOBILE_OFFER_STEP4;
                                        fish.trigger(IntroDef.ADD_INTRO, {
                                            element: '#acc-nbr-cards-intro',
                                            tipsContents: tips,
                                            position: 'top'
                                        });
                                    } else if (fish.store.get('accountOpeningMethod') == 'boundleOffer') {
                                        tips = IntroDef.BOUNDLE_OFFER_STEP11;
                                        fish.trigger(IntroDef.ADD_INTRO, {
                                            element: '#acc-nbr-cards-intro',
                                            tipsContents: tips,
                                            position: 'top'
                                        });
                                        // MODIFIED：Unnecessary return statement.
                                    }
                                }
                                // MODIFIED：The function binding is unnecessary.
                            }, 400);

                            fish.each(
                                accNbrList,
                                function (accNbr) {
                                    this.$('.js-acc-nbr-cards')
                                        .find('#accNbr_' + accNbr.accNbrId)
                                        .on(
                                            'click',
                                            function (e) {
                                                var logModel = new TrackPointLogModel();
                                                logModel.set('operCode', 'OE001000');
                                                logModel.set('stepCode', 'OE001080');
                                                CommonAction.recordPageTrackPointLog(logModel);

                                                this.selectedAccNbr = accNbr;
                                                this.accNbrChoosed = accNbr.accNbr;
                                                var $currentItem = this.$('.js-acc-nbr-cards').find('.selected');
                                                $currentItem.removeClass('selected');
                                                this.$(e.target).parents('.number-card').addClass('selected');
                                                if (
                                                    portal.appGlobal.get('isBolLogin') &&
                                                    (fish.store.get('accountOpeningMethod') == 'mobileOffer' ||
                                                        fish.store.get('accountOpeningMethod') == 'boundleOffer' ||
                                                        fish.store.get('accountOpeningMethod') == 'contractPhone')
                                                ) {
                                                    this.onOkClick();
                                                }
                                            }.bind(this)
                                        );
                                }.bind(this)
                            );
                            this.$('.js-change-acc-nbrs').show();
                            this.$('.js-ok').attr('disabled', false);
                        } else {
                            // 返回结果为空
                            accNbrList = [];
                            var nodata =
                                '<div class="nodata">\n' +
                                '<div>\n' +
                                '<div class = "nodata-img"><img class="nodata-img" src="crm/modules/order/orderentry/img/nodata.svg"></div>\n' +
                                '<div class = "no-record" style="font-family: OpenSans;font-style: normal;">' +
                                I18N.NO_DATA +
                                '</div>\n' +
                                '</div>\n' +
                                '</div>';

                            // 添加到DOM元素中
                            this.$('.js-acc-nbr-cards').append(nodata);
                            this.$('.js-ok').attr('disabled', true);
                            this.$('.js-change-acc-nbrs').hide();
                            // fish.info(I18N.HINT_SEARCH_MATCH_NULL);
                        }
                    }

                    /* var that = this;
                this.$('.js-acc-nbr-pagination').pagination({
                    records: result.cnt,
                    rowNum: this.qryParamMap.dataSetFilter.pageSize,
                    pgTotal: false,
                    pgInput: false,
                    rowList: [],
                    onPageClick: function (e, eventData) {
                        that.qryParamMap.dataSetFilter.pageIndex = eventData.page;
                        that.qryAccNbrs();
                    }
                }); */
                    $.unblockUI();
                }.bind(this)
            );
        },

        /**
         * 初始化证件类型下拉框
         */
        qryForDumpCertTypeResource: function () {
            // 获取客户类型
            var qryParam = { custId: this.options.custId };
            CustAction.qryCustomerInfoByCustId(
                qryParam,
                function (data) {
                    if (data && data.custBaseDetailDtoList && data.custBaseDetailDtoList.length > 0) {
                        var custInfo = data.custBaseDetailDtoList[0];
                        this.qryParamMap.reserveType = 'I';
                        this.$('.js-acct-nbr-qry-form').find(":input[name='certTypeId']").val(custInfo.certTypeId);
                        this.$('.js-acct-nbr-qry-form').find(":input[name='certNbr']").val(custInfo.certNbr);
                        this.queryNbrByParam();
                    }
                }.bind(this)
            );
        },

        onOkClick: function () {
            var logModel = new TrackPointLogModel();
            logModel.set('operCode', 'OE001000');
            logModel.set('stepCode', 'OE001110');
            // logParam.event_info = [{
            //     "key": "ORDER_ENTRY_SERVICE_NUMBER_CONFIRM"
            // }];
            CommonAction.recordPageTrackPointLog(logModel);
            var rowData = this.selectedAccNbr;
            if (this.options.chooseNbrCallback && fish.isFunction(this.options.chooseNbrCallback)) {
                this.options.chooseNbrCallback(rowData, this.oldAccNbr);
            }
            if (
                portal.appGlobal.get('isBolLogin') &&
                (fish.store.get('accountOpeningMethod') == 'mobileOffer' ||
                    fish.store.get('accountOpeningMethod') == 'boundleOffer' ||
                    fish.store.get('accountOpeningMethod') == 'contractPhone')
            ) {
                fish.trigger(IntroDef.EXIT_INTRO);
            }
            // if(rowData.isBindingFlag == 'Y' && (!rowData.simCardId || rowData.simCardId == '')) {
            //     fish.error(I18N.ORDER_ENTRY_IS_NOT_AVAILABLE);
            //     return;
            // }
            this.popup.close(rowData);
        },

        // Query按钮点击事件
        onQueryClick: function () {
            if (!this.$('.js-acct-nbr-qry-form').isValid()) {
                return;
            }

            var formValue = this.$('.js-acct-nbr-qry-form').form('value');
            formValue.accNbrClassId = this.$(":input[name='accNbrClassId']").multiselect('value');
            if (formValue.accNbrClassId) {
                if (fish.isArray(formValue.accNbrClassId)) {
                    formValue.accNbrClassId = formValue.accNbrClassId.join(',');
                }
            }
            // 选号费精度开始转化
            if (formValue.accNbrPriceStart) {
                formValue.accNbrPriceStart = this.$(":input[name='accNbrPriceStart']").currencybox('currencyValue');
            }
            // 选号费精度结束转化
            if (formValue.accNbrPriceEnd) {
                formValue.accNbrPriceEnd = this.$(":input[name='accNbrPriceEnd']").currencybox('currencyValue');
            }

            var param = this.getChooseAccNbrParam();

            param.selectFlag = 'Q';
            // 调用手动选号接口
            var paramData = {};
            paramData.requestParam = fish.extend(formValue, param);
            OrderEntryAction.chooseNbr(
                paramData,
                function (result) {
                    this.$accNbrGrid.grid('clearData');
                    var accNbrList = result.accNbrList;
                    if (accNbrList && accNbrList.length > 0) {
                        var dataList = [];
                        for (var i = 0; i < accNbrList.length; i++) {
                            var data = {};
                            data.accNbrId = accNbrList[i].accNbrId;
                            data.prefix = accNbrList[i].prefix;
                            data.accNbr = accNbrList[i].accNbr;
                            data.accNbrClassName = accNbrList[i].accNbrClassName;
                            data.accNbrPrice = accNbrList[i].accNbrPrice;
                            dataList.push(data);
                        }
                        this.$accNbrGrid.grid('reloadData', dataList);
                        this.$('.js-ok').attr('disabled', false);
                        this.$accNbrGrid.grid('setSelection', dataList[0]);
                        this.accNbrFillFlag = true;
                        this.accNbrChoosed = data.accNbr;
                    } else {
                        // 返回结果为空
                        this.$('.js-ok').attr('disabled', true);
                        fish.info(I18N.HINT_SEARCH_MATCH_NULL);
                    }
                }.bind(this)
            );
        },

        getChooseAccNbrParam: function () {
            var param = {};
            param.contactChannelId = ContactChannelDef.CSR;

            if (this.options.orgId) {
                param.orgId = this.options.orgId;
            } else {
                param.orgId = portal.appGlobal.get('orgId');
            }
            if (this.options.jobId) {
                param.jobId = this.options.jobId;
            } else {
                param.jobId = portal.appGlobal.get('jobId');
            }

            if (this.options.staffId) {
                param.staffId = this.options.staffId;
            } else {
                param.staffId = portal.appGlobal.get('staffId');
            }

            param.indepProdSpecId = this.options.indepProdSpecId;
            param.custId = this.options.custId;
            // param.certTypeId = options.certTypeId;
            // param.certNbr = options.certNbr;
            param.servType = this.options.servType;
            param.spId = this.options.spId;
            param.servType = this.options.servType;

            // 换号需要带hlrId去查
            if (this.options.hlrId) {
                param.hlrId = this.options.hlrId;
            }

            var offerIds = [];
            offerIds.push(this.options.subsPlanId);
            param.offerIds = offerIds;

            if (this.options.accNbrClassId && !this.qryParamMap.accNbrClassId) {
                param.accNbrClassId = this.options.accNbrClassId;
            }
            if (this.options.accNbrRuleId && !this.qryParamMap.accNbrRuleId) {
                param.accNbrRuleId = this.options.accNbrRuleId;
            }
            return param;
        },

        autoSlideMenu: function () {
            var $tpl = this.$('.js-number-rule');
            var $ulArr = $tpl.find('ul');
            $ulArr.each(function () {
                var AUTO_HEIGHT = 28; // 默认高度(数值类型)
                $(this).css({ overflow: 'hidden', position: 'relative' });
                var realHeight = parseInt($(this)[0].scrollHeight, 10); // 真实高度(数值类型)
                if (realHeight > AUTO_HEIGHT) {
                    $(this).append('<span class="iconfont icon-chevron-down js-toggle-slide"></span>');
                    $(this).height(AUTO_HEIGHT);
                    $(this)
                        .find('.js-toggle-slide')
                        .css({ position: 'absolute', top: '0', right: '0' })
                        .on('click', function () {
                            var minHight = 28;
                            var currentHight = parseInt(this.parentNode.scrollHeight, 10);
                            if ($(this).hasClass('icon-chevron-down')) {
                                $(this).removeClass('icon-chevron-down');
                                $(this).addClass('icon-chevron-up');
                                $(this).parent().height(currentHight);
                            } else {
                                $(this).removeClass('icon-chevron-up');
                                $(this).addClass('icon-chevron-down');
                                $(this).parent().height(minHight);
                            }
                        });
                }
            });
        }
    });
});
