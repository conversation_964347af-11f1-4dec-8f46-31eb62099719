﻿/**
 * 社交账号管理组件
 *
 * @prop {Object} $rootEl - 根DOM元素容器
 * @prop {boolean} detailFlag - 详情展示模式标志
 * @prop {Object} orderItem - 订单项主数据
 * @prop {Object} orderItemDetail - 订单项明细数据
 *
 */
define([
    'hbs!crm/modules/order/components/templates/SocialNbrTpl.hbs',
    'i18n!crm/modules/pos/flowpage/common/i18n/CustomerCare',
    'crm/modules/fbf/bfm/models/DataInput',
    'crm/modules/fbf/bfm/utils/Controller',
    'crm/modules/common/components/SocialTypeCombobox',
    'crm/modules/order/orderentry/utils/BoUtils',
    'crm/modules/fbf/bfm/constant/OperationTypeDef',
    'crm/modules/common/constant/CommonDef',
], function (SocialNbrTpl, I18N, DataInput, Controller, SocialTypeCombobox, BoUtils, OperationTypeDef, CommonDef) {
    return portal.BaseView.extend({
        template: SocialNbrTpl,
        serialize: I18N,
        events: {
            'click .js-new': 'onNewClick',
            'click .js-remove': 'onRemoveClick',
            'combobox:change .js-social-type': 'onSocialTypeChange',
            'blur .js-social-medial': 'onSocialNbrFocusOut',
        },
        initialize: function (initOptions) {
            this.$rootEl = initOptions.rootEl ? initOptions.rootEl : initOptions.options;
            this.$socialTypeCombobox = null;
            this.num = 1;
            this.count = 1;
            this.socialOrderSeqMap = {};
            this.orderItem = initOptions.options.orderItem;
            this.orderItemDetail = initOptions.orderItemDetail;
            this.detailFlag = initOptions.detailFlag;
            // MODIFIED BY humm 2025-05-27
            // this.commonDataId = initOptions?.options?.commonDataId;
        },
        afterRender: function () {
            console.log('SocialNbrComponent111', Math.random() * 10000);
            this.$socialTypeCombobox = new SocialTypeCombobox(this.$(":input[name='SocialType1']"));
            // 先隐藏   是否隐藏，需要根据   this.detailFlag
            if (this.detailFlag) {
                this.$('.socialNbrInfo1').hide();
            }
            if (this.orderItem && this.orderItem.SUBS_SOCIAL_ORDER) {
                // 有订单需要展示在显示
                if (this.detailFlag) {
                    this.$('.socialNbrInfo1').show();
                }
                var that = this;
                var firstflag = true;
                fish.each(that.orderItem.SUBS_SOCIAL_ORDER, function (socialOrder) {
                    if (
                        socialOrder.OPERATION_TYPE &&
                        socialOrder.OPERATION_TYPE !== 'D' &&
                        CommonDef.SOCIAL_NBR_TYPE_SUBSCRIBER_EMAIL != socialOrder.SOCIAL_NBR_TYPE
                    ) {
                        if (firstflag) {
                            firstflag = false;
                            that.socialOrderSeqMap[that.num] = socialOrder.SEQ;
                            that.$(":input[name='SocialType1']").val(socialOrder.SOCIAL_NBR_TYPE);
                            that.$(":input[name='SocialMedial1']").val(socialOrder.SOCIAL_NBR);
                        } else {
                            if (that.detailFlag) {
                                if (socialOrder.SOCIAL_NBR_TYPE && socialOrder.SOCIAL_NBR) {
                                    that.num = that.num + 1;
                                    that.count = that.count + 1;
                                    var html = that.getOfferTcLinkHtml(
                                        I18N.SOCIAL_MEDIAL_TYPE,
                                        I18N.SOCIAL_MEDIAL_ID,
                                        I18N.ADD,
                                        I18N.REMOVE,
                                        that.num
                                    );
                                    that.$('.socialNbrInfos').append(html);
                                    that.$socialTypeCombClassobox = new SocialTypeCombobox(this.$(":input[name='SocialType" + that.num + "']"));
                                    that.socialOrderSeqMap[that.num] = socialOrder.SEQ;
                                    that.$(":input[name='SocialType" + that.num + "']").val(socialOrder.SOCIAL_NBR_TYPE);
                                    that.$(":input[name='SocialMedial" + that.num + "']").val(socialOrder.SOCIAL_NBR);
                                }
                            } else {
                                that.num = that.num + 1;
                                that.count = that.count + 1;
                                var html = that.getOfferTcLinkHtml(I18N.SOCIAL_MEDIAL_TYPE, I18N.SOCIAL_MEDIAL_ID, I18N.ADD, I18N.REMOVE, that.num);
                                that.$('.socialNbrInfos').append(html);
                                that.$socialTypeCombClassobox = new SocialTypeCombobox(this.$(":input[name='SocialType" + that.num + "']"));
                                that.socialOrderSeqMap[that.num] = socialOrder.SEQ;
                                that.$(":input[name='SocialType" + that.num + "']").val(socialOrder.SOCIAL_NBR_TYPE);
                                that.$(":input[name='SocialMedial" + that.num + "']").val(socialOrder.SOCIAL_NBR);
                            }
                        }
                    }
                });
            }
            if (this.orderItemDetail) {
                if (this.orderItemDetail.orderSubsSocialList) {
                    // 有订单需要展示在显示
                    this.$('.socialNbrInfo1').show();
                }

                var that = this;
                var firstflag = true;
                fish.each(that.orderItemDetail.orderSubsSocialList, function (orderSubsSocialDetail) {
                    if (orderSubsSocialDetail.operationType !== 'D') {
                        if (firstflag) {
                            firstflag = false;
                            that.$(":input[name='SocialType1']").val(orderSubsSocialDetail.socialNbrType);
                            that.$(":input[name='SocialMedial1']").val(orderSubsSocialDetail.socialNbr);
                        } else {
                            that.num = that.num + 1;
                            that.count = that.count + 1;
                            var html = that.getOfferTcLinkHtml(I18N.SOCIAL_MEDIAL_TYPE, I18N.SOCIAL_MEDIAL_ID, I18N.ADD, I18N.REMOVE, that.num);
                            that.$('.socialNbrInfos').append(html);
                            that.$socialTypeCombClassobox = new SocialTypeCombobox(this.$(":input[name='SocialType" + that.num + "']"));
                            that.$(":input[name='SocialType" + that.num + "']").val(orderSubsSocialDetail.socialNbrType);
                            that.$(":input[name='SocialMedial" + that.num + "']").val(orderSubsSocialDetail.socialNbr);
                        }
                    }
                });
            }
        },
        onNewClick: function () {
            this.num = this.num + 1;
            this.count = this.count + 1;
            var html = this.getOfferTcLinkHtml(I18N.SOCIAL_MEDIAL_TYPE, I18N.SOCIAL_MEDIAL_ID, I18N.ADD, I18N.REMOVE, this.num);
            this.$('.socialNbrInfos').append(html);
            this.$socialTypeCombClassobox = new SocialTypeCombobox(this.$(":input[name='SocialType" + this.num + "']"));

            var socialNbr = this.$(":input[name='SocialMedial" + this.num + "']").val();
            var socialNbrType = this.$(":input[name='SocialType" + this.num + "']").val();
            var Data = {};
            Data.ORDER_ITEM_ID = this.orderItem.ORDER_ITEM_ID;
            Data.PART_ID = this.orderItem.PART_ID;
            Data.ROUTING_ID = this.orderItem.ROUTING_ID;
            Data.OPERATION_TYPE = 'A';
            Data.SEQ = fish.getUUID();
            Data.SOCIAL_NBR_TYPE = socialNbrType;
            Data.SOCIAL_NBR = socialNbr;
            this.socialOrderSeqMap[this.num] = Data.SEQ;
            var dataInput = new DataInput();
            dataInput.set('boAccessName', 'subsSocialOrder');
            dataInput.set('operationType', OperationTypeDef.NEW);
            dataInput.set('value', Data);
            dataInput.set('bo', true);
            Controller.getInstance().sendSingleBoChange(
                this.commonDataId,
                dataInput,
                this,
                null,
                function (result) {
                    BoUtils.fetchOrderItem(
                        this.orderItem.ORDER_ITEM_ID,
                        this,
                        function (temp) {
                            this.orderItem = temp;
                        }.bind(this)
                    );
                }.bind(this)
            );
        },
        onRemoveClick: function (e) {
            var SocialOrder;
            var currentName = e.currentTarget.name;
            var Id = currentName.substring(6, currentName.length);
            seq = this.socialOrderSeqMap[Id];
            if (this.orderItem.SUBS_SOCIAL_ORDER) {
                fish.each(this.orderItem.SUBS_SOCIAL_ORDER, function (subsSocialOrder) {
                    if (subsSocialOrder.SEQ == seq) {
                        SocialOrder = subsSocialOrder;
                    }
                });
            }

            if (this.count > 1) {
                this.$('.socialNbrInfo' + Id + '').hide();
                this.count = this.count - 1;
            } else if (this.count == 1) {
                this.$(":input[name='SocialType" + Id + "']").combobox('clear');
                this.$(":input[name='SocialMedial" + Id + "']").val('');
            }
            if (!seq || seq == '') {
                return;
            }
            this.socialOrderSeqMap[Id] = '';

            var Data = {};
            var socialNbr = '';
            var socialNbrType = '';
            Data.ORDER_ITEM_ID = this.orderItem.ORDER_ITEM_ID;
            Data.SEQ = seq;
            Data.PART_ID = this.orderItem.PART_ID;
            Data.SOCIAL_NBR_TYPE = socialNbrType;
            Data.SOCIAL_NBR = socialNbr;
            var dataInput = new DataInput();

            if (SocialOrder && (SocialOrder.OPERATION_TYPE == 'X' || SocialOrder.OPERATION_TYPE == 'M')) {
                dataInput.set('operationType', OperationTypeDef.CHANGE);
                Data.OPERATION_TYPE = 'D';
            } else {
                dataInput.set('operationType', OperationTypeDef.CANCEL);
            }
            dataInput.set('boAccessName', 'subsSocialOrder');
            dataInput.set('value', Data);
            dataInput.set('bo', true);
            Controller.getInstance().sendSingleBoChange(
                this.commonDataId,
                dataInput,
                this,
                null,
                function (result) {
                    BoUtils.fetchOrderItem(
                        this.orderItem.ORDER_ITEM_ID,
                        this,
                        function (temp) {
                            this.orderItem = temp;
                        }.bind(this)
                    );
                }.bind(this)
            );
        },
        onSocialTypeChange: function (e) {
            var SocialOrder;
            var currentName = e.currentTarget.name;
            var Id = currentName.substring(10, currentName.length);
            seq = this.socialOrderSeqMap[Id];
            if (this.orderItem.SUBS_SOCIAL_ORDER) {
                fish.each(this.orderItem.SUBS_SOCIAL_ORDER, function (subsSocialOrder) {
                    if (subsSocialOrder.SEQ == seq) {
                        SocialOrder = subsSocialOrder;
                    }
                });
            }

            if (!seq || seq == '') {
                var Data = {};
                Data.ORDER_ITEM_ID = this.orderItem.ORDER_ITEM_ID;
                Data.PART_ID = this.orderItem.PART_ID;
                Data.ROUTING_ID = this.orderItem.ROUTING_ID;
                Data.OPERATION_TYPE = 'A';
                Data.SEQ = fish.getUUID();
                this.socialOrderSeqMap[Id] = Data.SEQ;
                var dataInput = new DataInput();
                dataInput.set('boAccessName', 'subsSocialOrder');
                dataInput.set('operationType', OperationTypeDef.NEW);
                dataInput.set('value', Data);
                dataInput.set('bo', true);
                Controller.getInstance().sendSingleBoChange(
                    this.commonDataId,
                    dataInput,
                    this,
                    null,
                    function (result) {
                        BoUtils.fetchOrderItem(
                            this.orderItem.ORDER_ITEM_ID,
                            this,
                            function (temp) {
                                this.orderItem = temp;
                            }.bind(this)
                        );
                    }.bind(this)
                );
            }

            var socialNbr = this.$(":input[name='SocialMedial" + Id + "']").val();
            var socialNbrType = this.$(":input[name='SocialType" + Id + "']").val();

            var Data = {};
            seq = this.socialOrderSeqMap[Id];
            Data.SEQ = seq;
            Data.ORDER_ITEM_ID = this.orderItem.ORDER_ITEM_ID;
            Data.PART_ID = this.orderItem.PART_ID;
            Data.ROUTING_ID = this.orderItem.ROUTING_ID;
            Data.SOCIAL_NBR_TYPE = socialNbrType;
            Data.SOCIAL_NBR = socialNbr;
            if (SocialOrder && (SocialOrder.OPERATION_TYPE == 'X' || SocialOrder.OPERATION_TYPE == 'M')) {
                Data.OPERATION_TYPE = 'M';
            } else {
                Data.OPERATION_TYPE = 'A';
            }

            var dataInput = new DataInput();
            dataInput.set('boAccessName', 'subsSocialOrder');
            dataInput.set('operationType', OperationTypeDef.CHANGE);
            if (!socialNbr && !socialNbrType) {
                if (SocialOrder && (SocialOrder.OPERATION_TYPE == 'X' || SocialOrder.OPERATION_TYPE == 'M')) {
                    Data.OPERATION_TYPE = 'D';
                    dataInput.set('operationType', OperationTypeDef.CHANGE);
                } else {
                    dataInput.set('operationType', OperationTypeDef.CANCEL);
                }
            }
            dataInput.set('value', Data);
            dataInput.set('bo', true);
            Controller.getInstance().sendSingleBoChange(
                this.commonDataId,
                dataInput,
                this,
                null,
                function (result) {
                    BoUtils.fetchOrderItem(
                        this.orderItem.ORDER_ITEM_ID,
                        this,
                        function (temp) {
                            this.orderItem = temp;
                        }.bind(this)
                    );
                }.bind(this)
            );
        },
        onSocialNbrFocusOut: function (e) {
            var SocialOrder;
            var currentName = e.currentTarget.name;
            var Id = currentName.substring(12, currentName.length);
            seq = this.socialOrderSeqMap[Id];
            if (this.orderItem.SUBS_SOCIAL_ORDER) {
                fish.each(this.orderItem.SUBS_SOCIAL_ORDER, function (subsSocialOrder) {
                    if (subsSocialOrder.SEQ == seq) {
                        SocialOrder = subsSocialOrder;
                    }
                });
            }
            if (!seq || seq == '') {
                var Data = {};
                Data.ORDER_ITEM_ID = this.orderItem.ORDER_ITEM_ID;
                Data.PART_ID = this.orderItem.PART_ID;
                Data.ROUTING_ID = this.orderItem.ROUTING_ID;
                Data.OPERATION_TYPE = 'A';
                Data.SEQ = fish.getUUID();
                // this.$(":input[name='SocialType"+this.num+"']").addClass(Data.SEQ);
                this.socialOrderSeqMap[Id] = Data.SEQ;
                var dataInput = new DataInput();
                dataInput.set('boAccessName', 'subsSocialOrder');
                dataInput.set('operationType', OperationTypeDef.NEW);
                dataInput.set('value', Data);
                dataInput.set('bo', true);
                Controller.getInstance().sendSingleBoChange(
                    this.commonDataId,
                    dataInput,
                    this,
                    null,
                    function (result) {
                        BoUtils.fetchOrderItem(
                            this.orderItem.ORDER_ITEM_ID,
                            this,
                            function (temp) {
                                this.orderItem = temp;
                            }.bind(this)
                        );
                    }.bind(this)
                );
            }

            var socialNbr = this.$(":input[name='SocialMedial" + Id + "']").val();
            var socialNbrType = this.$(":input[name='SocialType" + Id + "']").val();

            var Data = {};
            Data.ORDER_ITEM_ID = this.orderItem.ORDER_ITEM_ID;
            Data.PART_ID = this.orderItem.PART_ID;
            Data.ROUTING_ID = this.orderItem.ROUTING_ID;
            seq = this.socialOrderSeqMap[Id];
            Data.SEQ = seq;
            Data.SOCIAL_NBR_TYPE = socialNbrType;
            Data.SOCIAL_NBR = socialNbr;
            if (SocialOrder && (SocialOrder.OPERATION_TYPE == 'X' || SocialOrder.OPERATION_TYPE == 'M')) {
                Data.OPERATION_TYPE = 'M';
            } else {
                Data.OPERATION_TYPE = 'A';
            }
            var dataInput = new DataInput();
            dataInput.set('boAccessName', 'subsSocialOrder');
            dataInput.set('operationType', OperationTypeDef.CHANGE);
            if (!socialNbr && !socialNbrType) {
                if (SocialOrder && (SocialOrder.OPERATION_TYPE == 'X' || SocialOrder.OPERATION_TYPE == 'M')) {
                    Data.OPERATION_TYPE = 'D';
                    dataInput.set('operationType', OperationTypeDef.CHANGE);
                } else {
                    dataInput.set('operationType', OperationTypeDef.CANCEL);
                }
            }
            dataInput.set('value', Data);
            dataInput.set('bo', true);
            Controller.getInstance().sendSingleBoChange(
                this.commonDataId,
                dataInput,
                this,
                null,
                function (result) {
                    BoUtils.fetchOrderItem(
                        this.orderItem.ORDER_ITEM_ID,
                        this,
                        function (temp) {
                            this.orderItem = temp;
                        }.bind(this)
                    );
                }.bind(this)
            );
        },
        getOfferTcLinkHtml: function (type, id, add, remove, num) {
            var hrfmHtml =
                '<div class="socialNbrInfo' +
                num +
                '">' +
                '<div class="row form-horizontal">' +
                '<div class="col-xs-12 col-sm-8 col-md-4">' +
                '<div class="form-group">' +
                '<label class="col-md-4 col-sm-4 control-label" style="text-align: right" title="' +
                type +
                '">' +
                type +
                '</label>' +
                '<div class="col-md-8 col-sm-8">' +
                '<input autocomplete="off"  type="text" name="SocialType' +
                num +
                '" class="form-control js-social-type">' +
                '</div>' +
                '</div>' +
                '</div>' +
                '<div class="col-xs-12 col-sm-8 col-md-4 ">' +
                '<div class="form-group">' +
                '<label class="col-md-4 col-sm-4 control-label" style="text-align: right">' +
                id +
                '</label>' +
                '<div class="col-md-8 col-sm-8">' +
                '<input autocomplete="off"  type="text" name="SocialMedial' +
                num +
                '" class="form-control js-social-medial">' +
                '</div>' +
                '</div>' +
                '</div>' +
                '<div class="col-xs-12 col-sm-8 col-md-4 ">' +
                '<div class="col-md-8 col-sm-8">' +
                '<button type="button" name="add' +
                num +
                '" style="min-width: 42px" class="btn btn-min-width btn-default js-new">' +
                add +
                '</button>' +
                '<button type="button" name="remove' +
                num +
                '" style="margin-left:4px;min-width: 42px" class="btn btn-min-width btn-default js-remove">' +
                remove +
                '</button>' +
                '</div>' +
                '</div>' +
                '</div>' +
                '</div>';
            return hrfmHtml;
        },
    });
});
