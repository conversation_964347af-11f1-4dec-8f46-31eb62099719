/**
 * @component InstallationAddressView
 * @description 安装地址选择组件
 *
 * @property {Object} viewOptions - 视图选项
 * @property {string} customClassNameFishInPageDesigner - 页面设计器中的自定义类名
 * @property {Function} onClose - 关闭弹窗回调函数
 * @property {Function} onClear - 清除选择回调函数
 *
 * @method getValue - 获取选中的地址值
 * @method setValue - 设置地址值
 * @method disable - 禁用组件
 * @method enable - 启用组件
 * @method clear - 清除选择
 */

define([], function () {
    return function ($el, options) {
        this.$el = $el;

        var iOptions = options || {};
        var newOptions = iOptions.viewOptions;

        if (iOptions.customClassNameFishInPageDesigner) {
            newOptions.customClassNameFishInPageDesigner = iOptions.customClassNameFishInPageDesigner;
        }

        if (iOptions.popeditOptions) {
            this.initOptions = iOptions.popeditOptions;
            this.initOptions.dataTextField = 'text';
            this.initOptions.dataValueField = 'value';
        } else {
            // page-designer 中的默认配置
            this.initOptions = {
                open: function () {
                    fish.popupView({
                        url: 'crm/modules/fix/common/views/popwin/SelStandAddressPopView',
                        viewOption: newOptions,
                        close: iOptions.onClose,
                        width: '70%'
                    });
                },
                dataTextField: 'text',
                dataValueField: 'value',
                change: function (e, data) {
                    // 点击 叉 图标后的函数回调
                    if (data == null && iOptions && typeof iOptions.onClear == 'function') {
                        iOptions.onClear();
                    }
                }
            };
        }

        this.create = function () {
            this.popWin = this.$el.popedit(this.initOptions);
            return this;
        }.bind(this)();

        // 引入popedit 的 获取/设置/禁用/启用/清除 方法
        this.popedit = function (type) {
            var args = Array.prototype.slice.call(arguments, 1);
            switch (type) {
                case 'getValue':
                    return this.popWin.popedit('getValue');
                case 'setValue':
                    return this.popWin.popedit.apply(this.popWin, ['setValue'].concat(args));
                case 'disable':
                    return this.popWin.popedit('disable');
                case 'enable':
                    return this.popWin.popedit('enable');
                case 'clear':
                    return this.popWin.popedit('clear');
                default:
                    return this.popWin.popedit.apply(this.popWin, arguments);
            }
        }.bind(this);
    };
});
