/**
 * 处于表单内的账户选择器，可供低码平台使用
 *
 * @param $el input 元素
 * @param options
 * @returns {AccountSelectorInFormView}
 */

define([], function () {
    return function ($el, options) {
        this.$el = $el;

        // 当前 form-group 元素
        this.$formGroup = this.$el.parents('.form-group:first');

        // 当前 form-group 元素的父元素，即当前 input 所在的整个 column 元素
        this.$formGroupColumn = this.$el.parents('.form-group:first').parent();

        // 显示当前 column 的 label
        this.$formGroupLabel = this.$el.parents('.form-group:first').find('label:first');

        // 展示当前整个 column 元素
        this.showColumn = function () {
            this.$formGroupColumn.show();
        };

        // 设置 label 的名称
        this.setLabelText = function (label) {
            this.$formGroupLabel.text(label);
        };

        // 设置 input 的值
        this.setInputValue = function (value) {
            this.$el.val(value);
        };

        // 获取 input 的值
        this.getInputValue = function () {
            return this.$el.val();
        };

        // 设置 是否只读
        this.setReadonly = function (flag) {
            if (flag) {
                const nextEl = this.$el.next();
                if (nextEl.is('span') && nextEl.hasClass('form-clear-input')) {
                    nextEl.remove();
                }
            }
        };

        // 检测是否存在该类名，如果没有则增加，反之忽略
        this.addClassOptimized = function ($targetEl, className) {
            if (!$targetEl.hasClass(className)) {
                $targetEl.addClass(className);
            }
        };

        // 检测是否存在该类名，如果有则移除，反之忽略
        this.removeClassOptimized = function ($targetEl, className) {
            if ($targetEl.hasClass(className)) {
                $targetEl.removeClass(className);
            }
        };

        // 处理当前 column 是否必填
        this.dealClassRequired = function (flag) {
            if (flag) {
                this.addClassOptimized(this.$formGroup, 'required');
            } else {
                this.removeClassOptimized(this.$formGroup, 'required');
            }
        };

        this.create = function () {
            //  设置 label 默认值
            if (options && options.defaultLabelText) {
                this.setLabelText(options.defaultLabelText);
            }

            //  设置 input 默认值
            if (options && options.defaultInputValue) {
                this.setInputValue(options.defaultValue);
            }

            //  设置 是否只读默认值
            if (options && options.readonly) {
                this.setReadonly(options.readonly);
            }

            // 设置是否默认展示 column
            if (options && options.defaultShowColumn) {
                this.showColumn();
            }

            // 设置 是否必填默认值
            if (options) {
                if (typeof options.isRequired === 'boolean') {
                    this.dealClassRequired(options.isRequired);
                }
            }

            return this;
        }.bind(this)();
    };
});
