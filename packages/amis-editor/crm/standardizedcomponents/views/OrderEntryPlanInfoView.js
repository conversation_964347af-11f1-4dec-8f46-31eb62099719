/* eslint-disable indent */
define([
    'hbs!crm/standardizedcomponents/templates/OrderEntryPlanInfoTpl.hbs',
    'i18n!crm/modules/pos/workspace/i18n/OrderEntry',
    'i18n!crm/modules/pos/flowpage/custorderInfo/i18n/CustOrderInfo',
    'i18n!crm/modules/pos/flowpage/common/i18n/CustomerCare',
    'crm/modules/common/actions/ConfigItemAction',
    'crm/modules/common/components/DefLanguageCombobox',
    'crm/modules/pos/flowpage/business/component/constants/InputTypeDef',
    'crm/modules/pos/common/utils/BoUtils',
    'crm/modules/order/components/AccountSelectorComponent',
    'crm/modules/common/components/UserSelectPopedit'
], function (
    OrderEntryPlanInfoTpl,
    OrderEntryI18N,
    CustOrderInfoI18N,
    CustomerCareI18N,
    ConfigItemAction,
    DefLanguageCombobox,
    InputTypeDef,
    BoUtils,
    AccountSelectorComponent,
    UserSelectPopedit
) {
    return function (context, options, Controller, customOrderEntryPlanInfoTpl) {
        this.userHideFlag = null;
        this.$vasTreeView = null;
        this.$selectNumberPop = null;
        this.options = options || {};

        var $el = context.$orderEntryPlanInfoView;
        if ($el && $el.length > 0 && typeof $el.append === 'function') {
            if (customOrderEntryPlanInfoTpl) {
                $el.append(customOrderEntryPlanInfoTpl(fish.extend({}, OrderEntryI18N, CustOrderInfoI18N, CustomerCareI18N)));
            } else {
                $el.append(OrderEntryPlanInfoTpl(fish.extend({}, OrderEntryI18N, CustOrderInfoI18N, CustomerCareI18N)));
            }
        }

        this.prepaidCreateAcctFlagFn = function () {
            context.prepaidCreateAcctFlag = ConfigItemAction.qryParamValue('ACCT', 'COMMON', 'PREPAID_CREATE_ACCT_FLAG');
        };

        // 获取屏蔽组件的配置项信息
        this.qryHideConfig = function () {
            context.userHideFlag = ConfigItemAction.qryParamValue('CUSTOMER_CARE', 'CC_PUBLIC', 'USER_HIDE_FLAG');
        };

        this.initBasicInfoForm =
            context.initBasicInfoForm && typeof context.initBasicInfoForm === 'function'
                ? context.initBasicInfoForm.bind(context)
                : function () {
                      var self = this;
                      // 初始化userName
                      context.$(":input[name='userName']").val(context.subsBaseOrder.CUST_NAME);
                      if (!fish.isEmpty(context.userHideFlag) && context.userHideFlag == 'Y') {
                          context.$('.js-user-name').hide();
                      }

                      // 初始化subsPlanName
                      context.$(":input[name='subsPlanName']").val(context.subsBaseOrder.SUBS_PLAN_NAME);
                      // 初始化orderNbr
                      context.$(":input[name='orderNbr']").val(context.orderItem.ORDER_NBR);
                      // 初始化默认语言
                      self.initDefLang();
                      // // 初始化预约日期,先不考虑
                      // 初始化支付账户
                      self.initAccountSelectorComponent();

                      if (context.showVpnNameFlag && context.subsBriefDetail) {
                          context.$('.js-container-vpn').show().find('[name=vpnName]').val(context.subsBriefDetail.subsPlanName);
                      }
                  };

        /**
         * 初始化默认语言
         */
        this.initDefLang = function () {
            // 初始化默认语言
            var initDefLangId = '';
            if (context.subsBaseOrder) {
                initDefLangId = context.subsBaseOrder.DEF_LANG_ID;
            }
            context.$defaultLanguageCombobox = new DefLanguageCombobox(context.$(":input[name='languageId']"), {
                subsFlag: true,
                initDefLangId: initDefLangId,
                boAccess: {
                    selector: ":input[name='languageId']",
                    boAccessName: 'subsBaseOrder',
                    boItemAccessName: 'defLangId',
                    $currentView: context,
                    inputType: InputTypeDef.COMBOBOX,
                    success: null,
                    fail: null
                }
            });
        };

        // 初始化支付账户查询选择组件
        this.initAccountSelectorComponent = function () {
            var self = this;
            if (BoUtils.isSameAcct(context.custOrder, context.bundleOrderItem)) {
                context.$('.js-container-acct-id').remove();
                return;
            }
            var initOptions = {};

            var $parentView = context.parentView || context.$customParentView;

            if (context.bundleOrderItem) {
                initOptions.syncOneAcctFlag = true;
                initOptions.$syncOneAcctRootView = $parentView;
            }

            if (context.subsBaseOrder && context.subsBaseOrder.ACCT_ID) {
                initOptions.acctId = context.subsBaseOrder.ACCT_ID;
                initOptions.acctNbr = context.subsBaseOrder.ACCT_NBR;
                initOptions.acctName = context.orderItem.ACCT ? context.orderItem.ACCT.ACCT_NAME : '';
                initOptions.defLangId = context.subsBaseOrder.DEF_LANG_ID;
            } else if (context.orderItem.ACCT) {
                initOptions.acctId = context.orderItem.ACCT.ACCT_ID;
                initOptions.acctNbr = context.orderItem.ACCT.ACCT_NBR;
                initOptions.acctName = context.orderItem.ACCT.ACCT_NAME;
            }

            if (context.orderItem.POSTPAID == 'N' && context.orderItem.CUST_INFO.CUST_TYPE == 'A' && context.prepaidCreateAcctFlag == 'Y') {
                initOptions.disabled = true;
            }
            if (context.acctDisabled) {
                initOptions.disabled = true;
            }
            initOptions.custType = context.orderItem.CUST_INFO.CUST_TYPE;
            initOptions.custId = context.orderItem.CUST_ID;
            initOptions.postpaid = context.orderItem.POSTPAID;
            initOptions.isPrimary = context.isPrimary;
            initOptions.boAccess = {
                $currentView: context,
                selector: ":input[name='acctNbr']",
                boAccessName: 'subsBaseOrder',
                boItemAccessName: 'acctId',
                inputType: InputTypeDef.CUSTOM,
                success: null,
                fail: function () {
                    // 选择账户 bfm 后台失败，清空 acct，不然可以继续 next
                    this.$(":input[name='acctNbr']").val('');
                    this.$accountSelectorComponent.setAcct(null);
                }.bind(context)
            };
            initOptions.changeCallback = function (e) {
                if ($parentView && $parentView.initDeliveryPanel) {
                    $parentView.initDeliveryPanel();
                }
                if (this.$vasTreeView) {
                    this.$vasTreeView.orderItem.ACCT_ID = e.acctId;
                }
                self.subsPlanOnChangeByAcct(e);
            }.bind(context);

            // 返回 account 内容
            initOptions.pageDesignerCallback = context.acctChangeCallback || function () {};

            this.$accountSelectorComponent = new AccountSelectorComponent(context.$(":input[name='acctNbr']"), initOptions);
        };

        this.subsPlanOnChangeByAcct = function () {
            BoUtils.fetchOrderItem(
                context.orderItem.ORDER_ITEM_ID,
                context,
                function (orderItem) {
                    this.orderItem = orderItem;
                    // 重新加载Vas树与主产品属性
                    if (this.$vasTreeView && this.$vasTreeView.removeView) {
                        this.$vasTreeView.removeView();
                    }
                    this.initVasCart();
                }.bind(context)
            );
        };

        // 初始化使用者组件
        this.initUserSelectPopedit =
            context.initUserSelectPopedit && typeof context.initUserSelectPopedit
                ? context.initUserSelectPopedit.bind(context)
                : function () {
                      if (!fish.isEmpty(context.userHideFlag) && context.userHideFlag == 'Y') {
                          context.$('.js-container-user').hide();
                          return;
                      }

                      var corporateFlag = context.orderItem.CUST_INFO.CUST_TYPE == 'C';
                      context.$userSelectPopedit = new UserSelectPopedit(context.$(':input[name="user"]'), {
                          corporateFlag: corporateFlag,
                          boAccess: {
                              $currentView: context,
                              selector: ':input[name="user"]',
                              boAccessName: 'subsBaseOrder',
                              boItemAccessName: 'userId',
                              inputType: InputTypeDef.POP_EDIT
                          },
                          changeCallback: context.userChangeCallback || function () {},
                          isShowBfmKey: context.isShowBfmKey || false
                      });

                      // 订单预览界面返回上一步，重新赋值使用者信息
                      if (context.orderItem && context.orderItem.SUBS_BASE_ORDER) {
                          if (context.orderItem.SUBS_BASE_ORDER.USER_ID && context.orderItem.SUBS_BASE_ORDER.USER_NAME) {
                              var val = {
                                  name: context.orderItem.SUBS_BASE_ORDER.USER_NAME,
                                  data: context.orderItem.SUBS_BASE_ORDER.USER_ID
                              };
                              context.$userSelectPopedit.setValue(val);
                          }
                      }
                  };

        this.onPaymentAcctComponent = function () {
            var acctNbr = $.trim(context.$(":input[name='acctNbr']").val());
            if (acctNbr) {
                context.$(":input[name='acctNbr']").resetElement();
            }
        };

        this.registerIccidChange = function () {
            var self = this;
            var boAccess = {
                selector: ":input[name='iccid']",
                boAccessName: 'orderItem',
                boItemAccessName: 'iccid',
                $currentView: context,
                inputType: InputTypeDef.TEXT,
                success: self.changeIccidSuccessHandler.bind(context),
                fail: self.changeIccidFailHandler.bind(context)
            };

            if (boAccess && boAccess.$currentView) {
                Controller.getInstance().registerChange(boAccess.$currentView, boAccess);
            }
        };

        this.changeIccidSuccessHandler = function (data) {
            var dirtyList = data.dirtyList;
            if (fish.isEmpty(dirtyList)) {
                return;
            }
            var accNbrObj = fish.findWhere(dirtyList, {
                boAccessName: 'orderItem',
                boItemAccessName: 'accNbr'
            });
            if (accNbrObj && accNbrObj.accNbr) {
                context.$selectNumberPop.setAccNbr({
                    accNbrId: accNbrObj.accNbrId,
                    accNbr: accNbrObj.accNbr,
                    prefix: accNbrObj.prefix
                });
            }

            // 手动输入的19 或者20位 iccid,数据回显
            var iccidObj = fish.findWhere(dirtyList, {
                boAccessName: 'orderItem',
                boItemAccessName: 'iccid'
            });

            if (iccidObj && iccidObj.value) {
                var baseTypeId = context.$(":input[name='baseTypeName']").val();
                // console.log('baseTypeId: ' + baseTypeId);
                var iccid = iccidObj.value;
                var param = {
                    ICCID: iccid,
                    ICCID_BEGIN: iccid,
                    CUST_ID: context.orderItem.CUST_ID,
                    QRY_CUST_NET_TYPE_FLAG: 'Y'
                };
                if (!iccid) {
                    var items = context.custOrder.ORDER_ITEM;
                    var currentItem = context.orderItem;
                    for (var i = 0; i < items.length; i++) {
                        if (currentItem.ORDER_ITEM_ID == items[i].ORDER_ITEM_ID) {
                            context.custOrder.ORDER_ITEM[i].ICCID = iccid;
                            context.orderItem.ICCID = iccid;
                            break;
                        }
                    }
                    return;
                }
                SimCardMgrAction.qrySimCardDetails(
                    param,
                    // bind(context)
                    function (iData) {
                        // console.log(JSON.stringify(data));
                        if (iData.z_d_r) {
                            var zdrElement = iData.z_d_r[0];
                            if (zdrElement == null || zdrElement.baseTypeId != baseTypeId) {
                                $(":input[name='iccid']").val('');
                                this.$(":input[name='iccid']").trigger('change', '');
                                fish.error("The Serial Sim Card's type is not match Sim Type selected,Please re-input!");
                            }
                        }
                    }.bind(context)
                );
            }
        };

        this.changeIccidFailHandler = function (data) {
            var errors = data.errors;
            if (errors && errors.length > 0) {
                context.$(":input[name='iccid']").val('').attr('simCardId', '');
            }
        };

        this.create = function () {
            return this;
        }.bind(this)();
    };
});
