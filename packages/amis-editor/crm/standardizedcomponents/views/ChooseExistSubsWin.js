/**
 * @param {Object} options - 初始化选项对象
 * @param {*} options.bundleOrderItem - 捆绑订单项
 * @param {*} options.custId - 客户 ID
 * @param {*} options.custType - 客户类型
 * @param {*} options.fixInstallOrder - 固定安装订单
 * @param {*} options.oldSubsList - 旧的订阅列表
 * @param {*} options.servType - 服务类型
 * @param {*} options.showPreAccNbr - 是否显示预账号
 * @param {*} options.subsList - 订阅列表
 * @param {*} options.subsPlanList - 订阅计划列表
 */
define([
    'hbs!crm/modules/pos/flowpage/bundle/components/templates/ChooseExistSubsWinTpl.hbs',
    'hbs!crm/modules/pos/flowpage/bundle/components/templates/CheckboxLabelTpl.hbs',
    'i18n!crm/modules/pos/flowpage/bundle/i18n/Bundle',
    'i18n!crm/modules/pos/workspace/i18n/OrderEntry',
    'modules/common/util/ObjectUtils',
    'crm/modules/pos/flowpage/bundle/utils/BundleUtils',
    'crm/modules/common/util/StringUtils',
    'crm/modules/pos/common/constant/ServTypeDef',
    'crm/modules/pos/flowpage/bundle/actions/BundleAction',
    'crm/modules/common/constant/CustTypeDef',
    'crm/modules/customer/actions/CustAction'
], function (OfferSelectPopWinTpl, CheckboxLabelTpl, I18N, i18nData, ObjectUtils, BundleUtils, StringUtils, ServTypeDef,
             BundleAction, CustTypeDef, CustAction) {
    return portal.BaseView.extend({
        template: OfferSelectPopWinTpl,
        serialize: fish.extend(I18N, i18nData),
        initFlag: false,
        events: {
            'click .js-exist-subs-query': 'loadExistSubs',
            'click .js-exist-subs-reset': 'onReset',
            'click .js-ok': 'onOkClick',
            'click .js-advanced': 'onAdvancedClick',
            'click .js-search-cust': 'onSearchCustClick',
            'keydown .js-search-cust-param': 'onSearchCustKeydown',
        },
        initialize: function (initOptions) {
            console.log("StandardizedComponents ChooseExistSubsWin initialized.8");
            this.options = initOptions;
            this.bundleOrderItem = this.options.bundleOrderItem;
            this.subsPlanList = this.options.subsPlanList;
            this.servType = this.options.servType;
            this.subsList = this.options.subsList || [];
            this.oldSubsList = this.options.oldSubsList || [];
            this.subsIdArr = fish.pluck(this.subsList, "subsId");
            this.accNbrArr = fish.pluck(this.subsList, "accNbr");
            this.corporateFlag = false;
            this.custType = this.options.custType;
            if (this.options.custType == CustTypeDef.CORPORATE) {
                this.corporateFlag = true;
            }
            this.custId = this.options.custId;
            this.newCustId = null;
            this.showPreAccNbr = this.options.showPreAccNbr;
            this.fixInstallOrder = this.options.fixInstallOrder;

        },
        afterRender: function () {
            this.$form = this.$(".js-subs-query-form");
            // 初始化DOM元素
            this.$existSubsGrid = this.$('.js-exist-subs');

            if (this.custId) {
                this.$(".js-custName-search").hide();
            }
            // 初始化查询
            this.initBundleSearch();

            // 初始化VAS树
            this.initExistSubsGrid();

            // 加载VAS树
            this.loadExistSubs();
        },

        // Advanced点击事件
        onAdvancedClick: function () {
            fish.popupView({
                url: "crm/modules/common/views/popwin/AdvancedSearchCustPopView",
                width: 800,
                viewOption: {
                    initShowGrid: true,
                    simpleCondFlag: false,
                    corporateFlag: this.corporateFlag, // 只查询企业客户
                    // menuType : this.menuType
                },
                close: function (closeData) {
                    if (!closeData) {
                        return;
                    }
                    this.fillUpCustId(closeData);
                }.bind(this),
                callback: function (popup) {
                    popup.hide();
                }
            });
        },

        // 搜索客户文本框键盘监听事件
        onSearchCustKeydown: function (e) {
            if (e.keyCode == 13) {
                // 回车之后自动失去焦点，防止触发多次回车事件
                this.$(e.target).blur();
                this.onSearchCustClick();
            }
        },

        // 简单查询按钮点击事件
        onSearchCustClick: function (e) {
            var searchValue = $.trim(this.$(".js-search-cust-param").val());
            if (!searchValue) {
                return;
            }
            // 清空输入项
            this.$(".js-search-cust-param").val('');
            this.searchParam = searchValue;
            var paramData = {};

            paramData.CUST_NAME_ACC_BER_CERT_NBR = searchValue;
            paramData.CUST_TYPE = this.custType;
            paramData.SEARCH_COND_TYPE = this.$currentSearchCondType;
            this.searchCustHandler(paramData)
        },

        searchCustHandler: function (paramData) {
            $.blockUI();
            CustAction.qryCustByNameOrANbrOrCNbr(paramData, function (result) {
                $.unblockUI();
                var custBaseDetailDtoList = [];
                if (result.custBaseDetailDtoList && result.custBaseDetailDtoList.length) {
                    custBaseDetailDtoList = result.custBaseDetailDtoList;
                }
                else {
                    if (result.custBaseDetailDtoList) {
                        custBaseDetailDtoList.push(result.custBaseDetailDtoList)
                    }
                }

                if (custBaseDetailDtoList.length == 0) {
                    fish.info(I18N.HINT_SEARCH_MATCH_NULL);
                    return;
                }
                // 搜索结果只有一条
                if (custBaseDetailDtoList.length == 1) {
                    this.fillUpCustId(custBaseDetailDtoList[0]);
                }
                // 搜索结果有多条，则弹框选择一条
                else {
                    fish.popupView({
                        url: "crm/modules/order/orderentry/views/popwin/CustSearchResultPopView",
                        width: 800,
                        viewOption: {
                            custList: custBaseDetailDtoList
                        },
                        close: function (custBaseDetailDto) {
                            if (!custBaseDetailDto) {
                                return;
                            }
                            custBaseDetailDtoList = [];
                            custBaseDetailDtoList.push(custBaseDetailDto);
                            this.fillUpCustId(custBaseDetailDto);
                        }.bind(this)
                    });
                }
            }.bind(this));
        },


        fillUpCustId: function (custBaseDetailDto) {
            this.$(".js-search-cust-param").val(custBaseDetailDto.custName);
            this.newCustId = custBaseDetailDto.custId;
        },

        /**
         * 初始化Bundle Tree
         */
        initExistSubsGrid: function () {
            this.$existSubsGrid.uniqueId();
            var gridId = this.$existSubsGrid.attr('id');
            var that = this;
            var gridOpt = {
                id: gridId,
                height: '100%',
                width: '100%',
                treeGrid: true,
                expandColumn: 'accNbr',
                multiselect: true,
                // multiselectWidth: 1,
                scrollrows: false,
                autoencode: false,
                colModel: [{
                    name: 'key',
                    label: '',
                    sortable: false,
                    key: true,
                    hidden: true
                }, {
                    name: 'subsId',
                    label: '',
                    sortable: false,
                    key: true,
                    hidden: true
                }, {
                    name: 'accNbr',
                    label: I18N.BUNDLE_NUM_MSISDN,
                    sortable: false,
                    search: true,
                    width: 2,
                    title: false,
                    formatter: function (cellval, opts, rwdat, operType) {
                        var value = rwdat.accNbr || '';
                        if (this.showPreAccNbr && rwdat.accNbr && rwdat.prefix) {
                            value = rwdat.prefix + rwdat.accNbr;
                        }
                        return value;
                    }.bind(this)
                }, {
                    name: 'subsPlanName',
                    label: i18nData.ORDER_ENTRY_SUBS_PLAN,
                    width: 2
                }, {
                    name: 'prodStateName',
                    label: i18nData.ORDER_ENTRY_PROD_STATE_NAME,
                    width: 2
                },
                    //     {
                    //     name: 'agreementEffDate',
                    //     label: I18N.BUNDLE_AGREEMENT_EXP_DATE,
                    //     formatter: 'date',
                    //     formatoptions: {
                    //         newformat: 'mm/dd/yyyy'
                    //     },
                    //     width: 2
                    // }, {
                    //     name: 'agreementExpDate',
                    //     label: i18nData.ORDER_ENTRY_AGREEMENT_EXP_DATE,
                    //     formatter: 'date',
                    //     formatoptions: {
                    //         newformat: 'mm/dd/yyyy'
                    //     },
                    //     width: 2
                    // }
                ],
                treeIcons: {
                    leaf: '',
                    folderOpen: '',
                    folderClosed: ''
                }
            };
            this.$existSubsGrid.grid(gridOpt);

            // 删除全选复选框
            this.$existSubsGrid.find('[role=rowheader]>th:eq(0)').css({ 'visibility': 'hidden' });
        },


        /**
         * 加载Bundle成员
         */
        loadExistSubs: function () {
            var qryParam = ObjectUtils.deepClone(this.options);

            if (this.custId)
            {
                qryParam.custId = this.custId;
            }
            else {
                // 客户和accNbr
                var custName = this.$(".js-search-cust-param").val();
                if (!fish.isEmpty(custName) && !fish.isEmpty(this.newCustId)) {
                    qryParam.custId = this.newCustId;
                }
            }
            var accNbr = this.$("input[name='accNbr']").val();
            if (!fish.isEmpty(accNbr)) {
                qryParam.accNbr = accNbr;
            }
            delete qryParam.subsList;
            delete qryParam.oldSubsList;
            BundleAction.qryExchangeSubsList(qryParam, function (result) {
                var subsList = [];
                if (result && result.subsList)
                {
                    subsList = result.subsList || [];
                }

                var subsListTmp = [];
                fish.each(subsList, function (item) {
                    if (fish.contains(this.accNbrArr, item.accNbr) || !fish.contains(this.options.exclusiveNumber, item.accNbr)) {
                        subsListTmp.push(item);
                    }
                }.bind(this));
                subsList = subsListTmp;

                var disableRowIds = [];
                fish.each(subsList, function (item) {
                    item.key = item.subsId;
                    if (item.servType == '51') {
                        disableRowIds.push(item);
                    }
                }.bind(this));
                this.$existSubsGrid.grid('reloadData', subsList);
                this.$existSubsGrid.grid('expandAll');
                this.$existSubsGrid.grid('setCheckDisabled', this.getBundleRowId(disableRowIds), true);
                // this.afterBundleTreeReload();
                // 加载已经选中的
                this.loadSelectedSubs();
            }.bind(this));
        },

        getBundleRowId: function(array) {
            var idList = [];
            $.each(array, function(i, item) {
                idList.push(item.key);
            });
            return idList;
        },

        /**
         * 加载选中的offer
         */
        loadSelectedSubs: function () {
            if (fish.isEmpty(this.subsIdArr)) {
                return;
            }
            this.$existSubsGrid.grid('setCheckRows', this.subsIdArr, true);
            fish.each(this.oldSubsList, function (subs) {
                var $row = this.$existSubsGrid.find('tr#' + subs.subsId);
                if ($row) {
                    $row.find("input[type='checkbox']").attr("disabled", true);
                }
            }.bind(this));
        },

        /**
         * 加载Bundle查询条件
         */
        initBundleSearch: function () {

        },

        /**
         * 获取选中的offer列表
         */
        onOkClick: function () {
            // 校验
            var errMsgList = [];
            // 校验不通过
            if (!fish.isEmpty(errMsgList)) {
                // 展示所有提示信息
                fish.warn(errMsgList.join('<br>'));
                return;
            }

            // 返回数据
            var addedSubsArr = this.$existSubsGrid.grid('getCheckRows') || [];
            var allSubsArr = [];
            var data = {};
            if (!fish.isEmpty(addedSubsArr) || !fish.isEmpty(this.oldSubsList)) {
                var subsAccNbrsStr = "";
                var subsMsisdnsStr = "";
                fish.each(this.oldSubsList, function (subs) {
                    var msisdn = subs.accNbr;
                    if (this.showPreAccNbr) {
                        msisdn = subs.prefix + subs.accNbr;
                    }
                    subsAccNbrsStr = subsAccNbrsStr + subs.accNbr + ",";
                    subsMsisdnsStr = subsMsisdnsStr + msisdn + ",";
                    allSubsArr.push(subs);
                }.bind(this));
                fish.each(addedSubsArr, function (subs) {
                    var oldSelectedSubs = fish.findWhere(this.oldSubsList, { subsId: subs.subsId });
                    var oldSelectedSubs2 = fish.findWhere(this.oldSubsList, { subsId: +subs.subsId });
                    if (fish.isEmpty(oldSelectedSubs) && fish.isEmpty(oldSelectedSubs2)) {
                        subsAccNbrsStr = subsAccNbrsStr + subs.accNbr + ",";
                        var msisdn = subs.accNbr;
                        if (this.showPreAccNbr) {
                            msisdn = subs.prefix + subs.accNbr;
                        }
                        subsMsisdnsStr = subsMsisdnsStr + msisdn + ",";
                        allSubsArr.push(subs);
                    }
                }.bind(this));
                var reqParam = {
                    'SUBS_LIST': allSubsArr,
                    'CONTRACT_ITEM_ID':this.bundleOrderItem ? this.bundleOrderItem.CONTRACT_ITEM_ID : null,
                    'SUBS_PLAN_LIST': this.subsPlanList,
                    'STD_ADDR_ID': this.fixInstallOrder ? this.fixInstallOrder.STD_ADDR_ID : null,
                    'SUBS_EVENT_ID': this.options.subsEventId
                };
                // UmBundleAction.checkProjectCerdikSubsCanJoinBundle(reqParam, function (result) {
                //     if (result && "N" == result.canChange) {
                //         fish.warn(I18N.CAN_NOT_CHANGE_CUSTOMER_PLAN_IN_ACTIVATION);
                //         return;
                //     }
                //     else {
                BundleAction.checkSubsCanSwitchFromOneBundleToAnother(reqParam, function (result) {
                    var retErrorMsgList = [];
                    var retAlertMsgList = [];
                    var retInfoMsgList = [];
                    var retMsgList = [];
                    if (result.checkMsgList) {
                        if (!fish.isArray(result.checkMsgList)) {
                            retMsgList = [result.checkMsgList];
                        } else {
                            retMsgList = result.checkMsgList;
                        }
                        fish.forEach(retMsgList, function (msgData) {
                            var strings = msgData.split('|');
                            // 返回消息类型0:ERROR 1:Alert
                            // 0|CC-S-SALES-02569|The operation will cause bundle termination of [{0}], members will quit the bundle and exist seperately.
                            if ('0' === strings[0]) {
                                retErrorMsgList.push(strings[2]);
                            } else if ('1' === strings[0]) {
                                retAlertMsgList.push(strings[2]);
                            } else if ('2' === strings[0]) {
                                retInfoMsgList.push(strings[2]);
                            }
                        });
                    }
                    if (retErrorMsgList.length > 0) {
                        var msg = retErrorMsgList.join('<br>') + retAlertMsgList.join('<br>');
                        fish.warn(msg);
                        return;
                    } else if (retAlertMsgList.length > 0) {
                        var msg = retErrorMsgList.join('<br>') + retAlertMsgList.join('<br>');
                        fish.warn(msg);
                    } else if (retInfoMsgList.length > 0) {
                        var msg = retErrorMsgList.join('<br>') + retAlertMsgList.join('<br>') + retInfoMsgList.join('<br>');
                        fish.info(msg);
                    }
                    subsAccNbrsStr = subsAccNbrsStr.slice(0, subsAccNbrsStr.length - 1);
                    subsMsisdnsStr = subsMsisdnsStr.slice(0, subsMsisdnsStr.length - 1);
                    data.subsAccNbrsStr = subsAccNbrsStr;
                    data.subsMsisdnsStr = subsMsisdnsStr;
                    data.subsList = allSubsArr;
                    this.popup.close(data);
                }.bind(this));
                //     }
                //
                // }.bind(this));
            } else {
                this.popup.close(data);
            }
        },
        onReset: function () {
            this.$form.form('clear');
        },

        resize: function () {
            this.$existSubsGrid.grid('setGridHeight', this.$('.modal-body').height() - 120);
        }
    });
});
