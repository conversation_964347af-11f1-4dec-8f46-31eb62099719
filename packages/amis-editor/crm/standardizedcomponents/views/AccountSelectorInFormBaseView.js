/**
 * 账户选择器,加载基础 html 结构
 */
define([
    'hbs!crm/standardizedcomponents/templates/AccountSelectorInFormTpl.hbs',
    'i18n!crm/modules/feature/wholesale/orderentry/i18n/OrderEntry'
], function (AccountSelectorInFormTpl, i18n) {
    return function (context, options) {
        this.options = options || {};

        var $el = context.$accountSelector;
        $el.append(AccountSelectorInFormTpl(i18n));

        this.create = function () {
            return this;
        }.bind(this)();
    };
});
