define([
    'hbs!crm/standardizedcomponents/templates/ArticleTitleTpl.hbs'
], function (ArticleTitleTpl) {
    return portal.BaseView.extend({
        template: ArticleTitleTpl,

        initialize: function (options) {
            this.$customEl = options.el;
            this.customTitle = options.customTitle || 'Custom Title';
        },

        afterRender: function () {
            this.resetTitle(this.customTitle);
        },

        resetTitle: function (newTitle) {
            this.customTitle = newTitle;
            this.$customEl.find('.article-title').text(this.customTitle);
        }
    });
});
