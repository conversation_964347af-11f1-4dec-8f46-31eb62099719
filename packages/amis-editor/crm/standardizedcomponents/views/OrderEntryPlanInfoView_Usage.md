# OrderEntryPlanInfoView 组件使用说明

## 概述

OrderEntryPlanInfoView 是一个标准化的订单录入计划信息组件，用于GSM新开户流程中的基本信息表单配置。该组件包含用户名、订单号、订购计划、VPN、语言、账户和使用者等基本信息字段，以及相关的业务逻辑处理。

## 特性

- 标准化的组件接口和事件机制
- 完整的参数验证和错误处理
- 支持全局数据访问模式
- 兼容Fish框架的组件规范
- 支持自定义配置和主题
- 集成了多个子组件（语言选择、账户选择、使用者选择等）

## 安装和使用

### 基本使用（实例化方式）

```javascript
// 在主组件中引入
define([
    'crm/standardizedcomponents/views/OrderEntryPlanInfoView'
], function (OrderEntryPlanInfoView) {
    // 在主组件的方法中使用
    initOrderEntryPlanInfoView: function () {
        try {
            this.$orderEntryPlanInfoView = this.$(".classname");
            this.$newOrderEntryPlanInfoView = new OrderEntryPlanInfoView(this, {
                orderItem: this.orderItem,
                custOrder: this.custOrder,
                subsBaseOrder: this.subsBaseOrder,
                subsBriefDetail: this.subsBriefDetail,
                bundleOrderItem: this.bundleOrderItem,
                isPrimary: this.isPrimary,
                acctDisabled: this.acctDisabled,
                portalAppGlobalData: this.portalAppGlobalData,
                componentConfig: {
                    theme: 'default',
                    locale: 'zh-CN',
                    debug: false
                }
            });
        } catch (error) {
            console.error('Failed to initialize OrderEntryPlanInfoView:', error);
        }
    }
});
```

### HTML模板配置

在主组件的模板文件中添加组件容器：

```html
<!-- 订单录入计划信息组件容器 -->
<div class="js-order-entry-plan-info"></div>
```

## API 文档

### Props

| 参数名 | 类型 | 必填 | 默认值 | 描述 |
|--------|------|------|--------|------|
| context | Object | 是 | - | 调用方的上下文对象 |
| options | Object | 是 | - | 组件配置选项 |
| options.orderItem | Object | 是 | - | 订单项数据 |
| options.custOrder | Object | 是 | - | 客户订单数据 |
| options.subsBaseOrder | Object | 是 | - | 订购基础订单数据 |
| options.portalAppGlobalData | Object | 否 | portal.appGlobal | 全局数据访问对象 |
| options.subsBriefDetail | Object | 否 | - | 订购简要详情 |
| options.bundleOrderItem | Object | 否 | - | 捆绑订单项 |
| options.isPrimary | Boolean | 否 | false | 是否为主要订购 |
| options.acctDisabled | Boolean | 否 | false | 账户是否禁用 |
| options.componentConfig | Object | 否 | {} | 组件配置对象 |
| options.componentConfig.theme | String | 否 | 'default' | 主题配置 |
| options.componentConfig.locale | String | 否 | 'zh-CN' | 语言配置 |
| options.componentConfig.debug | Boolean | 否 | false | 调试模式 |

### Events

| 事件名 | 描述 | 事件数据 |
|--------|------|----------|
| initialized | 组件初始化完成事件 | `{componentId, version, timestamp}` |
| dataChanged | 数据变更事件 | `{field, action, timestamp}` |
| componentError | 组件错误事件 | `{error, context, timestamp}` |

### Methods

| 方法名 | 描述 | 参数 | 返回值 |
|--------|------|------|-------|
| init() | 初始化组件 | 无 | 无 |
| render() | 渲染组件模板 | 无 | 无 |
| initBasicInfoForm() | 初始化基本信息表单 | 无 | 无 |
| initDefLang() | 初始化默认语言组件 | 无 | 无 |
| initAccountSelectorComponent() | 初始化账户选择组件 | 无 | 无 |
| initUserSelectPopedit() | 初始化使用者选择组件 | 无 | 无 |
| initIndepProdOrderAttrComponent() | 初始化独立产品订单属性组件 | 无 | 无 |
| prepaidCreateAcctFlagFn() | 获取预付费创建账户标识 | 无 | 无 |
| qryHideConfig() | 获取屏蔽组件配置 | 无 | 无 |
| triggerStandardEvent(eventName, data) | 触发标准化事件 | eventName: String, data: Object | 无 |
| handleError(error, context) | 处理错误 | error: Error/String, context: String | 无 |

## 子组件说明

### DefLanguageCombobox
- 默认语言选择下拉框
- 支持订购标识和初始语言ID设置

### AccountSelectorComponent
- 账户选择组件
- 支持账户查询、选择和验证
- 集成了业务规则验证

### UserSelectPopedit
- 使用者选择弹出编辑组件
- 支持个人和企业客户类型
- 集成了用户信息回显

### IndepProdOrderAttrComponent
- 独立产品订单属性组件
- 动态渲染产品属性字段

## 示例

### 基础示例

```javascript
// 在GSMNewSubsPlanView中使用
afterRender: function () {
    // 初始化订单录入计划信息组件
    this.initOrderEntryPlanInfoView();
    
    // 其他初始化逻辑...
},

initOrderEntryPlanInfoView: function () {
    this.$orderEntryPlanInfoView = this.$(".classname");
    this.$newOrderEntryPlanInfoView = new OrderEntryPlanInfoView(this, {
        orderItem: this.orderItem,
        custOrder: this.custOrder,
        subsBaseOrder: this.subsBaseOrder,
    });
}
```

## 最佳实践

### 性能优化

1. **组件复用**：避免重复创建组件实例
2. **事件清理**：在页面销毁时清理事件监听器
3. **延迟初始化**：根据需要延迟初始化子组件

### 错误处理

1. **参数验证**：组件会自动验证必填参数
2. **异常捕获**：使用try-catch包装组件初始化代码
3. **错误回调**：监听组件的错误事件进行处理

### 数据绑定

1. **BO访问**：子组件支持标准的BO数据绑定
2. **数据验证**：表单字段支持自动验证
3. **数据回显**：支持订单预览返回时的数据回显

## 常见问题

### 问题1：组件初始化失败

**原因**：缺少必填参数或参数格式不正确

**解决方案**：检查传入的参数是否完整，特别是orderItem、custOrder、subsBaseOrder

### 问题2：子组件渲染异常

**原因**：HTML容器元素不存在或选择器错误

**解决方案**：确保在主模板中包含了`.classname`容器元素

### 问题3：账户选择组件不工作

**原因**：账户相关的业务规则配置问题

**解决方案**：检查prepaidCreateAcctFlag等配置项，确保业务规则正确

### 问题4：语言选择组件初始化失败

**原因**：缺少默认语言ID或语言数据

**解决方案**：确保subsBaseOrder中包含DEF_LANG_ID字段

## 更新日志

### v1.0 (2024-07-14)
- 初始版本发布
- 实现订单录入计划信息的标准化组件
- 集成多个子组件（语言、账户、使用者选择等）
- 支持标准化的事件机制和参数验证
- 完整的API文档和使用示例

## 技术支持

如有问题或建议，请联系Fish团队或提交Issue。
