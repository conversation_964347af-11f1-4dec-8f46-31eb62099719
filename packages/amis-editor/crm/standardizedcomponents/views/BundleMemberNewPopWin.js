/* eslint-disable */
/**
 * BundleMemberNewPopWin组件 - 用于套餐成员选择和管理的弹窗组件
 * @component
 * @extends {portal.BaseView}
 * @param {Object} options - 初始化参数
 * @param {Array} options.bundleMemberOperOrderList - 套餐成员操作订单列表
 * @param {Object} options.bundleOrderItem - 套餐订单项
 * @param {Array} options.bundleUnitList - 套餐单元列表
 * @param {Object} options.cust - 客户信息
 * @param {Object} options.custContract - 客户合同信息
 * @param {Object} options.custOrder - 客户订单信息
 * @param {string} options.addressSurveryId - 地址勘察ID
 * @param {boolean} [options.memberOperOrderFlag=false] - 是否为成员订单操作标志
 * @param {Array} [options.bundleMemberOperOrderExtendList] - 扩展的套餐成员操作订单列表
 * @param {Array} [options.childOrderItemArr] - 子订单项数组
 * @param {Object} [options.fixInstallOrder] - 固网安装订单信息
 * @param {string} [options.showPreAccNbr] - 是否显示预账号标志
 * @event finishSelectBundleUnitSubsPlan - 完成选择套餐单元订户套餐时触发
 * @event IntroDef.ADD_INTRO - 在BOL登录场景下触发的引导提示事件
 * @event IntroDef.EXIT_INTRO - 在BOL登录场景下触发的引导退出事件
 */
define([
    'hbs!crm/modules/pos/flowpage/bundle/templates/BundleMemberNewPopWinTpl.hbs',
    'hbs!crm/modules/pos/flowpage/business/component/templates/popwin/CheckboxLabelTpl.hbs',
    'i18n!crm/modules/pos/flowpage/bundle/i18n/Bundle',
    'crm/modules/pos/common/utils/ObjectUtils',
    'crm/modules/pos/flowpage/bundle/utils/BundleUtils',
    'crm/modules/common/util/StringUtils',
    'crm/modules/pos/common/constant/SubsEventDef',
    'crm/modules/pos/flowpage/bundle/constants/BundleDef',
    'crm/modules/common/actions/ConfigItemAction',
    'crm/modules/customer/actions/SubsAction',
    'crm/modules/pos/common/constant/IntroDef',
    'crm/modules/pos/orderview/actions/OrderAction',
], function (
    BundleMemberNewPopWinTpl,
    CheckboxLabelTpl,
    I18N,
    ObjectUtils,
    BundleUtils,
    StringUtils,
    SubsEventDef,
    BundleDef,
    ConfigItemAction,
    SubsAction,
    IntroDef,
    OrderAction
) {
    return portal.BaseView.extend({
        template: BundleMemberNewPopWinTpl,
        serialize: I18N,
        initFlag: false,
        events: {
            'click .js-ok': 'onOkClick',
            'click .js-member-del': 'onDeleteRowClick',

            // 'blur .js-added-member-qty': 'onAddMember'
        },
        initialize: function (initOptions) {
            this.bundleUnitList = ObjectUtils.deepClone(initOptions.bundleUnitList);
            this.custOrder = initOptions.custOrder;
            if (initOptions.custOrder && initOptions.custOrder.ORDER_TYPE === 'H') {
                fish.forEach(
                    this.bundleUnitList,
                    function (unit) {
                        if (unit.bundleUnitSubsPlanList) {
                            unit.bundleUnitSubsPlanList = fish.filter(
                                unit.bundleUnitSubsPlanList,
                                function (item) {
                                    return this.custOrder.FORBID_REVISE_SERV_TYPE.indexOf(',' + item.servType + ',') == -1;
                                }.bind(this)
                            );
                        }
                    }.bind(this)
                );
                this.bundleUnitList = fish.filter(this.bundleUnitList, function (item) {
                    return !fish.isEmpty(item.bundleUnitSubsPlanList);
                });
            }

            fish.forEach(
                this.bundleUnitList,
                function (unit) {
                    if (unit.bundleUnitSubsPlanList) {
                        unit.bundleUnitSubsPlanList = fish.filter(
                            unit.bundleUnitSubsPlanList,
                            function (item) {
                                let flag = true;
                                if (item.applyChannelList && item.applyChannelList.indexOf(',' + this.custOrder.CONTACT_CHANNEL_ID + ',') === -1){
                                    flag = false;
                                }
                                return  flag;
                            }.bind(this)
                        );
                    }
                }.bind(this)
            );
            this.bundleUnitList = fish.filter(this.bundleUnitList, function (item) {
                return !fish.isEmpty(item.bundleUnitSubsPlanList);
            });



            this.bundleOrderItem = initOptions.bundleOrderItem;
            this.bundleMemberOperOrderList = initOptions.bundleMemberOperOrderList;
            this.fixInstallOrder = initOptions.fixInstallOrder;

            // 已经选中的订户套餐订单
            this.memberOperOrderFlag = initOptions.memberOperOrderFlag || false;
            if (!this.memberOperOrderFlag) {
                this.childOrderItemArr = initOptions.childOrderItemArr;
                this.memberMap = fish.groupBy(this.childOrderItemArr, function (data) {
                    return data.SUBS_PLAN_ID + ',' + data.SUBS_EVENT_ID;
                });
            } else {
                this.bundleMemberOperOrderExtendList = initOptions.bundleMemberOperOrderExtendList;
                this.memberMap = fish.groupBy(this.bundleMemberOperOrderList, function (data) {
                    return data.SUBS_PLAN_ID + ',' + data.newSubsEventId;
                });
                this.extendMemberMap = fish.groupBy(this.bundleMemberOperOrderExtendList, function (data) {
                    return data.SUBS_PLAN_ID + ',' + data.newSubsEventId;
                });
            }
            this.cust = initOptions.cust;
            this.custContract = initOptions.custContract;
            this.bundleAddressSurveyId = initOptions.addressSurveryId;
            this.showPreAccNbr = initOptions.showPreAccNbr;
            this.initCfg();
        },

        initCfg: function () {
            var featurePkgCfg = ConfigItemAction.qryParamValue('CUSTOMER_CARE', 'CC_PUBLIC', 'FEATURE_PACKAGE');
            if (!fish.isEmpty(featurePkgCfg)) {
                featurePkgCfg = ',' + featurePkgCfg + ',';
                if (featurePkgCfg.indexOf(',9,') != -1) {
                    this.wishOrderVisble = true;
                }
            }
        },

        afterRender: function () {
            // 初始化DOM元素
            this.$bundleTreeGrid = this.$('.js-bundle-tree');
            this.$addressGrid = this.$('.js-address-member-tree');

            // 初始化查询
            this.initBundleSearch();

            // 初始化VAS树
            this.initBundleTree();

            // 初始化地址成员树
            this.initBundleMemberGrid();

            // 加载VAS树
            this.loadBundleTree();

            if (this.memberOperOrderFlag) {
                this.$('.js-add-member-header').hide();
                this.$('.js-add-member-button').hide();
                this.$('.js-add-member-body').removeClass('modal-body');
            }

            this.$('.js-address-member-tree').hide();
        },

        /**
         * 初始化Bundle Tree
         */
        initBundleTree: function () {
            this.$bundleTreeGrid.uniqueId();
            var gridId = this.$bundleTreeGrid.attr('id');
            var gridOpt = {
                id: gridId,
                height: 'auto',
                width: '100%',
                treeGrid: true,
                expandColumn: 'name',
                multiselect: false,
                scrollrows: false,
                autoencode: false,
                colModel: [
                    {
                        name: 'key',
                        label: '',
                        sortable: false,
                        key: true,
                        hidden: true,
                    },
                    {
                        name: 'name',
                        label: I18N.BUNDLE_MEMBER,
                        sortable: false,
                        width: 4,
                        title: false,
                        formatter: function (cellval, opts, rwdat) {
                            // BundleUnit，必填红星
                            var value = rwdat.name || '';
                            if (rwdat.necessary == '1') {
                                value += "<span class='text-red'>&nbsp;*</span>";
                            }
                            // 需要删除的新开户或joinBundle成员，增加删除样式
                            if (rwdat.BUNDLE_MEMBER_OPER_SEQ) {
                                value = "<span class='text-line-through'>" + value + '</span>';
                            }

                            if (rwdat.isValidSubsPlan == 'N') {
                                value = "<span class='text-line-through'>" + value + '</span>';
                            }

                            return value;
                        }.bind(this),
                    },
                    {
                        name: 'existedSubs',
                        label: I18N.BUNDLE_EXISTED_SUBS,
                        sortable: false,
                        formatter: function (val, gridData, rwdat) {
                            if (
                                rwdat.subsPlanId &&
                                rwdat.bundleComposeType != BundleDef.BUNDLE_COMPOSE_TYPE_1 &&
                                rwdat.notMatchSalesCondition != 'Y' &&
                                rwdat.isValidSubsPlan != 'N'
                            ) {
                                return (
                                    '<div class="col-md-12 input-group"><input type="text" class="form-control js-choose-subs' +
                                    rwdat.subsPlanId +
                                    '"></div>'
                                );
                            }
                            return '';
                        }.bind(this),
                        width: 3,
                    },
                    {
                        name: 'newMemberQty',
                        label: I18N.BUNDLE_ADDED_QTY,
                        sortable: false,
                        width: 2,
                        title: false,
                        formatter: function (cellval, opts, rwdat, operType) {
                            // 需要新增订购的成员数量
                            if (
                                rwdat.subsPlanId &&
                                rwdat.bundleComposeType != BundleDef.BUNDLE_COMPOSE_TYPE_2 &&
                                rwdat.notMatchSalesCondition != 'Y' &&
                                rwdat.isValidSubsPlan != 'N'
                            ) {
                                return (
                                    '<input type="text" class=\'js-added-member-qty' +
                                    rwdat.subsPlanId +
                                    "' bundleUnitId='" +
                                    rwdat.bundleUnitId +
                                    "' subsPlanId='" +
                                    rwdat.subsPlanId +
                                    '\' autocomplete="off" value=\'' +
                                    cellval +
                                    "'>"
                                );
                            }
                            return '';
                        }.bind(this),
                    },
                    {
                        name: 'seq',
                        label: '',
                        hidden: true,
                        sorttype: 'int',
                    },
                ],
                sortname: 'seq',
                sortorder: 'asc',
                treeIcons: {
                    leaf: '',
                    folderOpen: '',
                    folderClosed: '',
                },
            };

            if (this.showExistedMemberQty) {
                gridOpt.colModel.push({
                    //已经存在的成员数量，用于告知用户该成员实例已经存在多少，避免误操作
                    name: 'existedMemberQty',
                    label: I18N.BUNDLE_EXISTED_QTY,
                    sortable: false,
                    width: 2,
                    title: false,
                    formatter: function (cellval, opts, rwdat) {
                        //订户套餐才展示
                        if (!ObjectUtils.isNullOrUndefined(rwdat.subsPlanId)) {
                            cellval = ObjectUtils.isNullOrUndefined(cellval) ? 0 : Number(cellval);
                            return '<div class="text-center">' + cellval + '</div>';
                        }
                        return '';
                    }.bind(this),
                });
            }

            this.$bundleTreeGrid.grid(gridOpt);
        },

        initBundleMemberGrid: function () {
            // 参数配置
            this.$addressGrid = this.$('.js-address-member-tree').grid({
                datatype: 'json',
                height: 'auto',
                width: '100%',
                multiselect: false,
                scrollrows: false,
                autoencode: false,
                colModel: [
                    {
                        name: 'uuid',
                        key: true,
                        hidden: true,
                    },
                    {
                        name: 'name',
                        label: I18N.BUNDLE_MEMBER,
                        width: 4,
                    },
                    {
                        name: 'address',
                        label: I18N.MEMBER_ADDRESS,
                        width: 5,
                    },
                    {
                        name: '',
                        label: I18N.MEMBER_ADDRESS_SURVEY,
                        formatter: 'actions',
                        formatoptions: {
                            editbutton: true,
                            delbutton: false,
                        },
                        width: 2,
                    },
                    {
                        name: '',
                        label: '',
                        formatter: function (cellval, opts, rwdat, operType) {
                            return '<span class="iconfont icon-trash js-member-del" rowId="' + opts.rowId + '"\\>';
                        }.bind(this),
                        width: 1,
                    },
                    {
                        name: 'seq',
                        label: '',
                        hidden: true,
                        sorttype: 'int',
                    },
                ],
                sortname: 'seq',
                sortorder: 'asc',
                editIcons: {
                    edit: 'iconfont icon-pencil',
                    del: 'iconfont icon-trash',
                    save: 'iconfont icon-ok',
                    cancel: 'iconfont icon-delete',
                },

                beforeEditRow: function (e, rowid, data, option) {
                    // 先选中行,避免要编辑的行和选中的行不是同一行数据
                    this.$addressGrid.grid('setSelection', data);
                    // 触发Edit按钮点击事件
                    this.onEditMemberClick();

                    // 阻止默认的编辑行操作
                    return false;
                }.bind(this),
            });
        },

        onDeleteRowClick: function (e) {
            var item = this.$addressGrid.grid('getSelection');
            this.$addressGrid.grid('setSelection', item);
            this.offChangeFlag = true;
            var num = this.$('.js-added-member-qty' + item.subsPlanId).stepper('value') - 1;
            this.$('.js-added-member-qty' + item.subsPlanId).stepper('value', num);
            this.$addressGrid.grid('delRowData', item);
            this.offChangeFlag = false;
        },

        onEditMemberClick: function () {
            var rowData = this.$addressGrid.grid('getSelection');
            var custDetail = {};
            if (this.cust) {
                custDetail.custId = this.cust.CUST_ID;
                custDetail.custName = this.cust.CUST_NAME;
                custDetail.custType = this.cust.CUST_TYPE;
            }

            // param.$orderEntryView = this.$orderEntryView;
            var url = 'crm/modules/pos/orderentry/views/popwin/FixAddressSurveryPopView';
            if (this.wishOrderVisble) {
                url = 'crm/modules/pos/orderentry/views/popwin/SingleOfferFixAddressSurveryPopView';
            }
            fish.popupView({
                url: url,
                width: 816,
                height: 608,

                viewOption: {
                    custDetail: custDetail,
                    isNewConn: 'Y',
                    subsPlanId: rowData.subsPlanId,
                    isBundleMemberFlag: true,
                },
                close: function (data) {
                    if (data) {
                        this.changeGridAddress(data);
                    }
                    // var offerData = this.getOfferData(this.subsPlanDetailList);
                    // fish.trigger(ShopCardEventDef.DISPATCH_ORDER + this.pageUuid, {
                    //     subsPlans: offerData,
                    //     subsEventName: I18N.OFFER_NEW_CONNECTION
                    // });
                }.bind(this),
            });
        },

        changeGridAddress: function (data) {
            var rowData = this.$addressGrid.grid('getSelection');
            rowData.accessType = data.offerData.accessType;
            rowData.addressId = data.addressSurvey.addressId;
            rowData.address = data.addressSurvey.addressName;
            rowData.bandwidth = data.offerData.bandwidth;
            rowData.stdAddrFullName = data.addressSurvey.addressName;
            rowData.stdAddrId = data.addressSurvey.addressId;
            this.$addressGrid.grid('setRowData', rowData);
        },

        onAddMember: function (e) {
            if (event && event.target) {
                var $target = $(event.target);
                var subsPlanId = $target.attr('subsPlanId');
                var bundleUnitId = $target.attr('bundleUnitId');
                // var bundleUnit = fish.findWhere(this.bundleUnitList, {"bundleUnitId" : bundleUnitId});
                // var subsPlan = fish.findWhere(bundleUnit.children, {"subsPlanId" : subsPlanId});
                // var bundleUnit = this.$bundleTreeGrid.grid('getRowData', bundleUnitId);
                var subsPlan = this.$bundleTreeGrid.grid('getRowData', subsPlanId);

                var newMemberQty = $target.val();
                newMemberQty = parseInt(newMemberQty.replace(/,/g, '').replace(new RegExp(/^[^\d\-]*(\-?)(\d+)(\.\d*)?\D*$/), '$1$2$3'));
                if ((!newMemberQty && newMemberQty != 0) || isNaN(newMemberQty) || newMemberQty < subsPlan.memberQty) {
                    $target.val(subsPlan.memberQty);
                    subsPlan.newMemberQty = subsPlan.memberQty;
                } else {
                    $target.val(newMemberQty);
                    subsPlan.newMemberQty = newMemberQty;
                }
                this.$bundleTreeGrid.grid('setRowData', subsPlan);
            }
        },

        /**
         * 校验BundleUnit成员数量限制
         */
        validateMemberNumberLimit: function (parentRowData, errMsgList) {
            // var subsPlanRows = this.$bundleTreeGrid.grid('getNodeChildren', parentRowData);
            var finalMemberQty = 0;
            fish.each(
                parentRowData.children,
                function (subsPlan) {
                    finalMemberQty = finalMemberQty + subsPlan.newMemberQty;
                    if (!fish.isEmpty(subsPlan.subsList)) {
                        finalMemberQty = finalMemberQty + subsPlan.subsList.length;
                    }
                }.bind(this)
            );
            if (parentRowData.upperLimit && finalMemberQty > parentRowData.upperLimit) {
                errMsgList.push(StringUtils.format(I18N.BUNDLE_MEMBER_UPPER_LIMIT_WARN, parentRowData.memberAlias, parentRowData.upperLimit));
            } else if (parentRowData.lowerLimit && finalMemberQty < parentRowData.lowerLimit) {
                errMsgList.push(StringUtils.format(I18N.BUNDLE_MEMBER_LOWER_LIMIT_WARN, parentRowData.memberAlias, parentRowData.lowerLimit));
            }
        },

        validateMemberAddress: function (errMsgList) {
            var rowDatas = this.$addressGrid.grid('getRowData') || [];
            fish.each(
                rowDatas,
                function (rowData) {
                    if (!rowData.addressId) {
                        errMsgList.push(I18N.MEMBER_ADDRESS_SURVEY_CHECK_WARN);
                        return true;
                    }
                }.bind(this)
            );
        },

        /**
         * 加载Bundle成员
         */
        loadBundleTree: function () {
            fish.each(
                this.bundleUnitList,
                function (item) {
                    item.key = 'B' + item.bundleUnitId;
                    item.name = BundleUtils.parseBundleUnitName(item);
                    item.children = item.bundleUnitSubsPlanList;
                    fish.each(
                        item.children,
                        function (child) {
                            child.key = child.subsPlanId;
                            child.name = child.subsPlanName;
                            this.computeQty(child);
                            this.computeSubsList(child);
                        }.bind(this)
                    );
                    delete item.bundleUnitSubsPlanList;
                }.bind(this)
            );
            if (this.memberOperOrderFlag) {
                this.deletedMemberOperOrderArr = this.addDeletedOrder();
                this.deletedMemberOperOrderArr = this.bundleUnitList.concat(this.deletedMemberOperOrderArr);
                this.$bundleTreeGrid.grid('reloadData', this.deletedMemberOperOrderArr);
                this.deletedMemberOperOrderArr = this.addDeletedOrder();
            } else {
                this.$bundleTreeGrid.grid('reloadData', this.bundleUnitList);
            }
            this.$bundleTreeGrid.grid('expandAll');

            if (portal.appGlobal.get('isBolLogin')) {
                fish.trigger(IntroDef.ADD_INTRO, {
                    element: '.js-bundle-tree',
                    tipsContents: IntroDef.BOUNDLE_OFFER_STEP9,
                    position: 'top',
                });
            }
            this.afterBundleTreeReload();
        },

        /**
         * 展示删除的开户或joinBundle订单
         */
        addDeletedOrder: function () {
            var deletedMemberOperOrderArr = [];
            fish.each(
                this.bundleMemberOperOrderExtendList,
                function (bundleMemberOperOrderExtend) {
                    if (SubsEventDef.CHANGELESS_0 == bundleMemberOperOrderExtend.newSubsEventId) {
                        bundleMemberOperOrderExtend.key = bundleMemberOperOrderExtend.SUBS_PLAN_ID;
                        bundleMemberOperOrderExtend.name = bundleMemberOperOrderExtend.SUBS_PLAN_NAME + ' * ' + bundleMemberOperOrderExtend.ACC_NBR;
                        bundleMemberOperOrderExtend.servType = bundleMemberOperOrderExtend.SERV_TYPE;
                        bundleMemberOperOrderExtend.offerName = bundleMemberOperOrderExtend.SUBS_PLAN_NAME;
                        deletedMemberOperOrderArr.push(bundleMemberOperOrderExtend);
                    }
                }.bind(this)
            );
            return deletedMemberOperOrderArr;
        },

        /**
         * 计算新开户数量
         * @param child
         */
        computeQty: function (child) {
            if (!this.memberOperOrderFlag) {
                var newSubsPlanMemberArr = this.memberMap[child.subsPlanId + ',' + SubsEventDef.NEW_CONNECTION];
                if (!fish.isEmpty(newSubsPlanMemberArr)) {
                    // child.memberQty =  newSubsPlanMemberArr.length;
                    // 当走过订单预览 再回来 ，这个时候相同的member 或者 offer 已经合并了，只有一个订单项 ，不能按照上面那个来了
                    child.memberQty =
                        newSubsPlanMemberArr.length > 0 && newSubsPlanMemberArr[0].GSM_QUANTITY
                            ? newSubsPlanMemberArr[0].GSM_QUANTITY
                            : newSubsPlanMemberArr.length;
                } else {
                    child.memberQty = 0;
                }
                child.newMemberQty = child.memberQty;
            } else {
                var memberOperOrderArr = this.memberMap[child.subsPlanId + ',' + SubsEventDef.NEW_CONNECTION];
                if (!fish.isEmpty(memberOperOrderArr) && memberOperOrderArr[0].QTY) {
                    child.memberQty = memberOperOrderArr[0].OLD_QTY;
                    child.newMemberQty = memberOperOrderArr[0].QTY;
                } else {
                    child.memberQty = 0;
                    child.newMemberQty = 0;
                }
            }
        },

        /**
         * 计算Join Bundle数量
         * @param child
         */
        computeSubsList: function (child) {
            var joinBundleMemberArr = this.memberMap[child.subsPlanId + ',' + SubsEventDef.JOIN_BUNDLE];
            if (!fish.isEmpty(joinBundleMemberArr)) {
                var subsList = [];
                var subsAccNbrsStr = '';
                fish.each(
                    joinBundleMemberArr,
                    function (memeberItem) {
                        var subs = {};
                        subs.subsId = memeberItem.SUBS_ID;
                        subs.subsPlanId = memeberItem.SUBS_PLAN_ID;
                        subs.subsPlanName = memeberItem.SUBS_PLAN_NAME;
                        subs.accNbr = memeberItem.ACC_NBR;
                        subs.addedFlag = true;
                        subsList.push(subs);
                        subsAccNbrsStr = subsAccNbrsStr + memeberItem.ACC_NBR + ',';
                    }.bind(this)
                );
                subsAccNbrsStr = subsAccNbrsStr.slice(0, subsAccNbrsStr.length - 1);
                child.subsAccNbrsStr = subsAccNbrsStr;
                child.subsList = subsList;
                child.oldSubsList = subsList;
            }
        },

        afterBundleTreeReload: function () {
            fish.each(
                this.bundleUnitList,
                function (item) {
                    fish.each(
                        item.children,
                        function (subsPlan) {
                            this.initExistedSubsPop(subsPlan);
                            this.initAddmemberInput(subsPlan);
                        }.bind(this)
                    );
                }.bind(this)
            );
        },

        initAddmemberInput: function (subsPlan) {
            if (!subsPlan.subsPlanId || subsPlan.bundleComposeType == BundleDef.BUNDLE_COMPOSE_TYPE_2) {
                return;
            }

            var stepInput = this.$('.js-added-member-qty' + subsPlan.subsPlanId).stepper({
                min: subsPlan.memberQty,
                max: 999999999,
                value: subsPlan.newMemberQty,
            });
            this.$('.js-added-member-qty' + subsPlan.subsPlanId).on(
                'stepper:change',
                function (e) {
                    var newMemberQty = stepInput.stepper('value');
                    var changeQty = 0;
                    if ((!newMemberQty && newMemberQty !== 0) || isNaN(newMemberQty) || newMemberQty < subsPlan.memberQty) {
                        stepInput.stepper('value', subsPlan.memberQty);
                        subsPlan.newMemberQty = subsPlan.memberQty;
                    } else {
                        if (subsPlan.newMemberQty) {
                            if (subsPlan.newMemberQty < newMemberQty) {
                                changeQty = newMemberQty - subsPlan.newMemberQty;
                            } else if (subsPlan.newMemberQty > newMemberQty) {
                                changeQty = newMemberQty - subsPlan.newMemberQty;
                            }
                        } else {
                            changeQty = newMemberQty;
                        }
                        subsPlan.newMemberQty = newMemberQty;
                    }
                    if (subsPlan.servType === '51') {
                        if (subsPlan.newMemberQty && subsPlan.newMemberQty === 0) {
                            return;
                        }
                        var servTypeStr = ConfigItemAction.qryParamValue('CUSTOMER_CARE', 'CC_PUBLIC', 'ADDR_FILTER_SERV_TYPE');
                        var bundleUnitList = [];
                        bundleUnitList = SubsAction.qryBundleUnitSubsPlanByOfferId({ offerId: subsPlan.subsPlanId });
                        var bundleUnitSubsPlanList = [];
                        fish.each(bundleUnitList || [], function (bundleUnit) {
                            fish.each(
                                bundleUnit.bundleUnitSubsPlan || [],
                                function (subsPlan) {
                                    bundleUnitSubsPlanList.push(subsPlan);
                                }.bind(this)
                            );
                        });
                        for (var i = 0; i < bundleUnitSubsPlanList.length; i++) {
                            if (servTypeStr.indexOf(bundleUnitSubsPlanList[i].servType) !== -1) {
                                if (this.$addressGrid.is(':hidden')) {
                                    this.$addressGrid.show();
                                    this.$addressGrid.resize();
                                    // this.resize();
                                }

                                if (changeQty > 0) {
                                    for (var j = 0; j < changeQty; j++) {
                                        var memberData = {};
                                        memberData.subsPlanId = subsPlan.subsPlanId;
                                        memberData.name = subsPlan.subsPlanName;
                                        memberData.indepProdSpecId = subsPlan.indepProdSpecId;
                                        memberData.uuid = fish.getUUID();
                                        this.$addressGrid.grid('addRowData', memberData);
                                    }
                                } else if (changeQty < 0) {
                                    if (this.offChangeFlag) {
                                        return;
                                    }
                                    for (var j = 0; j < -changeQty; j++) {
                                        // var datas = this.$addressGrid.grid("getDataIDs");
                                        var rows = fish.filter(
                                            this.$addressGrid.grid('getRowData'),
                                            function (rowData) {
                                                return rowData.subsPlanId == subsPlan.subsPlanId;
                                            }.bind(this)
                                        );
                                        if (rows.length > 0) {
                                            this.$addressGrid.grid('delRow', rows[rows.length - 1].uuid);
                                        }
                                    }
                                }
                                return true;
                            }
                        }
                    }
                    if (portal.appGlobal.get('isBolLogin') && fish.store.get('accountOpeningMethod') == 'boundleOffer') {
                        $.each(
                            this.$("input[name='stepper']"),
                            function (index, item) {
                                if (
                                    ($(this.$("input[name='stepper']")[0]).val() != 1 && $(this.$("input[name='stepper']")[1]).val() == 1) ||
                                    ($(this.$("input[name='stepper']")[0]).val() == 1 && $(this.$("input[name='stepper']")[1]).val() != 1)
                                ) {
                                    if ($(this.$("input[name='stepper']")[2]).val() != 1 || $(this.$("input[name='stepper']")[3]).val() != 1) {
                                        return;
                                    }
                                    this.introFlog = true;
                                }
                                // if($(this.$("input[name='stepper']")[0]).val() !=1 || $(this.$("input[name='stepper']")[1]).val() !=1 || $(this.$("input[name='stepper']")[2]).val() !=1) return
                                // this.introFlog = true

                                // setTimeout(function() {
                                //     fish.trigger(IntroDef.ADD_INTRO, {
                                //         element: '#bundle-member-intro',
                                //         tipsContents: IntroDef.BOUNDLE_OFFER_STEP9,
                                //         position: 'top'
                                //     })
                                // }, 600);
                            }.bind(this)
                        );
                        if (this.introFlog == true) {
                            fish.trigger(IntroDef.EXIT_INTRO);
                            setTimeout(
                                function () {
                                    this.onOkClick();
                                }.bind(this),
                                300
                            );
                        }
                    }
                }.bind(this)
            );

            //
            // this.$(".js-added-member-qty" + subsPlan.subsPlanId).on("blur", function (e) {
            //     var $target = $(event.target);
            //     var subsPlanId = $target.attr('subsPlanId');
            //     var bundleUnitId = $target.attr('bundleUnitId');
            //     var newMemberQty = $target.val();
            //     newMemberQty = parseInt(newMemberQty.replace(/,/g, '').replace(new RegExp(/^[^\d\-]*(\-?)(\d+)(\.\d*)?\D*$/), "$1$2$3"));
            //     if ((!newMemberQty && newMemberQty != 0) || isNaN(newMemberQty) || newMemberQty < subsPlan.memberQty) {
            //         $target.val(subsPlan.memberQty);
            //         subsPlan.newMemberQty = subsPlan.memberQty;
            //     }
            //     else {
            //         $target.val(newMemberQty);
            //         subsPlan.newMemberQty = newMemberQty;
            //     }
            // }.bind(this));
        },

        initExistedSubsPop: function (subsPlan) {
            if (!subsPlan.subsPlanId || subsPlan.bundleComposeType == BundleDef.BUNDLE_COMPOSE_TYPE_1) {
                return;
            }
            this.$('.js-choose-subs' + subsPlan.subsPlanId).popedit({
                showClearIcon: false,
                open: function () {
                    var viewOption = {
                        subsList: subsPlan.subsList,
                        oldSubsList: subsPlan.oldSubsList,
                        servType: subsPlan.servType,
                        bindTypes: 'N',
                        prodState: 'A',
                        relaTypes: "'3'", //移网没有共线关系，固网参照POST只查询可切换关系
                        subsPlanList: [subsPlan],
                        custId: this.cust.CUST_ID,
                        custType: this.cust.CUST_TYPE,
                        bundleSubsId: this.bundleOrderItem.SUBS_ID,
                        bundleOrderItem: this.bundleOrderItem,
                        bundleExistSubsFlag: true,
                        showPreAccNbr: this.showPreAccNbr,
                        exclusiveNumber: this.getExcludeNumber(subsPlan),
                        fixInstallOrder: this.fixInstallOrder,
                        subsEventId: this.custOrder.SUBS_EVENT_ID,
                        // bundleAddressSurveyId:this.bundleAddressSurveyId
                    };
                    // // email没有addressSurveyId，不能传addressSurveyId，否则后台会根据addressSurveyId查询subs，导致查不到数据
                    // if (ServTypeDef4POST.FIX_EMAIL == subsPlan.servType) {
                    //     delete viewOption.bundleAddressSurveyId;
                    // }
                    fish.popupView({
                        // url: 'crm/modules/pos/flowpage/bundle/components/ChooseExistSubsWin',

                        url: 'crm/standardizedcomponents/views/ChooseExistSubsWin',
                        width: '80%',
                        height: '60%',
                        viewOption: viewOption,
                        close: function (data) {
                            this.$('.js-choose-subs' + subsPlan.subsPlanId).popedit('setValue', {
                                name: data.subsMsisdnsStr,
                                value: data.subsList,
                            });
                            subsPlan.subsList = data.subsList;
                            subsPlan.subsAccNbrsStr = data.subsAccNbrsStr;
                            if (portal.appGlobal.get('isBolLogin')) {
                                fish.trigger(IntroDef.ADD_INTRO, {
                                    element: '.js-bundle-tree',
                                    tipsContents: IntroDef.BOUNDLE_OFFER_STEP9,
                                    position: 'top',
                                });
                            }
                        }.bind(this),
                        callback: function () {
                            if (portal.appGlobal.get('isBolLogin')) {
                                fish.trigger(IntroDef.EXIT_INTRO);
                            }
                        },
                        dismiss: function () {
                            if (portal.appGlobal.get('isBolLogin')) {
                                fish.trigger(IntroDef.ADD_INTRO, {
                                    element: '.js-bundle-tree',
                                    tipsContents: IntroDef.BOUNDLE_OFFER_STEP9,
                                    position: 'top',
                                });
                            }
                        },
                    });
                }.bind(this),
                change: function (e, data) {
                    // 可以绑定change事件改变初始值
                    if (fish.isEmpty(data)) {
                        subsPlan.subsList = null;
                        subsPlan.subsAccNbrsStr = null;
                    }
                }.bind(this),
                initialData: {
                    name: subsPlan.subsAccNbrsStr,
                    value: subsPlan.subsList,
                },
            });
        },

        /**
         * 加载Bundle查询条件
         */
        initBundleSearch: function () {
            var $ele = this.$('.js-bundle-search');
            $ele.searchbar({
                displayMode: 'tail',
                filterFields: [
                    {
                        label: I18N.BUNDLE_MEMBER_NAME,
                        value: 'name',
                    },
                ],
                query: function (payload, callback) {
                    var offerList = [],
                        labelName;
                    if (this.$bundleTreeGrid) {
                        var allOfferList = this.$bundleTreeGrid.grid('getRowData');
                        $.each(
                            allOfferList,
                            function (index, cur) {
                                if (cur.subsPlanId) {
                                    labelName = cur.subsPlanName;
                                    offerList.push({
                                        label: labelName,
                                        id: cur.key,
                                    });
                                }
                            }.bind(this)
                        );
                    }
                    callback(offerList);
                }.bind(this),
                select: function (e, data) {
                    if (data && data.id) {
                        if (this.$bundleTreeGrid) {
                            this.$bundleTreeGrid.grid('setSelection', data.id);
                        }
                    }
                }.bind(this),
            });
        },

        getExcludeNumber: function (subsPlan) {
            var findFlag = false;
            var excludeNumberTmp = '';

            if (this.bundleUnitList) {
                fish.forEach(
                    this.bundleUnitList,
                    function (item) {
                        if (item.children) {
                            fish.forEach(
                                item.children,
                                function (childItem) {
                                    if (childItem.subsPlanId != subsPlan.subsPlanId) {
                                        excludeNumberTmp = excludeNumberTmp + ',' + item.children[0].subsAccNbrsStr;
                                    }
                                }.bind(this)
                            );
                        }
                    }.bind(this)
                );
            }
            var excludeNumber = excludeNumberTmp.split(',');
            return excludeNumber;
        },

        /**
         * 获取选中的offer列表
         */
        onOkClick: function () {
            this.isChangeBundleOperOrder = false;
            if (this.validateFeasibleCheck()) {
                this.broadFeasibleCheck();
                return;
            }
            this.onOkClickOrder();
        },

        onOkClickOrder: function () {
            // 校验
            var errMsgList = [];
            // var parentRowDataList = this.$bundleTreeGrid.grid('getRootNodes');
            // bundle开户时才做校验，成员订单操作不校验
            if (!this.memberOperOrderFlag) {
                fish.each(
                    this.bundleUnitList,
                    function (rowData) {
                        // 必选一定有下限1，不必重复校验
                        this.validateMemberNumberLimit(rowData, errMsgList);
                    }.bind(this)
                );
                this.validateMemberAddress(errMsgList);
            }
            // 校验不通过
            if (!fish.isEmpty(errMsgList)) {
                // 展示所有提示信息
                fish.warn(errMsgList.join('<br>'));
                return;
            }

            var modifySubsPlan = this.getModifySubsPlanList() || [];
            modifySubsPlan = fish.sortBy(modifySubsPlan, function (item) {
                return item.isPrimary === 'Y' ? -1 : 1;
            });
            this.subsEventId;
            // 校验OrderOfferAcceptCheck
            var list = [];
            fish.each(modifySubsPlan, function (item) {
                if (item.newMemberQty > 0 && item.newMemberQty == item.qty) {
                    list.push({ offerId: item.subsPlanId });
                }
            });
            if (list.length > 0) {
                var param = {
                    offerList: list,
                    custOrder: this.custOrder,
                };
                OrderAction.orderOfferAcceptCheck(
                    param,
                    function (res) {
                        // 返回数据
                        this.trigger('finishSelectBundleUnitSubsPlan', modifySubsPlan);
                    }.bind(this)
                );
            } else {
                // 返回数据
                this.trigger('finishSelectBundleUnitSubsPlan', modifySubsPlan);
            }
        },

        validateFeasibleCheck: function () {
            var isNeedCheck = false;
            if (this.custOrder.STD_ADDR_ID || this.custOrder.stdAddrId || (this.custOrder.FIX_INSTALL_ORDER && this.bundleAddressSurveyId)) {
                return isNeedCheck;
            }
            var servTypeStr = ConfigItemAction.qryParamValue('CUSTOMER_CARE', 'CC_PUBLIC', 'ADDR_FILTER_SERV_TYPE');
            if (servTypeStr) {
                fish.each(
                    this.bundleUnitList,
                    function (rowData) {
                        fish.each(
                            rowData.children,
                            function (subsPlan) {
                                // 新增有需要地址校验但是上一层没有地址校验
                                if (servTypeStr.indexOf(subsPlan.servType) !== -1) {
                                    var bundleOrder = fish.find(
                                        this.custOrder.BUNDLE_OPER_ORDER,
                                        function (e) {
                                            return this.bundleOrderItem.ORDER_ITEM_ID == e.ORDER_ITEM_ID;
                                        }.bind(this)
                                    );
                                    if (bundleOrder && !bundleOrder.STD_ADDR_ID) {
                                        isNeedCheck = true;
                                    }
                                }
                            }.bind(this)
                        );
                    }.bind(this)
                );
            }
            return isNeedCheck;
        },
        broadFeasibleCheck: function () {
            // 此情况一般一次只需要校验一个
            var custDetail = {};
            if (this.cust) {
                custDetail.custId = this.cust.CUST_ID;
                custDetail.custName = this.cust.CUST_NAME;
                custDetail.custType = this.cust.CUST_TYPE;
            }
            var subsPlanIdArr = [];
            subsPlanIdArr.push(this.bundleOrderItem.subsPlanId || this.bundleOrderItem.SUBS_PLAN_ID);

            var broadListAll = []; // 如果quantity>1分开地址校验
            var broadItem = {};
            broadItem.quantity = 1;
            broadItem.subsPlanId = this.bundleOrderItem.SUBS_PLAN_ID;
            broadItem.name = this.bundleOrderItem.SUBS_PLAN_NAME;
            broadItem.checkSeq = 0;
            broadListAll.push(broadItem);

            var offerIdList = [];
            fish.each(
                this.bundleUnitList,
                function (rowData) {
                    fish.each(
                        rowData.children,
                        function (subsPlan) {
                            if (subsPlan.newMemberQty > 0) {
                                offerIdList.push(subsPlan.subsPlanId);
                            }
                        }.bind(this)
                    );
                }.bind(this)
            );

            fish.popupView({
                canClose: true,
                url: 'crm/modules/pos/orderentry/views/broadband/BroadbandCheckView',
                width: '60%',
                height: '60%',
                viewOption: {
                    custDetail: custDetail,
                    broadList: broadListAll,
                    subsPlanIdArr: subsPlanIdArr,
                    offerIdList: offerIdList,
                },
                close: function (data) {
                    if (data) {
                        // 只有一个
                        fish.forEach(
                            this.custOrder.BUNDLE_OPER_ORDER,
                            function (item) {
                                if (this.bundleOrderItem.ORDER_ITEM_ID == item.ORDER_ITEM_ID) {
                                    item.ACCESS_TYPE = data[0].accessType;
                                    item.BAND_WIDTH = data[0].bandWidth;
                                    item.STD_ADDR_FULL_NAME = data[0].address;
                                    item.STD_ADDR_ID = data[0].addressId;
                                    item.SUBS_PLAN_ID = this.bundleOrderItem.SUBS_PLAN_ID;
                                    item.SUBS_PLAN_NAME = this.bundleOrderItem.SUBS_PLAN_NAME;
                                }
                            }.bind(this)
                        );
                        // this.onOkClickOrder();
                        this.trigger('finishSelectBundleUnitSubsPlan', null, this.custOrder.BUNDLE_OPER_ORDER);
                    }
                }.bind(this),
            });
        },

        getDeleteSubsPlanList: function () {
            return this.deletedMemberOperOrderArr;
        },
        getModifySubsPlanList: function () {
            var modifySubsPlan = [];
            fish.each(
                this.bundleUnitList,
                function (item) {
                    fish.each(
                        item.children,
                        function (subsPlan) {
                            subsPlan.memberAlias = item.memberAlias;

                            var qty = 0;
                            if (subsPlan.newMemberQty > subsPlan.memberQty) {
                                qty = subsPlan.newMemberQty - subsPlan.memberQty;
                            }
                            var newExistSubsArr = [];
                            if (!fish.isEmpty(subsPlan.subsList)) {
                                fish.each(
                                    subsPlan.subsList,
                                    function (subs) {
                                        if (fish.isEmpty(subsPlan.oldSubsList)) {
                                            newExistSubsArr.push(subs);
                                        } else {
                                            var oldSelectedSubs = fish.findWhere(subsPlan.oldSubsList, { subsId: subs.subsId });
                                            if (fish.isEmpty(oldSelectedSubs)) {
                                                newExistSubsArr.push(subs);
                                            }
                                        }
                                    }.bind(this)
                                );
                            }
                            subsPlan.qty = qty;
                            subsPlan.newExistSubsList = newExistSubsArr;
                            if (!fish.isEmpty(this.custContract)) {
                                if (!fish.isEmpty(this.custContract.contractItemList)) {
                                    fish.each(
                                        this.custContract.contractItemList,
                                        function (contractItem) {
                                            if (contractItem.subsPlanId == subsPlan.subsPlanId) {
                                                subsPlan.contractItemId = contractItem.contractItemId;
                                            }
                                        }.bind(this)
                                    );
                                }
                            }

                            this.getMemberAddress(subsPlan);
                            modifySubsPlan.push(subsPlan);
                        }.bind(this)
                    );
                }.bind(this)
            );
            return modifySubsPlan;
        },
        getMemberAddress: function (subsPlan) {
            var rowDatas = this.$addressGrid.grid('getRowData') || [];
            var addressArr = [];
            fish.each(
                rowDatas,
                function (rowData) {
                    if (rowData.addressId && rowData.subsPlanId === subsPlan.subsPlanId) {
                        // var addressData = {};
                        rowData.ACCESS_TYPE = rowData.accessType;
                        rowData.BANDWIDTH = rowData.bandwidth;
                        rowData.STD_ADDR_FULL_NAME = rowData.addressName;
                        rowData.STD_ADDR_ID = rowData.addressId;
                        rowData.SUBS_PLAN_ID = rowData.subsPlanId;
                        addressArr.push(rowData);
                    }
                }.bind(this)
            );

            if (addressArr.length > 0) {
                subsPlan.addressList = addressArr;
            }
        },
        resize: function () {
            // if (this.memberOperOrderFlag) {
            //     this.$bundleTreeGrid.grid('setGridHeight', this.parentView.$el.innerHeight() - 254);
            // }
            // else {
            //     this.$bundleTreeGrid.grid('setGridHeight', this.$('.modal-body').height() - this.$('.modal-body').children().eq(0).height() - 30);
            // }
        },
    });
});
