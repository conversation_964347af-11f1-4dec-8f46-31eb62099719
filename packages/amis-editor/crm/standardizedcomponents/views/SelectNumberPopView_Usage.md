# SelectNumberPopView 组件使用说明

## 概述

SelectNumberPopView 是一个标准化的选号弹窗组件，为输入框提供手动选号和自动选号功能。该组件已按照 Fish 组件标准化规范进行优化，支持标准化的事件通信和错误处理。

## 功能特性

- 手动选号弹窗
- 自动选号功能
- 号码验证
- 标准化事件通信
- 错误处理和日志记录
- 全局数据安全访问
- 向后兼容性

## 安装和使用

### 基本使用

```javascript
// 创建组件实例
var selectNumberPop = new SelectNumberPopView($('#accNbrInput'), {
    servType: 695,
    indepProdSpecId: 'PROD001',
    custId: 'CUST001',
    spId: 'SP001',
    subsPlanId: 'PLAN001'
});
```

### 高级配置

```javascript
var selectNumberPop = new SelectNumberPopView($('#accNbrInput'), {
    servType: 695,
    indepProdSpecId: 'PROD001',
    custId: 'CUST001',
    spId: 'SP001',
    subsPlanId: 'PLAN001',
    
    // 可选参数
    showPreAccNbr: true,
    bingSimCardDisabled: false,
    isShowBfmKey: false,
    
    // 业务参数
    orgId: 'ORG001',
    staffId: 'STAFF001',
    hlrId: 'HLR001',
    
    // 回调函数
    chooseNbrCallback: function(data, oldAccNbr) {
        console.log('选号完成:', data);
    },
    
    enableResourceType: function() {
        console.log('启用资源类型');
    },
    
    // BO访问配置
    boAccess: {
        $currentView: this
    },
    
    // 页面设计器配置
    pageDesignerObj: {
        getSelectedData: function(data) {
            console.log('获取选中数据:', data);
        }
    }
});
```

## API 文档

### Props

| 参数名 | 类型 | 必填 | 默认值 | 描述 |
|--------|------|------|--------|------|
| $targetEL | jQuery | 是 | - | 目标输入框元素 |
| options | Object | 是 | - | 配置选项对象 |
| options.portalAppGlobalData | Object | 否 | portal.appGlobal | 门户全局数据对象 |
| options.servType | Number | 是 | - | 服务类型(如695) |
| options.indepProdSpecId | String | 是 | - | 独立产品规格ID |
| options.custId | String | 是 | - | 客户ID |
| options.spId | String | 是 | - | 服务提供商ID |
| options.subsPlanId | String | 是 | - | 套餐ID |
| options.showPreAccNbr | Boolean | 否 | false | 是否显示前缀号码 |
| options.bingSimCardDisabled | Boolean | 否 | false | 是否禁用SIM卡绑定 |
| options.isShowBfmKey | Boolean | 否 | false | 是否显示BFM键 |
| options.orgId | String | 否 | - | 组织ID，默认从全局数据获取 |
| options.staffId | String | 否 | - | 员工ID，默认从全局数据获取 |
| options.hlrId | String | 否 | - | HLR ID(用于换号查询) |
| options.chooseNbrCallback | Function | 否 | - | 选号回调函数 |
| options.enableResourceType | Function | 否 | - | 启用资源类型方法 |

### Events

| 事件名 | 参数 | 描述 |
|--------|------|------|
| dataChange | {prefix, accNbr, accNbrId, servType} | 号码数据变更事件 |
| numberSelectionStarted | {timestamp} | 选号开始事件 |
| accNbrValidationError | {accNbr, error} | 号码验证错误事件 |
| error | {error, context, timestamp} | 通用错误事件 |

### Methods

| 方法名 | 参数 | 返回值 | 描述 |
|--------|------|--------|------|
| setAccNbr | (accNbrObj, clearBySIMCard) | void | 设置号码信息 |
| getAccNbr | () | Object\|null | 获取当前号码信息 |
| getGlobalData | (key, defaultValue) | any | 安全获取全局数据 |
| setGlobalData | (key, value) | boolean | 安全设置全局数据 |
| triggerStandardEvent | (eventName, businessData) | void | 触发标准化事件 |
| handleError | (error, context) | void | 处理错误 |

## 示例

### 基础示例

```javascript
// HTML
<input type="text" id="accNbrInput" name="accNbr" class="form-control" />

// JavaScript
var selectNumberPop = new SelectNumberPopView($('#accNbrInput'), {
    servType: 695,
    indepProdSpecId: 'PROD001',
    custId: 'CUST001',
    spId: 'SP001',
    subsPlanId: 'PLAN001'
});

// 监听事件
$('#accNbrInput').on('dataChange', function(event, data) {
    console.log('号码变更:', data);
});
```

### 高级示例

```javascript
var selectNumberPop = new SelectNumberPopView($('#accNbrInput'), {
    servType: 695,
    indepProdSpecId: 'PROD001',
    custId: 'CUST001',
    spId: 'SP001',
    subsPlanId: 'PLAN001',
    showPreAccNbr: true,
    chooseNbrCallback: function(data, oldAccNbr) {
        console.log('选号完成:', data);
        // 处理选号结果
    }
});

// 监听标准化事件
$('#accNbrInput').on('numberSelectionStarted', function(event, eventData) {
    console.log('开始选号:', eventData);
});

$('#accNbrInput').on('error', function(event, eventData) {
    console.error('组件错误:', eventData);
});

// 获取号码信息
var accNbrInfo = selectNumberPop.getAccNbr();
if (accNbrInfo) {
    console.log('当前号码:', accNbrInfo.accNbr);
}

// 设置号码信息
selectNumberPop.setAccNbr({
    accNbr: '13800138000',
    accNbrId: 'NBR001',
    prefix: '138'
});
```

## 最佳实践

### 性能优化

1. **事件监听器管理**：在页面销毁时正确移除事件监听器
2. **错误处理**：使用组件提供的标准化错误处理方法
3. **全局数据访问**：使用组件提供的安全访问方法

### 错误处理

```javascript
// 监听错误事件
$('#accNbrInput').on('error', function(event, eventData) {
    console.error('SelectNumberPopView 错误:', eventData.data.error);
    // 自定义错误处理逻辑
});

// 监听号码验证错误
$('#accNbrInput').on('accNbrValidationError', function(event, eventData) {
    console.warn('号码验证失败:', eventData.data.accNbr);
    // 处理验证错误
});
```

## 常见问题

### 问题1：组件初始化失败

**原因**：缺少必填参数或目标元素不存在

**解决方案**：
```javascript
// 确保目标元素存在
if ($('#accNbrInput').length === 0) {
    console.error('目标元素不存在');
    return;
}

// 确保必填参数完整
var requiredParams = ['servType', 'indepProdSpecId', 'custId', 'spId', 'subsPlanId'];
var missingParams = requiredParams.filter(param => !options[param]);
if (missingParams.length > 0) {
    console.error('缺少必填参数:', missingParams);
    return;
}
```

### 问题2：事件未触发

**原因**：事件监听器设置时机不正确

**解决方案**：
```javascript
// 在组件创建后立即设置事件监听器
var selectNumberPop = new SelectNumberPopView($('#accNbrInput'), options);

// 立即设置事件监听器
$('#accNbrInput').on('dataChange', function(event, data) {
    console.log('号码变更:', data);
});
```

## 更新日志

### v2.0 (2024-07-15)
- 标准化改造，符合 Fish 组件标准化规范
- 添加标准化事件触发机制
- 优化错误处理和日志记录
- 添加安全的全局数据访问方法
- 完善 API 文档和使用说明
- 移除未使用的依赖项

### v1.0
- 初始版本
- 基本的选号功能
- 手动选号和自动选号
