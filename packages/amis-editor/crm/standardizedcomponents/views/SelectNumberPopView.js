/* eslint-disable no-shadow */
/* eslint-disable no-param-reassign */
/* eslint-disable no-redeclare */
/* eslint-disable newline-per-chained-call */
/* eslint-disable no-extra-bind */
/* 忽视的目的：保持原有的业务逻辑，不能随便修改 */

/**
 * @fileoverview 选号弹窗组件 - 提供手动选号和自动选号功能
 * @description 标准化的选号组件，支持手动选号弹窗、自动选号、号码验证等功能
 * <AUTHOR> Team
 * @since 2024-07-15
 * @version 2.0
 *
 * @component SelectNumberPopView
 * @description 选号弹窗组件，为输入框提供选号功能
 *
 * @props {jQuery} $targetEL - 目标输入框元素（必填）
 * @props {Object} options - 配置选项对象（必填）
 * @props {Object} options.portalAppGlobalData - 门户全局数据对象，默认为portal.appGlobal
 * @props {boolean} [options.showPreAccNbr=false] - 是否显示前缀号码
 * @props {boolean} [options.bingSimCardDisabled=false] - 是否禁用SIM卡绑定
 * @props {number} options.servType - 服务类型(如695)
 * @props {Object} [options.boAccess] - BO访问对象
 * @props {jQuery} [options.boAccess.$currentView] - 当前视图元素
 * @props {Object} [options.pageDesignerObj] - 页面设计器对象
 * @props {Function} [options.pageDesignerObj.getSelectedData] - 获取选中数据的方法
 * @props {string} [options.orgId] - 组织ID，默认从全局数据获取
 * @props {string} [options.staffId] - 员工ID，默认从全局数据获取
 * @props {string} options.indepProdSpecId - 独立产品规格ID
 * @props {string} options.custId - 客户ID
 * @props {string} options.spId - 服务提供商ID
 * @props {string} [options.hlrId] - HLR ID(用于换号查询)
 * @props {string} options.subsPlanId - 套餐ID
 * @props {Function} [options.chooseNbrCallback] - 选号回调函数
 * @props {Function} [options.enableResourceType] - 启用资源类型方法
 * @props {Object} [options.oldAccNbr] - 旧号码信息
 * @props {string} [options.oldAccNbr.accNbr] - 旧号码
 * @props {string} [options.oldAccNbr.accNbrId] - 旧号码ID
 * @props {string} [options.oldAccNbr.prefix] - 旧号码前缀
 * @props {boolean} [options.isShowBfmKey=false] - 是否显示BFM键
 *
 * @events
 * @event dataChange - 号码数据变更事件
 * @param {Object} data - 事件数据
 * @param {string} data.prefix - 号码前缀
 * @param {string} data.accNbr - 号码
 * @param {string} data.accNbrId - 号码ID
 * @param {number} data.servType - 服务类型
 *
 * @example
 * // 基本使用
 * var selectNumberPop = new SelectNumberPopView($('#accNbrInput'), {
 *     portalAppGlobalData: portal.appGlobal,
 *     servType: 695,
 *     indepProdSpecId: 'PROD001',
 *     custId: 'CUST001',
 *     spId: 'SP001',
 *     subsPlanId: 'PLAN001'
 * });
 */

define([
    'i18n!crm/modules/order/orderentry/i18n/OrderEntry',
    'crm/modules/order/orderentry/actions/OrderEntryAction',
    'crm/modules/common/actions/CommonAction',
    'crm/modules/fbf/bfm/utils/Controller',
    'crm/standardizedcomponents/utils/Controller',
    'crm/modules/common/constant/ContactChannelDef',
    'crm/modules/common/constant/AccNbrStateDef',
    'crm/modules/common/models/TrackPointLogModel',
    'crm/modules/common/util/PortalConfigUtil',
    'css!crm/modules/pos/orderentry/css/select-number.css',
    'css!crm/modules/pos/css/OrderEntry.css'
], function (
    I18N,
    OrderEntryAction,
    CommonAction,
    Controller,
    NewController,
    ContactChannelDef,
    AccNbrStateDef,
    TrackPointLogModel,
    PortalConfigUtil
) {
    return function ($targetEL, _options) {
        // 参数验证
        if (!$targetEL || !$targetEL.length) {
            throw new Error('SelectNumberPopView: $targetEL is required');
        }

        var options = _options || {};

        // 标准化改造：设置全局数据访问
        this.portalAppGlobalData = options.portalAppGlobalData || portal.appGlobal || {};

        // 创建安全的全局数据访问方法
        this.getGlobalData = function (key, defaultValue) {
            try {
                return this.portalAppGlobalData.get(key) || defaultValue;
            } catch (e) {
                return defaultValue;
            }
        }.bind(this);

        // 组件配置参数
        this.isShowBfmKey = options.isShowBfmKey || false;
        this.showPreAccNbr = options.showPreAccNbr;
        // 自动选号、手动选号、卡校验填充号码会设置为true，为true不会调用校验号码是否存在的接口
        this.accNbrFillFlag = false;
        // 选号选的号码，用于区分手动输入的号码
        this.accNbrChoosed = null;
        // 去通用的ajax异常处理
        this.commonAjaxErrorHandler = new fish.View().commonAjaxErrorHandler;
        this.oldAccNbr = null;

        if (options && options.bingSimCardDisabled) {
            this.bingSimCardDisabled = options.bingSimCardDisabled;
        }

        this.create = function () {
            this.configValue = PortalConfigUtil.configValue('BSS_ALL_2024Q4');
            $targetEL.parent().addClass('input-group');
            var $popIcon;
            var $autoIcon;
            if (options.servType == 695 && this.configValue == '1') {
                $popIcon = $("<span class='input-group-addon'><span class='iconfont icon-option-horizontal' disabled='disabled'></span></span>");
                $autoIcon = $(
                    "<span class='input-group-addon'><span title='" +
                        I18N.ORDER_ENTRY_AUTO +
                        ' ' +
                        I18N.ORDER_ENTRY_REFRESH +
                        "' class='iconfont icon-refresh' disabled='disabled'></span></span>"
                );
            } else {
                $popIcon = $("<span class='input-group-addon'><span class='iconfont icon-option-horizontal'></span></span>");
                $autoIcon = $(
                    "<span class='input-group-addon'><span title='" +
                        I18N.ORDER_ENTRY_AUTO +
                        ' ' +
                        I18N.ORDER_ENTRY_REFRESH +
                        "' class='iconfont icon-refresh'></span></span>"
                );
            }
            $targetEL.after($autoIcon);
            $targetEL.after($popIcon);
            // 点击弹出图标按钮
            if (options.servType != 695 || this.configValue != '1') {
                $popIcon.click(
                    function () {
                        this.onPopAccrNbrClick();
                    }.bind(this)
                );
                $autoIcon.click(
                    function () {
                        this.onAutoBtnClick();
                    }.bind(this)
                );
            }

            // 号码文本框鼠标离开
            $targetEL.blur(
                function () {
                    this.dealAccNbr();
                }.bind(this)
            );

            // 注册BO
            if (options.boAccess && options.boAccess.$currentView) {
                if (this.isShowBfmKey) {
                    NewController.getInstance().registerChange(options.boAccess.$currentView, options.boAccess, this.isShowBfmKey);
                } else {
                    Controller.getInstance().registerChange(options.boAccess.$currentView, options.boAccess);
                }
            }
        }.bind(this)();

        /**
         * 点击选号按钮处理方法
         */
        this.onPopAccrNbrClick = function () {
            // 记录操作日志
            var logModel = new TrackPointLogModel();
            logModel.set('operCode', 'OE001000');
            logModel.set('stepCode', 'OE001060');
            CommonAction.recordPageTrackPointLog(logModel);

            var initOptions = {
                getChooseAccNbrParam: this.getChooseAccNbrParam,
                commonAjaxErrorHandler: this.commonAjaxErrorHandler,
                oldAccNbr: this.oldAccNbr,
                bingSimCardDisabled: this.bingSimCardDisabled,
                showPreAccNbr: this.showPreAccNbr
            };

            // 把 options 传进去，不然选号取不到套餐等信息
            // extend( destination, [source] ) : Object
            // 复制source对象中的所有属性覆盖到destination对象上，并且返回 destination 对象. 复制是按顺序的, 所以后面的对象属性会把前面的对象属性覆盖掉(如果有重复)
            fish.extend(initOptions, options);

            // 手动选号 弹出选号弹框
            fish.popupView({
                // MODIFIED
                url: 'crm/standardizedcomponents/views/ChooseNumberPopView',
                width: 1068,
                height: '71%',
                keyboard: false,
                resizable: false,
                modal: true,
                viewOption: initOptions,
                close: function (closeData) {
                    // MODIFIED: 点击 OK 时的回调
                    if (options.pageDesignerObj) {
                        // 检查 getSelectedData 是否是一个函数，并调用它
                        var getSelectedData = options.pageDesignerObj.getSelectedData;
                        if (typeof getSelectedData === 'function') {
                            getSelectedData(closeData);
                        }
                    }
                    var showAccNbr = closeData.accNbr;
                    if (this.showPreAccNbr) {
                        showAccNbr = closeData.prefix + closeData.accNbr;
                    }
                    $targetEL
                        .val(showAccNbr)
                        .attr('newAccNbr', closeData.accNbr)
                        .attr('accNbrId', closeData.accNbrId)
                        .attr('prefix', closeData.prefix)
                        .attr('accNbrClassName', closeData.accNbrClassName)
                        .attr('accNbrClassId', closeData.accNbrClassId)
                        .attr('accNbrPrice', closeData.accNbrPrice)
                        .change()
                        .resetElement();
                    $targetEL
                        .prev(":input[name='accNbr_prefix']")
                        .val(closeData.prefix)
                        .attr('accNbrId', closeData.accNbrId)
                        .attr('prefix', closeData.prefix)
                        .attr('accNbrClassName', closeData.accNbrClassName)
                        .attr('accNbrClassId', closeData.accNbrClassId)
                        .attr('accNbrPrice', closeData.accNbrPrice)
                        .change()
                        .resetElement();
                    this.accNbrFillFlag = true;
                    this.accNbrChoosed = closeData.accNbr;
                    this.dealAccNbr();

                    this.oldAccNbr = {
                        accNbr: $.trim($targetEL.attr('newAccNbr')),
                        accNbrId: $targetEL.attr('accNbrId'),
                        prefix: $targetEL.attr('prefix')
                    };
                }.bind(this)
            });
        };

        this.onAutoBtnClick = function (hideErrMsgFlag) {
            var logModel = new TrackPointLogModel();
            logModel.set('operCode', 'OE001000');
            logModel.set('stepCode', 'OE001050');
            CommonAction.recordPageTrackPointLog(logModel);

            // 自动选号 调用接口
            var param = this.getChooseAccNbrParam();
            param.selectFlag = 'A';
            // 自动选号默认选择没有绑卡的号码
            param.bindFlag = 'N';
            // 调用选号接口
            var paramData = {};
            paramData.requestParam = param;
            $.blockUI();
            OrderEntryAction.chooseNbr(
                paramData,
                function (result) {
                    var accNbrList = result.accNbrList;
                    if (!fish.isEmpty(accNbrList)) {
                        var data = {};
                        data.accNbrId = accNbrList[0].accNbrId;
                        data.prefix = accNbrList[0].prefix;
                        data.accNbr = accNbrList[0].accNbr;
                        data.accNbrClassName = accNbrList[0].accNbrClassName;
                        data.accNbrPrice = accNbrList[0].accNbrPrice;
                        var showAccNbr = data.accNbr;
                        if (this.showPreAccNbr) {
                            showAccNbr = data.prefix + data.accNbr;
                        }
                        $targetEL
                            .val(showAccNbr)
                            .attr('newAccNbr', data.accNbr)
                            .attr('accNbrId', data.accNbrId)
                            .attr('prefix', data.prefix)
                            .attr('accNbrClassName', data.accNbrClassName)
                            .attr('accNbrPrice', data.accNbrPrice)
                            .change()
                            .resetElement();
                        $targetEL
                            .prev(":input[name='accNbr_prefix']")
                            .val(data.prefix)
                            .attr('accNbrId', data.accNbrId)
                            .attr('prefix', data.prefix)
                            .attr('accNbrClassName', data.accNbrClassName)
                            .attr('accNbrPrice', data.accNbrPrice)
                            .change()
                            .resetElement();
                        this.accNbrFillFlag = true;
                        this.accNbrChoosed = data.accNbr;
                        this.dealAccNbr();

                        if (options.chooseNbrCallback && fish.isFunction(options.chooseNbrCallback)) {
                            options.chooseNbrCallback(data, this.oldAccNbr);
                        }
                        this.oldAccNbr = {
                            accNbr: $.trim($targetEL.attr('newAccNbr')),
                            accNbrId: $targetEL.attr('accNbrId'),
                            prefix: $targetEL.attr('prefix')
                        };
                    } else {
                        $.unblockUI();
                        fish.warn(I18N.ORDER_ENTRY_ACC_NBR_NOT_EXISTS);
                    }
                }.bind(this),
                function (xhr, status, error) {
                    $targetEL.val('').change().attr('newAccNbr', '').attr('accNbrId', '').attr('prefix', '');
                    if (!hideErrMsgFlag) {
                        this.commonAjaxErrorHandler(xhr, status, error);
                    } else {
                        $.unblockUI();
                    }
                }.bind(this)
            );
        };

        // 号码处理
        this.dealAccNbr = function () {
            var accNbr = $.trim($targetEL.attr('newAccNbr')) || ''; // 新号码
            var oldAccNbr = $targetEL.attr('oldAccNbr') || ''; // 老号码

            // 新号码是手动填写的号码
            if (accNbr != this.accNbrChoosed) {
                this.accNbrFillFlag = false;
            } else if (accNbr == oldAccNbr) {
                // 老号码和新号码相同，直接返回
                return;
            }
            // 手动填写的号码，需要校验号码是否存在
            if (!this.accNbrFillFlag) {
                // 号码不存在或不可用 ，清空号码信息后返回
                this.validateAccNbrExists();
            } else {
                this.dealAccNbrRule();
            }
        };
        this.validateAccNbrExists = function () {
            var accNbr = $.trim($targetEL.attr('newAccNbr'));
            if (accNbr) {
                var oldAccNbr = $targetEL.attr('oldAccNbr') || '';
                if (accNbr == oldAccNbr) {
                    return;
                }

                var param = this.getChooseAccNbrParam();
                param.selectFlag = 'M';
                param.accNbr = accNbr;
                param.staffId = 1;
                // 调用选号接口
                var paramData = {};
                paramData.requestParam = param;
                OrderEntryAction.chooseNbr(
                    paramData,
                    function (result) {
                        var accNbrList = result.accNbrList;
                        if (!accNbrList || accNbrList.length == 0 || accNbrList[0].accNbrState == AccNbrStateDef.LOCKED) {
                            $targetEL.val('').change().attr('newAccNbr', '').attr('accNbrId', '').attr('prefix', '');
                            options.enableResourceType();
                            fish.error(I18N.ORDER_ENTRY_ACC_NBR_NOT_EXISTS);
                            return;
                        }

                        $targetEL.attr('accNbrId', accNbrList[0].accNbrId).attr('prefix', accNbrList[0].prefix);
                        // 新号号码为空

                        this.dealAccNbrRule();
                    }.bind(this),
                    function (xhr, status, error) {
                        $targetEL.val('').change().attr('newAccNbr', '').attr('accNbrId', '').attr('prefix', '');
                        if (!hideErrMsgFlag) {
                            this.commonAjaxErrorHandler(xhr, status, error);
                        } else {
                            $.unblockUI();
                        }
                    }.bind(this)
                );
            } else {
                $targetEL.val('').change().attr('newAccNbr', '').attr('accNbrId', '').attr('prefix', '');
                this.dealAccNbrRule();
            }
        };

        /**
         * 处理号码规则并触发数据变更事件
         */
        this.dealAccNbrRule = function () {
            var prefix = $targetEL.attr('prefix') || '';
            var accNbr = $.trim($targetEL.attr('newAccNbr')) || '';
            var accNbrId = $targetEL.attr('accNbrId') || '';

            var eventData = {
                prefix: prefix,
                accNbr: accNbr,
                accNbrId: accNbrId,
                servType: options.servType
            };
            // 保持向后兼容性
            $targetEL.trigger('dataChange', eventData);
        };

        /**
         * 获取选号参数
         * @returns {Object} 选号接口参数
         */
        this.getChooseAccNbrParam = function () {
            var param = {};
            param.contactChannelId = ContactChannelDef.CSR;

            if (options.orgId) {
                param.orgId = options.orgId;
            } else {
                param.orgId = this.portalAppGlobalData.get('orgId');
            }

            if (options.staffId) {
                param.staffId = options.staffId;
            } else {
                param.staffId = this.portalAppGlobalData.get('staffId');
            }

            param.indepProdSpecId = options.indepProdSpecId;
            param.custId = options.custId;
            param.servType = options.servType;
            param.spId = options.spId;

            // 换号需要带hlrId去查
            if (options.hlrId) {
                param.hlrId = options.hlrId;
            }

            var offerIds = [];
            offerIds.push(options.subsPlanId);
            param.offerIds = offerIds;
            return param;
        };

        /**
         * 设置号码信息
         * @param {Object|null} accNbrObj - 号码对象
         * @param {string} accNbrObj.accNbr - 号码
         * @param {string} accNbrObj.accNbrId - 号码ID
         * @param {string} accNbrObj.prefix - 号码前缀
         * @param {boolean} [clearBySIMCard=false] - 是否由SIM卡清空触发
         */
        this.setAccNbr = function (accNbrObj, clearBySIMCard) {
            if (accNbrObj) {
                var showAccNbr = accNbrObj.accNbr;
                if (this.showPreAccNbr) {
                    showAccNbr = accNbrObj.prefix + accNbrObj.accNbr;
                }
                $targetEL
                    .val(showAccNbr)
                    .attr('newAccNbr', accNbrObj.accNbr)
                    .attr('accNbrId', accNbrObj.accNbrId)
                    .attr('oldAccNbrId', accNbrObj.accNbrId)
                    .attr('oldAccNbr', accNbrObj.accNbr)
                    .attr('prefix', accNbrObj.prefix)
                    .change()
                    .resetElement();
                $targetEL
                    .prev(":input[name='accNbr_prefix']")
                    .val(accNbrObj.prefix)
                    .attr('accNbrId', accNbrObj.accNbrId)
                    .attr('oldAccNbrId', accNbrObj.accNbrId)
                    .attr('oldAccNbr', accNbrObj.accNbr)
                    .attr('prefix', accNbrObj.prefix)
                    .change()
                    .resetElement();
                this.dealAccNbr();
                this.accNbrFillFlag = true;
            } else {
                $targetEL.val('').attr('newAccNbr', '').attr('accNbrId', '').attr('prefix', '').change();

                // 清空卡同时清空绑定的号码时，不需要调用该接口
                if (!clearBySIMCard) {
                    this.dealAccNbr();
                } else {
                    $targetEL.val('').attr('newAccNbr', '').attr('oldAccNbrId', '').attr('oldAccNbr', '');
                }
                this.accNbrFillFlag = false;
            }
        };

        /**
         * 获取当前号码信息
         * @returns {Object|null} 号码对象，如果没有有效号码则返回null
         * @returns {string} accNbr - 号码
         * @returns {string} accNbrId - 号码ID
         * @returns {string} prefix - 号码前缀
         */
        this.getAccNbr = function () {
            var accNbr = $.trim($targetEL.attr('newAccNbr'));
            var accNbrId = $targetEL.attr('accNbrId');
            var prefix = $targetEL.attr('prefix');

            if (accNbr && accNbrId) {
                return {
                    accNbr: accNbr,
                    accNbrId: accNbrId,
                    prefix: prefix
                };
            }

            return null;
        };
        
        this.setHlrId = function(hlrId) {
            options.hlrId = hlrId;
        }
    };
});
