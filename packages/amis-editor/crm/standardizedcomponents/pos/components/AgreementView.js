﻿/**
 * @fileoverview AgreementView 标准化组件 - 协议处理视图组件
 * @description 用于处理各种业务流程中的协议选择、设备管理、资源配置等功能
 * <AUTHOR>
 * @since 2025-07-16
 * @version 2.0.0 - 标准化改造版本
 *
 * @component AgreementView
 * @extends {fish.View}
 *
 * @param {Object} options - 初始化参数
 * @param {Object} options.orderItem - 订单条目信息（必需）
 * @param {Object} [options.custOrder] - 客户订单信息
 * @param {string} [options.pageUuid] - 页面唯一标识
 * @param {string} [options.title] - 面板标题
 * @param {boolean} [options.isDelivery=false] - 是否为配送订单
 * @param {boolean} [options.showResourceType=false] - 是否显示资源类型面板
 * @param {boolean} [options.showOldAgreement=false] - 是否显示旧协议面板
 * @param {boolean} [options.showIccid=false] - 是否显示 ICCID 面板
 * @param {boolean} [options.showSimSku=false] - 是否显示 SIM SKU 选择
 * @param {boolean} [options.showClearIcon=false] - 是否显示清除图标
 * @param {boolean} [options.HANDLED_BY_ACCT_MANAGER=false] - 是否由客户经理处理
 * @param {boolean} [options.isBack=false] - 是否为返回操作
 * @param {boolean} [options.required=false] - 是否为必填项
 *
 */
/* eslint-disable */
define([
    'hbs!crm/modules/pos/components/templates/AgreementViewTpl.hbs',
    'i18n!crm/modules/pos/workspace/i18n/OrderEntry',
    'i18n!crm/modules/pos/flowpage/common/i18n/CustomerCare',
    'crm/modules/pos/components/HandSetComponent',
    'crm/modules/resource/card/actions/SimCardMgrAction',
    'crm/modules/fbf/bfm/utils/Controller',
    'crm/modules/pos/common/utils/BoUtils',
    'crm/modules/pos/flowpage/business/component/constants/InputTypeDef',
    'crm/modules/pos/constants/AgreementDef',
    'crm/modules/pos/components/AgreementMaintainerComponent',
    'crm/modules/common/components/SimSkuCombobox',
    'crm/modules/common/constant/SubsEventDef',
    'crm/modules/common/constant/OperationTypeDef',
    'crm/modules/common/util/ArrayUtils'
], function (template, I18N, I18N_CustomerCare, HandSetComponent, SimCardMgrAction, Controller, BoUtils,
    InputTypeDef, AgreementDef, AgreementMaintainerComponent, SimSkuCombobox, SubsEventDef, OperationTypeDef, ArrayUtils) {
    return fish.View.extend({
        template: template,
        serialize: $.extend(I18N, I18N_CustomerCare),
        events: {

        },

        initialize: function (options) {
            this.options = options || {};
            this.orderItem = options.orderItem;
            this.isDelivery = this.options.isDelivery;
            this.chooseDevice = false;
            this.custOrder = this.options.custOrder;
            this.CUST = {};
            if (this.custOrder) {
                this.CUST = this.custOrder.CUST || {};
            }
        },

        afterRender: function () {
            this.$form = this.$('.js-form');
            if (this.orderItem.AGREEMENT_ORDER && !fish.isEmpty(this.orderItem.AGREEMENT_ORDER)) {
                var newAgreementOrder = fish.find(this.orderItem.AGREEMENT_ORDER, function (item) {
                    return item.OPERATION_TYPE == 'A';
                });
                if (newAgreementOrder) {
                    this.chooseDevice = true;
                }

            }
            this.initHandSetComponent();


            if (this.options.showResourceType) {
                this.$('.js-resourceType-panel').removeClass('hidden');
                this.initSimCardBaseTypeSource(this.orderItem.SIM_TYPE_ID);
            }

            if (this.options.showOldAgreement) {
                this.initOldAgreement();
            }

            if (this.options.showIccid) {
                this.initIccidPanel();
            }
        },

        initOldAgreement: function() {
            if (this.orderItem.AGREEMENT_ORDER) {
                this.orderItem.AGREEMENT_ORDER.forEach(function(item) {
                    if (AgreementDef.isMainOfferAgreement(item.AGREEMENT_TYPE) && item.OPERATION_TYPE != OperationTypeDef.NEW) {
                        this.$('.js-oldAgreement-panel').removeClass('hidden');
                        //this.$('.js-oldAgreement').html(item.AGREEMENT_NAME);
                        this.$('.js-oldAgreement').val(item.AGREEMENT_NAME);
                        this.$('.js-oldAgreement').css('text-overflow','ellipsis');
                        this.$('.js-oldAgreement').css('overflow','hidden');
                    }
                }.bind(this));
            }
        },

        initHandSetComponent: function() {
            this.$('.js-agreement-panel').removeClass('hidden');
            if (!fish.isEmpty(this.options.title)) {
                this.$('.js-agreement-panel-title').text(this.options.title);
            }

            var agreementId = '';
            if (this.orderItem.AGREEMENT_ORDER) {
                this.orderItem.AGREEMENT_ORDER.forEach(function(item) {
                    if (item.OPERATION_TYPE == OperationTypeDef.NEW && AgreementDef.isMainOfferAgreement(item.AGREEMENT_TYPE)) {
                        agreementId = item.AGREEMENT_ID;
                        return;
                    }
                }.bind(this));
            }
            this.$(":input[name='agreement']").resetElement();
            this.$(":input[name='agreement']").attr('data-rule-ignore',true);
            this.$handSetView = new HandSetComponent(this.$(":input[name='agreement']"), this.$(":input[name='handset']"), this.$(":input[name='agreementMaintainer']"), {
                subsPlanId: this.orderItem.SUBS_PLAN_ID || this.orderItem.SUBS_BASE_ORDER.SUBS_PLAN_ID,
                custId: this.orderItem.CUST_ID,
                custOrderId: this.orderItem.CUST_ORDER_ID,
                orderItemId: this.orderItem.ORDER_ITEM_ID,
                custType: this.CUST.CUST_TYPE || '',
                orderItem: this.orderItem,
                HANDLED_BY_ACCT_MANAGER: this.options.HANDLED_BY_ACCT_MANAGER,
                $currentView: this,
                isBack: this.options.isBack,
                showClearIcon: this.options.showClearIcon,
                required: this.options.required,
                agreementId: agreementId,
                /* 合约机控制显示隐藏 */
                hiddenElement: function() {
                    this.$('.js-agreement-panel').addClass('hidden');
                    this.$(":input[name='handset']").parent().parent().parent().empty();
                    this.$(":input[name='agreement']").parent().parent().parent().empty();
                }.bind(this),
                hiddenHandset: function() {
                    this.$(":input[name='handset']").parent().parent().parent().addClass('hidden');
                }.bind(this),
                showHandset: function() {
                    this.$(":input[name='handset']").parent().parent().parent().removeClass('hidden');
                }.bind(this),
                requiredHander: function() {
                    this.$(":input[name='agreement']").resetElement();
                    this.$(":input[name='agreement']").removeAttr('data-rule-ignore');

                    this.$(".js-agreement-panel").removeClass('hidden');
                    this.$(":input[name='handset']").attr('data-rule', 'required;');
                    this.$(":input[name='agreement']").attr('data-rule', 'required;');

                    this.$(":input[name='handset']").parent().parent().addClass('required');
                    this.$(":input[name='agreement']").parent().parent().addClass('required');

                }.bind(this),
                successHander: function (data) {
                    this.handSetInited = true;
                    this.$(":input[name='agreementMaintainer']").val('');
                    this.$(":input[name='agreementMaintainer']").attr('data', JSON.stringify(''));
                    this.$(":input[name='agreementMaintainer']").attr('data-rule', '');
                    this.$(":input[name='agreementMaintainer']").parent().parent().parent().addClass('hidden');
                    // 选择了手机设备才允许选择卡
                    if (this.$baseTypeListPlace) {
                        this.$('.js-dup-sim-card-resource').combobox('enable');
                    }
                    else {
                        this.$('.js-dup-sim-card-resource').removeAttr('disabled');
                    }
                    this.$('.js-iccid').removeAttr('disabled');
                    this.$("input[name='simSku']").combobox('enable');

                    if (AgreementDef.isMultiPhoneContract(data.agreementType)) {
                        var agreementMaintainerObj = { agreementMaintainerObjList: [] };
                        if (data.agreementMaintainerList) {
                            var agreementMaintainer = data.agreementMaintainerList[0];
                            if (agreementMaintainer.alias) {
                                this.$(":input[name='agreementMaintainer']").parent().prev().html(agreementMaintainer.alias);
                            }
                            agreementMaintainerObj.limitCnt = agreementMaintainer.limitCnt;
                            agreementMaintainerObj.alias = agreementMaintainer.alias;
                            if (agreementMaintainer.agreementMaintainerObjList) {
                                fish.each(agreementMaintainer.agreementMaintainerObjList, function(item) {
                                    if (AgreementDef.isSubsPlan(item.objType)) {
                                        agreementMaintainerObj.agreementMaintainerObjList.push(item);
                                    }
                                });
                            }
                        }
                        this.$(":input[name='agreementMaintainer']").parent().parent().parent().removeClass("hidden");
                        new AgreementMaintainerComponent(this.$(":input[name='agreementMaintainer']"), {
                            agreementMaintainerObjList: agreementMaintainerObj.agreementMaintainerObjList,
                            limitCnt: agreementMaintainerObj.limitCnt,
                            alias: agreementMaintainerObj.alias,
                            orderItem: this.orderItem,
                            $currentView: this,
                            agreementName: data.agreementName,
                            agreementId: data.agreementId,
                            agreementVerId: data.agreementVerId,
                            isBack: this.options.isBack
                        });
                        this.$(":input[name='agreementMaintainer']").attr('data-rule', 'required;');
                    }
                }.bind(this),
                clearHander: function (data) {
                    if (this.$baseTypeListPlace) {
                        this.$('.js-dup-sim-card-resource').combobox('disable');
                    }
                    this.$('.js-iccid').attr('disabled', 'disabled');
                    this.$("input[name='simSku']").combobox('disable');
                }.bind(this)
            });
        },



        initSimCardBaseTypeSource: function (baseTypeId) {
            SimCardMgrAction.qrySimCardBaseType({}, function (data) {
                this.baseTypeList = data.baseTypeList;
                //this.$('.js-dup-sim-card-resource').attr('data-rule', 'required;');
                this.$baseTypeListPlace = this.$('.js-dup-sim-card-resource').combobox({
                    dataTextField: 'baseTypeName',
                    dataValueField: 'baseTypeId',
                    dataSource: this.baseTypeList,
                    value: baseTypeId,
                });
                this.$baseTypeListPlace.on('combobox:change', function (e) {
                    let curValue = e.target.value;
                    //this.initSimSku();
                    var item = ArrayUtils.getItemByField(this.baseTypeList, curValue, 'baseTypeId');
                    if ((curValue && curValue == '2') || (item && item.autoSelectionFlag === 'Y')) {
                        this.$('.js-iccid').attr('disabled', 'disabled');
                        let custId = this.custOrder.CUST_ID;
                        let netType = null;
                        if (this.custOrder.CUST) {
                            custId = this.custOrder.CUST.CUST_ID;
                            netType = this.custOrder.CUST.NET_TYPE;
                        }
                        var param = {
                            accNbrId: this.orderItem.oldAccNbrId,
                            orgId: portal.appGlobal.get('orgId'),
                            custId: custId,
                            netType: netType,
                            baseTypeId: curValue
                        }
                        this.$('.js-iccid-panel').removeClass('hidden');
                        this.setEsimCardInfo(param);
                    }
                    else {
                        this.$('.js-iccid').removeAttr('disabled');
                        this.$(":input[name='newICCID']").val('').change();
                        this.$('.js-iccid-panel').addClass('hidden');
                    }
                }.bind(this));
                this.registerBaseTypeChange();
                this.$baseTypeListPlace.combobox('disable');
                this.$('.js-iccid').attr('disabled', 'disabled');
                if (this.chooseDevice) {
                    this.$baseTypeListPlace.combobox('enable');
                    this.$('.js-iccid').removeAttr('disabled');
                }
                this.baseTypeItem = ArrayUtils.getItemByField(this.baseTypeList, this.baseTypeId, 'baseTypeId');
                if (this.orderItem.ICCID && baseTypeId && (baseTypeId === 2 || (this.baseTypeItem && this.baseTypeItem.autoSelectionFlag === 'Y'))) {
                    this.$('.js-iccid-panel').removeClass('hidden');
                    this.$('.js-iccid').attr('disabled', 'disabled');
                    this.$(':input[name=\'newICCID\']').val(this.orderItem.ICCID);
                }
                //this.initSimSku();
            }.bind(this))
        },

        initIccidPanel: function () {
            var boAccess = {
                selector: ":input[name='newICCID']",
                boAccessName: "orderItem",
                boItemAccessName: "iccid",
                $currentView: this,
                inputType: InputTypeDef.TEXT,
                success: this.changeIccidSuccessHandler,
                fail: this.changeIccidFailHandler
            };

            if (boAccess && boAccess.$currentView) {
                Controller.getInstance().registerChange(boAccess.$currentView, boAccess);
            }
        },

        registerBaseTypeChange: function () {
            var boAccess = {
                selector: ":input[name='newBaseTypeName']",
                boAccessName: "orderItem",
                boItemAccessName: "simTypeId",
                $currentView: this,
                inputType: InputTypeDef.COMBOBOX,
            };
            if (boAccess && boAccess.$currentView) {
                Controller.getInstance().registerChange(boAccess.$currentView, boAccess);
            }
        },

        changeIccidFailHandler: function(data) {
            var errors = data.errors;
            if (errors && errors.length > 0) {
                this.$(":input[name='newICCID']").val("").attr("simCardId", "");
                /* 2480095
                fish.each(errors, function (error) {
                    fish.error(error.message);
                })
                 */
            }
        },

        changeIccidSuccessHandler: function (data) {
            var dirtyList = data.dirtyList;
            if (fish.isEmpty(dirtyList)) {
                return;
            }
        },

        // initSimSku: function (isDelivery) {
        //     if (undefined != isDelivery) {
        //         this.isDelivery = isDelivery;
        //     }
        //
        //     if (this.isDelivery && this.getBaseTypeId() == '1') {
        //         this.$('.div-simSku').removeClass("hidden");
        //         if (!this.$simSkuListCombo) {
        //             this.$simSkuListCombo = new SimSkuCombobox(this.$(".js-dup-sim-card-sku"), {
        //                 baseTypeId: this.getBaseTypeId(),
        //                 custOrder: this.custOrder,
        //                 orderItem: this.orderItem,
        //                 boAccess: {
        //                     selector: ":input[name='simSku']",
        //                     boAccessName: "orderItem",
        //                     boItemAccessName: "simSku",
        //                     $currentView: this,
        //                     inputType: InputTypeDef.COMBOBOX
        //                 }
        //             });
        //         }
        //     }
        //     else {
        //         this.$('.div-simSku').addClass("hidden");
        //         if (this.$simSkuListCombo) {
        //             this.$simSkuListCombo.clear();
        //         }
        //     }
        // },
        setNewAcct: function (acct) {
            if (acct) {
                this.orderItem.acct = acct;
                this.orderItem.ACCT_ID = acct.acctId;
                this.initHandSetComponent();
            }
        },
        onDeliveryChange: function (data) {
            if (data) {
                this.isDelivery = data.isDelivery;
                if (this.$handSetView && this.handSetInited) {
                    if (this.$(":input[name='handset']") && this.$(":input[name='handset']").val()
                        && this.$(":input[name='handset']").val().length > 0) {
                        BoUtils.fetchOrderItem(this.orderItem.ORDER_ITEM_ID, this, function (orderItem) {
                            this.$handSetView.deReserveGoods();
                            this.$handSetView.sendBOClearAgreement();
                            var param = {
                                agreementId: orderItem.AGREEMENT_ORDER[0].AGREEMENT_ID,
                                agreementVerId: orderItem.AGREEMENT_ORDER[0].AGREEMENT_VER_ID
                            }
                            this.$handSetView.reserveGoods(param);
                        }.bind(this))
                    }
                    if (this.$(":input[name='agreement']") && fish.isEmpty(this.$(":input[name='agreement']").val())) {
                        var param = {
                            subsPlanId: this.orderItem.SUBS_PLAN_ID || this.orderItem.SUBS_BASE_ORDER.SUBS_PLAN_ID,
                            custId: this.orderItem.CUST_ID,
                            orderItem: this.orderItem,
                            isBack: this.options.isBack
                        }
                        this.$handSetView.initializeDefaultAgreement(param);
                    }
                }
            }
            if (data
                && data.TEL_SALE_AFTER_DELIVERY_CHANGE_RESELECT_HANSET == "Y"
                && this.$handSetView
                && this.$(":input[name='handset']")
                && this.$(":input[name='handset']").val()
                && this.$(":input[name='handset']").val().length > 1) {
                // telsale场景才会有这个入参：afterDeliveryChangeReselectHandSet
                this.$handSetView.sendBOClearAgreement();
                fish.warn(I18N.ORDER_ENRTY_RESELECT_HANDSET);
            }
        },
        getBaseTypeId: function () {
            if (this.$(".js-dup-sim-card-resource") && this.$(".js-dup-sim-card-resource")[0] && this.$(".js-dup-sim-card-resource")[0].value) {
                return this.$(".js-dup-sim-card-resource")[0].value;
            }
        },
        setEsimCardInfo: function (qryParam) {
            qryParam.servTypes = [this.orderItem.SERV_TYPE];
            qryParam.offerIds = [this.orderItem.SUBS_PLAN_ID];
            qryParam.prodIds = [this.orderItem.OFFER_ID];
            SimCardMgrAction.qrySingleESimCard(qryParam, function (data) {
                this.$(":input[name='newICCID']")
                    .val(data.iccid)
                    .attr('simCardId', data.simCardId)
                    .change();
            }.bind(this), function (data) {
                fish.warn(fish.escape(data.Msg));
                this.$baseTypeListPlace.combobox('value', '1');
            }.bind(this));
        },
        /**
         * Next时校验
         *
         * @returns {{validateFlag: boolean}|*}
         */
        validateData: function() {
            var baseValidate = this.$form.isValid();
            if (!baseValidate) {
                return { validateFlag: false };
            }
            return { validateFlag: true };
        }
    });
});