# CustOrderBasicInfoView 组件

## 概述

CustOrderBasicInfoView 是一个标准化的客户订单基本信息展示组件，用于在订单预览页面显示客户和订单的基本信息，包括客户名称、客户类型、事件名称、订单属性、倒签日期和备注等信息。

## 安装和使用

### 基本使用

```javascript
this.requireView({
    url: 'crm/standardizedcomponents/pos/components/CustOrderBasicInfoView',
    selector: '.js-cust-order-basic-info',
    viewOption: {
        custOrder: this.custOrder,
        cust: this.cust,
        genderArray: this.genderArray,
        commonDataId: this.commonDataId
    },
    callback: function ($view) {
        this.custOrderBasicInfoView = $view;
        this.setupEventListeners($view);
    }.bind(this)
});
```

### 高级配置

```javascript
// 设置事件监听器
setupEventListeners: function($componentView) {
    // 监听数据加载完成事件
    $componentView.on('dataLoaded', function(eventData) {
        console.log('客户订单基本信息加载完成:', eventData);
        // 处理数据加载完成后的逻辑
    });

    // 监听错误事件
    $componentView.on('error', function(eventData) {
        console.error('客户订单基本信息组件错误:', eventData);
        fish.error(eventData.data.message || '客户订单基本信息加载失败');
    });
}
```

### 异步加载处理

由于 `requireView` 是异步的，如果您的页面有其他初始化方法依赖组件的 DOM 元素，请使用异步组件管理器：

```javascript
// 在 afterRender 中
afterRender: function() {
    // 初始化异步组件管理器
    this.initAsyncComponentManager();

    // 注册异步组件
    this.registerAsyncComponent('custOrderBasicInfo', this.initCustOrderBasicInfo.bind(this));
},

// 事件监听器中标记组件加载完成
setupCustOrderBasicInfoEventListeners: function($componentView) {
    $componentView.on('dataLoaded', function(eventData) {
        // 标记组件加载完成
        this.markAsyncComponentLoaded('custOrderBasicInfo');
    }.bind(this));

    $componentView.on('error', function(eventData) {
        // 即使出错也要标记完成
        this.markAsyncComponentLoaded('custOrderBasicInfo');
    }.bind(this));
},

// 所有异步组件加载完成后执行
continueAfterComponentsLoaded: function() {
    // 现在可以安全地初始化依赖DOM的功能
    this.initOtherMethods();
}
```

详细信息请参考：[异步组件管理器使用指南](../../AsyncComponentManager_Guide.md)

## API 文档

### Props

| 属性名       | 类型   | 必填 | 默认值 | 描述                             |
| ------------ | ------ | ---- | ------ | -------------------------------- |
| custOrder    | Object | 是   | -      | 客户订单对象，包含订单的基本信息 |
| cust         | Object | 是   | -      | 客户信息对象，包含客户的基本信息 |
| genderArray  | Array  | 否   | []     | 性别数组，用于性别相关的显示     |
| commonDataId | String | 否   | -      | 通用数据 ID，用于数据关联        |

#### custOrder 对象结构

```javascript
{
    CUST_ORDER_ID: "订单ID",
    SUBS_EVENT_NAME: "事件名称",
    COMMENTS: "备注",
    BACKDATE: "倒签日期",
    CUST_ORDER_ATTR: [
        {
            ATTR_ID: "属性ID",
            VALUE: "属性值"
        }
    ],
    SUBS_EVENT_ID: "事件ID"
}
```

#### cust 对象结构

```javascript
{
    CUST_NAME: "客户名称",
    CUST_TYPE: "客户类型", // 1: 个人, 2: 企业
    CUST_ID: "客户ID"
}
```

### Events

| 事件名     | 参数      | 描述               |
| ---------- | --------- | ------------------ |
| dataLoaded | eventData | 数据加载完成时触发 |
| error      | eventData | 发生错误时触发     |

#### 事件数据结构

```javascript
// dataLoaded 事件
{
    type: 'dataLoaded',
    source: 'CustOrderBasicInfoView',
    timestamp: 1640995200000,
    data: {
        formData: {
            custName: "客户名称",
            custTypeName: "客户类型名称",
            eventName: "事件名称",
            comments: "备注",
            backdate: "倒签日期"
        }
    }
}

// error 事件
{
    type: 'error',
    source: 'CustOrderBasicInfoView',
    timestamp: 1640995200000,
    data: {
        message: "错误信息",
        error: Error对象
    }
}
```

### Methods

| 方法名      | 参数 | 返回值 | 描述             |
| ----------- | ---- | ------ | ---------------- |
| getFormData | -    | Object | 获取当前表单数据 |
| refreshData | -    | -      | 刷新组件数据     |

## 示例

### 基础示例

```javascript
// 在订单预览页面中使用
initCustOrderBasicInfo: function() {
    this.requireView({
        url: 'crm/standardizedcomponents/pos/components/CustOrderBasicInfoView',
        selector: '.js-cust-order-basic-info',
        viewOption: {
            custOrder: this.custOrder,
            cust: this.cust,
            genderArray: this.genderArray,
            commonDataId: this.commonDataId
        },
        callback: function($view) {
            this.custOrderBasicInfoView = $view;
        }.bind(this)
    });
}
```

### 高级示例

```javascript
// 带事件监听的完整示例
initCustOrderBasicInfo: function() {
    this.requireView({
        url: 'crm/standardizedcomponents/pos/components/CustOrderBasicInfoView',
        selector: '.js-cust-order-basic-info',
        viewOption: {
            custOrder: this.custOrder,
            cust: this.cust,
            genderArray: this.genderArray,
            commonDataId: this.commonDataId
        },
        callback: function($view) {
            this.custOrderBasicInfoView = $view;

            // 设置事件监听
            $view.on('dataLoaded', function(eventData) {
                console.log('基本信息加载完成');
                // 可以在这里执行后续操作
                this.onBasicInfoLoaded(eventData.data.formData);
            }.bind(this));

            $view.on('error', function(eventData) {
                console.error('基本信息加载失败:', eventData.data.message);
                this.handleBasicInfoError(eventData.data.error);
            }.bind(this));
        }.bind(this)
    });
},

// 处理基本信息加载完成
onBasicInfoLoaded: function(formData) {
    // 执行后续逻辑
},

// 处理基本信息加载错误
handleBasicInfoError: function(error) {
    // 错误处理逻辑
}
```

## 更新日志

### v1.0 (2025-01-01)

-   初始版本
-   支持客户订单基本信息展示
-   支持客户订单属性动态渲染
-   支持倒签日期显示
-   实现标准化事件通信机制
-   提供完整的 API 文档和使用示例

## 迁移指南

### 从原有实现迁移

如果您之前在 CustOrderPreviewView 中直接处理客户基本信息，可以按照以下步骤迁移：

1. **移除原有代码**：删除 initOrderDetail 方法中的客户基本信息处理逻辑
2. **更新模板**：将原有的表单 HTML 替换为组件容器
3. **添加组件调用**：使用 requireView 加载新组件
4. **设置事件监听**：根据需要添加事件监听器

### 破坏性变更

-   原有的 `this.$custOrderDetailForm` 不再可用
-   原有的 `initCustOrderAttrForm` 方法已被组件内部处理

### 兼容性说明

-   完全向后兼容，不影响现有功能
-   支持所有原有的数据格式和显示逻辑
-   保持原有的样式和布局
