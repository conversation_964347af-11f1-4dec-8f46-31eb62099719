/* eslint-disable */
/**
 * @fileoverview 商品优惠券组件 - 标准化改造版本
 * @description 用于显示和选择客户可用的优惠券，支持优惠券搜索、选择和详情查看
 * <AUTHOR>
 * @since 2018/12/21
 * @version 2.0.0 - 标准化改造版本
 *
 * @component GoodsCouponView
 * @description 商品优惠券选择组件
 *
 * @props {Object} custOrder - 客户订单对象，包含客户信息和订单详情
 * @props {String} [pageUuid] - 页面唯一标识符
 * @props {Object} [componentConfig] - 组件配置对象
 * @props {String} [componentConfig.theme='default'] - 主题配置
 * @props {String} [componentConfig.locale='zh-CN'] - 语言配置
 * @props {Boolean} [componentConfig.debug=false] - 调试模式
 *
 * @events
 * @event couponSelected - 优惠券选中事件
 * @param {Object} data - 事件数据
 * @param {String} data.type - 事件类型
 * @param {String} data.source - 事件源组件
 * @param {Number} data.timestamp - 时间戳
 * @param {Object} data.data - 业务数据
 * @param {String} data.data.couponId - 选中的优惠券ID
 * @param {Object} data.data.couponInfo - 优惠券详细信息
 * @param {Object} data.meta - 元数据
 *
 * @event couponDeselected - 优惠券取消选择事件
 * @param {Object} data - 事件数据
 * @param {String} data.type - 事件类型
 * @param {String} data.source - 事件源组件
 * @param {Number} data.timestamp - 时间戳
 * @param {Object} data.data - 业务数据
 * @param {String} data.data.couponId - 取消选择的优惠券ID
 * @param {Object} data.meta - 元数据
 *
 * @event moreDetailToggle - 详情展开/收起事件
 * @param {Object} data - 事件数据
 * @param {String} data.type - 事件类型
 * @param {String} data.source - 事件源组件
 * @param {Number} data.timestamp - 时间戳
 * @param {Object} data.data - 业务数据
 * @param {String} data.data.couponId - 操作的优惠券ID
 * @param {Boolean} data.data.isExpanded - 是否展开状态
 * @param {Element} data.data.target - 触发元素
 * @param {Object} data.meta - 元数据
 *
 * @event error - 错误事件
 * @param {Object} data - 事件数据
 * @param {String} data.type - 事件类型
 * @param {String} data.source - 事件源组件
 * @param {Number} data.timestamp - 时间戳
 * @param {Object} data.data - 业务数据
 * @param {String} data.data.error - 错误信息
 * @param {String} data.data.context - 错误上下文
 * @param {Object} data.meta - 元数据
 *
 * @example
 * // 使用示例
 * this.requireView({
 *     url: 'crm/standardizedcomponents/pos/components/GoodsCouponView',
 *     viewOption: {
 *         custOrder: this.custOrder,
 *         pageUuid: this.options.pageUuid,
 *         componentConfig: {
 *             theme: 'default',
 *             locale: 'zh-CN',
 *             debug: false
 *         }
 *     }
 * });
 */
define([
    'hbs!crm/modules/pos/flowpage/common/templates/GoodsCouponTpl.hbs',
    'i18n!crm/modules/pos/workspace/i18n/OrderEntry',
    'crm/modules/fbf/bfm/utils/Controller',
    'crm/modules/fbf/bfm/models/DataInput',
    'crm/modules/fbf/bfm/models/DataInputs',
    'crm/modules/fbf/bfm/constant/OperationTypeDef',
    'crm/modules/pos/GoodsSell/actions/GoodsSellAction',
    'crm/modules/common/util/CurrencyUtils',
    'css!crm/modules/pos/GoodsSell/css/GoodsSell.css'
], function (GoodsCouponTpl, I18N, Controller, DataInput, DataInputs, OperationTypeDef, GoodsSellAction, CurrencyUtils) {
    return portal.BaseView.extend({
        template: GoodsCouponTpl,
        serialize: I18N,
        events: {
            'keydown .js-search-coupon-code-info': 'onSearchCouponKeydown',
            'click .coupon-item': 'onCouponsCheckClick',
            'click .js-more-inform': 'onMoreDetail',
            'click .js-search-coupon-code': 'onSearchCouponByCode'
        },

        /**
         * 组件初始化
         * @param {Object} options - 初始化参数
         * @param {Object} options.custOrder - 客户订单对象
         * @param {String} [options.pageUuid] - 页面唯一标识符
         * @param {Object} [options.componentConfig] - 组件配置对象
         */
        initialize: function (options) {
            // 参数验证
            if (!options) {
                throw new Error('GoodsCouponView: options is required');
            }
            if (!options.custOrder) {
                throw new Error('GoodsCouponView: custOrder is required');
            }

            // 设置默认值
            this.options = fish.extend(
                {
                    componentConfig: {
                        theme: 'default',
                        locale: 'zh-CN',
                        debug: false
                    }
                },
                options
            );

            // 初始化组件状态
            this.goodsList = [];
            this.couponList = [];
            this.version = '2.0.0';
        },

        afterRender: function () {
            this.commonDataId = Controller.getInstance().getCommonDataId(this);
            this.getCouponList();
        },

        /**
         * 标准化事件触发方法
         * @param {String} eventName - 事件名称
         * @param {Object} businessData - 业务数据
         */
        triggerStandardEvent: function (eventName, businessData) {
            var eventData = {
                type: eventName,
                source: this.constructor.name || 'GoodsCouponView',
                timestamp: Date.now(),
                data: businessData || {},
                meta: {
                    version: '1.0',
                    componentVersion: this.version || '2.0.0'
                }
            };

            this.trigger(eventName, eventData);
        },

        /**
         * 错误处理方法
         * @param {Error} error - 错误对象
         * @param {String} context - 错误上下文
         */
        handleError: function (error, context) {
            // 记录错误信息
            console.error('GoodsCouponView error:', error, context);

            // 触发错误事件
            this.triggerStandardEvent('error', {
                error: error.message || error,
                context: context,
                timestamp: Date.now()
            });

            // 用户友好的错误提示
            if (this.options.componentConfig && this.options.componentConfig.debug !== false) {
                fish.error('操作失败，请稍后重试');
            }
        },

        onSearchCouponKeydown: function (e) {
            if (e.keyCode == 13) {
                // 回车之后自动失去焦点，防止触发多次回车事件
                this.$(e.target).blur();
                this.onSearchCouponByCode();
            }
        },

        onSearchCouponByCode: function () {
            var searchValue = $.trim(this.$('.js-search-coupon-code-info').val());
            if (!searchValue) {
                return;
            }
            var claimCouponReq = {
                pinCode: searchValue,
                custId: this.options.custOrder.CUST.CUST_ID,
                channelId: 1
            };
            GoodsSellAction.claimCouponInst(
                claimCouponReq,
                function (data) {
                    var coupon = data.couponResp;
                    this.coupList = this.coupList || [];
                    this.coupList.push(coupon);

                    // [2427379]马来UM_CRM_COC_Coupon使用-资料采集界面缺少根据Code搜索Coupon功能
                    // sic 对同一员工为同一客户领取优惠卷没有做校验，所以需要手动去除领取相同code的优惠卷
                    var ids = this.coupList.map(value => value.couponId);
                    var idsSet = new Set(ids);

                    if (idsSet.has(coupon.couponId) && idsSet.size != ids.length) {
                        this.$("li[data-id='coupon.couponId']").click();
                        this.coupList.pop(coupon);
                    }

                    fish.forEach(
                        this.coupList,
                        function (item, index) {
                            item.stateName = this.getStateNameAndClass(item.state);
                            var beginDate = item.effDate.substring(0, 10);
                            var endDate = item.expDate.substring(0, 10);
                            item.couponsTime = beginDate + ' ' + endDate;
                            /* 获取优惠券信息有限从查询接口最外层获取，获取不到的时候从couponSpec中获取*/
                            if (item.discountAmount == '') {
                                item.discountAmount = item.couponSpec.discountAmount;
                            }
                            if (item.comments == '') {
                                item.comments = item.couponSpec.comments;
                            }
                            if (item.limitAmount == '') {
                                item.limitAmount = item.couponSpec.limitAmount;
                            }
                            if (item.discountRate == '') {
                                item.discountRate = item.couponSpec.discountRate;
                            }

                            // 优惠券详细描述框样式left计算(212为固定优惠券宽度加间隔12)
                            var left = index * (212 + 12);
                            item.left = left + 'px';
                            this.qryDiscountType(item);
                        }.bind(this)
                    );
                    this.initCouponView();
                    this.$('.coupon-child li:last .coupon-item .coupon-item-wrap').click();
                }.bind(this)
            );
        },

        getCouponList: function () {
            /**
             3 查询客户已经领取的优惠券
             */
            try {
                if (this.options.custOrder.CUST.CUST_ID) {
                    var queryCouponsReq = {
                        state: 'A',
                        custId: this.options.custOrder.CUST.CUST_ID
                    };
                    GoodsSellAction.qryCoupons(
                        queryCouponsReq,
                        function (data) {
                            try {
                                this.coupList = data.couponRespList || [];
                                fish.forEach(
                                    this.coupList,
                                    function (item, index) {
                                        item.stateName = this.getStateNameAndClass(item.state);
                                        var beginDate = item.effDate.substring(0, 10);
                                        var endDate = item.expDate.substring(0, 10);
                                        item.couponsTime = beginDate + ' ' + endDate;
                                        /* 获取优惠券信息有限从查询接口最外层获取，获取不到的时候从couponSpec中获取*/
                                        if (item.discountAmount == '') {
                                            item.discountAmount = item.couponSpec.discountAmount;
                                        }
                                        if (item.comments == '') {
                                            item.comments = item.couponSpec.comments;
                                        }
                                        if (item.limitAmount == '') {
                                            item.limitAmount = item.couponSpec.limitAmount;
                                        }
                                        if (item.discountRate == '') {
                                            item.discountRate = item.couponSpec.discountRate;
                                        }

                                        // 优惠券详细描述框样式left计算(212为固定优惠券宽度加间隔12)
                                        var left = index * (212 + 12);
                                        item.left = left + 'px';
                                        this.qryDiscountType(item);
                                    }.bind(this)
                                );
                                this.initCouponView();
                            } catch (e) {
                                this.handleError(e, 'getCouponList_success');
                            }
                        }.bind(this),
                        function (error) {
                            this.handleError(error, 'getCouponList_api');
                        }.bind(this)
                    );
                }
            } catch (e) {
                this.handleError(e, 'getCouponList');
            }
        },

        onCouponsCheckClick: function (e) {
            var target = e.target.offsetParent;
            if (!$(target).hasClass('used') && $(target).hasClass('coupon-item')) {
                var dataInput = new DataInput({
                    boAccessName: 'custOrder',
                    bo: true
                });
                var goodsSaleOrderDetailList = [];
                var agreementOrderList = [];
                var dpOfferOrderList = [];
                this.mainOfferList = [];
                Controller.getInstance().fetchData(
                    this.commonDataId,
                    dataInput,
                    this,
                    function (result) {
                        if (!fish.isEmpty(result.dirtyList)) {
                            this.custOrder = result.dirtyList[0].value;
                            goodsSaleOrderDetailList =
                                this.custOrder.GOODS_SALE_ORDER && this.custOrder.GOODS_SALE_ORDER.length > 0
                                    ? this.custOrder.GOODS_SALE_ORDER[0].GOODS_SALE_ORDER_DETAIL
                                    : [];
                            agreementOrderList = this.custOrder.ORDER_ITEM[0].AGREEMENT_ORDER;
                            for (var i = 0; i < this.custOrder.ORDER_ITEM.length; i++) {
                                dpOfferOrderList = dpOfferOrderList.concat(this.custOrder.ORDER_ITEM[i].DP_OFFER_ORDER);
                                var subsPlanId = this.custOrder.ORDER_ITEM[i].SUBS_PLAN_ID;
                                if (this.custOrder.ORDER_ITEM[i].SUBS_BASE_ORDER && this.custOrder.ORDER_ITEM[i].SUBS_BASE_ORDER.SUBS_PLAN_ID) {
                                    subsPlanId = this.custOrder.ORDER_ITEM[i].SUBS_BASE_ORDER.SUBS_PLAN_ID;
                                }
                                if (subsPlanId) {
                                    var mainOffer = {
                                        mainOfferId: subsPlanId
                                    };
                                    this.mainOfferList.push(mainOffer);
                                }
                            }
                        }
                        this.goodsList = [];
                        this.agreementList = [];
                        this.dpOfferList = [];
                        if (!fish.isEmpty(dpOfferOrderList)) {
                            fish.forEach(
                                dpOfferOrderList,
                                function (dpOfferOrder) {
                                    if (dpOfferOrder.OPERATION_TYPE == 'A') {
                                        var dpOffer = {
                                            dpOfferId: dpOfferOrder.OFFER_ID
                                        };
                                        this.dpOfferList.push(dpOffer);
                                    }
                                }.bind(this)
                            );
                        }
                        if (!fish.isEmpty(goodsSaleOrderDetailList)) {
                            fish.forEach(
                                goodsSaleOrderDetailList,
                                function (goodsOrderDetail) {
                                    var goods = {
                                        offerId: goodsOrderDetail.OFFER_ID,
                                        modelId: goodsOrderDetail.SKU_ID,
                                        CNT: goodsOrderDetail.SALE_QTY
                                    };
                                    this.goodsList.push(goods);
                                }.bind(this)
                            );
                        }
                        if (!fish.isEmpty(agreementOrderList)) {
                            fish.forEach(
                                agreementOrderList,
                                function (agreementOrder) {
                                    var agreement = {
                                        agreementId: agreementOrder.AGREEMENT_ID,
                                        agreementTagId: agreementOrder.AGREEMENT_TAG_ID
                                    };
                                    this.agreementList.push(agreement);
                                }.bind(this)
                            );
                        }
                        if ((this.goodsList == null || this.goodsList.length == 0) && (this.dpOfferList == null || this.dpOfferList.length == 0)) {
                            fish.info('Please select the goods or additional offer first');
                            return;
                        }
                        this.$('.coupon-item').removeClass('used');
                        $(target).toggleClass('used');
                        this.couponId = $(target).get(0).dataset.id;

                        // 标准化改造：触发优惠券选中事件
                        var selectedCoupon = fish.findWhere(this.coupList, { couponId: this.couponId });
                        this.triggerStandardEvent('couponSelected', {
                            couponId: this.couponId,
                            couponInfo: selectedCoupon
                        });

                        var qryParam = {
                            goodsSaleList: this.goodsList,
                            couponId: this.couponId,
                            agreementList: this.agreementList,
                            dpOfferList: this.dpOfferList,
                            contactChannelId: 1,
                            mainOfferList: this.mainOfferList
                        };
                        GoodsSellAction.judgeCouponCanUse(
                            qryParam,
                            function () {
                                var dataInputs = new DataInputs();
                                var dataInput = new DataInput();
                                dataInput.set('boAccessName', 'custOrder');
                                dataInput.set('boItemAccessName', 'couponId');
                                dataInput.set('operationType', OperationTypeDef.CHANGE);
                                dataInput.set('value', this.couponId);
                                dataInputs.add(dataInput);
                                Controller.getInstance().sendChanges(
                                    null,
                                    dataInputs,
                                    this,
                                    null,
                                    null,
                                    function (data) {
                                        fish.each(data.errors, function (error) {
                                            fish.error(error.message);
                                        });
                                    }.bind(this)
                                );
                            }.bind(this),
                            function (result) {
                                this.couponId = null;
                                this.$('.coupon-item').removeClass('used');

                                // 标准化改造：触发优惠券取消选择事件
                                this.triggerStandardEvent('couponDeselected', {
                                    couponId: this.couponId
                                });

                                if (result && result.Msg) {
                                    fish.info(result.Msg);
                                }
                            }.bind(this)
                        );
                    }.bind(this)
                );
            } else if ($(target).hasClass('coupon-item')) {
                this.$('.coupon-item').removeClass('used');
                var previousCouponId = this.couponId;
                this.couponId = null;

                // 标准化改造：触发优惠券取消选择事件
                this.triggerStandardEvent('couponDeselected', {
                    couponId: previousCouponId
                });

                var dataInputs = new DataInputs();
                var dataInput = new DataInput();
                dataInput.set('boAccessName', 'custOrder');
                dataInput.set('boItemAccessName', 'couponId');
                dataInput.set('operationType', OperationTypeDef.CHANGE);
                dataInput.set('value', '');
                dataInputs.add(dataInput);
                Controller.getInstance().sendChanges(
                    null,
                    dataInputs,
                    this,
                    null,
                    null,
                    function (data) {
                        fish.each(data.errors, function (error) {
                            fish.error(error.message);
                        });
                    }.bind(this)
                );
            }
        },

        /**
         * 标准化改造：优惠券详情展开/收起处理
         * 改为通过事件通信，不直接操作DOM
         */
        onMoreDetail: function (e) {
            var couponId = $(e.target).closest('.coupon-item').data('id');
            var isExpanded = $(e.target).hasClass('icon-chevron-up');

            // 标准化改造：触发详情展开/收起事件，由调用方处理DOM操作
            this.triggerStandardEvent('moreDetailToggle', {
                couponId: couponId,
                isExpanded: !isExpanded,
                target: e.target
            });

            // 保留原有的DOM操作逻辑作为默认行为（如果调用方没有监听事件）
            if (isExpanded) {
                $(e.target).removeClass('icon-chevron-up').addClass('icon-chevron-down');
                $(e.target).parents('.coupon-item').siblings('.more-inform').hide();
            } else {
                $(e.target).removeClass('icon-chevron-down').addClass('icon-chevron-up');
                $(e.target).parents('.coupon-item').siblings('.more-inform').show();
            }
        },

        getGoodsList: function () {
            var dataInput = new DataInput();
            dataInput.set('boAccessName', 'goodsSaleOrderDetail');
            dataInput.set('bo', true);
            dataInput.set('boItemAccessName', 'goodsSaleOrderDetailId');
            var goodsSaleOrderDetailList = [];
            Controller.getInstance().fetchData(
                null,
                dataInput,
                this,
                function (result) {
                    if (!fish.isEmpty(result.dirtyList)) {
                        var dirtyObj = fish.findWhere(result.dirtyList, { boAccessName: 'goodsSaleOrderDetail' });
                        if (!fish.isArray(dirtyObj.value) && dirtyObj.value) {
                            goodsSaleOrderDetailList.push(dirtyObj.value);
                        } else {
                            goodsSaleOrderDetailList = dirtyObj.value;
                        }
                    }
                    if (!fish.isEmpty(goodsSaleOrderDetailList)) {
                        fish.forEach(
                            goodsSaleOrderDetailList,
                            function (goodsOrderDetail) {
                                var goods = {
                                    offerId: goodsOrderDetail.OFFER_ID,
                                    modelId: goodsOrderDetail.MODEL_ID,
                                    CNT: goodsOrderDetail.SALE_QTY
                                };
                                this.goodsList.push(goods);
                            }.bind(this)
                        );
                    } else {
                        this.goodsList = [];
                    }
                }.bind(this)
            );
        },

        initCouponView: function () {
            var coupData = {
                couponList: this.coupList
            };
            if (!fish.isEmpty(this.coupList)) {
                coupData.hasCoupon = true;
            } else {
                coupData.hasCoupon = false;
            }
            this.$el.html(this.template(fish.extend(I18N, coupData)));
            // this.$('.oe-coupon-box').empty().append(GoodsCouponTpl(coupData));
            if (this.options.custOrder.COUPON_ID) {
                this.$("[data-id='" + this.options.custOrder.COUPON_ID + "']").toggleClass('used');
            }
        },

        /* 获取状态 state*/
        getStateNameAndClass: function (state) {
            if (state == 'A') {
                return 'use';
            } else if (state == 'C') {
                return 'used';
            } else if (state == 'B') {
                return 'expired';
            }
        },

        /* 根据优惠券的不同类型，展示不同的内容*/
        qryDiscountType: function (item) {
            var couponSpec = item.couponSpec;
            var discountType = couponSpec.discountType;
            if (discountType == 'Full Minus') {
                item.currencySymbol = CurrencyUtils.getCurrency().currencySymbol;
                item.discountAmountShow = CurrencyUtils.intToCurrency(item.discountAmount, null, ',');
                item.limitAmountShow = item.discountAmountShow + ' off over ' + CurrencyUtils.intToCurrency(item.limitAmount, null, ',');
            } else if (discountType == 'Discount') {
                var discountRate = Number(item.discountRate);
                var rate = (10000 - discountRate) / 100 + '%';
                item.discountAmountShow = rate + ' off ';
                item.limitAmountShow = rate + ' off over ' + CurrencyUtils.intToCurrency(item.limitAmount, null, ',');
                item.currencySymbol = '';
            } else if (discountType == 'Per Minus') {
                item.currencySymbol = CurrencyUtils.getCurrency().currencySymbol;
                item.discountAmountShow = CurrencyUtils.intToCurrency(item.discountAmount, null, ',');
                item.limitAmountShow = item.discountAmountShow + ' off for every ' + CurrencyUtils.intToCurrency(item.limitAmount, null, ',');
            }
        },

        /**
         * 组件大小调整方法
         * @param {Number} delta - 大小变化量
         */
        resize: function (delta) {
            // 预留方法，用于响应容器大小变化
            if (this.options.componentConfig && this.options.componentConfig.debug) {
                console.log('GoodsCouponView resize:', delta);
            }
        }
    });
});
