/* eslint-disable no-shadow */
/* eslint-disable no-param-reassign */
/* eslint-disable no-redeclare */
/* eslint-disable newline-per-chained-call */
/* eslint-disable no-extra-bind */
/* 忽视的目的：保持原有的业务逻辑，不能随便修改 */

/**
 * @component SelectNumberPortInRequireView
 * @description 选择号码端口转入需求视图组件
 *
 * @param {Object} options - 组件初始化参数
 * @param {String} options.commonDataId - 公共数据ID
 * @param {Object} options.orderItem - 订单项数据
 * @param {Object} options.custOrder - 客户订单数据
 * @param {String} options.subsEventId - 订阅事件ID
 * @param {String} options.subsPlanUuid - 订阅计划UUID
 * @param {Boolean} options.isPrimary - 是否主号码
 * @param {Boolean} options.showPreAccNbr - 是否显示预选号码
 * @param {String} options.pageUuid - 页面UUID
 * @param {Boolean} options.acctDisabled - 账户是否禁用
 * @param {Boolean} options.isLastOrderItem - 是否最后一个订单项
 */

define([
    'hbs!crm/standardizedcomponents/pos/flowpage/business/newconnection/templates/SelectNumberPortInTpl.hbs',
    'i18n!crm/modules/pos/workspace/i18n/OrderEntry',
    'i18n!crm/modules/pos/flowpage/custorderInfo/i18n/CustOrderInfo',
    'i18n!crm/modules/pos/flowpage/common/i18n/CustomerCare',
    'crm/modules/offer/constants/VasConstants',
    'crm/modules/common/util/PortalConfigUtil',
    'crm/modules/order/orderentry/actions/OrderEntryAction',
    'crm/modules/resource/card/actions/SimCardMgrAction',
    'crm/modules/customer/actions/SubsAction',
    'crm/modules/common/actions/CommonAction',
    'crm/modules/common/actions/ConfigItemAction',
    'crm/modules/inventory/goods/actions/GoodsSalesAction',
    'crm/modules/resource/actions/AccNbrAction',
    'crm/modules/common/models/TrackPointLogModel',
    'crm/modules/common/util/ArrayUtils',
    'crm/modules/fbf/bfm/utils/Controller',
    'crm/modules/fbf/bfm/models/DataInput',
    'crm/modules/pos/flowpage/business/component/constants/InputTypeDef',
    'crm/modules/fbf/bfm/constant/OperationTypeDef',
    'crm/modules/common/constant/ServTypeDef',
    'crm/modules/pos/common/constant/IntroDef',
    'crm/modules/pos/constants/AgreementDef',
    'crm/modules/pos/common/utils/BoUtils',
    'crm/modules/pos/common/utils/AgreementUtils',
    'crm/modules/pos/flowpage/business/component/actions/NpDbAction',
    'crm/standardizedcomponents/views/SelectNumberPopView',
    'crm/modules/pos/components/NumberAgreementComponent'
], function (
    SelectNumberPortInTpl,
    OrderEntryI18N,
    CustOrderInfoI18N,
    CustomerCareI18N,
    VasConstants,
    PortalConfigUtil,
    OrderEntryAction,
    SimCardMgrAction,
    SubsAction,
    CommAction,
    ConfigItemAction,
    GoodsSalesAction,
    AccNbrAction,
    TrackPointLogModel,
    ArrayUtils,
    Controller,
    DataInput,
    InputTypeDef,
    OperationTypeDef,
    ServTypeDef,
    IntroDef,
    AgreementDef,
    BoUtils,
    AgreementUtils,
    PostNpDbAction,
    SelectNumberPopView,
    NumberAgreementComponent
) {
    return portal.BaseView.extend({
        template: SelectNumberPortInTpl,
        serialize: fish.extend({}, OrderEntryI18N, CustOrderInfoI18N, CustomerCareI18N),
        events: {
            'click .js-clear-contract-phone': 'onClearContractPhone',
            'keydown .js-goods-sn': 'onMatchGoodsSN',
            'click .js-select-goods': 'onSelectGoodsClick',
            "change :input[name='iccid']": 'onIccidChange',
            "change :input[name='accNbr']": 'onAccNbrChange',
            'click .js-check-portIn': 'onClickPortIn'
        },
        initialize: function (options) {
            // MODIFIED: view 接收的数据，用于渲染 select number 组件
            this.options = options || {};
            if (options.commonDataId) {
                this.commonDataId = options.commonDataId;
            }

            this.portinflag = false;
            this.orderItem = options.orderItem;
            this.custOrder = options.custOrder;
            this.acct = this.orderItem.ACCT;
            this.subsBaseOrder = this.orderItem.SUBS_BASE_ORDER;
            this.subsBriefDetail = options.subsBriefDetail;
            this.subsEventId = options.subsEventId ? options.subsEventId : options.orderItem.SUBS_EVENT_ID;
            this.subsPlanUuid = options.subsPlanUuid;
            this.bundleOrderItem = options.bundleOrderItem;
            this.isPrimary = options.isPrimary;
            this.availableOfferTypeArr = [
                VasConstants.OFFER_TYPE_DEPENDENT_PRODUCT,
                VasConstants.OFFER_TYPE_PRICE_PLAN,
                VasConstants.OFFER_TYPE_DEFAULT_PRICE_PLAN
            ];
            this.showPreAccNbr = options.showPreAccNbr;
            this.portInChangeEvent = 'portInChangeEvent';
            this.vasInfoList = null;
            this.pageUuid = options.pageUuid;
            this.acctDisabled = options.acctDisabled;
            // 加个标识，购物车加入多个offer时，会初始化多次，但提示信息只要弹一次 task:2580835
            if (options.isLastOrderItem === true) {
                this.isLastOrderItem = options.isLastOrderItem;
            } else if (options.isLastOrderItem === false) {
                this.isLastOrderItem = options.isLastOrderItem;
            } else {
                this.isLastOrderItem = true;
            }

            // add by tang.shiping 用于处理vas与device的offerRelation
            this.allGoodsSaleOrderDetailList = null;
            this.allDpOfferOrderList = null;
            // end

            this.$vasTreeView = null;
            this.$deviceTreeView = null;
            this.$defaultLanguageCombobox = null;
            this.$paymentAccountComponent = null;
            this.$accountSelectorComponent = null;
            // this.$accNbrComponent = null;
            this.$selectNumberPop = null;
            this.$userSelectPopedit = null;

            this.$indepProdOrderAttrComponent = null;
            this.indepProdOrderAttrList = null;
            this.$orderItemAttrComponent = null;
            this.$creditLimitComponent = null;

            // MODIFIED: 基本信息区域
            this.$orderEntryPlanInfoView = null;
            this.$newOrderEntryPlanInfoView = null;

            this.userHideFlag = null;
            this.currentVirtualSimCard = null;
            this.configValue = PortalConfigUtil.configValue('BSS_ALL_2024Q4');
        },

        afterRender: function () {
            this.$('.js-portIn').hide();
            this.checkCanPortIn();
            // 获取屏蔽组件的配置项信息
            this.qryHideConfig();

            this.initDisplayDom();

            this.prepaidCreateAcctFlag = ConfigItemAction.qryParamValue('ACCT', 'COMMON', 'PREPAID_CREATE_ACCT_FLAG');
            this.initAccNbrComponent();

            this.initMnpBusiParam();
            this.querySimCardBySingle(this.orderItem.ICCID, this.initSimCardBaseTypeSource.bind(this));

            // SA沟通先屏蔽Div，此方案后续要变更，现页面上显示后，无法操作，影响功能，
            this.$('.js-goods-sn-div').hide();
            this.$('.js-contract-phone-div').hide();
            this.isStockManage = ConfigItemAction.qryParamValue('INVENTORY', 'PUBLIC_RES', 'IS_STOCK_MANAGEMENT');
            if (this.isStockManage == 'N') {
                this.$('.js-goods-sn-div').hide();
                this.$('.js-model-code-div').hide();
            }

            this.listenTo(
                this.$orderItemAttrComponent,
                'portInChangeEvent',
                function (able) {
                    this.$orderItemAttrComponent.setReserveDateEditable(able);
                }.bind(this)
            );

            this.initDisplayIccid();

            this.initPortIn();
        },

        qryHideConfig: function () {
            this.userHideFlag = ConfigItemAction.qryParamValue('CUSTOMER_CARE', 'CC_PUBLIC', 'USER_HIDE_FLAG');
        },

        initDisplayDom: function () {
            this.$('.selectedService').hide();
            this.$('.js-port-in-info').hide();

            this.$('.js-contract-phone').prop('readonly', true);
        },

        // 检测是否展示 portIn 按钮
        checkCanPortIn: function () {
            var param = {
                subsPlanId: this.orderItem.SUBS_PLAN_ID,
                bundleSubsPlanId: this.orderItem.BUNDLE_SUBS_PLAN_ID
            };
            param.userId = portal.appGlobal.get('userId');
            OrderEntryAction.checkCanPortIn(
                param,
                function (data) {
                    if (data && data.checkResult == 'Y') {
                        this.$('.js-portIn').show();
                        this.portinflag = true;
                    }
                }.bind(this)
            );
        },

        // 初始化 iccid 输入框
        initDisplayIccid: function () {
            if (this.orderItem && this.orderItem.ACC_NBR_ID) {
                OrderEntryAction.qryAccNbrDetailSic(
                    { accNbrId: this.orderItem.ACC_NBR_ID },
                    function (data) {
                        if (data.z_d_r) {
                            var accnbr = data.z_d_r[0];
                            // 选择绑定卡时,resource_type Iccid禁用
                            if (accnbr && accnbr.isBindingFlag == 'Y') {
                                this.$('.div-iccid').show();
                                this.$('.js-new-res-info .js-iccid').attr('disabled', 'disabled');
                            } else {
                                SimCardMgrAction.qrySimCardBaseType(
                                    {},
                                    function (data1) {
                                        // modify by tang.shiping 2457630 physicalSim无需配置，都有权限，其他则根据配置获取
                                        var currBaseTypeList = data1.baseTypeList || [];
                                        var physicalSim = fish.find(currBaseTypeList, function (data2) {
                                            return data2.baseTypeId == '1';
                                        });
                                        if (physicalSim) {
                                            currBaseTypeList.push(physicalSim);
                                        }

                                        var currBaseTypeItem = ArrayUtils.getItemByField(currBaseTypeList, this.baseTypeId + '', 'baseTypeId');
                                        if (this.baseTypeId !== '2' && currBaseTypeItem && currBaseTypeItem.autoSelectionFlag !== 'Y') {
                                            this.$('.div-iccid').hide();
                                        }
                                    }.bind(this)
                                );
                            }
                        }
                    }.bind(this)
                );
            } else {
                this.$('.div-iccid').hide();
            }
        },

        /**
         * 下拉选项Resource Type下拉框数据
         */
        querySimCardBySingle: function (iccId, callback) {
            if (iccId) {
                SimCardMgrAction.querySimCardBySingle(
                    { ICCID: iccId },
                    function (result) {
                        result = result.z_d_r || [];
                        if (result[0]) {
                            var baseTypeId = result[0].baseTypeId;
                            callback(baseTypeId);
                        } else {
                            callback(null);
                        }
                    }.bind(this)
                );
            } else {
                callback(this.orderItem.SIM_TYPE_ID);
            }
        },

        qryGoodsOfferList: function (modelIdStr, callback) {
            var queryParam = {
                subsPlanId: this.subsPlanId,
                subsEventId: this.subsEventId,
                custId: this.orderItem.CUST_ID,
                orgId: portal.appGlobal.get('orgId'),
                modelIdStr: modelIdStr,
                locationId: 1
            };

            // 协议机如果没有传subsPlanId 则从号码列表中获取
            if (!queryParam.subsPlanId) {
                queryParam.subsPlanId = this.orderItem.SUBS_PLAN_ID;
            }
            BoUtils.fetchDpOfferOrderList(
                this,
                function (dpOfferOrderList) {
                    // 订购的依赖产品列表,作为实物的计价因子
                    queryParam.dpOfferOrderList = dpOfferOrderList;
                    GoodsSalesAction.qryGoodsAndVendor(
                        queryParam,
                        function (result) {
                            if (callback) {
                                callback(result.goodsList);
                            }
                        }.bind(this)
                    );
                }.bind(this)
            );
        },

        // 初始化 SIM Card
        initSimCardBaseTypeSource: function (baseTypeId) {
            this.baseTypeId = baseTypeId;
            this.baseTypeItem = ArrayUtils.getItemByField(this.baseTypeList, this.baseTypeId, 'baseTypeId');
            var param = {};
            param.userId = portal.appGlobal.get('userId');
            param.dataPrivCode = 'CAN_CHOOSE_ESIM_CARD';
            var privArr = [];
            SubsAction.qryUserDataPrivByDataPrivCodeAndUserId(
                param,
                function (result) {
                    if (result.privList && result.privList.length > 0) {
                        fish.each(
                            result.privList,
                            function (i) {
                                if (i.id === 'Y') {
                                    privArr.push(i);
                                }
                            }.bind(this)
                        );
                    }
                }.bind(this)
            );

            var priv = null;
            if (privArr && privArr.length > 0) {
                priv = privArr[0].id;
            }

            SimCardMgrAction.qrySimCardBaseType(
                {},
                function (data) {
                    // modify by tang.shiping 2457630 physicalSim无需配置，都有权限，其他则根据配置获取
                    this.baseTypeList = [];
                    if (priv && priv == 'Y') {
                        this.baseTypeList = data.baseTypeList;
                    } else {
                        var physicalSim = fish.find(data.baseTypeList, function (data) {
                            return data.baseTypeId == '1';
                        });
                        if (physicalSim) {
                            this.baseTypeList.push(physicalSim);
                        }
                    }

                    var _disable = false;
                    if (this.currentAccNbr && this.currentAccNbr.isBindFlag == 'Y') {
                        _disable = true;
                    }
                    this.$baseTypeListPlace = this.$('.js-new-res-info .js-dup-sim-card-resource')
                        .combobox({
                            dataTextField: 'baseTypeName',
                            dataValueField: 'baseTypeId',
                            dataSource: this.baseTypeList,
                            editable: false,
                            disable: _disable,
                            value: baseTypeId
                        })
                        .on(
                            'combobox:change',
                            function (e) {
                                var logModel = new TrackPointLogModel();
                                logModel.set('operCode', 'OE001000');
                                logModel.set('stepCode', 'OE001120');
                                CommAction.recordPageTrackPointLog(logModel);

                                var curValue = e.target.value;
                                this.baseTypeId = curValue;
                                this.baseTypeItem = ArrayUtils.getItemByField(this.baseTypeList, this.baseTypeId, 'baseTypeId');
                                // 当选择为ESIM时,将ICCID禁用,并把值传来,只读显示且自动查询出ICCID
                                if ((curValue && curValue === '2') || (this.baseTypeItem && this.baseTypeItem.autoSelectionFlag === 'Y')) {
                                    this.$('.js-new-res-info .js-iccid').attr('disabled', 'disabled');
                                    var param = {
                                        orgId: portal.appGlobal.get('orgId'),
                                        custId: this.orderItem.CUST_ID,
                                        baseTypeId: curValue
                                    };
                                    if (this.currentAccNbr && this.currentAccNbr.accNbrId) {
                                        param.accNbrId = this.currentAccNbr.accNbrId;
                                    }
                                    this.setEsimCardInfo(param);
                                    this.$('.div-iccid').show();
                                } else {
                                    // 当选择为Ordinary SIM时,解除ICCID禁用,调用sendIccidBoChange
                                    this.$('.js-new-res-info .js-iccid').removeAttr('disabled');
                                    this.$(":input[name='iccid']").val('').change();
                                    this.$('.div-iccid').hide();
                                    this.currentVirtualSimCard = null;
                                }
                                this.firstInitBaseTypeSource = false;
                            }.bind(this)
                        );
                    this.registerBaseTypeChange();
                    if (
                        portal.appGlobal.get('isBolLogin') &&
                        (fish.store.get('accountOpeningMethod') == 'ESIM' || fish.store.get('accountOpeningMethod') == 'boundleOffer') &&
                        this.baseTypeList.length > 1
                    ) {
                        this.$('.js-dup-sim-card-resource').combobox('value', '2');
                    }
                    if (this.currentAccNbr && this.currentAccNbr.isBindFlag == 'Y') {
                        // this.$(".js-dup-sim-card-resource").combobox('disable');
                        this.$baseTypeListPlace.combobox('disable');
                        this.$('.js-new-res-info .js-iccid').attr('disabled', 'disabled');
                        this.$('.div-iccid').show();
                    }

                    this.baseTypeItem = ArrayUtils.getItemByField(this.baseTypeList, this.baseTypeId, 'baseTypeId');
                    if (baseTypeId === '2' || (this.baseTypeItem && this.baseTypeItem.autoSelectionFlag === 'Y')) {
                        this.$('.js-iccid').attr('disabled', 'disabled');
                        this.$('.div-iccid').show();
                    }
                    if (this.orderItem.ICCID) {
                        this.$('.js-iccid')
                            .val(this.orderItem.ICCID)
                            .attr('simCardId', this.orderItem.SIM_CARD_ID)
                            .attr('oldiccid', this.orderItem.ICCID)
                            .change();
                    }
                }.bind(this)
            );
        },

        // 初始化选号组件 需要放在VAS树初始化之后
        initAccNbrComponent: function () {
            this.$selectNumberPop = new SelectNumberPopView(this.$(":input[name='accNbr']"), {
                indepProdSpecId: this.subsBaseOrder.INDEP_PROD_SPEC_ID,
                subsPlanId: this.subsBaseOrder.SUBS_PLAN_ID,
                custId: this.orderItem.CUST_ID,
                areaId: portal.appGlobal.get('areaId') ? portal.appGlobal.get('areaId') : 1,
                spId: this.orderItem.SP_ID,
                servType: this.orderItem.SERV_TYPE,
                showPreAccNbr: this.showPreAccNbr,
                $vasTreeView: this.$vasTreeView,
                bindFlag: 'B',
                $subsPlanView: this,
                boAccess: {
                    selector: ":input[name='accNbr']",
                    boAccessName: 'orderItem',
                    boItemAccessName: 'accNbrId',
                    boPath: '/orderItem/accNbrId',
                    $currentView: this,
                    inputType: InputTypeDef.CUSTOM,
                    success: this.changeAccNbrSuccessHandler.bind(this),
                    fail: this.changeAccNbrFailHandler.bind(this)
                },
                // 卡号不正确,还原设置
                enableResourceType: function () {
                    this.$('.js-new-res-info .js-dup-sim-card-resource').combobox('enable');
                    this.$('.js-new-res-info .js-iccid').removeAttr('disabled');
                    this.$('.js-new-res-info .js-iccid').val('');
                }.bind(this)
            });
            if (this.showPreAccNbr) {
                this.$(":input[name='accNbr']").attr('style', 'width: 100%; display: inline-block').change().resetElement();
                this.$(":input[name='accNbr_prefix']").addClass('hide');
            }

            // 订单预览界面返回上一步，重新赋值号码、卡信息
            if (this.orderItem && this.orderItem.ACC_NBR_ID) {
                this.currentAccNbr = {
                    accNbrId: this.orderItem.ACC_NBR_ID,
                    accNbr: this.orderItem.ACC_NBR,
                    prefix: this.orderItem.PREFIX
                };
                OrderEntryAction.qryAccNbrDetailSic(
                    { accNbrId: this.currentAccNbr.accNbrId },
                    function (data) {
                        if (data.z_d_r) {
                            var accnbr = data.z_d_r[0];
                            // 选择绑定卡时,resource_type Iccid禁用
                            if (accnbr && accnbr.isBindingFlag == 'Y') {
                                if (this.$baseTypeListPlace) {
                                    this.$baseTypeListPlace.combobox('disable');
                                }

                                this.currentAccNbr.isBindFlag = 'Y';
                            }

                            this.initNumberAgreementComponent({ accNbrClassId: accnbr.accNbrClassId }, true);
                        }
                    }.bind(this)
                );
                this.$selectNumberPop.setAccNbr(this.currentAccNbr);
                if (this.orderItem.autoChooseAccNbr) {
                    this.$selectNumberPop.setAutoChoose();
                }
                this.$(":input[name='iccid']")
                    .val(this.orderItem.ICCID)
                    .attr('simCardId', this.orderItem.SIM_CARD_ID)
                    .attr('oldiccid', this.orderItem.ICCID)
                    .change();
            }

            if (this.orderItem.SERV_TYPE == 695 && this.configValue == '1') {
                this.$('[name="accNbr"]').prop('disabled', true);
                // this.$('.js-new-res-info .js-iccid').attr('disabled', 'disabled');
            }
            if (this.orderItem.SERV_TYPE == 695 && !this.orderItem.ACC_NBR_ID && this.configValue == '1') {
                this.$selectNumberPop.onAutoBtnClick();
            }
        },

        /**
         * 选择号码成功
         */
        changeAccNbrSuccessHandler: function (data) {
            if (portal.appGlobal.get('isBolLogin')) {
                $('.order-content .order-fixed-btn-list').css({
                    position: 'absolute',
                    width: '100%'
                });
                $('.pos-order-btn-list').css({
                    position: 'absolute',
                    width: '100%'
                });
                var tips = '';
                if (fish.store.get('accountOpeningMethod') == 'mobileOffer') {
                    tips = IntroDef.MOBILE_OFFER_STEP5;
                } else if (fish.store.get('accountOpeningMethod') == 'ESIM') {
                    tips = IntroDef.ESIM_OFFER_STEP4;
                } else if (fish.store.get('accountOpeningMethod') == 'boundleOffer') {
                    if (this.parentView.setIntroEle) {
                        this.parentView.setIntroEle(3);
                    }
                    return;
                }
                fish.trigger(IntroDef.ADD_INTRO, {
                    element: '#next-btn-intro',
                    tipsContents: tips,
                    position: 'top'
                });
            }
            this.initNumberAgreementComponent(
                {
                    accNbrClassId: this.$(":input[name='accNbr']").attr('accNbrClassId')
                },
                false
            );
            this.currentAccNbr = this.$selectNumberPop.getAccNbr();
            // this.currentAccNbr = this.$accNbrComponent.getAccNbr();
            // 回填卡信息
            var dirtyList = data.dirtyList;
            if (!fish.isEmpty(dirtyList)) {
                var iccidObj = fish.findWhere(dirtyList, {
                    boAccessName: 'orderItem',
                    boItemAccessName: 'iccid'
                });

                if (iccidObj) {
                    this.$(":input[name='iccid']").val(iccidObj.value).change(); // 无需触发事件
                    this.$(":input[name='iccid']").resetElement();
                }
            }

            /**
             * 绑定卡 未绑定卡 (禁用resource type iccid)
             *
             *
             */
            if (this.currentAccNbr && this.currentAccNbr.accNbrId && !(dirtyList == null || dirtyList == '') && iccidObj && iccidObj.value) {
                OrderEntryAction.qryAccNbrDetailSic(
                    { accNbrId: this.currentAccNbr.accNbrId },
                    function (data) {
                        if (data.z_d_r) {
                            var accnbr = data.z_d_r[0];
                            // 选择绑定卡时,resource_type Iccid禁用
                            if (accnbr && accnbr.isBindingFlag == 'Y') {
                                this.$('.js-new-res-info .js-dup-sim-card-resource').combobox('disable');
                                this.$('.js-new-res-info .js-dup-sim-card-resource').combobox('value', '1');

                                this.$('.js-new-res-info .js-iccid').attr('disabled', 'disabled');
                                this.currentAccNbr.isBindFlag = 'Y';
                            } else {
                                var curValue = this.$('.js-dup-sim-card-resource').combobox('value');
                                var item = ArrayUtils.getItemByField(this.baseTypeList, curValue, 'baseTypeId');
                                if ((curValue && curValue === '2') || (item && item.autoSelectionFlag === 'Y')) {
                                    this.$('.js-new-res-info .js-iccid').attr('disabled', 'disabled');

                                    // 解决问题：ESIM卡开户界面选的ICCID和预览QRcode界面显示的ICCID不一致
                                    var iccidVal = this.$('.js-new-res-info .js-iccid').val();
                                    if (iccidVal && iccidVal.length > 0) {
                                        return;
                                    }
                                    var qryParam = {
                                        hlrId: accnbr.hlrId,
                                        accNbrId: accnbr.accNbrId,
                                        orgId: portal.appGlobal.get('orgId'),
                                        custId: this.orderItem.CUST_ID,
                                        baseTypeId: curValue
                                    };
                                    this.setEsimCardInfo(qryParam);
                                } else {
                                    this.$('.js-new-res-info .js-dup-sim-card-resource').combobox('enable');
                                    if (curValue && curValue != '2') {
                                        this.$('.js-new-res-info .js-iccid').removeAttr('disabled');
                                    }
                                }
                            }
                        }
                    }.bind(this)
                );
            } else if (this.baseTypeId !== '2') {
                this.$(":input[name='iccid']").val('').change();
                this.$('.js-new-res-info .js-dup-sim-card-resource').combobox('enable');
                this.$('.js-new-res-info .js-iccid').removeAttr('disabled');
            }
        },

        initNumberAgreementComponent: function (paramData, isBack) {
            if (!isBack) {
                AgreementUtils.sendBOResetAgreement(
                    this.orderItem,
                    this,
                    function (item) {
                        paramData.orderItem = item;
                        this.randerNumberAgreementComponent(paramData);
                    }.bind(this),
                    AgreementDef.AGREEMENT_TYPE_GOLDEN_NUMBER
                );
            } else {
                this.randerNumberAgreementComponent(paramData);
            }
        },

        randerNumberAgreementComponent: function (paramData) {
            paramData.subsPlanId = this.subsBaseOrder.SUBS_PLAN_ID;
            paramData.custId = this.orderItem.CUST_ID;
            paramData.custOrderId = this.orderItem.CUST_ORDER_ID;
            paramData.orderItemId = this.orderItem.ORDER_ITEM_ID;
            paramData.orderItem = this.orderItem;
            paramData.$currentView = this;
            paramData.hiddenElement = function () {
                this.$(":input[name='numberAgreement']").parent().parent().parent().css('display', 'none');
            }.bind(this);
            paramData.showElement = function () {
                this.$(":input[name='numberAgreement']").parent().parent().parent().css('display', 'block');
            }.bind(this);
            paramData.requiredHander = function () {
                this.$(":input[name='numberAgreement']").attr('data-rule', 'required;');
                this.$(":input[name='numberAgreement']").parent().parent().addClass('required');
            }.bind(this);

            new NumberAgreementComponent(this.$(":input[name='numberAgreement']"), paramData);
        },

        changeAccNbrFailHandler: function () {
            // 选号失败清空文本框
            this.$(":input[name='accNbr']").val('').attr('newAccNbr', '').attr('accNbrId', '').attr('prefix', '').attr('accNbr', '');
            this.$('.js-new-res-info .js-dup-sim-card-resource').combobox('enable');
            this.$('.js-new-res-info .js-iccid').removeAttr('disabled');
        },

        setEsimCardInfo: function (qryParam) {
            qryParam.servTypes = [this.orderItem.SERV_TYPE];
            qryParam.offerIds = [this.orderItem.SUBS_PLAN_ID];
            qryParam.prodIds = [this.orderItem.OFFER_ID];
            SimCardMgrAction.qrySingleESimCard(
                qryParam,
                function (data) {
                    this.$('.js-new-res-info .js-iccid').val(data.iccid);
                    this.$(":input[name='iccid']").val(data.iccid).attr('simCardId', data.simCardId).change();
                    this.currentVirtualSimCard = data;
                    this.$selectNumberPop.setHlrId(data.hlrId);
                }.bind(this),
                function (data) {
                    fish.warn(fish.escape(data.Msg));
                    this.$baseTypeListPlace.combobox('value', '1');
                    this.currentVirtualSimCard = null;
                }.bind(this)
            );
        },

        registerBaseTypeChange: function () {
            var boAccess = {
                selector: ":input[name='baseTypeName']",
                boAccessName: 'orderItem',
                boItemAccessName: 'simTypeId',
                $currentView: this,
                inputType: InputTypeDef.COMBOBOX
            };
            if (boAccess && boAccess.$currentView) {
                Controller.getInstance().registerChange(boAccess.$currentView, boAccess);
            }
        },

        // 清空合约电话
        onClearContractPhone: function () {
            if (this.$('.js-contract-phone').val()) {
                // 删除操作，从内存中去掉
                if (this.selectedContractPhoneItem) {
                    this.onRemoveGoodsOrderBoChange([this.selectedContractPhoneItem]);
                    this.selectedContractPhoneItem = null;
                    this.$('.js-contract-phone').val('');
                    this.showClearContractPhoneIcon(false);
                }
            }
        },

        /**
         * 输入SN后回车
         * @param e
         */
        onMatchGoodsSN: function (e) {
            // 查询实物
            if (e.keyCode == 13) {
                // 回车之后自动失去焦点，防止触发多次回车事件
                this.$(e.target).blur();
                var goodsSn = $.trim(this.$(e.target).val());
                if (!this.validGoodsSn(goodsSn)) {
                    this.$(e.target).val('');
                    return;
                }

                var queryParam = {
                    goodsSn: goodsSn,
                    orgId: portal.appGlobal.get('orgId')
                };
                GoodsSalesAction.matchHandSetByGoodsSn(
                    queryParam,
                    function (result) {
                        var modelIdList = result.modelIdList;
                        if (fish.isEmpty(modelIdList)) {
                            this.$('.js-goods-sn').val('');
                            fish.info(I18N.CAN_NOT_FIND_GOODS_BY_SN);
                        } else {
                            var modelIdStr;
                            if (fish.isArray(modelIdList)) {
                                modelIdStr = modelIdList.join();
                            } else {
                                modelIdStr = modelIdList;
                            }

                            this.qryGoodsOfferList(
                                modelIdStr,
                                function (goodsList) {
                                    if (fish.isEmpty(goodsList)) {
                                        this.$('.js-goods-sn').val('');
                                        fish.info(I18N.CAN_NOT_FIND_GOODS_BY_SN);
                                    } else if (goodsList.length > 1) {
                                        // 打开弹窗让客户选Model
                                        this.openSelModelPopup(goodsList);
                                    } else if (fish.isEmpty(this.selectedContractPhoneItem)) {
                                        goodsList[0].goodsSn = goodsSn;
                                        this.addGoods(goodsList[0]);
                                    } else if (this.selectedContractPhoneItem.goodsSn !== goodsSn) {
                                        var goodsItem = this.selectedContractPhoneItem;
                                        goodsItem.goodsSn = goodsSn;

                                        this.onRemoveGoodsOrderBoChange([this.selectedContractPhoneItem]);
                                        this.addGoods(goodsItem);
                                    }
                                }.bind(this)
                            );
                        }
                    }.bind(this)
                );
            }
        },

        validGoodsSn: function (goodsSn) {
            if (!goodsSn) {
                return false;
            }
            if (this.isStockManage === 'N') {
                return false;
            }
            var accNbrItem = this.$("input[name='accNbr']").val();
            if (!accNbrItem) {
                return false;
            }

            return true;
        },

        /**
         * 添加实物
         *
         * @param goods
         */
        addGoods: function (goods) {
            if (this.currentAccNbr) {
                goods.accNbr = this.currentAccNbr.accNbr;
                goods.accNbrId = this.currentAccNbr.accNbrId;
            }
            goods.cnt = 1;
            goods.salePrice = goods.salesPrice; // BO中的字段和查询出来的字段不一致 作个转换
            goods.saleListPrice = goods.listPrice;
            if (!goods.operationType) {
                goods.operationType = OperationTypeDef.NEW;
            }
            if (!goods.goodsOrderBoKey) {
                goods.goodsOrderBoKey = fish.getUUID();
            }
            goods.orgId = portal.appGlobal.get('orgId');
            if (this.refreshBo) {
                this.fillupGoodsAttrList(
                    goods.offerId,
                    function (goodsAttrList) {
                        goods.goodsAttrList = goodsAttrList;
                        this.onAddGoodsOrderBoChange([goods]);
                    }.bind(this)
                );
            } else {
                this.onAddGoodsOrderBoChange([goods]);
            }
        },

        fillupGoodsAttrList: function (offerId, callback) {
            var req = {};
            req.requestParam = {
                subsPlanId: this.subsPlanId,
                offerId: offerId
            };
            OfferQryAction.qryOfferAttrList(
                req,
                function (data) {
                    var attrList = data.propertyList || [];
                    var goodsAttrList = [];
                    fish.each(attrList, function (item) {
                        if (item.defaultValue) {
                            var attrValue = fish.find(item.attrValueList, function (attrValueItem) {
                                return attrValueItem.value == item.defaultValue;
                            });
                            goodsAttrList.push({
                                attrId: item.attrId,
                                attrValue: item.defaultValue,
                                operationType: OperationTypeDef.NEW,
                                valueMark: attrValue.valueMark
                            });
                        }
                    });
                    callback(goodsAttrList);
                }.bind(this)
            );
        },

        onAddGoodsOrderBoChange: function (goodsList) {
            var dataInputs = new DataInputs();
            fish.each(
                goodsList,
                function (goods) {
                    goods.orderItemId = this.orderItem.ORDER_ITEM_ID;
                    dataInputs.push(
                        new DataInput({
                            operationType: OperationTypeDef.NEW,
                            boAccessName: this.getGoodsOrderBoAccessName(),
                            bo: true,
                            value: goods
                        })
                    );
                    if (this.isBatch) {
                        goods.BO_ACCESS_NAME_IDENTIFY = 'BATCH_GOODS_ORDER';
                    }
                    fish.each(
                        goods.goodsAttrList,
                        function (goodsAttr) {
                            dataInputs.push(
                                new DataInput({
                                    operationType: OperationTypeDef.NEW,
                                    boAccessName: this.getGoodsOrderAttrAccessName(),
                                    bo: true,
                                    parentKeyValue: goods.goodsOrderBoKey,
                                    value: goodsAttr
                                })
                            );
                        }.bind(this)
                    );
                }.bind(this)
            );
            var callbackArgs = {
                goodsOrderList: goodsList
            };
            Controller.getInstance().sendBoChanges(
                this.commonDataId,
                dataInputs,
                this,
                callbackArgs,
                function (result, callbackArgs) {
                    var goodsOrder = callbackArgs.goodsOrderList[0];
                    if (goodsOrder.goodsTypeId === 'S') {
                        this.fillUpSimModelName(goodsOrder);
                    } else {
                        this.fillUpContractPhoneName(goodsOrder);
                    }
                }.bind(this)
            );
        },

        fillUpSimModelName: function (goodsOrder) {
            this.$('.js-sim-model').val(goodsOrder.modelName);
            this.showClearSimModelIcon(true);
            this.selectedSimModelItem = goodsOrder;
        },

        fillUpContractPhoneName: function (goodsOrder) {
            this.$('.js-contract-phone').val(goodsOrder.offerName);
            this.showClearContractPhoneIcon(true);
            this.selectedContractPhoneItem = goodsOrder;
        },

        /**
         * 刷新实物BO
         * @param goodsOrderList
         * @param operationType
         */
        onRemoveGoodsOrderBoChange: function (goodsOrderList) {
            var dataInputs = new DataInputs();
            fish.each(
                goodsOrderList,
                function (goodsOrder) {
                    dataInputs.push(
                        new DataInput({
                            operationType: OperationTypeDef.CANCEL,
                            boAccessName: this.getGoodsOrderBoAccessName(),
                            bo: true,
                            value: goodsOrder
                        })
                    );
                }.bind(this)
            );
            var callbackArgs = {
                goodsOrderList: goodsOrderList
            };
            Controller.getInstance().sendBoChanges(
                this.commonDataId,
                dataInputs,
                this,
                callbackArgs,
                function (result, callbackArgs) {
                    var goodsOrder = callbackArgs.goodsOrderList[0];
                    if (goodsOrder.goodsTypeId !== 'S') {
                        this.$('.js-goods-sn').removeClass('required');
                    }
                }.bind(this)
            );
        },

        /**
         * 选择实物
         */
        onSelectGoodsClick: function () {
            var accNbrItem = this.$("input[name='accNbr']").val();
            if (!accNbrItem) {
                fish.info(I18N.HINT_NOT_SELECT_SERVICE_NUMBER_FIRST);
            }
        },

        getGoodsOrderBoAccessName: function () {
            return this.isBatch ? 'batchGoodsOrder' : 'goodsOrder';
        },

        getGoodsOrderAttrAccessName: function () {
            return this.isBatch ? 'batchGoodsOrderAttr' : 'goodsOrderAttr';
        },

        showClearSimModelIcon: function (isShow) {
            if (isShow) {
                this.$('.js-clear-sim-model').show();
            } else {
                this.$('.js-clear-sim-model').hide();
            }
        },

        showClearContractPhoneIcon: function (isShow) {
            if (isShow) {
                this.$('.js-clear-contract-phone').show();
            } else {
                this.$('.js-clear-contract-phone').hide();
            }
        },

        onIccidChange: function () {
            var iccid = $.trim(this.$(":input[name='iccid']").val());
            // this.trigger(this.iccidChangeEvent, iccid);

            if (iccid) {
                var dataInput = new DataInput({
                    boAccessName: 'custOrder',
                    bo: true,
                    boItemAccessName: 'custOrderId'
                });
                dataInput.set('value', {
                    CUST_ORDER_ID: this.orderItem.custOrderId
                });

                Controller.getInstance().fetchData(
                    null,
                    dataInput,
                    this,
                    function (result) {
                        var custOrder = result.dirtyList[0].value;
                        var logModel = new TrackPointLogModel();
                        var items = custOrder.ORDER_ITEM;
                        var currentItem = this.orderItem;
                        var index = 0;
                        for (var i = 0; i < items.length; i++) {
                            if (currentItem.ORDER_ITEM_ID == items[i].ORDER_ITEM_ID) {
                                index = i;
                                custOrder.ORDER_ITEM[i].ICCID = iccid;
                                this.orderItem.ICCID = iccid;
                                break;
                            }
                        }
                        for (var i = 0; i < items.length; i++) {
                            // 当一个订单有多个order_item时，输入相同的ICCID需要被拦截，sic的校验是根据cust_order_nbr的而不是order_item
                            if (iccid == items[i].ICCID && i != index) {
                                custOrder.ORDER_ITEM[index].ICCID = '';
                                this.orderItem.ICCID = '';
                                fish.error('Duplicate ICCID in the order.');
                                // this.$(":input[name='iccid']").val("");
                                return;
                            }
                        }
                        // var param = {
                        //   ICCID: iccid,
                        //   ICCID_BEGIN: iccid,
                        //   CUST_ID: this.orderItem.CUST_ID,
                        //   QRY_CUST_NET_TYPE_FLAG: 'Y'
                        // };
                        // SimCardMgrAction.QrySingleSimCard(param, null, function (data) {
                        //   console.log(JSON.stringify(data));
                        //   if (data.isSuccess == false && data.callServiceSuccess == false) {
                        //     this.$(":input[name='iccid']").val("").attr("simCardId", "");
                        //
                        //     fish.error(data.Msg);
                        //     return;
                        //   }
                        // }.bind(this), false);
                        logModel.set('operCode', 'OE001000');
                        logModel.set('stepCode', 'OE001130');
                        CommAction.recordPageTrackPointLog(logModel);

                        this.$(":input[name='iccid']").resetElement();
                    }.bind(this)
                );
            }
        },

        onAccNbrChange: function () {
            var accNbrInput = $.trim(this.$(":input[name='accNbr']").val());
            if (accNbrInput) {
                var logModel = new TrackPointLogModel();
                logModel.set('operCode', 'OE001000');
                logModel.set('stepCode', 'OE001040');
                CommAction.recordPageTrackPointLog(logModel);
                var param = {
                    ACC_NBR: accNbrInput,
                    NEED_DEFAULT_PREFIX: true
                };
                AccNbrAction.qryAccNbrDetailByAccNbr(
                    param,
                    function (data) {
                        this.accNbrDetail = data.accNbrDto;
                    }.bind(this)
                );
            }

            var accNbr = $.trim(this.$(":input[name='accNbr']").attr('newAccNbr'));
            // this.trigger(this.accNbrChangeEvent, accNbr);
            if (accNbr) {
                this.$(":input[name='accNbr']").resetElement();
            }
        },

        // 初始化 portIn
        initPortIn: function () {
            if (this.orderItem.NP && this.orderItem.NP.DONOR_OPERATOR) {
                this.$('.js-check-portIn').icheck('check');
                this.onClickPortIn('Init');
            }
        },

        // 点击 checkbox 的事件
        onClickPortIn: function (e) {
            var logModel = new TrackPointLogModel();
            logModel.set('operCode', 'OE001000');
            logModel.set('stepCode', 'OE001030');
            CommAction.recordPageTrackPointLog(logModel);

            var viewOption = fish.extend({}, this.options);
            viewOption.attrId = this.attrId;
            viewOption.attrList = this.attrList;
            if (this.$("[name='portIn']").icheck('isChecked')) {
                this.privValidate();
                this.portInHide();
                if (!this.hasPriv) {
                    this.$("[name='portIn']").icheck('uncheck');
                    fish.info('No permission to Port In.');
                    return;
                }

                this.$('.js-port-in-number-parent').hide();
                this.$('.js-acc-nbr').hide();
                this.$("input[name='accNbr']").attr('data-rule-ignore', true);

                this.$("input[name='accNbr']").val('');
                if (e !== 'Init') {
                    this.$(":input[name='iccid']").val('');
                }
                if (this.baseTypeId !== '2') {
                    this.$(":input[name='iccid']").attr('disabled', false);
                }

                this.$('.js-port-in-info').show();
                // back 场景下listenTo无法监听到
                if (this.attrList && this.attrList.length > 0) {
                    if (this.orderItem.PORTIN_FLAG != null && this.orderItem.PORTIN_FLAG == 'Y') {
                        // && this.attrList[0].ATTR_VALUE == "1") {
                        this.$('.js-acc-nbr').hide();
                        this.$("input[name='accNbr']").attr('data-rule-ignore', true);
                    } else {
                        this.$('.js-acc-nbr').show();
                        this.$("input[name='accNbr']").removeAttr('data-rule-ignore', true);
                    }
                }
                this.requireView({
                    url: 'crm/modules/pos/flowpage/business/newconnection/views/GSMNewPortInInfoView',
                    selector: '.js-port-in-info',
                    viewOption: viewOption,
                    callback: function ($view) {
                        this.$gsmNewPortInInfoView = $view;
                        this.listenTo($view, 'finishSelectPortInType', function (data) {
                            if (data.portInWithTempNbr) {
                                // this.$("[name='portIn']").icheck('disable');
                                this.trigger(this.portInChangeEvent, false);
                                this.$('.js-acc-nbr').show();
                                this.$("input[name='accNbr']").removeAttr('data-rule-ignore', true);
                            } else if (data.portInNoTempNbr) {
                                // this.$("[name='portIn']").icheck('disable');
                                this.$("[name='accNbr']").attr('disabled', false);
                                this.trigger(this.portInChangeEvent, false);
                                this.$('.js-acc-nbr').hide();
                                this.$("input[name='accNbr']").attr('data-rule-ignore', true);
                            } else if (data.cancelPortIn) {
                                // this.$("[name='portIn']").icheck('enable');
                                this.$('.js-acc-nbr').show();
                                this.$("input[name='accNbr']").removeAttr('data-rule-ignore', true);
                                this.trigger(this.portInChangeEvent, true);
                            }
                            this.$("input[name='accNbr']").val('');
                        });
                    }.bind(this)
                });
            } else {
                this.portInShow();
                this.$('.js-port-in-info').hide();
                // 2520693 清空页面必填项校验, 清空portInFlag标识
                this.$('.js-port-in-info').empty();
                this.clearPortInFlag();
                this.$('.js-port-in-number-parent').show();
                this.$('.js-acc-nbr').show();
                this.$("input[name='accNbr']").removeAttr('data-rule-ignore', true);
            }
        },

        privValidate: function () {
            var that = this;
            that.hasPriv = false;
            var param = {};
            param.userId = portal.appGlobal.get('userId');
            param.dataPrivCode = 'CAN_DO_MNP_PORT_IN';
            SubsAction.qryUserDataPrivByDataPrivCodeAndUserId(param, function (result) {
                if (result.privList && result.privList.length > 0) {
                    fish.each(result.privList, function (i) {
                        if (i.id === 'Y') {
                            that.hasPriv = true;
                        }
                    });
                }
            });
        },

        clearPortInFlag: function () {
            var dataInput = new DataInput();
            dataInput.set('boAccessName', 'orderItem');
            dataInput.set('boItemAccessName', 'portinFlag');
            dataInput.set('value', '');
            Controller.getInstance().sendSingleChange(
                this.commonDataId,
                dataInput,
                this,
                null,
                null,
                function (data) {
                    fish.each(data.errors, function (error) {
                        fish.error(error.message);
                    });
                }.bind(this)
            );

            var dataInput2 = new DataInput();
            dataInput2.set('boAccessName', 'np');
            dataInput2.set('operationType', OperationTypeDef.CANCEL);
            dataInput2.set('value', '');
            Controller.getInstance().sendSingleBoChange(
                this.commonDataId,
                dataInput2,
                this,
                null,
                null,
                function (data) {
                    fish.each(data.errors, function (error) {
                        fish.error(error.message);
                    });
                }.bind(this)
            );

            var dataInput3 = new DataInput();
            dataInput3.set('boAccessName', 'orderItemAttr');
            dataInput3.set('operationType', OperationTypeDef.CANCEL);
            dataInput3.set('value', {
                attrId: this.attrId,
                attrValue: '0',
                orderItemId: this.orderItem.ORDER_ITEM_ID,
                spId: portal.appGlobal.get('spId')
            });
            Controller.getInstance().sendSingleBoChange(
                this.commonDataId,
                dataInput3,
                this,
                null,
                null,
                function (data) {
                    fish.each(data.errors, function (error) {
                        fish.error(error.message);
                    });
                }.bind(this)
            );
        },

        portInHide: function () {
            this.$('.js-port-in-hide').hide();
            fish.trigger('HANDSET_AGREEMENT_UNSUBSCRIBE' + this.pageUuid, {});
            fish.trigger('DEVICE_UNSUBSCRIBE' + this.pageUuid, {});
        },

        // 初始化是否支持port in
        initMnpBusiParam: function () {
            PostNpDbAction.qryNpOrderTypeList(
                { npOrderTypeId: 3 },
                function (result) {
                    if (!fish.isEmpty(result)) {
                        fish.each(
                            result,
                            function (item) {
                                if (ServTypeDef.GSM == item.servType) {
                                    if (item.paidFlag) {
                                        if (this.acct.POSTPAID == item.paidFlag && this.portinflag) {
                                            this.$('.js-portIn').show();
                                        } else {
                                            this.$('.js-portIn').hide();
                                        }
                                    } else if (this.portinflag) {
                                        // OPER_TYPE_SERV_TYPE.PAID_FLAG为空,表示预后付费都支持
                                        this.$('.js-portIn').show();
                                    }
                                }
                            }.bind(this)
                        );
                    }
                    this.initPortInTypeAttr();
                }.bind(this)
            );
        },

        initPortInTypeAttr: function () {
            PostNpDbAction.qryAttrByAttrCode(
                { attrCode: 'EXP_PORT_IN_TYPE' },
                function (result) {
                    if (result) {
                        this.attrId = result[0].attrId;
                        this.getMnpIsNeedTempNbrBusiParam();
                    }
                }.bind(this)
            );
        },

        getMnpIsNeedTempNbrBusiParam: function () {
            var dataInput = new DataInput();
            dataInput.set('boAccessName', 'orderItemAttr');
            dataInput.set('boItemAccessName', 'attrId');
            dataInput.set('bo', true);
            dataInput.set('value', {
                attrId: this.attrId,
                orderItemId: this.orderItem.ORDER_ITEM_ID
            });
            Controller.getInstance().fetchData(
                this.commonDataId,
                dataInput,
                this,
                function (result) {
                    var dirtyList = result.dirtyList || [];
                    if (dirtyList.length > 0) {
                        var attrList = dirtyList[0].value || [];
                        this.attrList = attrList;
                        if (attrList.length > 0) {
                            if (
                                this.orderItem.PORTIN_FLAG != null &&
                                this.orderItem.PORTIN_FLAG == 'Y' &&
                                (attrList[0].ATTR_VALUE == '1' || attrList[0].ATTR_VALUE == '0')
                            ) {
                                this.$("[name='portIn']").icheck('check');
                                this.onClickPortIn('Init');
                                this.trigger(this.portInChangeEvent, false);
                            } else {
                                this.trigger(this.portInChangeEvent, true);
                            }
                        }
                    }
                }.bind(this)
            );
        }
    });
});
