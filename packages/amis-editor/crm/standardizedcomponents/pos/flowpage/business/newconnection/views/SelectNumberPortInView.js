/* eslint-disable no-shadow */
/* eslint-disable no-param-reassign */
/* eslint-disable no-redeclare */
/* eslint-disable no-extra-bind */
/* eslint-disable newline-per-chained-call */
/* eslint-disable indent */
/* 忽视的目的：保持原有的业务逻辑，不能随便修改 */

/**
 * @component SelectNumberPortInView
 * @description 选择端口号组件
 *
 * @property {Object} context - 调用方法的上下文对象
 * @property {Object} options - 组件的配置选项
 */
define([
    'hbs!crm/standardizedcomponents/pos/flowpage/business/newconnection/templates/SelectNumberPortInTpl.hbs',
    'hbs!crm/standardizedcomponents/templates/ArticleTitleTpl.hbs',
    'i18n!crm/modules/pos/workspace/i18n/OrderEntry',
    'i18n!crm/modules/pos/flowpage/custorderInfo/i18n/CustOrderInfo',
    'i18n!crm/modules/pos/flowpage/common/i18n/CustomerCare',
    'crm/modules/offer/constants/VasConstants',
    'crm/modules/common/util/PortalConfigUtil',
    'crm/modules/order/orderentry/actions/OrderEntryAction',
    'crm/modules/resource/card/actions/SimCardMgrAction',
    'crm/modules/customer/actions/SubsAction',
    'crm/modules/common/actions/CommonAction',
    'crm/modules/common/actions/ConfigItemAction',
    'crm/modules/inventory/goods/actions/GoodsSalesAction',
    'crm/modules/resource/actions/AccNbrAction',
    'crm/modules/common/models/TrackPointLogModel',
    'crm/modules/common/util/ArrayUtils',
    'crm/modules/fbf/bfm/models/DataInput',
    'crm/modules/pos/flowpage/business/component/constants/InputTypeDef',
    'crm/modules/fbf/bfm/constant/OperationTypeDef',
    'crm/modules/common/constant/ServTypeDef',
    'crm/modules/pos/common/constant/IntroDef',
    'crm/modules/pos/constants/AgreementDef',
    'crm/modules/pos/common/utils/BoUtils',
    'crm/modules/pos/common/utils/AgreementUtils',
    'crm/modules/pos/flowpage/business/component/actions/NpDbAction',
    'crm/standardizedcomponents/views/SelectNumberPopView',
    'crm/modules/pos/components/NumberAgreementComponent',
    'crm/modules/pos/flowpage/business/newconnection/views/GSMNewPortInInfoView'
], function (
    SelectNumberPortInTpl,
    ArticleTitleTpl,
    OrderEntryI18N,
    CustOrderInfoI18N,
    CustomerCareI18N,
    VasConstants,
    PortalConfigUtil,
    OrderEntryAction,
    SimCardMgrAction,
    SubsAction,
    CommAction,
    ConfigItemAction,
    GoodsSalesAction,
    AccNbrAction,
    TrackPointLogModel,
    ArrayUtils,
    DataInput,
    InputTypeDef,
    OperationTypeDef,
    ServTypeDef,
    IntroDef,
    AgreementDef,
    BoUtils,
    AgreementUtils,
    PostNpDbAction,
    SelectNumberPopView,
    NumberAgreementComponent,
    GSMNewPortInInfoView
) {
    return function (context, options, Controller) {
        var $el = context.$portInView;
        if ($el && $el.length > 0 && typeof $el.append === 'function') {
            $el.append(SelectNumberPortInTpl(fish.extend({}, OrderEntryI18N, CustOrderInfoI18N, CustomerCareI18N)));
            $el.find('.port-in-wrapper')
                .first()
                .prepend(ArticleTitleTpl(fish.extend({}, OrderEntryI18N, CustOrderInfoI18N, CustomerCareI18N)));
        }
        // 添加标题
        this.$acomponentName = 'select number';

        this.options = options || {};
        this.pageDesignerFlag = options.pageDesignerFlag || false;
        this.portinflag = false;
        this.orderItem = options.orderItem;
        this.custOrder = options.custOrder;
        this.acct = this.orderItem.ACCT;
        this.subsBaseOrder = this.orderItem.SUBS_BASE_ORDER;
        this.subsBriefDetail = options.subsBriefDetail;
        this.subsEventId = options.subsEventId ? options.subsEventId : options.orderItem.SUBS_EVENT_ID;
        this.subsPlanUuid = options.subsPlanUuid;
        this.bundleOrderItem = options.bundleOrderItem;
        this.isPrimary = options.isPrimary;
        this.availableOfferTypeArr = [
            VasConstants.OFFER_TYPE_DEPENDENT_PRODUCT,
            VasConstants.OFFER_TYPE_PRICE_PLAN,
            VasConstants.OFFER_TYPE_DEFAULT_PRICE_PLAN
        ];
        this.showPreAccNbr = options.showPreAccNbr;
        this.portInChangeEvent = 'portInChangeEvent';
        this.vasInfoList = null;
        this.pageUuid = options.pageUuid;
        this.acctDisabled = options.acctDisabled;
        // 加个标识，购物车加入多个offer时，会初始化多次，但提示信息只要弹一次 task:2580835
        if (options.isLastOrderItem === true) {
            this.isLastOrderItem = options.isLastOrderItem;
        } else if (options.isLastOrderItem === false) {
            this.isLastOrderItem = options.isLastOrderItem;
        } else {
            this.isLastOrderItem = true;
        }

        // add by tang.shiping 用于处理vas与device的offerRelation
        // 这些参数会存在赋值操作
        this.allGoodsSaleOrderDetailList = null;
        this.allDpOfferOrderList = null;

        this.$vasTreeView = null;
        this.$deviceTreeView = null;
        this.$defaultLanguageCombobox = null;
        this.$paymentAccountComponent = null;
        this.$accountSelectorComponent = null;
        this.$selectNumberPop = null;
        this.$userSelectPopedit = null;

        this.$indepProdOrderAttrComponent = null;
        this.indepProdOrderAttrList = null;
        this.$orderItemAttrComponent = null;
        this.$creditLimitComponent = null;

        this.userHideFlag = null;
        this.currentVirtualSimCard = null;
        this.configValue = PortalConfigUtil.configValue('BSS_ALL_2024Q4');

        this.isBatch = options.isBatch || false;
        this.refreshBo = options.refreshBo || false;
        this.commonDataId = options.commonDataId;
        this.isStockManage = null;

        // 函数使用中定义的变量
        this.baseTypeId = null;
        this.baseTypeItem = null;
        this.currentAccNbr = null;

        this.selectedContractPhoneItem = null;
        this.selectedSimModelItem = null;
        // 检测是否展示 portIn 按钮
        this.checkCanPortIn =
            context.checkCanPortIn && typeof context.checkCanPortIn === 'function'
                ? context.checkCanPortIn.bind(context)
                : function () {
                      var param = {
                          subsPlanId: context.orderItem.SUBS_PLAN_ID,
                          bundleSubsPlanId: context.orderItem.BUNDLE_SUBS_PLAN_ID
                      };
                      param.userId = portal.appGlobal.get('userId');
                      OrderEntryAction.checkCanPortIn(
                          param,
                          function (data) {
                              if (data && data.checkResult == 'Y') {
                                  this.$('.js-portIn').show();
                                  this.portinflag = true;
                              }
                          }.bind(context)
                      );
                  };

        // 初始化 iccid 输入框
        this.initDisplayIccid =
            context.initDisplayIccid && typeof context.initDisplayIccid === 'function'
                ? context.initDisplayIccid.bind(context)
                : function () {
                      var self = this;

                      if (context.orderItem && context.orderItem.ACC_NBR_ID) {
                          OrderEntryAction.qryAccNbrDetailSic(
                              { accNbrId: context.orderItem.ACC_NBR_ID },
                              function (data) {
                                  if (data.z_d_r) {
                                      var accnbr = data.z_d_r[0];
                                      // 选择绑定卡时,resource_type Iccid禁用
                                      if (accnbr && accnbr.isBindingFlag == 'Y') {
                                          this.$('.div-iccid').show();
                                          this.$('.js-new-res-info .js-iccid').attr('disabled', 'disabled');
                                      } else {
                                          SimCardMgrAction.qrySimCardBaseType(
                                              {},
                                              function (data1) {
                                                  var currBaseTypeList = data1.baseTypeList || [];
                                                  var physicalSim = fish.find(currBaseTypeList, function (data2) {
                                                      return data2.baseTypeId == '1';
                                                  });
                                                  if (physicalSim) {
                                                      currBaseTypeList.push(physicalSim);
                                                  }

                                                  var currBaseTypeItem = ArrayUtils.getItemByField(
                                                      currBaseTypeList,
                                                      this.baseTypeId + '',
                                                      'baseTypeId'
                                                  );
                                                  if (this.baseTypeId !== '2' && currBaseTypeItem && currBaseTypeItem.autoSelectionFlag !== 'Y') {
                                                      this.$('.div-iccid').hide();
                                                  }
                                              }.bind(self)
                                          );
                                      }
                                  }
                              }.bind(context)
                          );
                      } else {
                          context.$('.div-iccid').hide();
                      }
                  };

        /**
         * 下拉选项Resource Type下拉框数据
         */
        this.querySimCardBySingle = function (iccId, callback) {
            if (iccId) {
                SimCardMgrAction.querySimCardBySingle(
                    { ICCID: iccId },
                    // bind(context)
                    function (result) {
                        result = result.z_d_r || [];
                        if (result[0]) {
                            var baseTypeId = result[0].baseTypeId;
                            callback(baseTypeId);
                        } else {
                            callback(null);
                        }
                    }.bind(context)
                );
            } else {
                callback(context.orderItem.SIM_TYPE_ID);
            }
        };

        this.qryGoodsOfferList = function (modelIdStr, callback) {
            var self = this;
            var queryParam = {
                subsPlanId: context.subsPlanId,
                subsEventId: self.subsEventId,
                custId: context.orderItem.CUST_ID,
                orgId: portal.appGlobal.get('orgId'),
                modelIdStr: modelIdStr,
                locationId: 1
            };

            // 协议机如果没有传subsPlanId 则从号码列表中获取
            if (!queryParam.subsPlanId) {
                queryParam.subsPlanId = context.orderItem.SUBS_PLAN_ID;
            }
            BoUtils.fetchDpOfferOrderList(
                context,
                function (dpOfferOrderList) {
                    // 订购的依赖产品列表,作为实物的计价因子
                    queryParam.dpOfferOrderList = dpOfferOrderList;
                    GoodsSalesAction.qryGoodsAndVendor(
                        queryParam,
                        function (result) {
                            if (callback) {
                                callback(result.goodsList);
                            }
                        }.bind(context)
                    );
                }.bind(context)
            );
        };

        // 初始化 SIM Card
        this.initSimCardBaseTypeSource = function (baseTypeId) {
            var self = this;
            context.baseTypeId = baseTypeId;
            context.baseTypeItem = ArrayUtils.getItemByField(self.baseTypeList, self.baseTypeId, 'baseTypeId');

            var param = {};
            param.userId = portal.appGlobal.get('userId');
            param.dataPrivCode = 'CAN_CHOOSE_ESIM_CARD';
            var privArr = [];
            SubsAction.qryUserDataPrivByDataPrivCodeAndUserId(
                param,
                // bind(context)
                function (result) {
                    if (result.privList && result.privList.length > 0) {
                        fish.each(
                            result.privList,
                            function (i) {
                                if (i.id === 'Y') {
                                    privArr.push(i);
                                }
                            }.bind(context)
                        );
                    }
                }.bind(context)
            );

            var priv = null;
            if (privArr && privArr.length > 0) {
                priv = privArr[0].id;
            }

            SimCardMgrAction.qrySimCardBaseType(
                {},
                // bind(context)
                function (data) {
                    // modify by tang.shiping 2457630 physicalSim无需配置，都有权限，其他则根据配置获取
                    this.baseTypeList = [];

                    if (priv && priv == 'Y') {
                        this.baseTypeList = data.baseTypeList;
                    } else {
                        var physicalSim = fish.find(data.baseTypeList, function (data) {
                            return data.baseTypeId == '1';
                        });
                        if (physicalSim) {
                            this.baseTypeList.push(physicalSim);
                        }
                    }

                    var _disable = false;
                    if (this.currentAccNbr && this.currentAccNbr.isBindFlag == 'Y') {
                        _disable = true;
                    }
                    this.$baseTypeListPlace = this.$('.js-new-res-info .js-dup-sim-card-resource')
                        .combobox({
                            dataTextField: 'baseTypeName',
                            dataValueField: 'baseTypeId',
                            dataSource: this.baseTypeList,
                            editable: false,
                            disable: _disable,
                            value: baseTypeId
                        })
                        .on(
                            'combobox:change',
                            function (e) {
                                var logModel = new TrackPointLogModel();
                                logModel.set('operCode', 'OE001000');
                                logModel.set('stepCode', 'OE001120');
                                CommAction.recordPageTrackPointLog(logModel);

                                var curValue = e.target.value;
                                this.baseTypeId = curValue;
                                this.baseTypeItem = ArrayUtils.getItemByField(this.baseTypeList, this.baseTypeId, 'baseTypeId');
                                // 当选择为ESIM时,将ICCID禁用,并把值传来,只读显示且自动查询出ICCID
                                if ((curValue && curValue === '2') || (this.baseTypeItem && this.baseTypeItem.autoSelectionFlag === 'Y')) {
                                    this.$('.js-new-res-info .js-iccid').attr('disabled', 'disabled');
                                    var param = {
                                        orgId: portal.appGlobal.get('orgId'),
                                        custId: this.orderItem.CUST_ID,
                                        baseTypeId: curValue
                                    };
                                    if (this.currentAccNbr && this.currentAccNbr.accNbrId) {
                                        param.accNbrId = this.currentAccNbr.accNbrId;
                                    }
                                    self.setEsimCardInfo(param);
                                    this.$('.div-iccid').show();
                                } else {
                                    // 当选择为Ordinary SIM时,解除ICCID禁用,调用sendIccidBoChange
                                    this.$('.js-new-res-info .js-iccid').removeAttr('disabled');
                                    this.$(":input[name='iccid']").val('').change();
                                    this.$('.div-iccid').hide();
                                    this.currentVirtualSimCard = null;
                                }
                                this.firstInitBaseTypeSource = false;
                            }.bind(context)
                        );
                    self.registerBaseTypeChange();
                    if (
                        portal.appGlobal.get('isBolLogin') &&
                        (fish.store.get('accountOpeningMethod') == 'ESIM' || fish.store.get('accountOpeningMethod') == 'boundleOffer') &&
                        this.baseTypeList.length > 1
                    ) {
                        this.$('.js-dup-sim-card-resource').combobox('value', '2');
                    }
                    if (this.currentAccNbr && this.currentAccNbr.isBindFlag == 'Y') {
                        // this.$(".js-dup-sim-card-resource").combobox('disable');
                        this.$baseTypeListPlace.combobox('disable');
                        this.$('.js-new-res-info .js-iccid').attr('disabled', 'disabled');
                        this.$('.div-iccid').show();
                    }

                    this.baseTypeItem = ArrayUtils.getItemByField(this.baseTypeList, this.baseTypeId, 'baseTypeId');
                    if (baseTypeId === '2' || (this.baseTypeItem && this.baseTypeItem.autoSelectionFlag === 'Y')) {
                        this.$('.js-iccid').attr('disabled', 'disabled');
                        this.$('.div-iccid').show();
                    }
                    if (this.orderItem.ICCID) {
                        this.$('.js-iccid')
                            .val(this.orderItem.ICCID)
                            .attr('simCardId', this.orderItem.SIM_CARD_ID)
                            .attr('oldiccid', this.orderItem.ICCID)
                            .change();
                    }
                }.bind(context)
            );
        }.bind(this);

        // 初始化选号组件 需要放在VAS树初始化之后
        this.initAccNbrComponent =
            context.initAccNbrComponent && typeof context.initAccNbrComponent === 'function'
                ? context.initAccNbrComponent.bind(context)
                : function () {
                      var self = this;
                      context.$selectNumberPop = new SelectNumberPopView(context.$(":input[name='accNbr']"), {
                          indepProdSpecId: context.subsBaseOrder.INDEP_PROD_SPEC_ID,
                          subsPlanId: context.subsBaseOrder.SUBS_PLAN_ID,
                          custId: context.orderItem.CUST_ID,
                          areaId: portal.appGlobal.get('areaId') ? portal.appGlobal.get('areaId') : 1,
                          spId: context.orderItem.SP_ID,
                          servType: context.orderItem.SERV_TYPE,
                          showPreAccNbr: context.showPreAccNbr,
                          $vasTreeView: context.$vasTreeView,
                          bindFlag: 'B',
                          $subsPlanView: context,
                          boAccess: {
                              selector: ":input[name='accNbr']",
                              boAccessName: 'orderItem',
                              boItemAccessName: 'accNbrId',
                              boPath: '/orderItem/accNbrId',
                              $currentView: context,
                              inputType: InputTypeDef.CUSTOM,
                              success: self.changeAccNbrSuccessHandler.bind(context),
                              fail: self.changeAccNbrFailHandler.bind(context)
                          },
                          // 卡号不正确,还原设置
                          enableResourceType: function () {
                              this.$('.js-new-res-info .js-dup-sim-card-resource').combobox('enable');
                              this.$('.js-new-res-info .js-iccid').removeAttr('disabled');
                              this.$('.js-new-res-info .js-iccid').val('');
                          }.bind(context)
                      });
                      if (context.showPreAccNbr) {
                          context.$(":input[name='accNbr']").attr('style', 'width: 100%; display: inline-block').change().resetElement();
                          context.$(":input[name='accNbr_prefix']").addClass('hide');
                      }

                      // 订单预览界面返回上一步，重新赋值号码、卡信息
                      if (context.orderItem && context.orderItem.ACC_NBR_ID) {
                          context.currentAccNbr = {
                              accNbrId: context.orderItem.ACC_NBR_ID,
                              accNbr: context.orderItem.ACC_NBR,
                              prefix: context.orderItem.PREFIX
                          };
                          OrderEntryAction.qryAccNbrDetailSic(
                              { accNbrId: context.currentAccNbr.accNbrId },
                              function (data) {
                                  if (data.z_d_r) {
                                      var accnbr = data.z_d_r[0];
                                      // 选择绑定卡时,resource_type Iccid禁用
                                      if (accnbr && accnbr.isBindingFlag == 'Y') {
                                          if (this.$baseTypeListPlace) {
                                              this.$baseTypeListPlace.combobox('disable');
                                          }

                                          this.currentAccNbr.isBindFlag = 'Y';
                                      }

                                      self.initNumberAgreementComponent({ accNbrClassId: accnbr.accNbrClassId }, true);
                                  }
                              }.bind(context)
                          );
                          context.$selectNumberPop.setAccNbr(this.currentAccNbr);
                          if (context.orderItem.autoChooseAccNbr) {
                              context.$selectNumberPop.setAutoChoose();
                          }
                          context
                              .$(":input[name='iccid']")
                              .val(context.orderItem.ICCID)
                              .attr('simCardId', context.orderItem.SIM_CARD_ID)
                              .attr('oldiccid', context.orderItem.ICCID)
                              .change();
                      }

                      if (context.orderItem.SERV_TYPE == 695 && context.configValue == '1') {
                          context.$('[name="accNbr"]').prop('disabled', true);
                          // this.$('.js-new-res-info .js-iccid').attr('disabled', 'disabled');
                      }
                      if (context.orderItem.SERV_TYPE == 695 && !context.orderItem.ACC_NBR_ID && context.configValue == '1') {
                          context.$selectNumberPop.onAutoBtnClick();
                      }
                  };

        /**
         * 选择号码成功
         */
        this.changeAccNbrSuccessHandler = function (data) {
            var self = this;
            if (portal.appGlobal.get('isBolLogin')) {
                $('.order-content .order-fixed-btn-list').css({
                    position: 'absolute',
                    width: '100%'
                });
                $('.pos-order-btn-list').css({
                    position: 'absolute',
                    width: '100%'
                });
                var tips = '';
                if (fish.store.get('accountOpeningMethod') == 'mobileOffer') {
                    tips = IntroDef.MOBILE_OFFER_STEP5;
                } else if (fish.store.get('accountOpeningMethod') == 'ESIM') {
                    tips = IntroDef.ESIM_OFFER_STEP4;
                } else if (fish.store.get('accountOpeningMethod') == 'boundleOffer') {
                    if (context.parentView.setIntroEle) {
                        context.parentView.setIntroEle(3);
                    }
                    return;
                }
                fish.trigger(IntroDef.ADD_INTRO, {
                    element: '#next-btn-intro',
                    tipsContents: tips,
                    position: 'top'
                });
            }
            self.initNumberAgreementComponent(
                {
                    accNbrClassId: context.$(":input[name='accNbr']").attr('accNbrClassId')
                },
                false
            );
            context.currentAccNbr = context.$selectNumberPop.getAccNbr();
            // 回填卡信息
            var dirtyList = data.dirtyList;
            if (!fish.isEmpty(dirtyList)) {
                var iccidObj = fish.findWhere(dirtyList, {
                    boAccessName: 'orderItem',
                    boItemAccessName: 'iccid'
                });

                if (iccidObj) {
                    context.$(":input[name='iccid']").val(iccidObj.value).change(); // 无需触发事件
                    context.$(":input[name='iccid']").resetElement();
                }
            }

            /**
             * 绑定卡 未绑定卡 (禁用resource type iccid)
             *
             *
             */
            if (context.currentAccNbr && context.currentAccNbr.accNbrId && !(dirtyList == null || dirtyList == '') && iccidObj && iccidObj.value) {
                OrderEntryAction.qryAccNbrDetailSic(
                    { accNbrId: context.currentAccNbr.accNbrId },
                    function (data) {
                        if (data.z_d_r) {
                            var accnbr = data.z_d_r[0];
                            // 选择绑定卡时,resource_type Iccid禁用
                            if (accnbr && accnbr.isBindingFlag == 'Y') {
                                this.$('.js-new-res-info .js-dup-sim-card-resource').combobox('disable');
                                this.$('.js-new-res-info .js-dup-sim-card-resource').combobox('value', '1');

                                this.$('.js-new-res-info .js-iccid').attr('disabled', 'disabled');
                                this.currentAccNbr.isBindFlag = 'Y';
                            } else {
                                var curValue = this.$('.js-dup-sim-card-resource').combobox('value');
                                var item = ArrayUtils.getItemByField(this.baseTypeList, curValue, 'baseTypeId');
                                if ((curValue && curValue === '2') || (item && item.autoSelectionFlag === 'Y')) {
                                    this.$('.js-new-res-info .js-iccid').attr('disabled', 'disabled');

                                    // 解决问题：ESIM卡开户界面选的ICCID和预览QRcode界面显示的ICCID不一致
                                    var iccidVal = this.$('.js-new-res-info .js-iccid').val();
                                    if (iccidVal && iccidVal.length > 0) {
                                        return;
                                    }
                                    var qryParam = {
                                        hlrId: accnbr.hlrId,
                                        accNbrId: accnbr.accNbrId,
                                        orgId: portal.appGlobal.get('orgId'),
                                        custId: this.orderItem.CUST_ID,
                                        baseTypeId: curValue
                                    };
                                    self.setEsimCardInfo(qryParam);
                                } else {
                                    this.$('.js-new-res-info .js-dup-sim-card-resource').combobox('enable');
                                    if (curValue && curValue != '2') {
                                        this.$('.js-new-res-info .js-iccid').removeAttr('disabled');
                                    }
                                }
                            }
                        }
                    }.bind(context)
                );
            } else if (context.baseTypeId !== '2') {
                context.$(":input[name='iccid']").val('').change();
                context.$('.js-new-res-info .js-dup-sim-card-resource').combobox('enable');
                context.$('.js-new-res-info .js-iccid').removeAttr('disabled');
            }
        };

        this.initNumberAgreementComponent = function (paramData, isBack) {
            var self = this;
            if (!isBack) {
                AgreementUtils.sendBOResetAgreement(
                    context.orderItem,
                    context,
                    function (item) {
                        paramData.orderItem = item;
                        self.randerNumberAgreementComponent(paramData);
                    }.bind(context),
                    AgreementDef.AGREEMENT_TYPE_GOLDEN_NUMBER
                );
            } else {
                self.randerNumberAgreementComponent(paramData);
            }
        };

        this.randerNumberAgreementComponent = function (paramData) {
            paramData.subsPlanId = context.subsBaseOrder.SUBS_PLAN_ID;
            paramData.custId = context.orderItem.CUST_ID;
            paramData.custOrderId = context.orderItem.CUST_ORDER_ID;
            paramData.orderItemId = context.orderItem.ORDER_ITEM_ID;
            paramData.orderItem = context.orderItem;
            paramData.$currentView = context;
            paramData.hiddenElement = function () {
                this.$(":input[name='numberAgreement']").parent().parent().parent().css('display', 'none');
            }.bind(context);
            paramData.showElement = function () {
                this.$(":input[name='numberAgreement']").parent().parent().parent().css('display', 'block');
            }.bind(context);
            paramData.requiredHander = function () {
                this.$(":input[name='numberAgreement']").attr('data-rule', 'required;');
                this.$(":input[name='numberAgreement']").parent().parent().addClass('required');
            }.bind(context);

            new NumberAgreementComponent(context.$(":input[name='numberAgreement']"), paramData);
        };

        this.changeAccNbrFailHandler = function () {
            // 选号失败清空文本框
            context.$(":input[name='accNbr']").val('').attr('newAccNbr', '').attr('accNbrId', '').attr('prefix', '').attr('accNbr', '');
            context.$('.js-new-res-info .js-dup-sim-card-resource').combobox('enable');
            context.$('.js-new-res-info .js-iccid').removeAttr('disabled');
        };

        this.setEsimCardInfo = function (qryParam) {
            qryParam.servTypes = [context.orderItem.SERV_TYPE];
            qryParam.offerIds = [context.orderItem.SUBS_PLAN_ID];
            qryParam.prodIds = [context.orderItem.OFFER_ID];
            SimCardMgrAction.qrySingleESimCard(
                qryParam,
                function (data) {
                    this.$('.js-new-res-info .js-iccid').val(data.iccid);
                    this.$(":input[name='iccid']").val(data.iccid).attr('simCardId', data.simCardId).change();
                    this.currentVirtualSimCard = data;
                    context.$selectNumberPop.setHlrId(data.hlrId);
                }.bind(context),
                function (data) {
                    fish.warn(fish.escape(data.Msg));
                    this.$baseTypeListPlace.combobox('value', '1');
                    this.currentVirtualSimCard = null;
                }.bind(context)
            );
        };

        this.registerBaseTypeChange = function () {
            var boAccess = {
                selector: ":input[name='baseTypeName']",
                boAccessName: 'orderItem',
                boItemAccessName: 'simTypeId',
                $currentView: context,
                inputType: InputTypeDef.COMBOBOX
            };
            if (boAccess && boAccess.$currentView) {
                Controller.getInstance().registerChange(boAccess.$currentView, boAccess);
            }
        };

        // 清空合同手机号
        this.onClearContractPhone = function () {
            var self = this;
            if (context.$('.js-contract-phone').val()) {
                // 删除操作，从内存中去掉
                if (context.selectedContractPhoneItem) {
                    self.onRemoveGoodsOrderBoChange([context.selectedContractPhoneItem]);
                    context.selectedContractPhoneItem = null;
                    context.$('.js-contract-phone').val('');
                    self.showClearContractPhoneIcon(false);
                }
            }
        };

        /**
         * 输入SN后回车
         * @param e
         */
        this.onMatchGoodsSN = function (e) {
            var self = this;
            // 查询实物
            if (e.keyCode == 13) {
                // 回车之后自动失去焦点，防止触发多次回车事件
                context.$(e.target).blur();
                var goodsSn = $.trim(context.$(e.target).val());
                if (!self.validGoodsSn(goodsSn)) {
                    context.$(e.target).val('');
                    return;
                }

                var queryParam = {
                    goodsSn: goodsSn,
                    orgId: portal.appGlobal.get('orgId')
                };
                GoodsSalesAction.matchHandSetByGoodsSn(
                    queryParam,
                    function (result) {
                        var modelIdList = result.modelIdList;
                        if (fish.isEmpty(modelIdList)) {
                            this.$('.js-goods-sn').val('');
                            fish.info(I18N.CAN_NOT_FIND_GOODS_BY_SN);
                        } else {
                            var modelIdStr;
                            if (fish.isArray(modelIdList)) {
                                modelIdStr = modelIdList.join();
                            } else {
                                modelIdStr = modelIdList;
                            }

                            self.qryGoodsOfferList(
                                modelIdStr,
                                function (goodsList) {
                                    if (fish.isEmpty(goodsList)) {
                                        this.$('.js-goods-sn').val('');
                                        fish.info(I18N.CAN_NOT_FIND_GOODS_BY_SN);
                                    } else if (goodsList.length > 1) {
                                        // 打开弹窗让客户选Model
                                        self.openSelModelPopup(goodsList);
                                    } else if (fish.isEmpty(this.selectedContractPhoneItem)) {
                                        goodsList[0].goodsSn = goodsSn;
                                        self.addGoods(goodsList[0]);
                                    } else if (this.selectedContractPhoneItem.goodsSn !== goodsSn) {
                                        var goodsItem = this.selectedContractPhoneItem;
                                        goodsItem.goodsSn = goodsSn;

                                        self.onRemoveGoodsOrderBoChange([this.selectedContractPhoneItem]);
                                        self.addGoods(goodsItem);
                                    }
                                }.bind(context)
                            );
                        }
                    }.bind(context)
                );
            }
        };

        this.openSelModelPopup = function () {};

        this.validGoodsSn = function (goodsSn) {
            if (!goodsSn) {
                return false;
            }
            if (context.isStockManage === 'N') {
                return false;
            }
            var accNbrItem = context.$("input[name='accNbr']").val();
            if (!accNbrItem) {
                return false;
            }

            return true;
        };

        /**
         * 添加实物
         *
         * @param goods
         */
        this.addGoods = function (goods) {
            var self = this;
            if (context.currentAccNbr) {
                goods.accNbr = context.currentAccNbr.accNbr;
                goods.accNbrId = context.currentAccNbr.accNbrId;
            }
            goods.cnt = 1;
            goods.salePrice = goods.salesPrice; // BO中的字段和查询出来的字段不一致 作个转换
            goods.saleListPrice = goods.listPrice;
            if (!goods.operationType) {
                goods.operationType = OperationTypeDef.NEW;
            }
            if (!goods.goodsOrderBoKey) {
                goods.goodsOrderBoKey = fish.getUUID();
            }
            goods.orgId = portal.appGlobal.get('orgId');
            if (context.refreshBo) {
                self.fillupGoodsAttrList(
                    goods.offerId,
                    function (goodsAttrList) {
                        goods.goodsAttrList = goodsAttrList;
                        self.onAddGoodsOrderBoChange([goods]);
                    }.bind(context)
                );
            } else {
                self.onAddGoodsOrderBoChange([goods]);
            }
        };

        this.fillupGoodsAttrList = function (offerId, callback) {
            var req = {};
            req.requestParam = {
                subsPlanId: context.subsPlanId,
                offerId: offerId
            };
            OfferQryAction.qryOfferAttrList(
                req,
                function (data) {
                    var attrList = data.propertyList || [];
                    var goodsAttrList = [];
                    fish.each(attrList, function (item) {
                        if (item.defaultValue) {
                            var attrValue = fish.find(item.attrValueList, function (attrValueItem) {
                                return attrValueItem.value == item.defaultValue;
                            });
                            goodsAttrList.push({
                                attrId: item.attrId,
                                attrValue: item.defaultValue,
                                operationType: OperationTypeDef.NEW,
                                valueMark: attrValue.valueMark
                            });
                        }
                    });
                    callback(goodsAttrList);
                }.bind(context)
            );
        };

        this.onAddGoodsOrderBoChange = function (goodsList) {
            var self = this;
            var dataInputs = new DataInputs();
            fish.each(
                goodsList,
                function (goods) {
                    goods.orderItemId = context.orderItem.ORDER_ITEM_ID;
                    dataInputs.push(
                        new DataInput({
                            operationType: OperationTypeDef.NEW,
                            boAccessName: self.getGoodsOrderBoAccessName(),
                            bo: true,
                            value: goods
                        })
                    );
                    if (self.isBatch) {
                        goods.BO_ACCESS_NAME_IDENTIFY = 'BATCH_GOODS_ORDER';
                    }
                    fish.each(
                        goods.goodsAttrList,
                        function (goodsAttr) {
                            dataInputs.push(
                                new DataInput({
                                    operationType: OperationTypeDef.NEW,
                                    boAccessName: self.getGoodsOrderAttrAccessName(),
                                    bo: true,
                                    parentKeyValue: goods.goodsOrderBoKey,
                                    value: goodsAttr
                                })
                            );
                        }.bind(context)
                    );
                }.bind(context)
            );
            var callbackArgs = {
                goodsOrderList: goodsList
            };
            Controller.getInstance().sendBoChanges(
                self.commonDataId,
                dataInputs,
                context,
                callbackArgs,
                function (_, callbackArgs) {
                    var goodsOrder = callbackArgs.goodsOrderList[0];
                    if (goodsOrder.goodsTypeId === 'S') {
                        self.fillUpSimModelName(goodsOrder);
                    } else {
                        self.fillUpContractPhoneName(goodsOrder);
                    }
                }.bind(context)
            );
        };

        this.fillUpSimModelName = function (goodsOrder) {
            var self = this;
            context.$('.js-sim-model').val(goodsOrder.modelName);
            self.showClearSimModelIcon(true);
            self.selectedSimModelItem = goodsOrder;
        };

        this.fillUpContractPhoneName = function (goodsOrder) {
            var self = this;
            context.$('.js-contract-phone').val(goodsOrder.offerName);
            self.showClearContractPhoneIcon(true);
            self.selectedContractPhoneItem = goodsOrder;
        };

        /**
         * 刷新实物BO
         * @param goodsOrderList
         * @param operationType
         */
        this.onRemoveGoodsOrderBoChange = function (goodsOrderList) {
            var self = this;
            var dataInputs = new DataInputs();
            fish.each(
                goodsOrderList,
                function (goodsOrder) {
                    dataInputs.push(
                        new DataInput({
                            operationType: OperationTypeDef.CANCEL,
                            boAccessName: self.getGoodsOrderBoAccessName(),
                            bo: true,
                            value: goodsOrder
                        })
                    );
                }.bind(context)
            );
            var callbackArgs = {
                goodsOrderList: goodsOrderList
            };
            Controller.getInstance().sendBoChanges(
                context.commonDataId,
                dataInputs,
                context,
                callbackArgs,
                // bind(context)
                function (_, callbackArgs) {
                    var goodsOrder = callbackArgs.goodsOrderList[0];
                    if (goodsOrder.goodsTypeId !== 'S') {
                        this.$('.js-goods-sn').removeClass('required');
                    }
                }.bind(context)
            );
        };

        /**
         * 选择实物
         */
        this.onSelectGoodsClick = function () {
            var accNbrItem = context.$("input[name='accNbr']").val();
            if (!accNbrItem) {
                fish.info(I18N.HINT_NOT_SELECT_SERVICE_NUMBER_FIRST);
            }
        };

        this.getGoodsOrderBoAccessName = function () {
            return this.isBatch ? 'batchGoodsOrder' : 'goodsOrder';
        };

        this.getGoodsOrderAttrAccessName = function () {
            return this.isBatch ? 'batchGoodsOrderAttr' : 'goodsOrderAttr';
        };

        this.showClearSimModelIcon = function (isShow) {
            if (isShow) {
                context.$('.js-clear-sim-model').show();
            } else {
                context.$('.js-clear-sim-model').hide();
            }
        };

        this.showClearContractPhoneIcon = function (isShow) {
            if (isShow) {
                context.$('.js-clear-contract-phone').show();
            } else {
                context.$('.js-clear-contract-phone').hide();
            }
        };

        this.onIccidChange = function () {
            var iccid = $.trim(context.$(":input[name='iccid']").val());

            if (iccid) {
                var dataInput = new DataInput({
                    boAccessName: 'custOrder',
                    bo: true,
                    boItemAccessName: 'custOrderId'
                });
                dataInput.set('value', {
                    CUST_ORDER_ID: context.orderItem.custOrderId
                });

                Controller.getInstance().fetchData(
                    null,
                    dataInput,
                    context,
                    function (result) {
                        var custOrder = result.dirtyList[0].value;
                        var logModel = new TrackPointLogModel();
                        var items = custOrder.ORDER_ITEM;
                        var currentItem = this.orderItem;
                        var index = 0;
                        for (var i = 0; i < items.length; i++) {
                            if (currentItem.ORDER_ITEM_ID == items[i].ORDER_ITEM_ID) {
                                index = i;
                                custOrder.ORDER_ITEM[i].ICCID = iccid;
                                this.orderItem.ICCID = iccid;
                                break;
                            }
                        }
                        for (var i = 0; i < items.length; i++) {
                            // 当一个订单有多个order_item时，输入相同的ICCID需要被拦截，sic的校验是根据cust_order_nbr的而不是order_item
                            if (iccid == items[i].ICCID && i != index) {
                                custOrder.ORDER_ITEM[index].ICCID = '';
                                this.orderItem.ICCID = '';
                                fish.error('Duplicate ICCID in the order.');
                                // this.$(":input[name='iccid']").val("");
                                return;
                            }
                        }
                        logModel.set('operCode', 'OE001000');
                        logModel.set('stepCode', 'OE001130');
                        CommAction.recordPageTrackPointLog(logModel);

                        this.$(":input[name='iccid']").resetElement();
                    }.bind(context)
                );
            }
        };

        this.onAccNbrChange = function () {
            var accNbrInput = $.trim(context.$(":input[name='accNbr']").val());
            if (accNbrInput) {
                var logModel = new TrackPointLogModel();
                logModel.set('operCode', 'OE001000');
                logModel.set('stepCode', 'OE001040');
                CommAction.recordPageTrackPointLog(logModel);
                var param = {
                    ACC_NBR: accNbrInput,
                    NEED_DEFAULT_PREFIX: true
                };
                AccNbrAction.qryAccNbrDetailByAccNbr(
                    param,
                    function (data) {
                        this.accNbrDetail = data.accNbrDto;
                    }.bind(context)
                );
            }

            var accNbr = $.trim(context.$(":input[name='accNbr']").attr('newAccNbr'));
            if (accNbr) {
                context.$(":input[name='accNbr']").resetElement();
            }
        };

        // 初始化 portIn
        this.initPortIn = function () {
            var self = this;
            if (context.orderItem.NP && context.orderItem.NP.DONOR_OPERATOR) {
                context.$('.js-check-portIn').icheck('check');
                self.onClickPortIn('Init');
            }
        };

        // listenTo 方法
        this.listenFnction = function ($view) {
            this.listenTo($view, 'finishSelectPortInType', function (data) {
                if (data.portInWithTempNbr) {
                    this.trigger(this.portInChangeEvent, false);
                    this.$('.js-acc-nbr').show();
                    this.$("input[name='accNbr']").removeAttr('data-rule-ignore', true);
                } else if (data.portInNoTempNbr) {
                    this.$("[name='accNbr']").attr('disabled', false);
                    this.trigger(this.portInChangeEvent, false);
                    this.$('.js-acc-nbr').hide();
                    this.$("input[name='accNbr']").attr('data-rule-ignore', true);
                } else if (data.cancelPortIn) {
                    this.$('.js-acc-nbr').show();
                    this.$("input[name='accNbr']").removeAttr('data-rule-ignore', true);
                    this.trigger(this.portInChangeEvent, true);
                }
                this.$("input[name='accNbr']").val('');
            });
        };

        // 点击 checkbox 的事件
        this.onClickPortIn = function (e) {
            var self = this;
            var logModel = new TrackPointLogModel();
            logModel.set('operCode', 'OE001000');
            logModel.set('stepCode', 'OE001030');
            CommAction.recordPageTrackPointLog(logModel);

            var viewOption = fish.extend({}, context.options);
            viewOption.attrId = context.attrId;
            viewOption.attrList = context.attrList;
            if (context.$("[name='portIn']").icheck('isChecked')) {
                self.privValidate();
                self.portInHide();
                if (!context.hasPriv) {
                    context.$("[name='portIn']").icheck('uncheck');
                    fish.info('No permission to Port In.');
                    if (this.pageDesignerFlag) {
                        context.resetCheckboxPosition();
                    }
                    return;
                }

                context.$('.js-port-in-number-parent').hide();
                context.$('.js-acc-nbr').hide();
                context.$("input[name='accNbr']").attr('data-rule-ignore', true);

                context.$("input[name='accNbr']").val('');

                if (e !== 'Init') {
                    context.$(":input[name='iccid']").val('');
                }
                if (context.baseTypeId !== '2') {
                    context.$(":input[name='iccid']").attr('disabled', false);
                }

                context.$('.js-port-in-info').show();
                // back 场景下listenTo无法监听到
                if (context.attrList && context.attrList.length > 0) {
                    if (context.orderItem.PORTIN_FLAG != null && context.orderItem.PORTIN_FLAG == 'Y') {
                        context.$('.js-acc-nbr').hide();
                        context.$("input[name='accNbr']").attr('data-rule-ignore', true);
                    } else {
                        context.$('.js-acc-nbr').show();
                        context.$("input[name='accNbr']").removeAttr('data-rule-ignore', true);
                    }
                }
                if (this.pageDesignerFlag) {
                    // 增加 page-designer 的监听事件
                    var newOptions = fish.extend({}, viewOption);
                    newOptions.el = $('.js-port-in-info');
                    var $GSMNewPortInInfoView = new GSMNewPortInInfoView(newOptions);
                    self.listenFnction.bind($GSMNewPortInInfoView)($GSMNewPortInInfoView);
                    $GSMNewPortInInfoView.render();
                    context.resetCheckboxPosition();
                } else {
                    context.requireView({
                        url: 'crm/modules/pos/flowpage/business/newconnection/views/GSMNewPortInInfoView',
                        selector: '.js-port-in-info',
                        viewOption: viewOption,
                        callback: function ($view) {
                            this.$gsmNewPortInInfoView = $view;
                            self.listenFnction.bind(this)($view);
                        }.bind(context)
                    });
                }
            } else {
                context.$('.js-port-in-hide').show();
                context.$('.js-port-in-info').hide();
                // 2520693 清空页面必填项校验, 清空portInFlag标识
                context.$('.js-port-in-info').empty();
                self.clearPortInFlag();
                context.$('.js-port-in-number-parent').show();
                context.$('.js-acc-nbr').show();
                context.$("input[name='accNbr']").removeAttr('data-rule-ignore', true);
            }
        };

        this.privValidate = function () {
            context.hasPriv = false;
            var param = {};
            param.userId = portal.appGlobal.get('userId');
            param.dataPrivCode = 'CAN_DO_MNP_PORT_IN';
            SubsAction.qryUserDataPrivByDataPrivCodeAndUserId(param, function (result) {
                if (result.privList && result.privList.length > 0) {
                    fish.each(result.privList, function (i) {
                        if (i.id === 'Y') {
                            context.hasPriv = true;
                        }
                    });
                }
            });
        };

        this.clearPortInFlag = function () {
            var dataInput = new DataInput();
            dataInput.set('boAccessName', 'orderItem');
            dataInput.set('boItemAccessName', 'portinFlag');
            dataInput.set('value', '');
            Controller.getInstance().sendSingleChange(
                context.commonDataId,
                dataInput,
                context,
                null,
                null,
                function (data) {
                    fish.each(data.errors, function (error) {
                        fish.error(error.message);
                    });
                }.bind(context)
            );

            var dataInput2 = new DataInput();
            dataInput2.set('boAccessName', 'np');
            dataInput2.set('operationType', OperationTypeDef.CANCEL);
            dataInput2.set('value', '');
            Controller.getInstance().sendSingleBoChange(
                context.commonDataId,
                dataInput2,
                context,
                null,
                null,
                function (data) {
                    fish.each(data.errors, function (error) {
                        fish.error(error.message);
                    });
                }.bind(context)
            );

            var dataInput3 = new DataInput();
            dataInput3.set('boAccessName', 'orderItemAttr');
            dataInput3.set('operationType', OperationTypeDef.CANCEL);
            dataInput3.set('value', {
                attrId: context.attrId,
                attrValue: '0',
                orderItemId: context.orderItem.ORDER_ITEM_ID,
                spId: portal.appGlobal.get('spId')
            });
            Controller.getInstance().sendSingleBoChange(
                context.commonDataId,
                dataInput3,
                context,
                null,
                null,
                function (data) {
                    fish.each(data.errors, function (error) {
                        fish.error(error.message);
                    });
                }.bind(context)
            );
        };

        this.portInHide = function () {
            context.$('.js-port-in-hide').hide();
            fish.trigger('HANDSET_AGREEMENT_UNSUBSCRIBE' + context.pageUuid, {});
            fish.trigger('DEVICE_UNSUBSCRIBE' + context.pageUuid, {});
        };

        // 初始化是否支持port in
        this.initMnpBusiParam = function () {
            var self = this;
            PostNpDbAction.qryNpOrderTypeList(
                { npOrderTypeId: 3 },
                function (result) {
                    if (!fish.isEmpty(result)) {
                        fish.each(
                            result,
                            function (item) {
                                if (ServTypeDef.GSM == item.servType) {
                                    if (item.paidFlag) {
                                        if (self.acct.POSTPAID == item.paidFlag && this.portinflag) {
                                            this.$('.js-portIn').show();
                                        } else {
                                            this.$('.js-portIn').hide();
                                        }
                                    } else if (this.portinflag) {
                                        // OPER_TYPE_SERV_TYPE.PAID_FLAG为空,表示预后付费都支持
                                        this.$('.js-portIn').show();
                                    }
                                }
                            }.bind(context)
                        );
                    }
                    self.initPortInTypeAttr();
                }.bind(context)
            );
        };

        this.initPortInTypeAttr = function () {
            var self = this;
            PostNpDbAction.qryAttrByAttrCode(
                { attrCode: 'EXP_PORT_IN_TYPE' },
                function (result) {
                    if (result) {
                        this.attrId = result[0].attrId;
                        self.getMnpIsNeedTempNbrBusiParam();
                    }
                }.bind(context)
            );
        };

        this.getMnpIsNeedTempNbrBusiParam = function () {
            var self = this;
            var dataInput = new DataInput();
            dataInput.set('boAccessName', 'orderItemAttr');
            dataInput.set('boItemAccessName', 'attrId');
            dataInput.set('bo', true);
            dataInput.set('value', {
                attrId: context.attrId,
                orderItemId: context.orderItem.ORDER_ITEM_ID
            });
            Controller.getInstance().fetchData(
                context.commonDataId,
                dataInput,
                context,
                function (result) {
                    var dirtyList = result.dirtyList || [];
                    if (dirtyList.length > 0) {
                        var attrList = dirtyList[0].value || [];
                        this.attrList = attrList;
                        if (attrList.length > 0) {
                            if (
                                this.orderItem.PORTIN_FLAG != null &&
                                this.orderItem.PORTIN_FLAG == 'Y' &&
                                (attrList[0].ATTR_VALUE == '1' || attrList[0].ATTR_VALUE == '0')
                            ) {
                                this.$("[name='portIn']").icheck('check');
                                self.onClickPortIn('Init');
                                this.trigger(this.portInChangeEvent, false);
                            } else {
                                this.trigger(this.portInChangeEvent, true);
                            }
                        }
                    }
                }.bind(context)
            );
        };

        this.create = function () {
            return this;
        }.bind(this)();
    };
});
