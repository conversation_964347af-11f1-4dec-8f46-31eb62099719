/**
 * 客户订阅者信息视图，用于展示和处理客户的订阅者相关数据
 * @class SubscriberView
 * @extends fish.View
 * @property {Object} contactChannelId - 接触渠道ID
 * @property {Object} custBaseDetailDto - 客户基本信息DTO对象
 * @property {string} pageUuid - 页面唯一标识符
 * @property {boolean} readcardflag - 读卡标志
 * @property {boolean} showPreAccNbr - 是否显示预占账号
 * @event clickOrderNow - 点击立即订购按钮时触发
 * @event refreshSubsCnt - 刷新订阅者数量时触发
 */
define([
    'hbs!crm/modules/pos/cust/templates/SubscriberTpl.hbs',
    'i18n!crm/modules/pos/i18n/pos'
], function (template, i18nData) {
    return fish.View.extend({
        el: false,

        template: template,

        serialize: i18nData,

        events: {
            'click .js-state-menu li': 'onProdStateClick'
        },

        initialize: function (options) {
            this.options = options;
            this.custBaseDetailDto = this.options.custBaseDetailDto;
            this.readcardflag = this.options.readcardflag;
            this.pageUuid = this.options.pageUuid;
            this.getCustName = ''; // 客户姓名值
            this.contactChannelId = this.options.contactChannelId;
            this.showPreAccNbr = this.options.showPreAccNbr;
        },

        afterRender: function () {
            // 有无数据的时候白色背景平铺，距离下方8px
            var height = $('.js-order-content-view').parent().parent().height();
            var currentHeight = $('.js-tab-order-change').height();
            if (currentHeight < height - 166) {
                $('.js-tab-order-change').css('height', height - 166);
            }

            this.initializedata(this.custBaseDetailDto, this.readcardflag);
        },

        reloadSubsList: function () {
            this.requireView({
                selector: '.js-subs-list',
                url: 'crm/modules/pos/cust/views/SubscriberListView',
                viewOption: {
                    custBaseDetailDto: this.custBaseDetailDto,
                    readcardflag: this.readcardflag,
                    pageUuid: this.pageUuid,
                    contactChannelId: this.contactChannelId,
                    showPreAccNbr: this.showPreAccNbr
                },
                callback: function () {
                    this.listenTo(
                        this.getView('.js-subs-list'),
                        'clickOrderNow',
                        function (param) {
                            this.trigger('clickOrderNow', param);
                        }.bind(this)
                    );
                    this.listenTo(
                        this.getView('.js-subs-list'),
                        'refreshSubsCnt',
                        function (param) {
                            this.trigger('refreshSubsCnt', param);
                        }.bind(this)
                    );
                }.bind(this)
            });
        },

        initializedata: function (custBaseDetailDto, readcardflag) {
            if (custBaseDetailDto == null) {
                $('#dataChange').html('No data. Please enter customer first.');
                return;
            }
            this.custBaseDetailDto = custBaseDetailDto;
            this.getCustName = this.custBaseDetailDto.custName;
            this.readcardflag = readcardflag;
            // 如果当前客户已登录且没用数据则显示No data
            if (this.getCustName) {
                $('#dataChange').html('No data');
            } else {
                $('#dataChange').html('No data. Please enter customer first.');
            }

            this.reloadSubsList();
        },
        resize: function () {
            // 没有数据的时候白色背景平铺，距离下方8px
            // if(!this.custBaseDetailDto) {
            //   var height = $('.js-order-content-view').parent().parent().height();
            //   var currentHeight = $('.js-tab-order-change').height();
            //
            //   if (currentHeight < height - 166) {
            //     $('.js-tab-order-change').css('height', height - 166);
            //   }
            // }else {
            //   $('.js-tab-order-change').css('height', "");
            // }
        }
    });
});
