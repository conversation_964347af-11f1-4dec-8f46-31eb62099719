/**
 * @fileoverview AccountDetail组件 - 账户详情管理组件
 * @description 用于显示和管理客户账户详情信息的标准化组件，支持账户列表展示、选择、添加等功能
 * <AUTHOR> Team
 * @since 2025-08-01
 * @version 2.0.0
 *
 * @component AccountDetail
 * @description 账户详情管理组件，提供账户列表展示、选择、添加等核心功能
 *
 * @props {Object} options - 组件初始化参数
 * @props {string} options.custId - 客户ID，必需参数，用于查询客户下的账户信息
 * @props {string} [options.acctId] - 账户ID，可选参数，用于默认选中指定账户
 * @props {string} [options.postpaid] - 后付费标识，可选参数，'Y'表示后付费账户
 * @props {boolean} [options.hideEle=false] - 是否隐藏UI元素，可选参数，用于弹窗模式
 * @props {string} [options.exclusiveFlag] - 排他标识，可选参数，用于过滤特定账户
 * @props {string} [options.custType] - 客户类型，可选参数，影响账户添加逻辑
 * @props {boolean} [options.orderAcctQry] - 订单账户查询标识，可选参数，用于过滤员工账户
 *
 * @events
 * @event accountSelected - 账户选择事件，当用户选择账户行时触发
 * @param {Object} data - 事件数据
 * @param {Object} data.selectedData - 选中的账户数据
 * @param {boolean} data.limitFlag - 信控标识
 * @param {Array} data.gridList - 账户列表数据
 * @param {string} data.postpaid - 后付费标识
 *
 * @event accountAdded - 账户添加事件，当成功添加新账户时触发
 * @param {Object} data - 事件数据
 * @param {string} data.acctId - 新添加的账户ID
 * @param {Object} data.accountData - 新账户的详细数据
 *
 * @event dataLoaded - 数据加载完成事件，当账户列表加载完成时触发
 * @param {Object} data - 事件数据
 * @param {Array} data.accountList - 加载的账户列表
 * @param {number} data.count - 账户数量
 *
 * @event accountCountChanged - 账户数量变更事件，当账户数量发生变化时触发
 * @param {Object} data - 事件数据
 * @param {number} data.count - 当前账户数量
 *
 * @event error - 错误事件，当组件发生错误时触发
 * @param {Object} data - 事件数据
 * @param {string} data.type - 错误类型
 * @param {string} data.message - 错误消息
 * @param {Error} data.error - 错误对象
 *
 * @methods
 * @method query - 查询账户列表数据
 * @param {string} [rowid] - 要选中的账户ID
 * @param {boolean} [status] - 是否查询过期账户
 *
 * @method reloadExpireAccount - 重新加载过期账户
 * @param {boolean} status - 是否显示过期账户
 *
 * @method rowSelectCallback - 账户行选择回调
 * @param {Event} e - 事件对象
 * @param {string} rowid - 选中的行ID
 * @param {boolean} state - 选择状态
 *
 * @method hideEle - 隐藏UI元素，用于弹窗模式
 *
 * @method resize - 调整组件大小
 * @param {number} delta - 高度变化量
 *
 * @method cleanup - 清理组件资源
 *
 * @example
 * // 基本使用 - 显示客户账户列表
 * this.requireView({
 *     url: 'crm/standardizedcomponents/um/components/AccountDetail',
 *     selector: '.account-detail-container',
 *     viewOption: {
 *         custId: '12345',
 *         postpaid: 'Y'
 *     },
 *     callback: function(view) {
 *         // 监听账户选择事件
 *         view.on('accountSelected', function(eventData) {
 *             console.log('Selected account:', eventData.data.selectedData);
 *         });
 *     }
 * });
 *
 * @example
 * // 弹窗模式使用
 * this.requireView({
 *     url: 'crm/standardizedcomponents/um/components/AccountDetail',
 *     selector: '.modal-body',
 *     viewOption: {
 *         custId: '12345',
 *         acctId: '67890',  // 默认选中账户
 *         hideEle: true,    // 隐藏弹窗相关元素
 *         exclusiveFlag: 'Y'
 *     }
 * });
 *
 * @example
 * // 监听所有事件
 * accountDetailView.on('accountSelected', this.handleAccountSelected.bind(this));
 * accountDetailView.on('accountAdded', this.handleAccountAdded.bind(this));
 * accountDetailView.on('dataLoaded', this.handleDataLoaded.bind(this));
 * accountDetailView.on('error', this.handleError.bind(this));
 */
define([
    'hbs!cvbs/modules/um/addAccount/templates/AccountDetail.hbs',
    'i18n!cvbs/modules/um/addAccount/i18n/addAccount',
    'cvbs/modules/um/addAccount/actions/addAccountAction'
    // "css!cvbs/modules/cust/newBusinessAccout/css/addBusinessAccount.css",
], function (Tpl, I18N, Action) {
    return portal.BaseView.extend({
        template: Tpl,
        serialize: I18N,
        events: {
            'click .js-ok': 'onOkClick',
            'click .js-cancel': 'onCancel',
            'click .js-account-add': 'onAddClick'
        },
        initialize: function (options) {
            this.options = options || {};
            this.custId = this.options.custId;
            this.colModel = [
                {
                    name: 'acctNbr',
                    label: 'Account Number',
                    sortable: true,
                    sorttype: 'int'
                },
                {
                    name: 'acctName',
                    label: 'Account Name',
                    sortable: true,
                    sorttype: 'int',
                    search: true
                },
                {
                    name: 'acctId',
                    hidden: true,
                    key: true
                },
                {
                    name: 'ccAcctCreditLimit',
                    hidden: true
                },
                {
                    name: 'billFlag',
                    hidden: true
                },
                {
                    name: 'acctCatgId',
                    hidden: true
                },
                {
                    name: 'billingCycleTypeId',
                    hidden: true
                },
                {
                    name: 'paymentMethodId',
                    hidden: true
                },
                {
                    name: 'deliverMethod',
                    hidden: true
                },
                {
                    name: 'email',
                    hidden: true
                },
                {
                    name: 'smsNbro',
                    hidden: true
                },
                {
                    name: 'smsNbrt',
                    hidden: true
                },
                {
                    name: 'faxNbro',
                    hidden: true
                },
                {
                    name: 'faxNbrt',
                    hidden: true
                },
                {
                    name: 'detailInfo',
                    hidden: true
                },
                {
                    name: 'custBillDeliveryInfoId',
                    hidden: true
                },
                {
                    name: 'jomPayAcctNbr',
                    hidden: true
                },
                {
                    name: 'billLang',
                    hidden: true
                },
                {
                    name: '',
                    label: '',
                    formatter: function (cellval, opts, rwdat, _act) {
                        if (rwdat.state == 'A') {
                            return (
                                '<div style="display: flex; align-items: center;">' +
                                '<div style="width: 10px; height: 10px;border-radius: 5px;background-color: green;margin-right: 10px;"></div>' +
                                '</div>'
                            );
                        } else if (rwdat.state == 'X') {
                            return (
                                '<div style="display: flex; align-items: center;">' +
                                '<div style="width: 10px; height: 10px;border-radius: 5px;background-color: red;margin-right: 10px;"></div>' +
                                '</div>'
                            );
                        } else {
                            return '';
                        }
                    }
                }
            ];
            this.gridList = [];
            this.acctId = '';
            this.postpaid = this.options.postpaid || '';
            this.custDealer = '';
            this.hideElement = this.options.hideEle || false; // 是否隐藏hideEle
            this.exclusiveFlag = this.options.exclusiveFlag || '';
        },
        afterRender: function () {
            if (this.postpaid == 'Y') {
                this.$('.js-account-add').show();
            } else {
                this.$('.js-account-add').hide();
            }
            if (this.hideElement == true) {
                this.hideEle();
            }
            this.$accountgrid = this.$('.js-account-grid');
            this.$accountgrid.grid({
                height: '100%',
                colModel: this.colModel,
                data: this.gridList,
                searchbar: true,

                onSelectRow: this.rowSelectCallback.bind(this)
            });
            this.$('.js-ok').attr('disabled', 'disabled');
            this.query(this.options.acctId);
            // 查询客户信息
            this.queryCustInfo();
        },
        queryCustInfo: function () {
            var paramlist = {};
            paramlist.userId = portal.appGlobal.get('userId');
            paramlist.spId = portal.appGlobal.get('spId') || '0';
            paramlist.appId = '1';
            paramlist.dataPrivCode = 'ACCOUNT_DEALER';
            Action.qryUserData(
                paramlist,
                function (data) {
                    var privList = data.privList;
                    var newPrivList = fish.filter(privList, function (item) {
                        return item.id == 'Y';
                    });
                    if (newPrivList.length > 0) {
                        this.custDealer = 'Y';
                    } else {
                        this.custDealer = 'N';
                    }
                }.bind(this)
            );
        },
        query: function (rowid, status) {
            $.blockUI();
            // 查询账户
            var params = {};
            if (status) {
                params = { custId: this.custId, postpaid: this.postpaid, qryType: 'O' };
            } else {
                params = { custId: this.custId, postpaid: this.postpaid };
            }
            if (this.options.orderAcctQry) {
                // oc过滤staffline员工账户参数
                params.orderAcctQry = this.options.orderAcctQry;
            }
            params.exclusiveFlag = this.exclusiveFlag;
            Action.QryAcctDetailUMSpec(
                params,
                function (data) {
                    $.unblockUI();
                    if (data.acctList) {
                        this.gridList = data.acctList;
                        if (
                            this.parentView &&
                            this.parentView.parentView &&
                            this.parentView.parentView.parentView &&
                            this.parentView.parentView.parentView.parentView &&
                            this.parentView.parentView.parentView.parentView.refreshAcctCount
                        ) {
                            this.parentView.parentView.parentView.parentView.refreshAcctCount(this.gridList.length);
                        }
                        // fish.each(this.gridList, function (item) {
                        //   item.children = [];
                        //   item.email = item.custBillDelivery.email;
                        //   item.faxNbr = item.custBillDelivery.faxNbr;
                        //   item.smsNbr = item.custBillDelivery.smsNbr;
                        //   item.detailInfo = item.custBillDelivery.detailInfo;
                        // })
                        fish.each(
                            this.gridList,
                            function (item) {
                                if (item.custBillDelivery) {
                                    item.email = item.custBillDelivery.email;
                                    if (item.custBillDelivery.smsNbr.indexOf('-') > -1) {
                                        item.smsNbro = item.custBillDelivery.smsNbr.split('-')[0];
                                        item.smsNbrt = item.custBillDelivery.smsNbr.split('-')[1];
                                    } else {
                                        item.smsNbro = '';
                                        item.smsNbrt = '';
                                    }
                                    if (item.custBillDelivery.faxNbr.indexOf('-') > -1) {
                                        item.faxNbro = item.custBillDelivery.faxNbr.split('-')[0];
                                        item.faxNbrt = item.custBillDelivery.faxNbr.split('-')[1];
                                    } else {
                                        item.faxNbro = '';
                                        item.faxNbrt = '';
                                    }
                                    item.detailInfo = item.custBillDelivery.detailInfo;
                                }

                                if (item.acctAttrValueList) {
                                    var jomPayObj = fish.find(item.acctAttrValueList, function (item) {
                                        return item.attrId == '400035';
                                    });
                                    if (jomPayObj) {
                                        item.jomPayAcctNbr = jomPayObj.attrValue;
                                    } else {
                                        item.jomPayAcctNbr = ' ';
                                    }
                                    var langObj = fish.find(item.acctAttrValueList, function (item) {
                                        return item.attrId == '100774';
                                    });
                                    if (langObj) {
                                        item.billLang = langObj.attrValue;
                                    } else {
                                        item.billLang = 'en-US';
                                    }
                                    // item.jomPayAcctNbr = item.acctAttrValueList[item.acctAttrValueList.length-1].attrValue;
                                } else {
                                    item.jomPayAcctNbr = ' ';
                                    item.billLang = 'en-US';
                                }
                            }.bind(this)
                        );
                        // this.dataList = portal.utils.getTree(this.gridList, "acctId", "parentAcctId", null);
                        this.$accountgrid.grid('reloadData', this.gridList);
                    } else {
                        if (this.parentView && this.parentView.removeCreditTabs) {
                            this.parentView.removeCreditTabs('N');
                        }
                    }

                    if (this.gridList) {
                        if (rowid) {
                            this.$accountgrid.grid('setSelection', rowid, true);
                            this.$('.js-ok').attr('disabled', false);
                            // this.limitFlag = true;
                        } else {
                            this.$accountgrid.grid('setSelection', this.gridList[0], true);
                            this.$('.js-ok').attr('disabled', false);
                            // this.limitFlag = false;
                        }
                    }
                }.bind(this)
            );
        },
        reloadExpireAccount: function (status) {
            // 根据是否在展示过期账户查询
            if (status) {
                this.query(this.options.acctId, status);
            } else {
                this.query(this.options.acctId);
            }
        },
        // 账户信息展示
        rowSelectCallback: function (e, rowid, state) {
            this.selectdata = this.$accountgrid.grid('getSelection', rowid);
            // 录入埋点数据
            fish.pagePoint.extendInfo({ 'acctNbr': this.selectdata.acctNbr });
            this.acctId = rowid;
            // 直接调用父组件方法，违反松耦合原则
            // if (this.parentView) {
            //     this.parentView.rowSelectCallback(this.selectdata, this.limitFlag, this.gridList);
            // }
            // if (this.parentView && this.parentView.resetShowAccountFlag) {
            //     this.parentView.resetShowAccountFlag();
            // }
            // if (this.parentView && this.parentView.removeCreditTabs) {
            //     this.parentView.removeCreditTabs(this.selectdata.postpaid);
            // }
              // 使用标准化事件通信
            this.triggerStandardEvent('accountSelected', {
                selectedData: this.selectdata,
                limitFlag: this.limitFlag,
                gridList: this.gridList,
                postpaid: this.selectdata.postpaid
            });

        },
        hideEle: function () {
            this.$('.detail-modal-head').hide();
            this.$('.detail-modal-footer').hide();
            this.$('.modal-body').addClass('panel');
            this.$('.modal-body').removeClass('modal-body');
            this.$('.panel-title').hide();
            // this.$('.ui-jqgrid-titlebar').show()
        },
        onOkClick: function () {
            this.popup.close(this.selectdata);
        },
        onCancel: function () {
            this.popup.close();
        },
        onAddClick: function () {
            if (this.$accountgrid.grid('option', 'data') && this.$accountgrid.grid('option', 'data').length) {
                fish.each(
                    this.$accountgrid.grid('option', 'data'),
                    function (item, index) {
                        if (item.postpaid == 'Y') {
                            this.postpaidFlog = true;
                            return;
                        }
                    }.bind(this)
                );
            }
            if (this.$accountgrid.grid('option', 'data') && this.$accountgrid.grid('option', 'data').length && this.postpaidFlog) {
                this.accoutNum = 'exit';
            } else {
                this.accoutNum = 'unexit';
            }
            fish.popupView({
                url: 'cvbs/modules/cust/businessAndPersonCommonEntry/views/custCommonEntry',
                width: '1200',
                height: '550',
                viewOption: {
                    requireType: 'Pop',
                    actionType: 'addPersonAccount',
                    custId: this.custId,
                    custType: this.options.custType,
                    modalTitle: 'Add Account',
                    draggable: false,
                    accoutNum: this.accoutNum,
                    custDealer: this.custDealer
                },
                close: function (data) {
                    // console.log('结果返回',data);
                    if (data) {
                        this.query(data.acctId);
                    }
                }.bind(this)
            });
        },
        resize: function (delta) {
            if (!this.hideElement) {
                portal.utils.gridIncHeight(this.$('.js-account-grid'), delta);
            }
        },
        cleanup: function () {
            // 清除掉录入埋点数据
            fish.pagePoint.extendInfo({ 'acctNbr': '' });
        }
    });
});