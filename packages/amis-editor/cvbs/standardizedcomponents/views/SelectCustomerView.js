/**
 * 客户顶部横幅视图组件
 * @class TopBannerView
 * @property {Object} props - 组件属性配置
 * @property {boolean} [props.hideFlag=false] - 是否隐藏整个组件
 * @property {boolean} [props.hideFlag360=false] - 是否隐藏360视图功能
 * @property {string} [props.readCardFlag='N'] - 读卡按钮显示状态(Y/N)
 * @property {string} [props.outUse='N'] - 是否外部使用(Y/N)
 * @property {boolean} [props.showHoleView=true] - 是否显示完整客户信息视图
 * @property {string} [props.FuzzyType] - 模糊搜索类型代码
 * @property {string} [props.hideNew] - 是否隐藏新增客户按钮(Y)
 * @property {string} [props.isPPID='N'] - 是否为PPID标识(Y/N)
 * @property {string} [props.custType] - 客户类型过滤条件
 * @property {string} [props.exlCustId] - 排除特定客户ID
 * @property {string} [props.noProfileFillFlag='N'] - 保密客户是否跳过资料补全(Y/N)
 * @property {boolean} [props.isOrderEntry] - 是否订单录入界面
 * @property {string} [props.outUseValid='Y'] - 外部使用时验证客户信息(Y/N)
 *
 * @property {Object} events -对外暴露给父组件事件
 * @property {Function} events.custSearchCallBack - 客户搜索回调(custBaseDetailDto)
 * @property {Function} events.advancedQueryBtnClick - 高级查询按钮点击事件
 * @property {Function} events.searchCustResult - 客户搜索结果事件
 *
 * @property {Function} events.updateGlobalData - 更新全局数据({key,value})
 * @property {Function} events.getGlobalData - 获取全局数据(key)
 * @property {Function} events.addStepIntro - 添加步骤引导(key)
 *
 * @property {Object} this.portalAppGlobalData - 全局数据
 * @property {string} this.portalAppGlobalData.userId - 当前用户ID
 * @property {string} this.portalAppGlobalData.spId - 服务提供商ID
 * @property {string} this.portalAppGlobalData.portalId - 门户ID
 * @property {string} this.portalAppGlobalData.mac - MAC地址
 * @property {string} this.portalAppGlobalData.isBolLogin - BOL登录状态
 * @property {string} this.portalAppGlobalData.BOL_CUST_ID - BOL客户ID
 * @property {Array} this.portalAppGlobalData.currUserPrivList - 用户权限列表
 * @property {Array} this.portalAppGlobalData.currUserCustTypePrivList - 客户类型权限列表
 * @property {Object} this.portalAppGlobalData.custInfo - 客户信息对象
 * @property {string} this.portalAppGlobalData.pageType - 页面类型
 * @property {Object} this.portalAppGlobalData.currentMenu - 当前菜单
 * @property {Array} this.portalAppGlobalData.pageList - 页面列表
 * @property {string} this.portalAppGlobalData.contactHistoryId - 联系历史ID
 * @property {string} this.portalAppGlobalData.obTaskId - OB任务ID
 * @property {string} this.portalAppGlobalData.activityType - 活动类型
 * @property {string} this.portalAppGlobalData.uccChannelId - UCC渠道ID
 * @property {string} this.portalAppGlobalData.actionType - 操作类型
 *
 * @method setGlobalData - 设置全局数据
 * @param {string} key - 数据键
 * @param {*} value - 数据值
 */
define([
    'text!cvbs/modules/cust/templates/TopBannerTpl.html',
    'cvbs/modules/cust/actions/CustActions',
    'i18n!cvbs/modules/cust/i18n/OrderEntry',
    'hbs!cvbs/modules/um/Cust360/templates/FuzzyQueryCardTpl.hbs',
    'cvbs/modules/cust/views/CustIndexView',
    'cvbs/modules/common/actions/ConfigItemAction',
    'cvbs/modules/cust/utils/CustParamUtils',
    'cvbs/modules/cust/utils/PrivilegeUtils',
    'crm/modules/pos/common/constant/IntroDef',
    'i18n!cvbs/modules/cust/i18n/CustPriviMsg',
    'crm/modules/order/orderentry/views/OrderEntryPage',
    'cvbs/modules/um/Cust360/actions/SearchCustIndexAction',
    'cvbs/modules/cust/views/SelectCustMixin',
    'css!cvbs/modules/cust/styles/css/top-content-box',
    'css!cvbs/modules/um/Cust360/styles/css/fuzzy-query-card.css'
], function (
    Tpl,
    CustAction,
    I18N,
    FuzzyQueryCardTpl,
    OldCustHomeView,
    ConfigItemAction,
    CustParamsUtils,
    PrivilegeUtils,
    IntroDef,
    CustPriviMsg,
    OrderEntryPage,
    SearchCustIndexAction,
    SelectCustMixin
) {
    var TopBannerView = portal.BaseView.extend(fish.extend({
        template: fish.compile(Tpl),
        serialize: I18N,
        events: {
            'keydown .js-search-cust-top-t-param': 'onSearchCustKeydown',
            'click .js-search-cust': 'onSearchCustClick',
            'click .js-advanced-query-btn': 'onAdvancedQueryClick',
            'click .js-order-entry-btn': 'onOrderEntryBtnClick',
            'click .js-cust-search-result-360 .cust-info-card-container': 'onCustCardClick',
            'click .js-read-card-btn': 'onReadCardBtnClick',
            'click .js-add-cust-btn': 'onAddCustBtnClick',
            'click .cert-type-item': 'onSelectCertType',
            'mouseenter .double-right-icon': 'changeArrIcons',
            'mouseleave .double-right-icon': 'changeArrIcons2',
            'mouseenter .scan-btn': 'changeCustScan',
            'mouseleave .scan-btn': 'changeCustScan2',
            'mouseenter .add-cust-btn': 'changeCustBtn',
            'mouseleave .add-cust-btn': 'changeCustBtn2'
        },
        initialize: function (options) {
            this.options = options || {};
            // MODIFIED BY humm 2025-04-24
            this.portalAppGlobalData = options.portalAppGlobalData || portal.appGlobal || {};
            this.fishStoreData = options.fishStoreData || fish.store || {};

            // 传hideFlag，用showFlag。因为考虑到不传的时候为undefined，和false一样，需要显示
            this.showFlag = true;
            this.hideFlag360 = this.options.hideFlag360 || false;
            this.readCardFlag = this.options.readCardFlag || 'N';
            this.showFlag = !this.options.hideFlag;
            this.outUse = this.options.outUse || 'N';
            this.showHoleView = this.options.showHoleView;
            this.outUseValid = this.options.outUseValid || 'Y';
            this.searchParam = '';
            this.$oldCustHomeView = null;
            this.pageUuid = fish.getUUID();
            this.popView = null;
            this.resultMap = [];
            this.userId = this.portalAppGlobalData.get('userId');
            this.certTypeList = [];
            this.certTypeReadCardStr = null;
            this.idTypeReadCardList = [];
            this.privList = [];
            this.custTypePrivList = [];
            this.searchTypeListCombobox = null;
            this.fuzzyType = options.FuzzyType || null;
            this.hideNew = options.hideNew || null;
            this.searchTypeCode = '';
            // 获取保密客户权限列表
            PrivilegeUtils.getPermitRestri();
            this.isPPID = this.options.isPPID || 'N';
            // 过户嵌入查询客户类型条件
            this.qryCustType = options.custType;
            // 过户嵌入过滤指定客户
            this.exlCustId = options.exlCustId;
            // 选择保密客户的时候是否需要弹出编辑客户弹框 Y: 不需要  N: 需要
            this.noProfileFillFlag = options.noProfileFillFlag || 'N';
        },
        render: function () {
            this.$el.html(this.template($.extend(I18N, { showFlag: this.showFlag })));
        },
        afterRender: function () {
            if (this.portalAppGlobalData.get('pageType') === 'esaleWeb') {
                this.$('.js-order-entry-btn').hide();
            }
            var idTypeReadCardStr = ConfigItemAction.qryParamValue('CUSTOMER', 'ID_TYPE', 'ID_TYPE_READ_CARD') || 'MyKad,MyTentera,MyKAS,MyPR';
            this.viewCustTypePrivStr = ConfigItemAction.qryParamValue('CUSTOMER', 'CUST_VIEW', 'VIEW_CUST_TYPE_PRIV');
            if (idTypeReadCardStr) {
                this.idTypeReadCardList = idTypeReadCardStr.split(',');
            }
            this.searchBar = ConfigItemAction.qryParamValue('CUSTOMER_CARE', 'CC_PUBLIC', 'READ_CARD_BUTTON_FLAG');
            this.certTypeReadCardStr = ConfigItemAction.qryParamValue('CUSTOMER', 'CERT_TYPE', 'CERT_TYPE_READ_CARD');

            // 首页扫描卡是否显示，Y展示  N不展示
            if (this.searchBar == 'Y' && this.certTypeReadCardStr !== undefined) {
                this.$('.js-read-card-btn').show();
            } else {
                this.$('.js-read-card-btn').hide();
            }
            $(document).click(
                function () {
                    this.hideCertTypeList();
                    this.hideCustSearchResult();
                }.bind(this)
            );
            if (this.viewCustTypePrivStr == 'Y') {
                this.getCustTypePrivList();
            }
            this.$('.js-search-cust-top-t-param').attr('placeholder', I18N.ORDER_ENTRY_CUST_QRY_HINT_UM);
            this.initSearchTypeCombobox();
            this.$('.js-cust-search-type').show();
            this.$('.cust-search-input').css('width', '500px');
            // 增加产品配置项判断
            this.getCustTypePrivList();
            if (this.portalAppGlobalData.get('currUserPrivList')) {
                this.judgeFlags();
            } else {
                var params = {};
                params.userId = this.portalAppGlobalData.get('userId');
                params.dataPrivCode = 'CUSTC_QUERY';
                CustAction.qryCustPrivilegeCustC(
                    params,
                    function (result) {
                        if (result && result.privList) {
                            this.portalAppGlobalData.set('currUserPrivList', result.privList);
                            this.judgeFlags();
                        }
                    }.bind(this)
                );
            }
            if (this.showFlag) {
                this.$('.js-cust-search-result-360').css('margin-left', '152px');
            } else {
                this.$('.js-cust-search-result-360').css('margin-left', '25px');
            }
            if (this.portalAppGlobalData.get('isBolLogin') && !this.showHoleView) {
                this.onSearchCustClick();
            }
            if (this.hideNew == 'Y') {
                this.$('.js-add-cust-btn').hide();
            }

            if (this.hideFlag360) {
                this.$('.js-cust-search-type').hide();
                this.$('.cust-search-input').hide();
                this.$('.js-advanced-query-btn').hide();
                this.$('.js-read-card-btn').hide();
                this.$('.js-add-cust-btn').hide();
            }

            window.onresize = function () {
                this.isWorkspaceWidth();
            }.bind(this);
            this.queryCreateCustomerPri();
        },

        getSearchTypeComboEle: function () {
            return this.$('.js-search-type-list');
        },
        getSearchCustInputEle: function () {
            return this.$('.js-search-cust-top-t-param');
        },
        queryCreateCustomerPri: function () {
            var paramlist = {};
            paramlist.userId = this.portalAppGlobalData.get('userId');
            paramlist.dataPrivCode = 'CREATE_CUSTOMER';
            paramlist.spId = this.portalAppGlobalData.get('spId') || '0';
            paramlist.appId = '1';
            $.blockUI();
            SearchCustIndexAction.qryUserData(
                paramlist,
                function (res) {
                    $.unblockUI();
                    var privList = res.privList || [];
                    // 过滤创建企业客户的权限
                    var businessFilter = fish.filter(privList, function (item) {
                        return item.id == 'C';
                    });
                    if (businessFilter.length == 0 && this.qryCustType == 'C') {
                        this.$('.js-add-cust-btn').addClass('disabled');
                        this.$('.js-add-cust-btn').attr('disabled', true);
                    }
                }.bind(this)
            );
        },

        judgeFlags: function () {
            var privList = this.portalAppGlobalData.get('currUserPrivList');
            var flagA = false;
            var flagB = false;
            var flagC = false;
            var flagD = false;
            fish.each(privList, function (item) {
                if (item.id == 'A') {
                    flagA = true;
                }
                if (item.id == 'B') {
                    flagB = true;
                }
                if (item.id == 'C') {
                    flagC = true;
                }
                if (item.id == 'D') {
                    flagD = true;
                }
            });
            if (flagA) {
                this.$('.js-search-cust-top-t-param').attr('disabled', false);
                this.$('.js-search-cust').attr('disabled', false);
            } else {
                this.$('.js-search-cust-top-t-param').attr('disabled', true);
                this.$('.js-search-cust').attr('disabled', true);
            }
            if (flagB || flagC) {
                this.$('.double-right-icon').attr('disabled', false);
            } else {
                this.$('.double-right-icon').attr('disabled', true);
            }
            if (flagD) {
                this.$('.js-read-card-btn').attr('disabled', false);
            } else {
                this.$('.js-read-card-btn').attr('disabled', true);
            }
        },

        // 搜索客户文本框键盘监听事件
        onSearchCustKeydown: function (e) {
            if (e.keyCode == 13) {
                // 回车之后自动失去焦点，防止触发多次回车事件
                this.$(e.target).blur();
                this.onSearchCustClick(e);
            }
        },
        // 简单查询按钮点击事件
        onSearchCustClick: function (e) {
            if (!this.portalAppGlobalData.get('isBolLogin') && this.showHoleView) {
                this.stopFunc(e);
            }
            var searchValue = $.trim(this.$('.js-search-cust-top-t-param').val());
            // 设置搜索埋点
            this.setPoint(searchValue);
            if (this.portalAppGlobalData.get('isBolLogin')) {
                CustAction.qryCustDoc(
                    { custId: this.portalAppGlobalData.get('BOL_CUST_ID') },
                    function (data) {
                        if (data && data.custBaseDetail) {
                            if (!this.isPermitRestri(data.custBaseDetail)) {
                                fish.warn(CustPriviMsg.CUST_MANAGE_RESTRICT_CUST_MSG);
                            } else {
                                this.showOldCustHomeView(data.custBaseDetail);
                            }
                            if (this.fishStoreData.get('accountOpeningMethod') == 'contractPhoneBuying') {
                                if ($('.icon-gene-man-manager').length) {
                                    $('.icon-gene-man-manager').parent().attr('id', 'intro-goods-cell');
                                    this.trigger(IntroDef.ADD_INTRO, {
                                        element: '#intro-goods-cell',
                                        tipsContents: IntroDef.GOODS_SELL_STEP1,
                                        position: 'top'
                                    });
                                    $('#intro-goods-cell')
                                        .parent()
                                        .parent()
                                        .parent()
                                        .parent()
                                        .parent()
                                        .parent()
                                        .attr('style', 'z-index:auto!important');
                                }
                            }
                        }
                    }.bind(this)
                );
                return;
            }
            if (!searchValue) {
                fish.info(I18N.ORDER_ENTRY_SEARCH_NULL_UM_NEW_CHNAGE);
                return;
            }
            this.searchParam = searchValue;
            var param = {};

            param.CUST_NAME_ACC_BER_CERT_NBR = searchValue;
            $.blockUI();
            // var menuId = this.portalAppGlobalData.get('currentMenu') == null ? '' : this.portalAppGlobalData.get('currentMenu').menuId;
            param.searchType = this.searchTypeListCombobox.combobox('value');
            // 设置过户嵌入搜索条件
            this.addThirdParams(param);
            // 是orderEntry界面查询客户
            if (this.options.isOrderEntry) {
                param.isOrderEntry = 'Y';
            }
            CustAction.queryCustInfoFuzzy(
                param,
                function (result) {
                    $.unblockUI();
                    var custQueryResult = [];
                    if (result.custQueryResult && result.custQueryResult.length) {
                        custQueryResult = result.custQueryResult;
                    }
                    if (custQueryResult.length == 0) {
                        fish.info(I18N.CUST_QRY_TIP);
                        this.$('.js-cust-search-result-360').hide();
                        return;
                    }
                    this.searchTypeCode = param.searchType;
                    var custBaseDetail = [];
                    fish.each(result.custQueryResult, function (item, index) {
                        item.custBaseDetail.custCatgNameList = item.custCatgNameList;
                        item.custBaseDetail.oldBrn = item.oldBrn;
                        item.custBaseDetail.resultIndex = index;
                        custBaseDetail.push(item.custBaseDetail);
                    });
                    this.resultMap = custBaseDetail;
                    fish.popupView({
                        url: 'cvbs/modules/um/Cust360/views/popwin/FuzzySearchResultPopView.js',
                        width: '70%',
                        height: '500',
                        viewOption: {
                            custList: this.resultMap
                        },
                        close: function (custInfo) {
                            if (custInfo) {
                                if (!this.isPermitRestri(custInfo)) {
                                    fish.warn(CustPriviMsg.CUST_MANAGE_RESTRICT_CUST_MSG);
                                } else {
                                    this.showOldCustHomeView(custInfo);
                                }
                            }
                        }.bind(this)
                    });
                    // this.$(".js-cust-search-result-360")
                    //   .empty()
                    //   .append(
                    //     FuzzyQueryCardTpl(
                    //       $.extend({}, { custInfoList: custBaseDetail }, I18N)
                    //     )
                    //   );
                    // this.$(".js-cust-search-result-360").show();
                }.bind(this)
            );
        },
        onAdvancedQueryClick: function (Flag, filteredCertTypeList, certTypeId) {
            if (this.showHoleView && !Flag.flag) {
                this.trigger('advancedQueryBtnClick');
            } else {
                fish.popupView({
                    url: 'cvbs/modules/um/Cust360/views/popwin/AdvancedQueryPopView',
                    width: 1000,
                    height: '95%',
                    modal: true,
                    viewOption: {
                        privList: this.portalAppGlobalData.get('currUserPrivList'),
                        callerCenter: 'custc',
                        certTypeList: filteredCertTypeList || null,
                        defaultCertTypeId: certTypeId || null,
                        isPPID: this.isPPID,
                        qryCustType: this.qryCustType, // 过户嵌入查询指定客户
                        exlCustId: this.exlCustId, // 过户嵌入过滤指定用户
                        noProfileFillFlag: this.noProfileFillFlag
                    },
                    close: function (closeData) {
                        if (closeData) {
                            if (!this.isPermitRestri(closeData)) {
                                fish.warn(CustPriviMsg.CUST_MANAGE_RESTRICT_CUST_MSG);
                            } else {
                                this.showOldCustHomeView(closeData);
                            }
                        }
                    }.bind(this)
                });
            }
        },

        onCustCardClick: function (e) {
            if (this.resultMap && this.resultMap.length) {
                var custBaseDetailDto = fish.find(this.resultMap, function (item) {
                    return item.resultIndex == e.currentTarget.attributes.resultIndex.value;
                });
                if (custBaseDetailDto) {
                    if (!this.isPermitRestri(custBaseDetailDto)) {
                        fish.warn(CustPriviMsg.CUST_MANAGE_RESTRICT_CUST_MSG);
                    } else {
                        this.showOldCustHomeView(custBaseDetailDto);
                    }
                }
            }
        },

        showOldCustHomeView: function (custBaseDetailDto) {
            if (!custBaseDetailDto.custId) {
                return;
            }
            if (custBaseDetailDto.readCardFlag) {
                // 读卡获取的客户
                this.readCardFlag = custBaseDetailDto.readCardFlag;
            } else {
                // 非读卡获取的客户
                this.readCardFlag = 'N';
            }
            if (this.isPotentialCustomer(custBaseDetailDto)) {
                // 2506598 个人潜在客户, 需要跳转修改客户界面
                this.initEditCustomer(custBaseDetailDto.custId);
                return;
            }
            if (this.outUse == 'Y') {
                if (this.outUseValid == 'Y') {
                    var temp = 1;
                    if (custBaseDetailDto.expDate) {
                        var currDate = new Date();
                        var expDate = new Date(custBaseDetailDto.expDate);
                        temp = expDate - currDate;
                    }
                    if (custBaseDetailDto.certTypeId == '7') {
                        fish.confirm(
                            {
                                title: 'Warning',
                                message: CustPriviMsg.CUST_CERT_TYPE_VALIDATE_MSG,
                                ok: CustPriviMsg.UPDATE_OK,
                                cancel: CustPriviMsg.SKIP_CANCEL
                            },
                            function () {
                                this.initEditCustomer(custBaseDetailDto.custId);
                            }.bind(this),
                            function () {
                                // MODIFIED BY humm 2025-04-24
                                // this.parentView.custSearchCallBack(custBaseDetailDto);
                                this.trigger('custSearchCallBack', custBaseDetailDto);
                            }.bind(this)
                        );
                    } else {
                        if (temp < 0) {
                            fish.warn(CustPriviMsg.CUST_ID_EXP_MSG);
                        }
                        // MODIFIED BY humm 2025-04-24
                        // this.parentView.custSearchCallBack(custBaseDetailDto);
                        this.trigger('custSearchCallBack', custBaseDetailDto);
                    }
                } else {
                    // MODIFIED BY humm 2025-04-24
                    // this.parentView.custSearchCallBack(custBaseDetailDto);
                    this.trigger('custSearchCallBack', custBaseDetailDto);
                }
            } else if (this.viewCustTypePrivStr == 'Y') {
                // 增加产品配置项判断
                var custTypePriv = fish.find(this.custTypePrivList, function (item) {
                    return item.id == custBaseDetailDto.custType;
                });
                if (!custTypePriv) {
                    // 没有VIEW_CUST_TYPE查看对应类型客户的权限
                    fish.warn(CustPriviMsg.CUST_VIEW_CUST_TYPE_MSG);
                    return;
                }
                if (!this.isPermitRestri(custBaseDetailDto)) {
                    fish.warn(CustPriviMsg.CUST_MANAGE_RESTRICT_CUST_MSG);
                    return;
                }
                var expireTag = 1;
                if (custBaseDetailDto.expDate) {
                    var currDateTemp = new Date();
                    var expDateTemp = new Date(custBaseDetailDto.expDate);
                    expireTag = expDateTemp - currDateTemp;
                }
                if (custBaseDetailDto.certTypeId == '7') {
                    fish.confirm(
                        {
                            title: 'Warning',
                            message: CustPriviMsg.CUST_CERT_TYPE_VALIDATE_MSG,
                            ok: CustPriviMsg.UPDATE_OK,
                            cancel: CustPriviMsg.SKIP_CANCEL
                        },
                        function () {
                            this.initEditCustomer(custBaseDetailDto.custId);
                        }.bind(this),
                        function () {
                            this.refreshCustView(custBaseDetailDto);
                        }.bind(this)
                    );
                } else if (expireTag < 0) {
                    fish.warn(CustPriviMsg.CUST_ID_EXP_MSG);
                    this.refreshCustView(custBaseDetailDto);
                } else {
                    this.refreshCustView(custBaseDetailDto);
                }
            } else {
                this.refreshCustView(custBaseDetailDto);
            }
        },

        refreshCustView: function (custBaseDetailDto) {
            if (this.searchTypeCode == 'servNbr') {
                custBaseDetailDto.selectedAccNbr = this.searchParam;
            }
            var menuId = this.portalAppGlobalData.get('currentMenu') == null ? '' : this.portalAppGlobalData.get('currentMenu').menuId;
            this.portalAppGlobalData.set('custInfo', custBaseDetailDto);
            this.custBaseDetailDto = custBaseDetailDto;

            this.portalAppGlobalData.set(menuId + 'custId', this.custBaseDetailDto.custId);
            this.portalAppGlobalData.set(menuId + 'routingId', this.custBaseDetailDto.routingId);
            portal.callServiceSyn('QrySystemCfg', { paramName: 'app.ajaxUrl.list' }, function (result) {
                var urlArray = result.result.split(',');
                this.portalAppGlobalData.set(menuId + 'rejectCustIdUrl', urlArray);
            });
            portal.callServiceSyn('QrySystemCfg', { paramName: 'app.servName.list' }, function (result) {
                var servArray = result.result.split(',');
                this.portalAppGlobalData.set(menuId + 'rejectCustIdServName', servArray);
            });
            this.trigger('searchCustResult');
        },

        initEditCustomer: function (custId) {
            fish.popupView({
                url: 'cvbs/modules/um/addCustomer/views/popwin/EditCustomerPopWin',
                width: '95%',
                height: '95%',
                viewOption: {
                    custId: custId,
                    popShow: 'Y',
                    validateFlag: true
                },
                close: function (rowData) {
                    if (rowData) {
                        if (this.outUse == 'Y') {
                            // this.parentView.custSearchCallBack(rowData);
                            this.trigger('custSearchCallBack', rowData);
                        } else {
                            this.refreshCustView(rowData);
                        }
                    }
                }.bind(this)
            });
        },
        onOrderEntryBtnClick: function () {
            var params = {
                mac: this.portalAppGlobalData.get('mac'),
                portalId: this.portalAppGlobalData.get('portalId'),
                userId: this.portalAppGlobalData.get('userId'),
                partCode: 'ORDER_ENTRY'
            };
            $.when(CustAction.checkPosPartOpen(params)).then(
                function (result) {
                    if (result.returnCode == 0) {
                        // 表示通过
                        this.goToOrderEntry();
                        return;
                    }
                    if (result.returnCode == 1) {
                        // 不通过提示
                        fish.warn(result.returnMsg);
                    }
                }.bind(this)
            );
        },

        goToOrderEntry: function () {
            var menuId = this.portalAppGlobalData.get('currentMenu') == null ? '' : this.portalAppGlobalData.get('currentMenu').menuId;
            var pageList = this.portalAppGlobalData.get('pageList' + menuId);
            var custInfo = this.portalAppGlobalData.get('custInfo');
            // WhaleTalk侧传入的参数，需要传给OC
            var contactHistoryId = this.portalAppGlobalData.get('contactHistoryId');
            var obTaskId = this.portalAppGlobalData.get('obTaskId');
            var activityType = this.portalAppGlobalData.get('activityType');
            var uccChannelId = this.portalAppGlobalData.get('uccChannelId');
            var actionType = this.portalAppGlobalData.get('actionType');
            // 获取当前菜单的custInfo
            if (pageList && pageList.length) {
                custInfo = pageList[0].custInfo;
            }
            if (custInfo) {
                var channelId = activityType ? uccChannelId : CustParamsUtils.getChannelId();
                var readCardFlag = this.readCardFlag;
                custInfo.fromCust360 = 'Y';
                var param = {
                    custInfo: custInfo,
                    readCardFlag: readCardFlag,
                    channelId: channelId,
                    contactHistoryId: contactHistoryId,
                    obTaskId: obTaskId,
                    activityType: activityType,
                    actionType: actionType
                };
                OrderEntryPage.openEntryMenu(param);
            }
        },

        onReadCardBtnClick: function (e) {
            this.stopFunc(e);
            if (this.certTypeList.length) {
                this.$('.js-cert-type-list').show();
            } else {
                CustAction.qryCertType(
                    function (data) {
                        // this.certTypeList = fish.filter(data.z_d_r, function(item){
                        //   return item.certTypeId != '97' && item.certTypeId != '98' && item.certTypeId != '99';
                        // });
                        this.certTypeList = data.z_d_r;
                        this.$('.js-cert-type-list').empty();
                        fish.each(
                            this.certTypeList,
                            function (item) {
                                this.$('.js-cert-type-list').append(
                                    '<li class="cert-type-item col-md-12">' +
                                    '<label class="control-label cert-type-name" title="' +
                                    item.certTypeName +
                                    '">' +
                                    item.certTypeName +
                                    '</label>' +
                                    '</li>'
                                );
                            }.bind(this)
                        );
                        this.$('.js-cert-type-list').show();
                    }.bind(this)
                );
            }
        },

        hideCertTypeList: function () {
            if (this.$('.js-cert-type-list').length) {
                this.$('.js-cert-type-list').hide();
            }
        },

        hideCustSearchResult: function () {
            if (this.$('.js-cust-search-result-360').length) {
                this.$('.js-cust-search-result-360').hide();
            }
        },

        onSelectCertType: function (e) {
            var certTypeName = e.currentTarget.textContent;
            var idTypeReadCard = fish.find(this.idTypeReadCardList, function (item) {
                return item.toLowerCase() == certTypeName.toLowerCase();
            });
            this.hideCertTypeList();
            if (idTypeReadCard) {
                var data = fish.find(this.certTypeList, function (item) {
                    return item.certTypeName.toLowerCase() == idTypeReadCard.toLowerCase();
                });
                fish.popupView({
                    url: 'cvbs/modules/um/addCustomer/views/popwin/TMReadKadPopView.js',
                    width: 1000,
                    // maxHeight: "80%",
                    viewOption: {
                        data: data,
                        dislist: 'N',
                        state: 'Q'
                    },
                    close: function (custBaseDetailDto) {
                        if (custBaseDetailDto) {
                            // readCardFlag返回给oc使用
                            custBaseDetailDto.readCardFlag = 'Y';
                            if (!this.isPermitRestri(custBaseDetailDto)) {
                                fish.warn(CustPriviMsg.CUST_MANAGE_RESTRICT_CUST_MSG);
                            } else {
                                this.showOldCustHomeView(custBaseDetailDto);
                            }
                        }
                    }.bind(this)
                });
            } else {
                var filteredCertTypeList = [];
                CustAction.queryCustPrivType(
                    function (result) {
                        if (result && result.notNeedReadCardCertTypeList) {
                            filteredCertTypeList = result.notNeedReadCardCertTypeList;
                            var certTypeTemp = fish.find(filteredCertTypeList, function (item) {
                                return item.certTypeName == certTypeName;
                            });
                            if (certTypeTemp) {
                                var certTypeId = certTypeTemp.certTypeId;
                                this.onAdvancedQueryClick({ flag: true }, filteredCertTypeList, certTypeId);
                            } else {
                                this.onAdvancedQueryClick({ flag: true });
                            }
                        } else {
                            this.onAdvancedQueryClick({ flag: true });
                        }
                    }.bind(this)
                );
            }
        },

        onAddCustBtnClick: function () {
            fish.popupView({
                url: 'cvbs/modules/addPersonAndBuinessCustomer/views/popSHowCustomerView',
                viewOption: {
                    addCustType: this.qryCustType
                },
                width: '40%',
                height: '50%',
                close: function (data) {
                    if (data) {
                        if (!this.isPermitRestri(data)) {
                            fish.warn(CustPriviMsg.CUST_MANAGE_RESTRICT_CUST_MSG);
                        } else {
                            this.showOldCustHomeView(data);
                        }
                    }
                }.bind(this)
            });
        },

        getCustTypePrivList: function () {
            var temp = this.portalAppGlobalData.get('currUserCustTypePrivList');
            if (temp) {
                this.custTypePrivList = temp;
            } else {
                var param = {};
                param.userId = this.portalAppGlobalData.get('userId');
                param.dataPrivCode = 'VIEW_CUST_TYPE';
                CustAction.qryCustPrivilegeCustC(
                    param,
                    function (result) {
                        if (result && result.privList && result.privList.length) {
                            this.portalAppGlobalData.set('currUserCustTypePrivList', result.privList);
                        } else {
                            this.portalAppGlobalData.set('currUserCustTypePrivList', []);
                        }
                        this.custTypePrivList = this.portalAppGlobalData.get('currUserCustTypePrivList');
                    }.bind(this)
                );
            }
        },

        stopFunc: function (e) {
            e.stopPropagation ? e.stopPropagation() : (e.cancelBubble = true);
            if (!this.portalAppGlobalData.get('isBolLogin') && this.showHoleView) {
                e.preventDefault ? e.preventDefault() : (e.returnValue = false);
            }
        },
        changeArrIcons: function () {
            this.$('.double-right-icon>img').attr('src', 'cvbs/modules/cust/styles/img/icons/arr-double-right2.svg');
        },
        changeArrIcons2: function () {
            this.$('.double-right-icon>img').attr('src', 'cvbs/modules/cust/styles/img/icons/arr-double-right.svg');
        },
        changeCustScan: function () {
            this.$('.scan-btn>img').attr('src', 'cvbs/modules/cust/styles/img/icons/scan2.svg');
        },
        changeCustScan2: function () {
            this.$('.scan-btn>img').attr('src', 'cvbs/modules/cust/styles/img/icons/scan.svg');
        },
        changeCustBtn: function () {
            this.$('.add-cust-btn>img').attr('src', 'cvbs/modules/cust/styles/img/icons/add user2.svg');
        },
        changeCustBtn2: function () {
            this.$('.add-cust-btn>img').attr('src', 'cvbs/modules/cust/styles/img/icons/add user.svg');
        },
        isPermitRestri: function (custBaseDetailDto) {
            if (
                custBaseDetailDto.custAttrValueEx &&
                custBaseDetailDto.custAttrValueEx.attrValue &&
                custBaseDetailDto.custAttrValueEx.attrValue === 'YES'
            ) {
                // 是保密客户需要判断权限
                var isPermitRestri = PrivilegeUtils.isPermitRestri(custBaseDetailDto.custType);
                return isPermitRestri;
            }
            return true;
        },
        setPoint: function (searchValue) {
            var queryCondition = {};
            if (this.searchTypeListCombobox) {
                var searchType = this.searchTypeListCombobox.combobox('value');
                queryCondition[searchType] = searchValue;
            } else {
                queryCondition.value = searchValue;
            }
            // 设置搜索埋点
            var ppBaseConfig = fish.pagePoint.getBaseConfig();
            fish.pagePoint.sendPagePoint({
                msgType: 'customEvent',
                message: fish.extend(ppBaseConfig, {
                    EVENT_TYPE: 'Query',
                    EVENT_CODE: 'QUERY_CUST_FUZZY',
                    extendInfo: JSON.stringify({
                        queryCondition: JSON.stringify(queryCondition)
                    }),
                    uiComponent: 'Customer Center\\Cust360\\Cust360\\Search [Button]'
                })
            });
        },
        addThirdParams: function (params) {
            if (this.qryCustType) {
                // 过户嵌入过滤客户类型条件
                params.custType = this.qryCustType;
            }
            if (this.exlCustId) {
                // 过户嵌入过滤指定客户
                params.exlCustId = this.exlCustId;
            }
        },
        isWorkspaceWidth: function () {
            // 如果是首页工作台打开的需要自适应
            var containWidth = this.$('.top-content-body').width();
            if (containWidth < 775) {
                this.$('.cust-search-input').css('width', '57%');
                this.$('.js-cust-search-type').css('width', '20%');
            } else {
                this.$('.cust-search-input').css('width', '500px');
                this.$('.js-cust-search-type').css('width', '');
            }
        },
        isPotentialCustomer: function (custInfo) {
            // noProfileFillFlag为Y不走潜在客户补录资料逻辑
            // 是否是个人潜在客户
            if (this.noProfileFillFlag === 'N' && custInfo.custType === 'A' && custInfo.netType === 'P') {
                // 如果是潜在个人客户，后续需要直接打开修改客户页面
                return true;
            }
            return false;
        },
        resize: function () {
            this.isWorkspaceWidth();
        }
    }, SelectCustMixin));

    return TopBannerView;
});
