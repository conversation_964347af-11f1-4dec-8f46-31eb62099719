.top-content-body.large .js-cust-search-type {
    position: relative;
    margin: 0 -25px 0 25px;
    padding: 0;
    max-width: 156px;
    height: 40px;
    border-radius: 4px 0 0 4px !important;
}

.top-content-body.large .js-cust-search-type .js-search-type-list {
    height: 40px;
}

.top-content-body.large .js-cust-search-type .form-control {
    height: 40px;
    padding-left: 12px;
    line-height: 32px;
    font-size: 14px;
    text-align: left;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

.top-content-body.large .js-cust-search-type .form-control .form-control-feedback {
    width: 38px;
    height: 40px;
    line-height: 40px;
}

.top-content-body.large .js-cust-search-type .form-control .form-control-feedback::before {
    color: #95969d;
}

.top-content-body.large .cust-search-input {
    width: 514px;
    height: 40px;
}

.top-content-body.large .cust-search-input .form-control {
    height: 40px;
    padding-left: 12px;
    font-size: 14px;
    border-left: 0;
    border-radius: 0;
}

.top-content-body.large .cust-search-input .form-control:focus {
    border-left: 1px solid;
    margin-left: -1px;
}

.top-content-body.large .search-input-icon {
    position: absolute;
    top: 0;
    right: 12px;
    display: flex;
    height: 40px;
    justify-content: center;
    align-items: center;
    text-align: center;
    cursor: pointer;
}

.top-content-body.large .search-input-icon .icon-big-font {
    width: 22px;
    height: 22px;
}

.top-content-body.large .js-advanced-query-btn {
    width: 40px;
    height: 40px;
}

.top-content-body.large .js-advanced-query-btn .icon-little-font {
    width: 20px;
    height: 20px;
}

.top-content-body.large .scan-btn {
    width: 40px;
    height: 40px;
    border-radius: 2px;
}

.top-content-body.large .scan-btn .icon-little-font {
    width: 20px;
    height: 20px;
}

.top-content-body.large .double-right-icon {
    width: 40px;
    height: 40px;
}

.dropdown-list>li,
.ui-multiselect-results li {
    padding-left: 12px !important;
}