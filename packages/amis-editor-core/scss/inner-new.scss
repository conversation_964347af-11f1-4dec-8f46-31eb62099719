@import '../src/themeVariable.module.scss';

.Editor-Demo {
  .Editor-header .Editor-header-actions {
    font-family: 'Inter Var';
    .ant-select {
      height: 28px;
      line-height: 28px;
    }
    div.header-action-btn {
      font-family: 'Inter Var';
      height: 28px;
      border-radius: 6px;
      padding-right: 8px;
      padding-left: 8px;
      margin-left: 12px;
      gap: 4px;
      background: $THEME_MAIN_COLOR;
      &:hover {
        background: $THEME_MAIN_COLOR_HOVER;
      }
    }

    div.header-action-default-btn,
    div.header-action-default-btn:hover {
      gap: 4px;
      margin-left: 8px;
      height: 28px;
      padding: 0 6px;
      color: #575966;
      text-align: center;
      font-family: 'Inter Var';
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      border: none;
      background: none;
      box-shadow: unset;
      outline: none;
      background: none !important;
      .anticon {
        color: #575966;
        font-size: 14px;
      }
    }

    div.header-action-default-btn:hover {
      color: $THEME_MAIN_COLOR;
      .anticon {
        color: $THEME_MAIN_COLOR;
      }
    }

    .header-action-default-bordedr-btn {
      border-radius: 6px;
      border: 1px solid #e0e0e0;
      background: #fff;
      cursor: pointer;
    }

    .header-action-default-bordedr-btn,
    .header-action-default-bordedr-btn:hover {
      display: flex;
      align-items: center;
      gap: 4px;
      margin-left: 8px;
      height: 28px;
      padding: 0 6px;
      // border-color: @THEME_MAIN_COLOR;
      background: none;
      box-shadow: unset;
      outline: none;
      background: none !important;
      .default-border-icon {
        color: $THEME_TEXT_COLOR_LEVEL2;
        font-size: 14px;
      }
      .default-border-text {
        color: $THEME_TEXT_COLOR_LEVEL2;
        text-align: center;
        font-family: 'Inter Var';
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px;
      }
    }
    .header-action-default-bordedr-btn:hover {
      border-color: $THEME_MAIN_COLOR !important;
      .default-border-icon {
        color: $THEME_MAIN_COLOR;
      }
      .default-border-text {
        color: $THEME_MAIN_COLOR;
      }
    }
  }

  .Editor-header
    .Editor-view-mode-group-container
    .Editor-view-mode-group
    div.Editor-view-mode-btn:hover
    > svg {
    color: $THEME_MAIN_COLOR;
  }
  .Editor-header
    .Editor-view-mode-group-container
    .Editor-view-mode-group
    div.Editor-view-mode-btn.is-active {
    // background-color: $THEME_MAIN_COLOR;
    background-color: rgb(from $THEME_MAIN_COLOR r g b / 14%);
    &:hover {
      // background-color: $THEME_MAIN_COLOR;
      background-color: rgb(from $THEME_MAIN_COLOR r g b / 14%);
    }
    > svg {
      color: $THEME_MAIN_COLOR;
      &:hover {
        color: $THEME_MAIN_COLOR;
      }
    }
  }
}
.ae-Editor-inner {
  .business-renderers {
    padding: 0px;
  }
  // 左右侧面板共享
  .editor-left-panel,
  .editor-right-panel {
    font-family: 'Inter var';
    .ae-RendererPanel,
    .ae-Outline-panel,
    .ae-InsertPanel,
    .ae-GlobalVarPanel,
    .ae-CodePanel {
      // 左侧面板Header
      .panel-header {
        margin: 8px 0;
        flex: 0 0 18px;
        padding: 0 12px;
        font-family: 'Inter var';
        font-size: 12px;
        color: var(--colors-neutral-text-2);
        letter-spacing: 0;
        line-height: 18px;
        font-weight: 700;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      // 搜索框样式
      .editor-InputSearch {
        margin: 0 12px;
        background: #fff !important;
        border: 1px solid $editor-border-color !important;
        border-radius: 4px !important;
        display: flex;
        justify-content: flex-start;
        align-items: center;

        &:hover {
          border-color: #b6b7bc !important;
        }

        &.is-focused {
          border-color: var(--theme-main-color) !important;
        }

        svg.icon-search,
        svg.icon-editor-search,
        svg.delete-btn-icon {
          top: 0 !important;
          width: 16px;
          height: 16px;
          color: #d4d6d9;
          fill: #d4d6d9;
          cursor: pointer;

          &:hover {
            color: $editor-active-color;
            fill: $editor-active-color;
          }

          &.margin-right {
            right: 4px !important;
          }
        }

        // clear 和 search 同时显示
        svg.delete-btn-icon {
          width: 14px;
          height: 14px;
        }
      }

      // 间隔线样式
      hr.margin-top {
        margin: 12px 6px 3px 6px;
        height: 1px;
        border: none;
        border-top: 1px solid $editor-border-color !important;
      }
    }

    .editor-panel-btn {
      top: 7px;
    }

    .cxd-Tabs--line
      > .cxd-Tabs-linksContainer-wrapper
      > .cxd-Tabs-linksContainer
      > .cxd-Tabs-linksContainer-main
      > .cxd-Tabs-links
      > li
      > a:first-child {
      border-bottom: none !important;
    }

    .cxd-Tabs--line
      > .cxd-Tabs-linksContainer-wrapper
      > .cxd-Tabs-linksContainer
      > .cxd-Tabs-linksContainer-main
      > .cxd-Tabs-links
      > li.is-active
      > a:first-child {
      border-bottom: none !important;
      .cxd-Tabs-link-text {
        color: #2d3040 !important;
        font-weight: 600 !important;
      }
    }

    .cxd-Tabs-links > .cxd-Tabs-link > a:first-child .cxd-Tabs-link-text {
      font-family: Inter;
      font-weight: 500;
      font-size: 12px;
      line-height: 22px;
      color: #95969d;
      overflow: visible;
    }

    .cxd-Tabs--line
      > .cxd-Tabs-linksContainer-wrapper
      > .cxd-Tabs-linksContainer
      > .cxd-Tabs-linksContainer-main
      > .cxd-Tabs-links {
      > li {
        padding: 0 6px;
        height: 32px;

        &:first-of-type {
          padding-left: 0;
        }

        &:last-of-type {
          padding-right: 0;
        }

        > a:first-child {
          border-width: 0;
          padding: 0 0 px2rem(8px);
          color: var(--Tabs--line-color);
          font-size: var(--Tabs--line-fontSize);
          font-weight: var(--Tabs--line-fontWeight);
          line-height: var(--Tabs--line-lineHeight);
        }

        &:not(.is-disabled):hover {
          a:first-child {
            color: var(--Tabs--line-hover-color);
            font-size: var(--Tabs--line-hover-fontSize);
            font-weight: var(--Tabs--line-hover-fontWeight);
            line-height: var(--Tabs--line-hover-lineHeight);
            background: transparent;
          }

          .cxd-Tabs-link-close {
            fill: var(--Tabs--line-hover-color);
          }
        }

        &.is-disabled {
          a:first-child {
            color: var(--Tabs--line-disabled-color);
            font-size: var(--Tabs--line-disabled-fontSize);
            font-weight: var(--Tabs--line-disabled-fontWeight);
            line-height: var(--Tabs--line-disabled-lineHeight);
          }
        }

        &.is-active {
          > a:first-child,
          > a:first-child:hover,
          > a:first-child:focus {
            border-bottom: var(--Tabs--line-active-border-width)
              var(--Tabs--line-active-border-style)
              var(--Tabs--line-onHover-borderColor);
            color: var(--Tabs--line-active-color);
            font-size: var(--Tabs--line-active-fontSize);
            font-weight: var(--Tabs--line-active-fontWeight);
            line-height: var(--Tabs--line-active-lineHeight);
            background: transparent;
          }
        }
      }
    }

    .cxd-Tabs--line
      > .cxd-Tabs-linksWrapper
      > .cxd-Tabs-links
      > li
      > a:first-child,
    .cxd-Tabs--line
      > .cxd-Tabs-linksContainer-wrapper
      > .cxd-Tabs-linksContainer
      > .cxd-Tabs-linksContainer-main
      > .cxd-Tabs-links
      > li
      > a:first-child {
      padding-bottom: 0px;
    }

    .cxd-Tabs--line > .cxd-Tabs-linksContainer-wrapper::before {
      border-bottom: 1px solid #e8e8e8;
    }

    .cxd-Tabs-linksContainer-wrapper .cxd-Tabs-linksContainer-arrow {
      margin-bottom: 5px;
      padding-bottom: 2px;
    }
    .ae-RendererList-tabs .ae-RendererList-tabs-content {
      margin-top: 8px;
    }
    .ae-RendererPanel .editor-InputSearch,
    .ae-Outline-panel .editor-InputSearch,
    .ae-InsertPanel .editor-InputSearch,
    .ae-CodePanel .editor-InputSearch {
      font-family: 'Inter Var';
      font-weight: 400;
      font-size: 12px;
      line-height: 22px;
      letter-spacing: 0%;
      height: 28px;
      margin: 0 7px;
      padding: 3px 8px;
      border-radius: 6px !important;
      border: 1px solid #e0e0e0 !important;
      .icon-editor-search {
        margin-left: 6px;
      }
    }
    .editor-InputSearch-panel {
      margin-bottom: 8px;
    }
    .ae-RendererList-groupWrap {
      padding: 0 0 36px 0;
    }
    .ae-RendererList-groupWrap,
    .ae-TemplateList-groupWrap {
      .collapse-header {
        font-family: 'Inter Var';
        font-weight: 500;
        font-size: 12px;
        height: 28px;
        line-height: 28px;
        color: #2d3040;
      }
    }
    .cxd-Tabs--line
      > .cxd-Tabs-linksWrapper
      > .cxd-Tabs-links
      > li:not(.is-disabled):hover
      a:first-child,
    .cxd-Tabs--line
      > .cxd-Tabs-linksContainer-wrapper
      > .cxd-Tabs-linksContainer
      > .cxd-Tabs-linksContainer-main
      > .cxd-Tabs-links
      > li:not(.is-disabled):hover
      a:first-child {
      color: $THEME_MAIN_COLOR;
    }
  }

  // 右侧面板
  .editor-right-panel .editor-prop-config-tabs ul[role='tablist'] > li,
  .editor-right-panel .editor-prop-config-tabs-links > li {
    height: 40px;
    // flex: unset;
  }
  .editor-right-panel
    .editor-prop-config-tabs
    ul[role='tablist']
    > li:not(:last-child):after,
  .editor-right-panel
    .editor-prop-config-tabs-links
    > li:not(:last-child):after {
    display: none;
  }

  // 左侧面板
  .editor-left-panel .ae-RendererList-group {
    grid-template-columns: repeat(3, 1fr);
    gap: 6px 4px;
    padding: 0px 11px;
    margin: 4px 0px;
  }
  .editor-left-panel .ae-RendererList-item {
    position: relative;
    display: block;
    width: 70px;
    height: auto;
    padding: 0;
    margin: 0;
    flex: unset;
    border: none;
    .ae-img-box {
      position: relative;
      display: flex;
      justify-content: center;
      cursor: pointer;
      &:hover {
        .img {
          border: 1px solid #c3c4c7;
        }
      }
      .img {
        border: 1px solid #e4e8ee;
      }
      .ae-RendererIcon {
        position: absolute;
        right: 10px;
        top: 4px;
        color: #95969d;
        &:hover {
          color: $THEME_MAIN_COLOR;
        }
      }
    }
    .ae-name-box {
      min-height: 22px;
      display: flex;
      justify-content: flex-start;
      align-items: center;

      .ae-RendererInfo {
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
        text-align: center;
        // overflow: unset;
        text-overflow: unset;
        white-space: unset;
        line-height: 14px;
        word-wrap: break-word;
      }
    }
    &:hover {
      .ae-RendererInfo {
        max-width: unset;
      }
      .ae-img-hover {
        display: block;
      }
    }
  }
  .editor-left-panel .ae-outline-tabs-header > li {
    flex-direction: unset !important;
  }
}

body {
  .ae-RendererThumb {
    max-width: 800px;
    box-shadow: 0px 4px 16px 0px #0000001f;
    border-radius: 8px;
    border: 1px solid var(--colors-neutral-line-6);
    padding: 0px;

    .cxd-Tooltip-body {
      padding: 0px;
    }

    .ae-Renderer-title {
      padding: 8px 16px;
      font-family: 'Inter Var';
      line-height: 22px;
      font-weight: 500;
      font-size: 14px;
      border-bottom: 1px solid var(--colors-neutral-line-6);
    }

    .ae-Renderer-info {
      padding: 12px 16px 4px 16px;
      margin: 0px;
      font-family: 'Inter Var';
      letter-spacing: 0;
      font-weight: 400;
    }

    .ae-Renderer-preview {
      font-family: 'Inter Var';
      max-height: 500px;
      padding: 0px 16px 16px 16px;
      .cxd-ImagesField {
        padding: 4px 0;
      }
      .cxd-Pagination-wrap {
        padding: 6px 0;
      }
    }
  }
  .cdp-common-dialog {
    .cxd-Modal-content {
      padding: 0;
      .cxd-Modal-header {
        padding: 6px 16px;
        border-radius: 8px 8px 0 0;
        background: #eff2fb;
        box-shadow: 0 -1px #f0f0f0 inset;
        height: 40px;
        border-bottom: none;
        margin-bottom: 8px;
        .cxd-Modal-title {
          color: #2d3040;
          font-family: 'Inter Var';
          font-size: 16px;
          font-style: normal;
          font-weight: 600;
          line-height: 24px;
        }
      }
      .cxd-Modal-body {
        padding: 16px;
        margin: 0;
        .cxd-Button--primary:not(.is-disabled) {
          background: var(--theme-main-color);
          border-color: var(--theme-main-color);
        }
        .cxd-Button--link {
          color: var(--theme-main-color);
        }
        .cxd-Form-control:not(.is-disabled) {
          .cxd-TextControl-input {
            &:hover,
            &.is-focused {
              border-color: var(--theme-main-color);
            }
          }
        }
        .cxd-InputBox {
          &:hover,
          &.is-focused {
            border-color: var(--theme-main-color);
          }
        }
        .cxd-InputTable .cxd-Field--quickEditable svg {
          color: var(--theme-main-color);
        }
        .cxd-SelectControl:not(.is-inline) > .cxd-Select:hover {
          border-color: var(--theme-main-color);
        }
        .cxd-Select.is-focused:not(.is-mobile),
        .cxd-Select.is-opened:not(.is-mobile) {
          border-color: var(--theme-main-color);
        }
        .cxd-Select-option.is-highlight {
          color: var(--theme-main-color);
        }
        .cxd-Select-option.is-active {
          color: var(--theme-main-color);
        }
        .cxd-TextControl.is-focused > .cxd-TextControl-input {
          border-color: var(--theme-main-color);
        }
        .cxd-Button--default:not(:disabled):not(.is-disabled):hover {
          border-color: var(--theme-main-color);
          color: var(--theme-main-color);
        }
        .cxd-Checkbox--checkbox--default:hover:not(.disabled):not(.checked)
          > i {
          border-color: var(--theme-main-color);
        }
        .cxd-Checkbox--checkbox--default--checked > i {
          background-color: #fff;
          border-color: rgba(224, 224, 224, 1);
          .checkbox-icon {
            color: var(--theme-main-color);
          }
        }
        .cxd-NumberControl--withUnit:hover {
          .cxd-NumberControl-unit,
          .cxd-Number {
            border-color: var(--theme-main-color);
          }
        }
        .cxd-NumberControl--withUnit
          .cxd-Number-focused
          + .cxd-NumberControl-unit {
          border-color: var(--theme-main-color);
        }
        .cxd-Number-handler:hover {
          color: var(--theme-main-color);
        }
        .cxd-NumberControl-unit.is-opened {
          border-color: var(--theme-main-color) !important;
        }
        .cxd-NumberControl--withUnit:has(.cxd-Select.is-opened) {
          .cxd-Number {
            border-color: var(--theme-main-color);
          }
        }
        .cxd-Number-focused {
          border-color: var(--theme-main-color);
        }
        .cxd-ResultBox.is-clickable:not(.is-disabled):hover {
          border-color: var(--theme-main-color);
        }
        .cxd-Switch.is-checked {
          background: #73d6c2;
        }
        .cxd-Combo--ver:not(.cxd-Combo--noBorder):not(.is-disabled)
          > .cxd-Combo-item:hover {
          border-color: var(--theme-main-color);
        }
        .cxd-Number:hover {
          border-color: var(--theme-main-color);
        }
        .cxd-Checkbox--radio--default:hover:not(.disabled) > i {
          border-color: var(--theme-main-color);
        }
        .cxd-Checkbox--radio--default--checked > i {
          border-color: var(--theme-main-color);
        }
        .cxd-Checkbox--radio--default > i .icon {
          color: var(--theme-main-color);
        }
        .cxd-TextareaControl > textarea:hover {
          border-color: var(--theme-main-color);
        }
        .cxd-TextareaControl.is-focused > .cxd-TextareaControl-input {
          border-color: var(--theme-main-color);
        }
        .cxd-Tag-text {
          color: var(--theme-main-color);
        }
        .cxd-TreeSelection-itemInner.is-active {
          color: var(--theme-main-color);
        }
        .cxd-FormulaEditor-FuncList-doc pre code {
          color: var(--theme-main-color);
        }
      }
      .cxd-PopOver {
        .cxd-GroupedSelection-item.is-active {
          color: var(--theme-main-color);
        }
      }
      .cxd-Modal-footer {
        display: flex;
        align-items: center;
        justify-content: end;
        flex-direction: row-reverse;
        padding: 7px 16px 7px 21px;
        margin-top: 12px;
        border-radius: 0 0 8px 8px;
        border-top: 1px solid #e0e0e0;
        background: #fff;
        .cxd-Button--default {
          margin-left: 8px;
          height: 28px;
          padding: 0 16px;
          border-radius: 6px;
          border: 1px solid #e0e0e0;
          background: #fff;
          &:hover {
            border: 1px solid $THEME_MAIN_COLOR;
            color: $THEME_MAIN_COLOR;
          }
        }
        .cxd-Button--primary:not(.is-disabled) {
          border: none;
          height: 28px;
          padding: 0 16px;
          border-radius: 6px;
          background: $THEME_MAIN_COLOR;
          outline: none !important;
          box-shadow: unset !important;
          &:hover {
            background: $THEME_MAIN_COLOR_HOVER;
          }
        }
      }
    }
  }
  .ae-action-config-dialog {
    .action-config-panel {
      margin-top: 20px;
      .common-actions {
        top: -2.0625rem;
      }
    }
  }
  .ae-Scaffold-Modal {
    .ae-Steps {
      max-width: 31.875rem;
      .ae-Steps-Icon {
        margin-top: 0;
      }
    }
  }
}
