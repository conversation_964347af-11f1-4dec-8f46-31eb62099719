// 自定义组件相关文件生成
const fs = require('fs');
const path = require('path');
const chalk = require('chalk');
const utilsFunction = require('./utilsFunction');

function generateFile(filePath, content) {
  if (!fs.existsSync(filePath)) {
    // 路径中，若文件夹不存在则创建该文件夹
    fs.mkdirSync(path.dirname(filePath), { recursive: true });
    fs.writeFile(filePath, content, err => {
      if (err) {
        console.error(err);
        return;
      }
      console.log(chalk.green(`${filePath} 创建成功`));
    });
  } else {
    // console.log(`${filePath} 已存在`);
  }
}

function generateFn(element) {
  const {
    name,
    desc,
    fn = false,
    tag = 'Common',
    prefix = 'pad-pos',
    dirName = 'Business',
  } = element;
  // 相对路径长度
  let pathLength = 2;
  const iDirName = dirName;

  // 存在 index
  const hasIndexPath = /\/index$/.test(iDirName);

  // amis文件夹下 文件路径拼接
  let curPathInAmis;
  // amis-editor 文件夹下，文件路径路径拼接
  let curPathInAmisEditor;

  if (hasIndexPath) {
    // 存在 index 文件夹
    curPathInAmis = path.resolve(`packages/amis/src/renderers/${iDirName}.tsx`);
    curPathInAmisEditor = path.resolve(
      `packages/amis-editor/src/plugin/${iDirName.replace('/index', '')}.tsx`,
    );
    pathLength = iDirName?.split('/')?.length - 1;
  } else if (iDirName.endsWith('.tsx')) {
    // 路径以 tsx 结尾
    curPathInAmis = path.resolve(`packages/amis/src/renderers/${iDirName}`);
    curPathInAmisEditor = path.resolve(
      `packages/amis-editor/src/plugin/${iDirName}`,
    );
    pathLength = iDirName?.split('/')?.length;
  } else {
    curPathInAmis = path.resolve(
      `packages/amis/src/renderers/${iDirName}`,
      `${name}.tsx`,
    );
    curPathInAmisEditor = path.resolve(
      `packages/amis-editor/src/plugin/${iDirName}`,
      `${name}.tsx`,
    );
    pathLength = iDirName?.split('/')?.length + 1;
  }

  // pad pos 组件
  const amisTpContentUseClass = `import React from 'react';
import { Renderer, RendererProps } from 'amis-core';
import { ${name} as I${name} } from 'pos-components';
import { ConfigProvider as ConfigProvider5 } from 'antd-v5';

interface ${name}Props extends RendererProps {}

@Renderer({
  type: 'pad-pos-${utilsFunction.toKebabCase(name)}',
  autoVar: true,
})
export default class ${name} extends React.Component<${name}Props, any> {
  state = {}
  constructor(props: any) {
    super(props);
  }

  componentDidMount() {}

  render() {
    const { className, style } = this.props;

    return (
      <ConfigProvider5 prefixCls="ant5">
        <I${name} classname={className} style={style} />
      </ConfigProvider5>
    )
  }
}
  `;

  // fish 组件
  const amisTpContentUseClassFish = `import { Renderer, RendererProps, uuidv4 } from 'amis-core';
import classNames from 'classnames';
import customClassNameFishInPageDesigner from '../../constant/customClassNameFishInPageDesigner';
import { parseJson, remove$$Ids } from '../../../../utils/common-utils.help';
import React from 'react';

interface ${name}Props extends RendererProps { }

@Renderer({
  type: '${prefix}-${utilsFunction.toKebabCase(name)}',
  autoVar: true,
})
export class ${name} extends React.Component<${name}Props, any> {
  state = {};

  constructor(props: any) {
    super(props);
  }

  id = uuidv4();

  // 渲染 fish 组件
  renderFishComponent = () => {
    this.removeFishComponent();

    const { bfmKey, ${name}OptionsData, ${name}MockData } = this.props;
    
    const curOptions = remove$$Ids(
      ${name}MockData ||
      parseJson(${name}OptionsData) ||
      {},
    );

    if (bfmKey) {
      curOptions.commonDataId = bfmKey;
    }

    const curId = this.id;
    const that: any = this;
    
    window.require(
      [''],
      function (View: any) {
        that.myView = new View({
          ...curOptions,
          el: $(\`#\${curId}\`),
        });
        that.myView.render();
      },
    );
  };

  // 卸载 fish 组件
  removeFishComponent = () => {
    const escapedId = CSS.escape(this.id);
    const myDiv: any = document.querySelector?.(\`#\${escapedId}\`);

    if (myDiv) {
      while (myDiv.firstChild) {
        myDiv.removeChild(myDiv.firstChild);
      }
    }
  }

  componentDidMount() {
    this.renderFishComponent();
  }

  componentDidUpdate(prevProps: Readonly<${name}Props>): void { }

  render() {
    const { className, style } = this.props;
    return (
      <div
        className={classNames(className, customClassNameFishInPageDesigner)}
        style={style}
        id={this.id}
      ></div>
    );
  }
} 
  `;

  // 普通函数式组件
  const amisTpContentUseFnOther = `import { Renderer, RendererProps } from 'amis-core';
import React, { useEffect } from 'react';

interface ${name}Props extends RendererProps {}

class ${name} extends React.Component<${name}Props, any> {
  onCallbackValues = (data: any) => {
    // const { dispatchEvent = () => {} } = this.props;
    // dispatchEvent('onSelect', data);
  };

  render() {
    return (
      <RenderFunc
        {...(this.props as RendererProps)}
        onChange={this.onCallbackValues}
      />
    );
  }
}

const RenderFunc = (props: RendererProps) => {
  const { className, style = {} } = props;

  useEffect(() => {}, []);

  return <div className={className} style={style} />;
};

export default Renderer({
  type: '${prefix}-${utilsFunction.toKebabCase(name)}',
  autoVar: true,
})(${name});
`;

  // 普通类组件
  const amisTpContentUseClassOther = `import React from 'react';
import { Renderer, RendererProps } from 'amis-core';
import classNames from 'classnames';
import styles from  './index.module.less';

interface ${name}Props extends RendererProps {}

@Renderer({
  type: '${prefix}-${utilsFunction.toKebabCase(name)}',
  autoVar: true,
})
export class ${name} extends React.Component<${name}Props, any> {
  state = {};
  
  constructor(props: any) {
    super(props);
  }

  componentDidMount() {}

  render() {
    const { className, style, render, env: { fetcher } } = this.props;

    return (
      <div className={classNames(styles.${utilsFunction.pascalToCamelCase(
        name,
      )}, className)} style={style}>${name}</div>
    )
  }
}
  `;

  // 组件 plugin 信息
  const amisEditorTpContent = `import {
  BaseEventContext,
  BasePlugin,
  getSchemaTpl,
  registerEditorPlugin,
  RendererPluginEvent,
} from 'amis-editor-core';
import { getEventControlConfig } from '${'../'.repeat(
    pathLength,
  )}renderer/event-control/helper';

export class ${name}Plugin extends BasePlugin {
  static id = '${name}Plugin';
  rendererName = '${prefix}-${utilsFunction.toKebabCase(
    name,
  )}'; // 这里需要和注册的渲染器名称保持一致
  $schema = '/schemas/${name}Schema.json'; // 目前还不清楚这个字段的作用

  tags = ['${tag}']; // 组名称
  name = '${desc}';
  isBaseComponent = false; // 控制渲染器在编辑器组件面板中的位置
  isBusinessComponent = true;
  description = '${desc}';

  // 当组件拖入编辑器时，默认插入的schema，参数会作为props传递给组件
  scaffold = {
    type: '${prefix}-${utilsFunction.toKebabCase(name)}',
    name: '${utilsFunction.pascalToCamelCase(name)}',
    editorSetting: {
      mock: {},
    },
  };

  previewSchema = {
    ...this.scaffold,
  };

  panelTitle = '${desc}';

  // 事件定义
  events: RendererPluginEvent[] = [];

  panelBodyCreator = (context: BaseEventContext) => {
    return [
      getSchemaTpl('tabs', [
        {
          title: '属性',
          body: [
            // 修改为自定义的属性
            getSchemaTpl('tplFormulaControl', {
              name: 'custId',
              label: '客户标识'
            }),
          ],
        },
        {
          title: '外观',
          className: 'p-none',
          body: getSchemaTpl('collapseGroup', [
            {
              title: '基本',
              body: [
                {
                  type: 'input-number',
                  label: '长度',
                  min: 0,
                  name: 'style.width',
                },
                {
                  type: 'input-number',
                  label: '高度',
                  min: 1,
                  name: 'style.height',
                },
              ],
            },
            {
              header: '内外边距',
              key: 'box-model',
              body: [
                {
                  type: 'style-box-model',
                  label: false,
                  name: 'style',
                }
              ],
            },
            {
              header: '边框',
              key: 'border',
              body: [
                {
                  type: 'style-border',
                  label: false,
                  name: 'style',
                  disableRadius: true,
                }
              ],
            },
            {
              title: '背景',
              body: [
                {
                  type: 'style-background',
                  label: false,
                  name: 'style',
                  noImage: true,
                }
              ],
            }
          ])
        },
        {
          title: '事件',
          className: 'p-none',
          body: getSchemaTpl('eventControl', {
            name: 'onEvent',
            ...getEventControlConfig(this.manager, context),
          }),
        }
      ])
    ];
  };
}
registerEditorPlugin(${name}Plugin);
  `;

  if (name) {
    switch (prefix) {
      case 'pad-pos':
        generateFile(curPathInAmis, amisTpContentUseClass);
        break;
      case 'crm':
        generateFile(curPathInAmis, amisTpContentUseClassFish);
        break;
      default:
        generateFile(
          curPathInAmis,
          fn ? amisTpContentUseFnOther : amisTpContentUseClassOther,
        );
        break;
    }
    generateFile(curPathInAmisEditor, amisEditorTpContent);
  }
}

// 读取 registeredComponents.json，获取组件名称
fs.readFile(
  path.resolve('registeredComponentsUtils/registeredComponents.json'),
  'utf8',
  (err, data) => {
    if (err) {
      console.log(chalk.red(`registeredComponents.json 读取失败：${err}`));
      return;
    }

    const curData = JSON.parse(data);
    const { conNameList = [] } = curData;
    conNameList.forEach(element => {
      generateFn(element);
    });
  },
);
